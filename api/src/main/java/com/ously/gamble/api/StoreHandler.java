package com.ously.gamble.api;

import com.ously.gamble.payload.purchase.OuslyPurchaseReceipt;
import com.ously.gamble.payload.purchase.RefundOrCancelRequest;
import com.ously.gamble.payload.purchase.RefundOrCancelResponse;
import com.ously.gamble.persistence.model.Purchase;

public interface StoreHandler {

    String getStoreId();

    boolean isActive();

    OuslyPurchaseReceipt verifyPurchase(OuslyPurchaseReceipt opr);

    RefundOrCancelResponse refund(RefundOrCancelRequest req, Purchase p);


}

package com.ously.gamble.api.auth;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

@Schema
@JsonInclude(Include.NON_NULL)
public
class JwtAuthenticationResponse {
    private String accessToken;
    private String tokenType = "Bearer";
    private String refreshToken;
    @NotNull
    private JwtAuthenticationResponseStatus status;

    public JwtAuthenticationResponse() {
    }

    public JwtAuthenticationResponse(String accessToken, String refreshToken, String tType) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.tokenType = tType;
        this.status = JwtAuthenticationResponseStatus.SUCCESS;
    }

    public JwtAuthenticationResponse(String accessToken, String refreshToken) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.status = JwtAuthenticationResponseStatus.SUCCESS;
    }

    public JwtAuthenticationResponse(JwtAuthenticationResponseStatus status) {
        this.status = status;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public JwtAuthenticationResponseStatus getStatus() {
        return status;
    }

    public void setStatus(JwtAuthenticationResponseStatus status) {
        this.status = status;
    }
}

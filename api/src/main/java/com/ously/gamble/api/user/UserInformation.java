package com.ously.gamble.api.user;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.ously.gamble.persistence.model.user.User;
import com.ously.gamble.persistence.model.user.UserAddress;
import com.ously.gamble.persistence.model.user.UserInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

@Schema
@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class UserInformation {

    String name;
    String surname;
    String username;
    String mobile;
    String gender;
    String email;
    String postcode;
    String street;
    String streetNr;
    String city;
    String country;
    LocalDate birthdate;
    String birthplace;

    Boolean emailVerified;
    Boolean newsletterAllowed;
    Boolean agbAccepted;
    Boolean directMsgAllowed;
    Boolean slotSoundOn;
    Boolean gameSoundOn;
    String preferredLanguageCode;
    String userAppSettings;
    Boolean usernamePublicUse = false;
    String extId;
    Map<String, FieldStatus> fieldStatus;

    public UserInformation() {
    }

    public UserInformation(UserInfo userInfo, User user, Map<String, FieldStatus> fieldStatus) {
        this.name = userInfo.getName();
        this.surname = userInfo.getSurname();
        this.mobile = userInfo.getMobileNumber();
        this.gender = userInfo.getGender();
        this.birthdate = userInfo.getBirthDate();
        this.birthplace = userInfo.getBirthPlace();
        this.emailVerified = Objects.requireNonNullElse(user.getEmailVerified(), Boolean.FALSE);
        this.newsletterAllowed = Objects.requireNonNullElse(userInfo.getNewsletterEnabled(), false);
        this.agbAccepted = Objects.requireNonNullElse(userInfo.getAgbSigned(), true);
        this.directMsgAllowed = Objects.requireNonNullElse(userInfo.getDirectContactEnabled(), true);
        this.username = user.getDisplayName();
        this.slotSoundOn = userInfo.getSlotSound();
        this.gameSoundOn = userInfo.getGameSound();
        this.preferredLanguageCode = userInfo.getLangCode();
        this.userAppSettings = userInfo.getUserAppSettings();
        this.fieldStatus = fieldStatus;
        this.usernamePublicUse = userInfo.getUsernamePublic();
        this.extId = user.getExtId();
        if (this.usernamePublicUse == null) {
            this.usernamePublicUse = Boolean.FALSE;
        }
        if (!user.getEmail().endsWith("@nomail.nomail")) {
            this.email = user.getEmail();
        } else {
            this.email = null;
        }
    }

    /**
     * @return true, if any item was changed vs. prior setting
     */
    public boolean updateModel(UserInfo userInfo, User user) {
        var updated = false;

        // username acceptance only once (from false to true)
        if (userInfo.getUsernamePublic() != null) {
            if ((!userInfo.getUsernamePublic()) && changed(this.usernamePublicUse, userInfo.getUsernamePublic())) {
                userInfo.setUsernamePublic(true);
                updated = true;
            }
        }

        if (changed(this.username, user.getDisplayName())) {
            user.setDisplayName(this.username);
            // we assume that this also includes the acceptance of public use
            userInfo.setUsernamePublic(true);
            updated = true;
        }

        if (changed(this.name, userInfo.getName())) {
            userInfo.setName(name);
            updated = true;
        }
        if (changed(this.surname, userInfo.getSurname())) {
            userInfo.setSurname(surname);
            updated = true;
        }
        if (changed(this.mobile, userInfo.getMobileNumber())) {
            userInfo.setMobileNumber(mobile);
            updated = true;
        }
        if (changed(this.gender, userInfo.getGender())) {
            userInfo.setGender(gender);
            updated = true;
        }
        if (changed(this.birthdate, userInfo.getBirthDate())) {
            userInfo.setBirthDate(birthdate);
            updated = true;
        }
        if (changed(this.birthplace, userInfo.getBirthPlace())) {
            userInfo.setBirthPlace(birthplace);
            updated = true;
        }
        if (changed(this.newsletterAllowed, userInfo.getNewsletterEnabled())) {
            userInfo.setNewsletterEnabled(newsletterAllowed);
            updated = true;
        }
        if (changed(this.agbAccepted, userInfo.getAgbSigned())) {
            userInfo.setAgbSigned(agbAccepted);
            updated = true;
        }
        if (changed(this.directMsgAllowed, userInfo.getDirectContactEnabled())) {
            userInfo.setDirectContactEnabled(directMsgAllowed);
            updated = true;
        }

        if (changed(this.slotSoundOn, userInfo.getSlotSound())) {
            userInfo.setSlotSound(this.slotSoundOn);
            updated = true;
        }

        if (changed(this.gameSoundOn, userInfo.getGameSound())) {
            userInfo.setSlotSound(this.gameSoundOn);
            updated = true;
        }

        if (changed(this.preferredLanguageCode, userInfo.getLangCode())) {
            if (StringUtils.isNotEmpty(this.preferredLanguageCode)) {
                userInfo.setLangCode(this.preferredLanguageCode.toUpperCase(Locale.ROOT));
            } else {
                userInfo.setLangCode("EN");
            }
            updated = true;
        }

        // Address
        if (userInfo.getAddress() == null) {
            userInfo.setAddress(new UserAddress());
        }

        if (changed(this.postcode, userInfo.getAddress().getZipCode())) {
            userInfo.getAddress().setZipCode(this.postcode);
            updated = true;
        }
        if (changed(this.street, userInfo.getAddress().getStreet())) {
            userInfo.getAddress().setStreet(this.street);
            updated = true;
        }
        if (changed(this.city, userInfo.getAddress().getCity())) {
            userInfo.getAddress().setCity(this.city);
            updated = true;
        }
        if (changed(this.streetNr, userInfo.getAddress().getStreetNr())) {
            userInfo.getAddress().setStreetNr(this.streetNr);
            updated = true;
        }
        if (changed(this.country, userInfo.getAddress().getCountry())) {
            userInfo.getAddress().setCountry(this.country);
            updated = true;
        }


        if (changed(this.userAppSettings, userInfo.getUserAppSettings())) {
            userInfo.setUserAppSettings(this.userAppSettings);
            updated = true;
        }

        return updated;
    }


    private static boolean changed(LocalDate newDate, LocalDate oldDate) {
        if (newDate == null) {
            return false;
        }
        return !newDate.equals(oldDate);
    }

    private static boolean changed(String newVal, String oldVal) {
        if (newVal == null) {
            return false;
        }
        return !newVal.equals(oldVal);
    }

    private static boolean changed(Boolean newBool, Boolean oldBool) {
        if (newBool == null) {
            return false;
        }
        return !newBool.equals(oldBool);
    }

    public String getUserAppSettings() {
        return userAppSettings;
    }

    public void setUserAppSettings(String userAppSettings) {
        this.userAppSettings = userAppSettings;
    }

    public Boolean getSlotSoundOn() {
        return slotSoundOn;
    }

    public void setSlotSoundOn(Boolean slotSoundOn) {
        this.slotSoundOn = slotSoundOn;
    }

    public Boolean getGameSoundOn() {
        return gameSoundOn;
    }

    public void setGameSoundOn(Boolean gameSoundOn) {
        this.gameSoundOn = gameSoundOn;
    }

    public String getPreferredLanguageCode() {
        return preferredLanguageCode;
    }

    public void setPreferredLanguageCode(String preferredLanguageCode) {
        this.preferredLanguageCode = preferredLanguageCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSurname() {
        return surname;
    }

    public void setSurname(String surname) {
        this.surname = surname;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public LocalDate getBirthdate() {
        return birthdate;
    }

    public void setBirthdate(LocalDate birthdate) {
        this.birthdate = birthdate;
    }

    public Boolean getNewsletterAllowed() {
        return newsletterAllowed;
    }

    public void setNewsletterAllowed(Boolean newsletterAllowed) {
        this.newsletterAllowed = newsletterAllowed;
    }

    public Boolean getAgbAccepted() {
        return agbAccepted;
    }

    public void setAgbAccepted(Boolean agbAccepted) {
        this.agbAccepted = agbAccepted;
    }

    public Boolean getDirectMsgAllowed() {
        return directMsgAllowed;
    }

    public void setDirectMsgAllowed(Boolean directMsgAllowed) {
        this.directMsgAllowed = directMsgAllowed;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getExtId() {
        return extId;
    }

    public void setExtId(String extId) {
        this.extId = extId;
    }

    public Map<String, FieldStatus> getFieldStatus() {
        return fieldStatus;
    }

    public void setFieldStatus(Map<String, FieldStatus> fieldStatus) {
        this.fieldStatus = fieldStatus;
    }

    public Boolean getUsernamePublicUse() {
        return Objects.requireNonNullElse(this.usernamePublicUse, false);
    }

    public void setUsernamePublicUse(Boolean usernamePublicUse) {
        this.usernamePublicUse = usernamePublicUse;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getStreetNr() {
        return streetNr;
    }

    public void setStreetNr(String streetNr) {
        this.streetNr = streetNr;
    }

    public String getBirthplace() {
        return birthplace;
    }

    public void setBirthplace(String birthplace) {
        this.birthplace = birthplace;
    }
}

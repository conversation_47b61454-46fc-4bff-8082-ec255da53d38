package com.ously.gamble.api.crm;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ously.gamble.persistence.dto.CRMUserDetails;
import com.ously.gamble.persistence.dto.CRMUserRevDetails;
import com.ously.gamble.persistence.model.Wallet;
import com.ously.gamble.persistence.model.user.User;
import com.ously.gamble.persistence.model.user.UserInfo;
import com.ously.gamble.persistence.model.user.UserRegistration;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Objects;


public final class CRMUserUpdate {

    private static final BigDecimal BD100 = new BigDecimal(100);

    long timestamp = Instant.now().getEpochSecond();
    String country;
    String localId;
    long userId;
    String status;
    String email;
    long createdAt;
    String displayName;
    String mobileNumber;
    String loginProvider;
    String birthdate;
    boolean anonymous;
    CRMPlatform platform = CRMPlatform.UNKNOWN;
    String language;
    Integer piggybank;
    String name;
    String surname;
    boolean agb;
    boolean newsletter;
    boolean directcontact;
    String gender;
    @JsonProperty("signin_provider")
    String signinProvider;
    BigDecimal balance;
    long level=1;
    long levelProgress;
    String lastPurchaseDate;
    String lastPurchaseItem;
    Long lastPurchaseAt;
    Long numPurchases;
    BigDecimal sumPurchases;

    // Deposits/Payouts

    Long lastDepositAt;
    Long countDeposits;
    BigDecimal sumDeposits;

    Long lastPayoutAt;
    Long countPayouts;
    BigDecimal sumPayouts;

    BigDecimal sumRakebacks;
    Long numRakebacks;
    BigDecimal sumAffrew;
    Long numAffrew;

    String tags;

    BigDecimal avgBet;

    // Game stats
    Integer lastPlayedGame;
    Integer mostPlayedGame;

    // Revenue stats (only for paying users)

    BigDecimal revPerWeek1;
    BigDecimal revPerWeek2;
    BigDecimal revPerWeek4;
    BigDecimal revPerWeek8;
    BigDecimal revPerWeek12;

    BigDecimal pkgsAvgPerWeek1;
    BigDecimal pkgsAvgPerWeek2;
    BigDecimal pkgsAvgPerWeek4;
    BigDecimal pkgsAvgPerWeek8;
    BigDecimal pkgsAvgPerWeek12;


    BigDecimal totalRev;

    Integer earliestRevWeek;
    Integer latestRevWeek;


    public CRMUserUpdate(Long userId) {
        this.userId = userId;
    }

    public CRMUserUpdate(CRMUserDetails crmDetails, CRMUserRevDetails revDetails,
                         String country) {
        this.userId = crmDetails.getUid();
        this.country = Objects.requireNonNullElse(country, "EN");
        this.status = crmDetails.getStatus();
        this.email = crmDetails.getEmail();
        this.displayName = crmDetails.getDisplay_name();
        this.createdAt = crmDetails.getCreated_at();
        this.loginProvider = crmDetails.getLogin_provider();
        this.localId = crmDetails.getLocal_id();
        this.agb = crmDetails.getAgb();
        this.birthdate = crmDetails.getBirthdate();
        this.directcontact = crmDetails.getDirectcontact();
        this.gender = crmDetails.getGender();
        this.signinProvider = crmDetails.getLogin_provider();
        this.mobileNumber = crmDetails.getMobile_number();
        this.newsletter = crmDetails.getNewsletter();
        this.name = crmDetails.getU_name();
        this.surname = crmDetails.getU_surname();
        this.balance = crmDetails.getBalance();
        this.level = crmDetails.getLevel()+1;
        this.levelProgress = crmDetails.getLevel_progress();
        this.piggybank = crmDetails.getPiggybank();
        this.sumPurchases = crmDetails.getSum_purchases();
        this.numPurchases = crmDetails.getCount_purchases();
        this.lastPurchaseAt = crmDetails.getLast_purchase_at();
        this.lastPurchaseItem = null;
        this.language = crmDetails.getLanguage();
        this.tags = crmDetails.getTags();
        this.avgBet = crmDetails.getAvgBet();
        if (revDetails != null) {
            this.revPerWeek1 = revDetails.getRevPerWeek1();
            this.revPerWeek2 = revDetails.getRevPerWeek2();
            this.revPerWeek4 = revDetails.getRevPerWeek4();
            this.revPerWeek8 = revDetails.getRevPerWeek8();
            this.revPerWeek12 = revDetails.getRevPerWeek12();

            this.pkgsAvgPerWeek1 = revDetails.getPkgsAvgPerWeek1();
            this.pkgsAvgPerWeek2 = revDetails.getPkgsAvgPerWeek2();
            this.pkgsAvgPerWeek4 = revDetails.getPkgsAvgPerWeek4();
            this.pkgsAvgPerWeek8 = revDetails.getPkgsAvgPerWeek8();
            this.pkgsAvgPerWeek12 = revDetails.getPkgsAvgPerWeek12();

            this.totalRev = revDetails.getTotalRev();
            this.earliestRevWeek = revDetails.getEarliestRevWeek();
            this.latestRevWeek = revDetails.getLatestRevWeek();
        }
    }

    public void setGameStats(int lastPlayed, int mostPlayed) {
        this.lastPlayedGame = lastPlayed;
        this.mostPlayedGame = mostPlayed;
    }

    public CRMUserUpdate(User user) {
        this(user, null);
    }

    public CRMUserUpdate(User user, UserInfo ui) {
        this(user, ui, null, null);
    }

    public CRMUserUpdate(User user, UserInfo ui, Wallet wallet, UserRegistration userRegistration) {
        super();
        this.status = user.getStatus().name();
        this.userId = user.getId();
        this.createdAt = user.getCreatedAt().getEpochSecond();
        this.loginProvider = user.getSigninProvider();
        this.anonymous = getAnonymousState(user, userRegistration);
        if (!this.anonymous) {
            this.email = user.getEmail();
        }
        this.displayName = user.getDisplayName();

        if (ui != null) {
            this.mobileNumber = ui.getMobileNumber();
            this.birthdate = CRMUtils.createBirthdateInIso8601(ui.getBirthDate());

            if (ui.getLangCode() != null) {
                this.language = ui.getLangCode();
            } else {
                this.language = "DE";
            }

            if (ui.getName() != null) {
                this.name = ui.getName();
            }
            if (ui.getSurname() != null) {
                this.surname = ui.getSurname();
            }
            if (ui.getAgbSigned() != null) {
                this.agb = ui.getAgbSigned();
            }
            if (ui.getNewsletterEnabled() != null) {
                this.newsletter = ui.getNewsletterEnabled();
            }
            if (ui.getDirectContactEnabled() != null) {
                this.directcontact = ui.getDirectContactEnabled();
            }
            if (ui.getBirthDate() != null) {
                this.birthdate = CRMUtils.createBirthdateInIso8601(ui.getBirthDate());
            }
            if (ui.getGender() != null) {
                this.gender = ui.getGender();
            }
            if (ui.getMobileNumber() != null) {
                this.mobileNumber = ui.getMobileNumber();
            }
        }
        if (wallet != null) {
            this.balance = wallet.getBalance();
            this.level = wallet.getLevel()+1;
            this.levelProgress = wallet.getPercnl().multiply(BD100).longValue();
            this.piggybank = Objects.requireNonNullElse(wallet.getSaveup(), BigDecimal.ZERO).intValue();
        }
        checkEntries();
    }

    public CRMUserUpdate() {
        super();
    }


    private static boolean getAnonymousState(User user, UserRegistration userRegistration) {
        var login_provider = user.getSigninProvider();
        if (userRegistration != null) {
            login_provider = userRegistration.getLoginProvider();
        }
        return StringUtils.isEmpty(login_provider) || "anonymous".equalsIgnoreCase(login_provider) || "custom".equalsIgnoreCase(login_provider);
    }


    public String getBirthdate() {
        return birthdate;
    }

    public CRMPlatform getPlatform() {
        return platform;
    }

    @JsonIgnore
    private void checkEntries() {
        if (email != null) {
            if (email.contains("@nomail.nomail")) {
                email = null;
            }
        }
    }

    public boolean isAnonymous() {
        return anonymous;
    }


    public String getEmail() {
        return email;
    }


    public Boolean getAgb() {
        return agb;
    }


    public Boolean getNewsletter() {
        return newsletter;
    }


    public Boolean getDirectcontact() {
        return directcontact;
    }


    public String getGender() {
        return gender;
    }


    public BigDecimal getBalance() {
        return balance;
    }


    public Long getLevel() {
        return level;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getLocalId() {
        return localId;
    }

    public void setLocalId(String localId) {
        this.localId = localId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getLoginProvider() {
        return loginProvider;
    }

    public void setLoginProvider(String loginProvider) {
        this.loginProvider = loginProvider;
    }

    public void setBirthdate(String birthdate) {
        this.birthdate = birthdate;
    }

    public void setAnonymous(boolean anonymous) {
        this.anonymous = anonymous;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public void setPiggybank(Integer piggybank) {
        this.piggybank = piggybank;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSurname() {
        return surname;
    }

    public void setSurname(String surname) {
        this.surname = surname;
    }

    public void setAgb(Boolean agb) {
        this.agb = agb;
    }

    public void setNewsletter(Boolean newsletter) {
        this.newsletter = newsletter;
    }

    public void setDirectcontact(Boolean directcontact) {
        this.directcontact = directcontact;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public void setLevel(long level) {
        this.level = level;
    }

    public long getLevelProgress() {
        return levelProgress;
    }

    public void setLevelProgress(long levelProgress) {
        this.levelProgress = levelProgress;
    }

    public String getLastPurchaseDate() {
        return lastPurchaseDate;
    }

    public void setLastPurchaseDate(String lastPurchaseDate) {
        this.lastPurchaseDate = lastPurchaseDate;
    }

    public String getLastPurchaseItem() {
        return lastPurchaseItem;
    }

    public void setLastPurchaseItem(String lastPurchaseItem) {
        this.lastPurchaseItem = lastPurchaseItem;
    }

    public Long getLastPurchaseAt() {
        return lastPurchaseAt;
    }

    public void setLastPurchaseAt(Long lastPurchaseAt) {
        this.lastPurchaseAt = lastPurchaseAt;
    }

    public Long getNumPurchases() {
        return numPurchases;
    }

    public void setNumPurchases(Long numPurchases) {
        this.numPurchases = numPurchases;
    }

    public BigDecimal getSumPurchases() {
        return sumPurchases;
    }

    public void setSumPurchases(BigDecimal sumPurchases) {
        this.sumPurchases = sumPurchases;
    }

    public Long getLastDepositAt() {
        return lastDepositAt;
    }

    public void setLastDepositAt(Long lastDepositAt) {
        this.lastDepositAt = lastDepositAt;
    }

    public Long getCountDeposits() {
        return countDeposits;
    }

    public void setCountDeposits(Long countDeposits) {
        this.countDeposits = countDeposits;
    }

    public BigDecimal getSumDeposits() {
        return sumDeposits;
    }

    public void setSumDeposits(BigDecimal sumDeposits) {
        this.sumDeposits = sumDeposits;
    }

    public Long getLastPayoutAt() {
        return lastPayoutAt;
    }

    public void setLastPayoutAt(Long lastPayoutAt) {
        this.lastPayoutAt = lastPayoutAt;
    }

    public Long getCountPayouts() {
        return countPayouts;
    }

    public void setCountPayouts(Long countPayouts) {
        this.countPayouts = countPayouts;
    }

    public BigDecimal getSumPayouts() {
        return sumPayouts;
    }

    public void setSumPayouts(BigDecimal sumPayouts) {
        this.sumPayouts = sumPayouts;
    }

    public BigDecimal getSumRakebacks() {
        return sumRakebacks;
    }

    public void setSumRakebacks(BigDecimal sumRakebacks) {
        this.sumRakebacks = sumRakebacks;
    }

    public Long getNumRakebacks() {
        return numRakebacks;
    }

    public void setNumRakebacks(Long numRakebacks) {
        this.numRakebacks = numRakebacks;
    }

    public BigDecimal getSumAffrew() {
        return sumAffrew;
    }

    public void setSumAffrew(BigDecimal sumAffrew) {
        this.sumAffrew = sumAffrew;
    }

    public Long getNumAffrew() {
        return numAffrew;
    }

    public void setNumAffrew(Long numAffrew) {
        this.numAffrew = numAffrew;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getLanguage() {
        return language;
    }

    public Integer getPiggybank() {
        return piggybank;
    }

    public void setPlatform(CRMPlatform platform) {
        this.platform = platform;
    }

    public BigDecimal getAvgBet() {
        return avgBet;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "CRMUserUpdate{" +
                "timestamp=" + timestamp +
                ", userId=" + userId +
                ", email='" + email + '\'' +
                '}';
    }

    public Integer getLastPlayedGame() {
        return lastPlayedGame;
    }

    public void setLastPlayedGame(Integer lastPlayedGame) {
        this.lastPlayedGame = lastPlayedGame;
    }

    public Integer getMostPlayedGame() {
        return mostPlayedGame;
    }

    public void setMostPlayedGame(Integer mostPlayedGame) {
        this.mostPlayedGame = mostPlayedGame;
    }

    public BigDecimal getRevPerWeek1() {
        return revPerWeek1;
    }

    public void setRevPerWeek1(BigDecimal revPerWeek1) {
        this.revPerWeek1 = revPerWeek1;
    }

    public BigDecimal getRevPerWeek2() {
        return revPerWeek2;
    }

    public void setRevPerWeek2(BigDecimal revPerWeek2) {
        this.revPerWeek2 = revPerWeek2;
    }

    public BigDecimal getRevPerWeek4() {
        return revPerWeek4;
    }

    public void setRevPerWeek4(BigDecimal revPerWeek4) {
        this.revPerWeek4 = revPerWeek4;
    }

    public BigDecimal getRevPerWeek8() {
        return revPerWeek8;
    }

    public void setRevPerWeek8(BigDecimal revPerWeek8) {
        this.revPerWeek8 = revPerWeek8;
    }

    public BigDecimal getRevPerWeek12() {
        return revPerWeek12;
    }

    public void setRevPerWeek12(BigDecimal revPerWeek12) {
        this.revPerWeek12 = revPerWeek12;
    }

    public BigDecimal getTotalRev() {
        return totalRev;
    }

    public void setTotalRev(BigDecimal totalRev) {
        this.totalRev = totalRev;
    }

    public Integer getEarliestRevWeek() {
        return earliestRevWeek;
    }

    public void setEarliestRevWeek(Integer earliestRevWeek) {
        this.earliestRevWeek = earliestRevWeek;
    }

    public Integer getLatestRevWeek() {
        return latestRevWeek;
    }

    public void setLatestRevWeek(Integer latestRevWeek) {
        this.latestRevWeek = latestRevWeek;
    }

    public BigDecimal getPkgsAvgPerWeek1() {
        return pkgsAvgPerWeek1;
    }

    public void setPkgsAvgPerWeek1(BigDecimal pkgsAvgPerWeek1) {
        this.pkgsAvgPerWeek1 = pkgsAvgPerWeek1;
    }

    public BigDecimal getPkgsAvgPerWeek2() {
        return pkgsAvgPerWeek2;
    }

    public void setPkgsAvgPerWeek2(BigDecimal pkgsAvgPerWeek2) {
        this.pkgsAvgPerWeek2 = pkgsAvgPerWeek2;
    }

    public BigDecimal getPkgsAvgPerWeek4() {
        return pkgsAvgPerWeek4;
    }

    public void setPkgsAvgPerWeek4(BigDecimal pkgsAvgPerWeek4) {
        this.pkgsAvgPerWeek4 = pkgsAvgPerWeek4;
    }

    public BigDecimal getPkgsAvgPerWeek8() {
        return pkgsAvgPerWeek8;
    }

    public void setPkgsAvgPerWeek8(BigDecimal pkgsAvgPerWeek8) {
        this.pkgsAvgPerWeek8 = pkgsAvgPerWeek8;
    }

    public BigDecimal getPkgsAvgPerWeek12() {
        return pkgsAvgPerWeek12;
    }

    public void setPkgsAvgPerWeek12(BigDecimal pkgsAvgPerWeek12) {
        this.pkgsAvgPerWeek12 = pkgsAvgPerWeek12;
    }

    public String getSigninProvider() {
        return signinProvider;
    }

    public void setSigninProvider(String signinProvider) {
        this.signinProvider = signinProvider;
    }

    public String getSigninmethod() {
        return signinProvider;
    }

    public void setSigninmethod(String signinmethod) {
        this.signinProvider = signinmethod;
    }

    public String getCountry() {
        return country;
    }
}

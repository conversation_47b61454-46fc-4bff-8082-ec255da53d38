package com.ously.gamble.api.user;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

public class UserActivePerk implements Serializable {

    @JsonIgnore
    private long validUntil;
    @JsonProperty("type")
    @NotNull
    private String multiplierType;
    @JsonProperty("multiplier")
    @NotNull
    private double multiplier;

    public UserActivePerk() {
    }

    public UserActivePerk(Perk p) {
        this.multiplier = p.getMultiplier();
        this.multiplierType = p.getType().toString();
        this.validUntil = p.getValidUntil();
    }

    public boolean isValid() {
        return (this.validUntil >= Instant.now().getEpochSecond());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        var that = (UserActivePerk) o;
        return Double.compare(that.multiplier, multiplier) == 0 &&
                multiplierType.equals(that.multiplierType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(multiplierType, multiplier);
    }

    @JsonGetter("remainingSeconds")
    public long getRemainingSeconds() {
        return validUntil - (Instant.now().getEpochSecond());
    }

    public String getMultiplierType() {
        return multiplierType;
    }

    public void setMultiplierType(String multiplierType) {
        this.multiplierType = multiplierType;
    }

    public double getMultiplier() {
        return multiplier;
    }

    public void setMultiplier(double multiplier) {
        this.multiplier = multiplier;
    }

    public long getValidUntil() {
        return validUntil;
    }

    public void setValidUntil(long validUntil) {
        this.validUntil = validUntil;
    }
}

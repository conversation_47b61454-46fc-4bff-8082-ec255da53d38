package com.ously.gamble.api.features;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

public class InitiateDoubleUpResponse {

    private static final List<GambleWeapon> EMPTY_WEAPONS_LIST = Collections.emptyList();

    private static final List<GambleFoe> EMPTY_FOES_LIST = Collections.emptyList();


    @NotNull
    String secret;

    @NotNull
    Long round;

    @Schema(format = "double")
    @NotNull
    BigDecimal maxBetAmount;

    @NotNull
    List<GambleWeapon> weapons = EMPTY_WEAPONS_LIST;

    @NotNull
    List<GambleFoe> foes = EMPTY_FOES_LIST;

    public List<GambleFoe> getFoes() {
        return foes;
    }

    public void setFoes(List<GambleFoe> foes) {
        this.foes = foes;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public Long getRound() {
        return round;
    }

    public void setRound(Long round) {
        this.round = round;
    }

    public BigDecimal getMaxBetAmount() {
        return maxBetAmount;
    }

    public void setMaxBetAmount(BigDecimal maxBetAmount) {
        this.maxBetAmount = maxBetAmount;
    }

    public List<GambleWeapon> getWeapons() {
        return weapons;
    }

    public void setWeapons(List<GambleWeapon> weapons) {
        this.weapons = weapons;
    }
}

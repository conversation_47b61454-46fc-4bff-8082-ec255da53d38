package com.ously.gamble.api.bridge;

import java.io.Serializable;

public class GameSettings implements Serializable {

    String language = "DE";
    boolean soundOff;
    String userCountry = "DE";
    String ip;

    int betLevel;
    String stage = "test";

    public int getBetLevel() {
        return betLevel;
    }

    public void setBetLevel(int betLevel) {
        this.betLevel = betLevel;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public boolean isSoundOff() {
        return soundOff;
    }

    public void setSoundOff(boolean soundOff) {
        this.soundOff = soundOff;
    }

    public String getUserCountry() {
        return userCountry;
    }

    public void setUserCountry(String userCountry) {
        this.userCountry = userCountry;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }


    @Override
    public String toString() {
        return "GameSettings{" +
                "language='" + language + '\'' +
                ", soundOff=" + soundOff +
                ", userCountry='" + userCountry + '\'' +
                ", ip='" + ip + '\'' +
                ", betLevel=" + betLevel +
                ", stage='" + stage + '\'' +
                '}';
    }
}

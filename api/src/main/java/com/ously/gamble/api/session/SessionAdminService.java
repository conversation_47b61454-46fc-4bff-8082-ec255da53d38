package com.ously.gamble.api.session;

import com.ously.gamble.api.statistics.CacheStatistics;
import com.ously.gamble.api.statistics.DailyProviderStatistics;
import com.ously.gamble.api.user.UserSessionInfoPL;
import com.ously.gamble.persistence.projections.ProviderGameStatisticPJ;
import org.springframework.data.domain.Page;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;

public interface SessionAdminService {
    void deactivateExpiredSessions(int maxSessions);

    void migrateOldSessionDataToNewSession(long userId, long sessionId, boolean force);

    SessionStatistics updateSessionStatistics(long sessionId, long userId);

    CacheStatistics getCacheStatistics();

    List<DailyProviderStatistics> getMonthlyTotal(int year, int month, boolean fillEmptyDays,
                                                  boolean excludeOusly);

    List<DailyProviderStatistics> getMonthlyProviderStats(String providerName, LocalDate from,
                                                          LocalDate to, boolean emptyDays,
                                                          boolean excludeOusly);

    List<ProviderGameStatisticPJ> getProviderGameStats(String providerName, LocalDate from,
                                                       LocalDate to, Boolean excludeOusly);

    Page<UserSessionInfoPL> getStatisticsForUser(Long userId, int page, int size);

//    Page<TransactionPL> getTransactionsForSession(Long sessionId, int page, int size);

    List<Long> findSessionsToMigrate(ZonedDateTime from, ZonedDateTime to, List<String> providers);

    List<Long> findSessionsToMigrate(ZonedDateTime from, ZonedDateTime to, List<String> providers,
                                     boolean force);

    List<Long> findSessionsToRefresh(ZonedDateTime from, ZonedDateTime to);

    void doOldTxRemoval(Long userId, Long sessionId);

    int migrateInactiveSessions(int minAge, int maxSessions, long backlookDays);

    int recalcStatisticsForNewInactive(int minAge, int maxSessionsPerCall);

    SessionStatistics recreateStatsForUserAndSession(long userId, Long sessionId);
}

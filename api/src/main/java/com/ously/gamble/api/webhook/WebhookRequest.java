package com.ously.gamble.api.webhook;

import jakarta.validation.constraints.NotEmpty;

public class WebhookRequest {
    @NotEmpty
    protected String sender;
    @NotEmpty
    protected WebhookAction action;

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public WebhookAction getAction() {
        return action;
    }

    public void setAction(WebhookAction action) {
        this.action = action;
    }

    @Override
    public String toString() {
        return "WebhookRequest{" +
               "sender='" + sender + '\'' +
               ", action='" + action + '\'' +
               '}';
    }
}

package com.ously.gamble.api.bridge;

import com.ously.gamble.api.games.CasinoGame;

import java.io.Serializable;
import java.time.Instant;

public class CreateFreespinsRequest implements Serializable {
    long userId;
    CasinoGame game;
    int level;
    int count;
    String bonusId;
    Instant validFrom;
    Instant validTo;

    public CreateFreespinsRequest(long userId, CasinoGame game, int level, int count,
                                  String bonusId, Instant validFrom, Instant validTo) {
        this.userId = userId;
        this.game = game;
        this.level = level;
        this.count = count;
        this.bonusId = bonusId;
        this.validFrom = validFrom;
        this.validTo = validTo;
    }

    public CreateFreespinsRequest() {
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public CasinoGame getGame() {
        return game;
    }

    public void setGame(CasinoGame game) {
        this.game = game;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getBonusId() {
        return bonusId;
    }

    public void setBonusId(String bonusId) {
        this.bonusId = bonusId;
    }

    public Instant getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(Instant validFrom) {
        this.validFrom = validFrom;
    }

    public Instant getValidTo() {
        return validTo;
    }

    public void setValidTo(Instant validTo) {
        this.validTo = validTo;
    }
}

package com.ously.gamble.api.games;

import com.ously.gamble.persistence.dto.CasinoGameCategory;
import com.ously.gamble.persistence.model.game.Game;
import com.ously.gamble.persistence.model.game.GameGenre;
import com.ously.gamble.persistence.model.game.GameJackpotMode;
import com.ously.gamble.persistence.model.game.GameType;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;


/**
 * Administratable game (can be modified by administrator)
 */
public class CasinoGame implements Serializable {
    static final List<CasinoGameCategory> emptyCats = Collections.emptyList();

    @NotNull
    private String gameId;
    @NotNull
    private long id;

    private Integer vendorId;

    @NotNull
    private String name;
    @NotNull
    private boolean active;
    @NotNull
    private boolean mobile;
    @NotNull
    private boolean desktop;
    @NotNull
    private boolean ios = true;
    @NotNull
    private boolean android = true;
    @NotNull
    private String providerName;


    private GameGenre genre;
    private GameType type;
    private GameJackpotMode jackpotMode;
    private LocalDate releaseDate;


    private String title;
    private Instant createdAt;
    private Instant updatedAt;
    @NotNull
    private List<CasinoGameCategory> categories;

    private Set<Integer> gametags;
    private boolean lastPlayed;
    private int slotrank = 99999;

    private Integer gameInfoId;

    private int sortOrder = 99999;

    private BigDecimal rtp;

    private String smallThumb;
    private String mediumThumb;
    private String largeThumb;

    private int level;

    private String blockedCountries;

    public CasinoGame() {
    }

    public CasinoGame(Game aGame) {
        id = aGame.getId();
        vendorId = aGame.getVendor().getId();
        if (aGame.getVendor() != null) {
            blockedCountries = Objects.requireNonNullElse(aGame.getVendor().getBlockedCountries()
                    , "");
        }
        name = aGame.getName();
        active = aGame.isActive();
        mobile = aGame.isMobile();
        desktop = aGame.isDesktop();
        android = aGame.isAndroid();
        genre = aGame.getGenre();
        type = aGame.getType();
        jackpotMode = aGame.getJackpotMode();
        releaseDate = aGame.getReleaseDate();
        ios = aGame.isIos();
        level = aGame.getLevel();
        rtp = (aGame.getRtp() == null) ? null : aGame.getRtp();
        sortOrder = (aGame.getSortOrder() == null) ? 99999 : aGame.getSortOrder();
        providerName = aGame.getProviderName();
        title = aGame.getName();
        gameId = aGame.getGameId();
        updatedAt = aGame.getUpdatedAt();
        createdAt = aGame.getCreatedAt();
        this.categories = emptyCats;
    }

    public CasinoGame(Game aGame, String baseUrl) {
        id = aGame.getId();
        if (aGame.getVendor() != null) {
            vendorId = aGame.getVendor().getId();
            blockedCountries = Objects.requireNonNullElse(aGame.getVendor().getBlockedCountries()
                    , "");
        }
        name = aGame.getName();
        active = aGame.isActive();
        mobile = aGame.isMobile();
        desktop = aGame.isDesktop();
        android = aGame.isAndroid();

        genre = aGame.getGenre();
        type = aGame.getType();
        jackpotMode = aGame.getJackpotMode();
        releaseDate = aGame.getReleaseDate();

        ios = aGame.isIos();
        level = aGame.getLevel();
        rtp = (aGame.getRtp() == null) ? null : aGame.getRtp();
        sortOrder = (aGame.getSortOrder() == null) ? 99999 : aGame.getSortOrder();
        providerName = aGame.getProviderName();
        title = aGame.getName();
        gameId = aGame.getGameId();
        updatedAt = aGame.getUpdatedAt();
        createdAt = aGame.getCreatedAt();
        if (baseUrl != null) {
            this.smallThumb = baseUrl + aGame.getId() + "_s.jpg";
            this.mediumThumb = baseUrl + aGame.getId() + "_m.jpg";
            this.largeThumb = baseUrl + aGame.getId() + "_l.jpg";
        }


        if (aGame.getCategories() != null) {
            this.categories =
                    aGame.getCategories().stream().filter(Objects::nonNull).map(CasinoGameCategory::new).sorted(Comparator.comparing(CasinoGameCategory::getName)).toList();
        } else {
            this.categories = emptyCats;
        }
        if (aGame.getGameInfo() != null) {
            slotrank = aGame.getGameInfo().getSlotRank().intValue();
            gameInfoId = aGame.getGameInfo().getId();
        }
    }

    public CasinoGame(Game aGame, String baseUrl, List<CasinoGameCategory> cats) {
        id = aGame.getId();
        if (aGame.getVendor() != null) {
            vendorId = aGame.getVendor().getId();
            blockedCountries = Objects.requireNonNullElse(aGame.getVendor().getBlockedCountries()
                    , "");
        }
        name = aGame.getName();
        active = aGame.isActive();
        mobile = aGame.isMobile();
        desktop = aGame.isDesktop();
        android = aGame.isAndroid();

        genre = aGame.getGenre();
        type = aGame.getType();
        jackpotMode = aGame.getJackpotMode();
        releaseDate = aGame.getReleaseDate();

        ios = aGame.isIos();
        level = aGame.getLevel();
        rtp = (aGame.getRtp() == null) ? null : aGame.getRtp();
        sortOrder = (aGame.getSortOrder() == null) ? 99999 : aGame.getSortOrder();
        providerName = aGame.getProviderName();
        title = aGame.getName();
        gameId = aGame.getGameId();
        updatedAt = aGame.getUpdatedAt();
        createdAt = aGame.getCreatedAt();
        if (baseUrl != null) {
            this.smallThumb = baseUrl + aGame.getId() + "_s.jpg";
            this.mediumThumb = baseUrl + aGame.getId() + "_m.jpg";
            this.largeThumb = baseUrl + aGame.getId() + "_l.jpg";
        }

        categories = Objects.requireNonNullElse(cats, emptyCats);
        if (aGame.getGameInfo() != null) {
            slotrank = aGame.getGameInfo().getSlotRank().intValue();
            gameInfoId = aGame.getGameInfo().getId();
            if (rtp == null) {
                var giRtp = aGame.getGameInfo().getRtp();
                if (giRtp != null && giRtp.signum() > 0) {
                    rtp = giRtp;
                }
            }
        }
    }

    public GameGenre getGenre() {
        return genre;
    }

    public void setGenre(GameGenre genre) {
        this.genre = genre;
    }

    public GameType getType() {
        return type;
    }

    public void setType(GameType type) {
        this.type = type;
    }

    public GameJackpotMode getJackpotMode() {
        return jackpotMode;
    }

    public void setJackpotMode(GameJackpotMode jackpotMode) {
        this.jackpotMode = jackpotMode;
    }

    public LocalDate getReleaseDate() {
        return releaseDate;
    }

    public void setReleaseDate(LocalDate releaseDate) {
        this.releaseDate = releaseDate;
    }

    public boolean isIos() {
        return ios;
    }

    public void setIos(boolean ios) {
        this.ios = ios;
    }

    public boolean isAndroid() {
        return android;
    }

    public void setAndroid(boolean android) {
        this.android = android;
    }

    public String getSmallThumb() {
        return smallThumb;
    }

    public void setSmallThumb(String smallThumb) {
        this.smallThumb = smallThumb;
    }

    public String getMediumThumb() {
        return mediumThumb;
    }

    public void setMediumThumb(String mediumThumb) {
        this.mediumThumb = mediumThumb;
    }

    public String getLargeThumb() {
        return largeThumb;
    }

    public void setLargeThumb(String largeThumb) {
        this.largeThumb = largeThumb;
    }

    public int getSlotrank() {
        return slotrank;
    }

    public void setSlotrank(int slotrank) {
        this.slotrank = slotrank;
    }

    public Integer getGameInfoId() {
        return gameInfoId;
    }

    public void setGameInfoId(Integer gameInfoId) {
        this.gameInfoId = gameInfoId;
    }

    public boolean isLastPlayed() {
        return lastPlayed;
    }

    public void setLastPlayed(boolean lastPlayed) {
        this.lastPlayed = lastPlayed;
    }

    public void setId(long id) {
        this.id = id;
    }

    public List<CasinoGameCategory> getCategories() {
        return categories;
    }

    public void setCategories(List<CasinoGameCategory> categories) {
        this.categories = categories;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public boolean isMobile() {
        return mobile;
    }

    public void setMobile(boolean mobile) {
        this.mobile = mobile;
    }

    public boolean isDesktop() {
        return desktop;
    }

    public void setDesktop(boolean desktop) {
        this.desktop = desktop;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    public void setSortOrder(int sortOrder) {
        this.sortOrder = sortOrder;
    }

    public BigDecimal getRtp() {
        return rtp;
    }

    public void setRtp(BigDecimal rtp) {
        this.rtp = rtp;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public Set<Integer> getGametags() {
        return gametags;
    }

    public void setGametags(Set<Integer> gametags) {
        this.gametags = gametags;
    }

    public String getBlockedCountries() {
        return blockedCountries;
    }

    public void setBlockedCountries(String blockedCountries) {
        this.blockedCountries = blockedCountries;
    }

    @Override
    public String toString() {
        return "CasinoGame{" + "gameId='" + gameId + '\'' + ", id=" + id + ", name='" + name + '\'' + ", providerName='" + providerName + '\'' + '}';
    }
}

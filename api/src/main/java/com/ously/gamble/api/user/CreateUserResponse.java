package com.ously.gamble.api.user;

public class CreateUserResponse {
    UserInformation userInformation;
    String status;

    public CreateUserResponse() {
    }

    public CreateUserResponse(String reason) {
        this.status = reason;
    }

    public UserInformation getUserInformation() {
        return userInformation;
    }

    public void setUserInformation(UserInformation userInformation) {
        this.userInformation = userInformation;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}

package com.ously.gamble.api.games;

import com.ously.gamble.persistence.projections.FilterPJ;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;

public class Filter implements Serializable {
    @NotNull
    String name;
    @NotNull
    int count;

    public Filter(String name, int i) {
        this.name = name;
        this.count = i;
    }

    public Filter(FilterPJ pj) {
        this.name = pj.getName();
        this.count = pj.getCnt();
    }

    public Filter() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}

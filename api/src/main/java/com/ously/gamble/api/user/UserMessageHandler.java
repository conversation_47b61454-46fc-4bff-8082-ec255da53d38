package com.ously.gamble.api.user;

import com.ously.gamble.persistence.model.messages.UserMessage;
import com.ously.gamble.persistence.model.messages.UserMessageType;

import java.util.Set;

public interface UserMessageHandler {
    /**
     * @return a set of message types this handler supports
     */
    Set<UserMessageType> supportedTypes();

    /**
     * @param msg    the message
     * @param action the action literal
     * @return result
     */
    UserMessageActionResult handleMessage(UserMessage msg, String action);
}

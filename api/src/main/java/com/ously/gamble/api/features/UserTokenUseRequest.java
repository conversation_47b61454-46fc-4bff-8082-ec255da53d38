package com.ously.gamble.api.features;

import jakarta.validation.constraints.NotNull;

public class UserTokenUseRequest {

    @NotNull
    private long tokenId;
    @NotNull
    private String tokenSecret;

    public long getTokenId() {
        return tokenId;
    }

    public void setTokenId(long tokenId) {
        this.tokenId = tokenId;
    }

    public String getTokenSecret() {
        return tokenSecret;
    }

    public void setTokenSecret(String tokenSecret) {
        this.tokenSecret = tokenSecret;
    }
}

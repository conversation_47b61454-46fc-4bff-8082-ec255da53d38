package com.ously.gamble.api.gamemanager;

import com.ously.gamble.persistence.model.game.GameTagScope;

public enum GMGameTagScope {
    LOCAL, GAME, PLATFORM, STAGE;

    public static GMGameTagScope fromTagScope(GameTagScope type) {
        return switch (type) {
            case LOCAL -> LOCAL;
            case GAME -> GAME;
            case PLATFORM -> PLATFORM;
            case STAGE -> STAGE;
        };
    }

    public static GameTagScope toTagScope(GMGameTagScope type) {
        return switch (type) {
            case LOCAL -> GameTagScope.LOCAL;
            case GAME -> GameTagScope.GAME;
            case PLATFORM -> GameTagScope.PLATFORM;
            case STAGE -> GameTagScope.STAGE;
        };
    }
}

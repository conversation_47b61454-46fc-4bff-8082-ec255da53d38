package com.ously.gamble.api.user;

import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.user.User;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

@Schema
public class UserSummary {
    @NotNull
    private long id;
    @NotNull
    private String displayName;
    @NotNull
    private String email;

    public UserSummary(CasinoUser user) {
        this.id = user.getId();
        this.email = user.getEmail();
        this.displayName = user.getDisplayName();
    }

    public UserSummary(User user) {
        this.id = user.getId();
        this.email = user.getEmail();
        this.displayName = user.getDisplayName();
    }

    public void updateUser(User user) {
        if (this.displayName != null) {
            user.setDisplayName(displayName);
        }
    }

    public UserSummary() {
    }


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

}

package com.ously.gamble.api.crm;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class CRMUserUpdates {
    List<CRMUserUpdate> updates;

    public CRMUserUpdates() {
    }

    public CRMUserUpdates(Collection<CRMUserUpdate> updates) {
        this.updates = new ArrayList<>(updates);
    }

    public void add(CRMUserUpdate upd) {
        if (updates != null) {
            updates.add(upd);
        } else {
            this.updates = new ArrayList<>(5);
            this.updates.add(upd);
        }
    }

    public List<CRMUserUpdate> getUpdates() {
        return updates;
    }

    public void setUpdates(List<CRMUserUpdate> updates) {
        this.updates = updates;
    }
}

package com.ously.gamble.api.gamemanager;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.ously.gamble.persistence.dto.GameIntegrationDto;

import java.io.Serializable;
import java.util.Objects;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GMIntegration implements Serializable {

    private Integer id;
    private String name;
    private String description;
    private String status;
    private String infos;

    public GMIntegration() {
    }

    public GMIntegration(GameIntegrationDto dto) {
        this.id = dto.id();
        this.name = dto.name();
        this.description = dto.description();
        this.status = dto.status();
        this.infos=dto.infos();
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public String getStatus() {
        return status;
    }

    public String getInfos() {
        return infos;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, description, status,infos);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        var entity = (GMIntegration) o;
        return Objects.equals(this.id, entity.id) &&
                Objects.equals(this.name, entity.name) &&
                Objects.equals(this.description, entity.description) &&
               Objects.equals(this.status, entity.status) &&
                  Objects.equals(this.infos, entity.infos);
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + '(' +
                "id = " + id + ", " +
                "name = " + name + ", " +
                "description = " + description + ", " +
                "status = " + status + ')';
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setInfos(String infos) {
        this.infos = infos;
    }
}

package com.ously.gamble.api.games;

import com.ously.gamble.api.bridge.AvailableGame;
import com.ously.gamble.api.bridge.CreateFreespinsResponse;
import com.ously.gamble.api.slotcatalog.SCGameInfo;
import com.ously.gamble.persistence.dto.CasinoGameCategory;
import com.ously.gamble.persistence.model.PromotionType;
import com.ously.gamble.persistence.model.game.*;
import com.querydsl.core.types.Predicate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public interface GameAdminService {

    CasinoGamePromotion newCasinoGamePromotion(CasinoGamePromotion newGamePromo);

    Boolean deleteGamePromotion(Long id);

    CasinoGamePromotion updateCasinoGamePromotion(CasinoGamePromotion updatedGamePromotion,
                                                  Long id);

    Map<Long, Integer> setGameOrder(int newOrder, long gameId);

    Set<Integer> addTagToGame(long gameId, int tagId);

    Set<Integer> removeTagFromGame(long gameId, int tagId);

    CasinoGame updateCasinoGame(CasinoGame updatedGame, Long id);

    CasinoGame newCasinoGame(CasinoGame newGame);

    CasinoGameCategory hideCasinoGameCategory(boolean hidden, int categoryId);

    Page<Game> getAllCasinoGamesForPredicate(Predicate predicate, Pageable page);

    void flushGames();

    Map<String, List<AvailableGame>> getAvailableGamesForAll();

    CasinoGamePromotion getGamePromotion(Long id);

    List<String> getAllGameProviders();

    List<String> getAllGameProvidersFromDB();

    List<CasinoGamePromotion> getAllGamePromotions();

    List<PromotionType> getAllPromotionTypes();

    Page<Game> getAllCasinoGamesForVendor(String vendor, int page, int size);

    Page<GameView> getAllGamesViewQDSL(Pageable pageable, Predicate predicate);

    @Transactional(readOnly = true)
    Page<GameView> getAllGamesViewQDSL(Pageable pageable, Specification<GameView> predicate);

    Page<GameView> getAllGamesViewQDSL(Pageable pageable);

    Optional<GameView> getGamesView(Long gameId);

    void unlinkGameInfo(Long gameId);

    SCGameInfo linkGameInfoToGame(Integer gameInfoId, Long gameId);

    String backsync();

    CreateFreespinsResponse createFreespinsForUser(long userId, long gameId, int level, int amount,
                                                   String fsCId);

    List<GameGenre> getAllGameGenres();

    List<GameType> getAllGameTypes();

    List<GameJackpotMode> getAllGameJackpotModes();


    @Transactional
    void enableJackpotForGame(Long gameId);

    @Transactional
    void disableJackpotForGame(Long gameId);
}

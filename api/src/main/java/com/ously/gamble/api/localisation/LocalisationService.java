package com.ously.gamble.api.localisation;


import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface LocalisationService {

    Localised findLocalised(String literal, LanguageCode langcode);

    List<Localised> getAll();

    Localised updateLocalised(Localised localised);

    Localised saveNewLocalised(Localised localised);

    void deleteLocalised(Localised localised);

    Map<String, String> resolveTemplatesForLang(String vars, LanguageCode code, String... literals);

    Map<String, String> resolveTemplatesForLang(Map<String, String> vars, LanguageCode code,
                                                String... literals);

    Optional<Localised> getLocalised(long id);

    Optional<Localised> updateLocalised(long id, Localised loc);

    Optional<Localised> newLocalised(Localised loc);

    Optional<LocalisedResult> testLocalised(long id, LocalisedRequest req);
}

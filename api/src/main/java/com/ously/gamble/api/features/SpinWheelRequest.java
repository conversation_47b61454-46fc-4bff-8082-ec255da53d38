package com.ously.gamble.api.features;

public class SpinWheelRequest {


    boolean freeSpin;

    Long currentTokenCount;

    public boolean isFreeSpin() {
        return freeSpin;
    }

    public void setFreeSpin(boolean freeSpin) {
        this.freeSpin = freeSpin;
    }

    public Long getCurrentTokenCount() {
        return currentTokenCount;
    }

    public void setCurrentTokenCount(Long currentTokenCount) {
        this.currentTokenCount = currentTokenCount;
    }
}

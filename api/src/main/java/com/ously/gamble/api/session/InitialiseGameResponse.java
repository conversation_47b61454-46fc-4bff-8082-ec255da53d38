package com.ously.gamble.api.session;

import com.ously.gamble.api.bridge.CreateSessionStatus;
import com.ously.gamble.api.bridge.GameStart;
import jakarta.validation.constraints.NotNull;

public class InitialiseGameResponse {

    private CreateSessionStatus status = CreateSessionStatus.OK;

    @NotNull
    private String theData;

    @NotNull
    private boolean internal;

    @NotNull
    private boolean isURL;

    private Long sessionId;

    public InitialiseGameResponse() {
    }

    public InitialiseGameResponse(GameStart theData) {
        if (theData != null) {
            this.theData = theData.data();
            this.sessionId = theData.sessionId();
        }
    }

    public CreateSessionStatus getStatus() {
        return status;
    }

    public void setStatus(CreateSessionStatus status) {
        this.status = status;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public String getTheData() {
        return theData;
    }

    public void setTheData(String theData) {
        this.theData = theData;
    }

    public boolean isInternal() {
        return internal;
    }

    public void setInternal(boolean internal) {
        this.internal = internal;
    }

    public boolean isURL() {
        return isURL;
    }

    public void setURL(boolean isUrl) {
        isURL = isUrl;
    }

    @Override
    public String toString() {
        return "InitialiseGameResponse{" +
               "theData='" + theData + '\'' +
               ", internal=" + internal +
               ", isURL=" + isURL +
               '}';
    }
}

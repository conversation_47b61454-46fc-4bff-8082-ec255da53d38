package com.ously.gamble.api.user;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.ously.gamble.api.achievements.UserAchievement;
import com.ously.gamble.api.features.UserToken;
import com.ously.gamble.api.popups.PopupKey;
import com.ously.gamble.events.WalletRecacheEvent;
import com.ously.gamble.payload.TxPriceType;
import com.ously.gamble.persistence.model.Wallet;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ZERO;

@Schema
@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class UserStats implements Serializable {


    @JsonIgnore
    private long spintimerEpoch;

    @JsonIgnore
    private Instant nextPopupTime = Instant.MIN;

    @Schema(description = "the treasure chest fill gauge: 0-150")
    private Long piggyStatus;

    @Schema(description = "next free wheelspin countdown. If emtpy or null a free wheelspin is available")
    private String spinTimer;

    private UserMessageStatus msgs;

    private List<UserAchievement> achievements; // = Collections.emptyList();

    private List<UserActivePerk> activePerks; //= Collections.emptyList();

    @Schema(description = "unclaimed reward tickets")
    private Integer ucrew = null;

    @Schema(format = "double", description = "balance")
    @NotNull
    private BigDecimal balance;


    @NotNull
    @Schema(description = "the users level")
    private long level;

    @NotNull
    @Schema(description = "the users xp")
    private long xp;

    private long mpb;

    @Schema(format = "double", description = "percentage to next level")
    @NotNull
    private BigDecimal percToNextLevel;

    @NotNull
    private long pollFreqMs = 5000;

    private List<UserToken> tokens = Collections.emptyList();

    @NotNull
    private Instant lastHistoryOrFavsUpdate = Instant.now();

    private PopupKey popup;

    @Schema(description = "cost of a wheelspin in diamonds")
    private Integer cpws;

    public UserStats() {
    }

    public static UserStats NewForSocial(Wallet w, List<UserAchievement> achievements,
                                         List<UserToken> tokens,
                                         UserMessageStatus umStatus, Integer unclaimedRewards) {
        return new UserStats(w, achievements, tokens, umStatus, unclaimedRewards, true);
    }

    public static UserStats NewForCash(Wallet w, UserMessageStatus umStatus) {
        return new UserStats(w, null, null, umStatus, null, false);
    }

    UserStats(Wallet w, List<UserAchievement> achievements, List<UserToken> tokens,
              UserMessageStatus umStatus, Integer unclaimedRewards, boolean isSocial) {
        this.update(w, achievements, tokens, umStatus, unclaimedRewards);
    }

    public UserStats updateForUsedSpinToken(int diamondsUsed) {
        if (this.tokens == null) {
            return this;
        }
        if (diamondsUsed > 0) {
            for (var tk : this.tokens) {
                if (tk.getType() == TxPriceType.D) {
                    tk.setCount(Math.max(tk.getCount() - diamondsUsed, 0));
                }
            }

        } else {
            for (var tk : this.tokens) {
                if (tk.getType() == TxPriceType.WF) {
                    tk.setCount(Math.max(tk.getCount() - 1, 0));
                }
            }
        }
        // filter out empty tokens
        if (this.tokens != null && !this.tokens.isEmpty()) {
            this.tokens = this.tokens.stream().filter(a -> a.getCount() > 0).collect(Collectors.toList());
        }
        return this;
    }


    private UserStats update(Wallet w, List<UserAchievement> achvmnts,
                             List<UserToken> ntokens, UserMessageStatus umStatus, Integer unclaimedRewards) {
        this.balance = w.getBalance();
        this.level = w.getLevel()+1;
        this.mpb = 100L;
        this.ucrew = unclaimedRewards;
        this.msgs = umStatus;
        if (w.getPercnl() != null) {
            this.percToNextLevel = w.getPercnl().setScale(2, RoundingMode.DOWN);
        } else {
            this.percToNextLevel = ZERO;
        }
        this.xp = w.getXp();

        if (w.getSpintimer() == null) {
            this.spintimerEpoch = 0L;
        } else {
            this.spintimerEpoch = w.getSpintimer().getEpochSecond();
        }
        createCountDownTimeForWheel();
        this.achievements = achvmnts;
        this.activePerks = parsePerks(w.getActivePerks());
        if (ntokens != null && !ntokens.isEmpty()) {
            this.tokens = ntokens.stream().filter(a -> a.getCount() > 0).collect(Collectors.toList());
        }
        return this;
    }

    public UserStats update(WalletRecacheEvent w, List<UserAchievement> achvmnts,
                            List<UserToken> ntokens) {
        this.balance = w.balance();
        this.level = w.level()+1;
        this.mpb = 100L;

        if (w.spintimer() == null) {
            this.spintimerEpoch = 0L;
        } else {
            this.spintimerEpoch = w.spintimer().getEpochSecond();
        }
        this.achievements = achvmnts;
        this.activePerks = parsePerks(w.activePerks());
        if (ntokens != null && !ntokens.isEmpty()) {
            this.tokens = ntokens.stream().filter(a -> a.getCount() > 0).collect(Collectors.toList());
        }
        createCountDownTimeForWheel();
        if (w.percnl() != null) {
            this.percToNextLevel = w.percnl().setScale(2, RoundingMode.DOWN);
        } else {
            this.percToNextLevel = ZERO;
        }
        this.xp = w.xp();
        return this;
    }

    public void createCountDownTimeForWheel() {
        var nowEpoch = Instant.now().getEpochSecond();

        if (spintimerEpoch == 0L || spintimerEpoch < nowEpoch) {
            this.spinTimer = "00:00:00";
            return;
        }

        var secondsDiff = Math.max(spintimerEpoch - nowEpoch, 0L);

        var seconds = secondsDiff % 60;
        var minutes = (secondsDiff / 60) % 60;
        var hours = (secondsDiff / 3600) % 24;

        this.spinTimer = String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }

    private List<UserActivePerk> parsePerks(String activePerks) {
        if (activePerks == null || activePerks.isEmpty()) {
            return Collections.emptyList();
        }
        return Perk.parsePerks(activePerks).stream().map(UserActivePerk::new).collect(Collectors.toList());
    }


    public List<UserToken> getTokens() {
        return tokens;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public long getLevel() {
        return level;
    }

    public long getXp() {
        return xp;
    }

    public long getMpb() {
        return mpb;
    }

    public BigDecimal getPercToNextLevel() {
        return percToNextLevel;
    }

    public long getPollFreqMs() {
        return pollFreqMs;
    }

    public void setPollFreqMs(long pollFreqMs) {
        this.pollFreqMs = pollFreqMs;
    }

    public List<UserAchievement> getAchievements() {
        return achievements;
    }

    public void setAchievements(List<UserAchievement> achievements) {
            this.achievements = Objects.requireNonNullElse(achievements, Collections.emptyList());
    }

    public UserMessageStatus getMsgs() {
        return msgs;
    }

    public void setMsgs(UserMessageStatus msgs) {
        this.msgs = msgs;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public void setLevel(long level) {
        this.level = level;
    }

    public void setXp(long xp) {
        this.xp = xp;
    }

    public void setMpb(long mpb) {
        this.mpb = mpb;
    }

    public void setPercToNextLevel(BigDecimal percToNextLevel) {
        if (percToNextLevel != null) {
            this.percToNextLevel = percToNextLevel.setScale(2, RoundingMode.DOWN);
        } else {
            this.percToNextLevel = ZERO;
        }
    }

    public List<UserActivePerk> getActivePerks() {
        return activePerks;
    }

    public void setActivePerks(List<UserActivePerk> activePerks) {
        this.activePerks = Objects.requireNonNullElse(activePerks, Collections.emptyList());
    }

    public void setTokens(List<UserToken> tokens) {
        if (tokens == null || tokens.isEmpty()) {
            this.tokens = Collections.emptyList();
        } else {
            this.tokens = tokens.stream().filter(a -> a.getCount() > 0).collect(Collectors.toList());
        }
    }

    public Instant getLastHistoryOrFavsUpdate() {
        return lastHistoryOrFavsUpdate;
    }

    public Long getPiggyStatus() {
        return piggyStatus;
    }

    public void setPiggyStatus(Long piggyStatus) {
        this.piggyStatus = piggyStatus;
    }

    public void setLastHistoryOrFavsUpdate(Instant lastHistoryOrFavsUpdate) {
        this.lastHistoryOrFavsUpdate = lastHistoryOrFavsUpdate;
    }

    public String getSpinTimer() {
        return spinTimer;
    }

    public void setSpinTimer(String spinTimer) {
        this.spinTimer = spinTimer;
    }

    public void removeAchievement(Long id) {
        if (achievements == null) {
            return;
        }
        for (var a : achievements) {
            if (a.getId().equals(id)) {
                achievements.remove(a);
                break;
            }
        }
    }

    public void resetSpinTimer() {
        createCountDownTimeForWheel();
    }

    public Instant getNextPopupTime() {
        return nextPopupTime;
    }

    public void setNextPopupTime(Instant nextPopupTime) {
        this.nextPopupTime = nextPopupTime;
    }

    public PopupKey getPopup() {
        return popup;
    }

    public void setPopup(PopupKey popup) {
        this.popup = popup;
    }

    public long getSpintimerEpoch() {
        return spintimerEpoch;
    }

    public Integer getCpws() {
        return cpws;
    }

    public void setCpws(int costForLevel) {
        if (costForLevel <= 0) {
            this.cpws = null;
        } else {
            this.cpws = costForLevel;
        }
    }
}

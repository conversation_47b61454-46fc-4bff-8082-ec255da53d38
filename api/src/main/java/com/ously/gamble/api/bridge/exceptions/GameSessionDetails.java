package com.ously.gamble.api.bridge.exceptions;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.persistence.dto.CasinoUser;

import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

public record GameSessionDetails(
        long gameId, long userId, long vendorId,
        long timestamp, Map<String, String> details
) {


    public GameSessionDetails(long gameId, long userId, long vendorId, Map<String, String> details) {
        this(gameId, userId, vendorId, Instant.now().getEpochSecond(), details);
    }

    public GameSessionDetails(long gameId, long userId, long vendorId) {
        this(gameId, userId, vendorId, Instant.now().getEpochSecond(), Collections.emptyMap());
    }

    public GameSessionDetails(CasinoUser cUser, CasinoGame cg, String platform, GameSettings settings) {
        this(cg.getId(), cUser.getId(), cg.getVendorId(),
                createMapFrom(cUser, cg, platform, settings));

    }

    private static Map<String, String> createMapFrom(CasinoUser cUser, CasinoGame cg, String platform, GameSettings settings) {
        Map<String, String> result = new HashMap<>();
        result.put("userip", settings.getIp());
        result.put("userstatus", cUser.getStatus());
        result.put("gamename", cg.getName());
        result.put("providername", cg.getProviderName());
        result.put("gameid", cg.getGameId());
        result.put("locale", settings.getLanguage());
        result.put("platform", platform);
        return result;
    }

    public GameSessionDetails(long userId, CasinoGame cg, String platform, GameSettings settings) {
        this(cg.getId(), userId, cg.getVendorId(), createMapFrom(userId, cg, platform, settings));
    }

    private static Map<String, String> createMapFrom(long userId, CasinoGame cg, String platform, GameSettings settings) {
        Map<String, String> result = new HashMap<>();
        result.put("userip", settings.getIp());
        result.put("gamename", cg.getName());
        result.put("providername", cg.getProviderName());
        result.put("gameid", cg.getGameId());
        result.put("locale", settings.getLanguage());
        result.put("platform", platform);
        return result;
    }


    @JsonIgnore
    public String getDetailsAsString() {
        return details.entrySet().stream().map(a -> a.getKey() + '=' + a.getValue()).collect(Collectors.joining(","));
    }
}

package com.ously.gamble.api.missions;

import com.ously.gamble.api.achievements.Reward;

import java.io.Serializable;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;

public class DailyMission implements Serializable, Comparable<DailyMission> {

    Long id;
    ZonedDateTime validFrom;
    ZonedDateTime validUntil;
    MissionType type;
    MissionGoal goal;
    Long goalValue;
    List<Long> selectors;
    Long levelGoalMultiplier;
    Long levelRewardMultiplier;
    List<Reward> rewards;
    long validTillES;


    public DailyMission(long id, Instant validFrom, Instant validUntil, MissionType type,
                        MissionGoal goal, Long goalValue, String selector, Long levelGoalMultiplier,
                        Long levelRewardMultiplier, String rewards) {
        this.id = id;
        this.validFrom = validFrom.atZone(ZoneOffset.UTC);
        this.validUntil = validUntil.atZone(ZoneOffset.UTC);
        this.validTillES = this.validUntil.toEpochSecond();
        this.type = type;
        this.goal = goal;
        this.goalValue = goalValue;
        this.selectors = Arrays.stream(selector.split(",")).map(Long::valueOf).toList();
        this.levelGoalMultiplier = levelGoalMultiplier;
        this.levelRewardMultiplier = levelRewardMultiplier;
        this.rewards = Reward.createRewardsFromDefinition(rewards);
    }

    public DailyMission() {
    }


    @Override
    public int compareTo(DailyMission o) {
        var v1 = validFrom.compareTo(o.validFrom);
        if (v1 == 0) {
            return (int) (id - o.id);
        }
        return v1;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ZonedDateTime getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(ZonedDateTime validFrom) {
        this.validFrom = validFrom;
    }

    public ZonedDateTime getValidUntil() {
        return validUntil;
    }

    public void setValidUntil(ZonedDateTime validUntil) {
        this.validUntil = validUntil;
    }

    public MissionType getType() {
        return type;
    }

    public void setType(MissionType type) {
        this.type = type;
    }

    public MissionGoal getGoal() {
        return goal;
    }

    public void setGoal(MissionGoal goal) {
        this.goal = goal;
    }

    public Long getGoalValue() {
        return goalValue;
    }

    public void setGoalValue(Long goalValue) {
        this.goalValue = goalValue;
    }

    public List<Long> getSelectors() {
        return selectors;
    }

    public void setSelectors(List<Long> selectors) {
        this.selectors = selectors;
    }

    public Long getLevelGoalMultiplier() {
        return levelGoalMultiplier;
    }

    public void setLevelGoalMultiplier(Long levelGoalMultiplier) {
        this.levelGoalMultiplier = levelGoalMultiplier;
    }

    public Long getLevelRewardMultiplier() {
        return levelRewardMultiplier;
    }

    public void setLevelRewardMultiplier(Long levelRewardMultiplier) {
        this.levelRewardMultiplier = levelRewardMultiplier;
    }

    public long getValidTillES() {
        return validTillES;
    }

    public void setValidTillES(long validTillES) {
        this.validTillES = validTillES;
    }

    public List<Reward> getRewards() {
        return rewards;
    }

    public void setRewards(List<Reward> rewards) {
        this.rewards = rewards;
    }

    @Override
    public String toString() {
        return "DailyMission{" +
               "id=" + id +
               ", type=" + type +
               ", goal=" + goal +
               ", valid=" + validTillES +
               '}';
    }
}

package com.ously.gamble.api.statistics;

import com.ously.gamble.persistence.projections.PlatformStatsPJ;

import java.math.BigDecimal;

public record PlatformStats(
        BigDecimal ggr,
        BigDecimal sumBet,
        Integer bets,
        BigDecimal sumWin,
        Integer wins,
        BigDecimal bonusGgr,
        BigDecimal sumBonusBet,
        Integer bonusBets,
        BigDecimal sumBonusWin,
        Integer bonusWins,
        Integer deposits,
        BigDecimal sumDeposits,
        Integer payouts,
        BigDecimal sumPayouts,
        Integer signups,
        Integer maxSignups,
        BigDecimal avgSignups,
        Integer fTDs,
        Integer maxFTDs,
        BigDecimal avgFTDs,
        Integer activePlayers,
        Integer maxActivePlayers,
        BigDecimal avgActivePlayers
) {

    public PlatformStats(PlatformStatsPJ platformStatsPJ) {
        this(platformStatsPJ.getGgr(),
                platformStatsPJ.getSumBet(),
                platformStatsPJ.getBets(),
                platformStatsPJ.getSumWin(),
                platformStatsPJ.getWins(),
                platformStatsPJ.getBonusGgr(),
                platformStatsPJ.getSumBonusBet(),
                platformStatsPJ.getBonusBets(),
                platformStatsPJ.getSumBonusWin(),
                platformStatsPJ.getBonusWins(),
                platformStatsPJ.getDeposits(),
                platformStatsPJ.getSumDeposits(),
                platformStatsPJ.getPayouts(),
                platformStatsPJ.getSumPayouts(),
                platformStatsPJ.getSignups(),
                platformStatsPJ.getMaxSignups(),
                platformStatsPJ.getAvgSignups(),
                platformStatsPJ.getFTDs(),
                platformStatsPJ.getMaxFTDs(),
                platformStatsPJ.getAvgFTDs(),
                platformStatsPJ.getActivePlayers(),
                platformStatsPJ.getMaxActivePlayers(),
                platformStatsPJ.getAvgActivePlayers()
        );
    }
}

package com.ously.gamble.api.features;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.ously.gamble.payload.TxPriceType;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;

@JsonInclude(Include.NON_NULL)
public class UserToken implements Serializable {
    public UserToken() {
    }

    public UserToken(TxPriceType type, int duration, int boost, int count) {
        this.type = type;
        this.durationInMinutes = duration;
        this.boost = boost;
        this.count = count;
    }


    @NotNull
    long tokenId;
    @NotNull
    TxPriceType type;
    int durationInMinutes;
    int boost;
    int count;
    @NotNull
    String secret;

    public long getTokenId() {
        return tokenId;
    }

    public void setTokenId(long tokenId) {
        this.tokenId = tokenId;
    }

    public TxPriceType getType() {
        return type;
    }

    public void setType(TxPriceType type) {
        this.type = type;
    }

    public int getDurationInMinutes() {
        return durationInMinutes;
    }

    public void setDurationInMinutes(int durationInMinutes) {
        this.durationInMinutes = durationInMinutes;
    }

    public int getBoost() {
        return boost;
    }

    public void setBoost(int boost) {
        this.boost = boost;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }
}

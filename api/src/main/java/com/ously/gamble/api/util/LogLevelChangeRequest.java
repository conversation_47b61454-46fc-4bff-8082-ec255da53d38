package com.ously.gamble.api.util;

import java.io.Serializable;

public class LogLevelChangeRequest implements Serializable {

    String loggerName;
    String level;

    public LogLevelChangeRequest() {
    }

    public LogLevelChangeRequest(String loggerName, String level) {
        this.loggerName = loggerName;
        this.level = level;
    }

    public String getLoggerName() {
        return loggerName;
    }

    public void setLoggerName(String loggerName) {
        this.loggerName = loggerName;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    @Override
    public String toString() {
        return "LogLevelChangeRequest{" +
                "loggerName='" + loggerName + '\'' +
                ", level='" + level + '\'' +
                '}';
    }
}


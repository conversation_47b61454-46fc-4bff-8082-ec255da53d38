package com.ously.gamble.api.session.archive;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import com.ously.gamble.persistence.dto.JsonViews;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.session.ArchivedTransactionDto;
import com.ously.gamble.persistence.model.session.SessionTransaction;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

@Schema(name = "ATransaction")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public record ArchivedTransaction(
        long id,
        long ts,
        String eid,
        String rid,
        TransactionType type,
        boolean canc,
        long xp,
        int lvl,
        @JsonView(JsonViews.JVAdmin.class)
        String bst,
        @JsonView(JsonViews.JVAdmin.class)
        String pnl,
        String bet,
        String win,
        String bal

) {
    public ArchivedTransaction(SessionTransaction st) {
        this(st.getTxId(), st.getCreatedAt().getEpochSecond(), st.getExternalOrigTxId(), st.getRoundReference(),
                st.getType(), st.isCancelled(), st.getEarnedXp(), st.getLevel(),
                getOptNumericStr(st.getBoost()), getOptNumericStr(st.getPercnl()),
                getOptNumericStr(st.getBet()), getOptNumericStr(st.getWin()), getOptNumericStr(st.getBalanceAfter())
        );
    }

    public ArchivedTransaction(ArchivedTransactionDto st) {
        this(st.id(), st.ts().getEpochSecond(), st.extid(), st.rndid(),
                TransactionType.valueOf(st.type()), st.canc(), st.xp(), st.lvl(),
                getOptNumericStr(st.boost()), getOptNumericStr(st.pnl()),
                getOptNumericStr(st.bet()), getOptNumericStr(st.win()), getOptNumericStr(st.bal()));
    }

    private static String getOptNumericStr(BigDecimal val) {
        if (val == null || val.signum() == 0) {
            return "0";
        }
        return val.toString();
    }
}

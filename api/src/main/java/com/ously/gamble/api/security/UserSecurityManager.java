package com.ously.gamble.api.security;

import com.querydsl.core.types.Predicate;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.Optional;

public interface UserSecurityManager {

    Optional<UserRoleInfo> getPermissionInfoForUser(long userId);

    List<UserRole> getRolesForUser(long userId);

    List<String> getAuthoritiesForUser(long userId);

    List<UserRole> addRoleToUser(String role, long userId, boolean makeSystemIfNeeded);

    UserRole addNewRole(String role, String description);

    UserPermission addNewPermission(String permName, String description);

    UserRole addPermissionToRole(String roleName, String permName);

    UserRole removePermissionFromRole(String roleName, String permName);

    List<UserRole> getRoles(Predicate predicate, Sort sort);

    List<UserPermission> getPermissions(Predicate predicate, Sort sort);

    List<UserRole> getRolesOfUser(long userId);

    List<String> getAuthsForUser(long userId);

    List<UserRole> removeRoleFromUser(String roleId, long userId);
}

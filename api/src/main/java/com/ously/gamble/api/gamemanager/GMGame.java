package com.ously.gamble.api.gamemanager;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.ously.gamble.persistence.model.game.GameGenre;
import com.ously.gamble.persistence.model.game.GameJackpotMode;
import com.ously.gamble.persistence.model.game.GameType;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GMGame {
    int id;
    GMProvider provider;
    Integer infoId;
    String name;


    LocalDate releaseDate;
    GameGenre genre;
    GameType type;
    GameJackpotMode jackpotMode;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER, timezone = "UTC")
    Instant createdAt;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER, timezone = "UTC")
    Instant updatedAt;
    int sortOrder;
    List<GMGameSetup> setups;
    List<GMCategory> categories;

    Set<Integer> gametags;
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, timezone = "UTC")
    private Instant infoModifiedAt;


    public GMProvider getProvider() {
        return provider;
    }

    public void setProvider(GMProvider provider) {
        this.provider = provider;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }


    public Integer getInfoId() {
        return infoId;
    }

    public void setInfoId(Integer infoId) {
        this.infoId = infoId;
    }

    public List<GMGameSetup> getSetups() {
        return setups;
    }

    public void setSetups(List<GMGameSetup> setups) {
        this.setups = setups;
    }

    public List<GMCategory> getCategories() {
        return categories;
    }

    public void setCategories(List<GMCategory> categories) {
        this.categories = categories;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public int getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(int sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Instant getInfoModifiedAt() {
        return infoModifiedAt;
    }

    public void setInfoModifiedAt(Instant infoModifiedAt) {
        this.infoModifiedAt = infoModifiedAt;
    }

    public LocalDate getReleaseDate() {
        return releaseDate;
    }

    public void setReleaseDate(LocalDate releaseDate) {
        this.releaseDate = releaseDate;
    }

    public GameGenre getGenre() {
        return genre;
    }

    public void setGenre(GameGenre genre) {
        this.genre = genre;
    }

    public GameType getType() {
        return type;
    }

    public void setType(GameType type) {
        this.type = type;
    }

    public GameJackpotMode getJackpotMode() {
        return jackpotMode;
    }

    public void setJackpotMode(GameJackpotMode jackpotMode) {
        this.jackpotMode = jackpotMode;
    }

    public Set<Integer> getGametags() {
        if (gametags == null) {
            return Collections.emptySet();
        }
        return gametags;
    }

    public void setGametags(Set<Integer> gametags) {
        this.gametags = gametags;
    }
}

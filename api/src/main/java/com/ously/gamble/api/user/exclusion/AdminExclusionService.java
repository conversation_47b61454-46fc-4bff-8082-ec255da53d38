package com.ously.gamble.api.user.exclusion;

import java.util.List;
import java.util.Optional;

public interface AdminExclusionService {

    List<UserExclusionDto> getExclusionsForUser(long userId);

    Optional<UserExclusionDto> addExclusionForUser(UserExclusionDto req, long adminId);

    Optional<UserExclusionDto> removeExclusionForUser(UserExclusionRemovalRequest req, long adminId);

    int removeExpiredExclusions();

    Optional<UserExclusionDto> updateExclusionForUser(UserExclusionDto upd, long adminId);

}

package com.ously.gamble.api.issues;

import com.ously.gamble.persistence.model.issues.Issue;
import com.ously.gamble.persistence.model.issues.Issue.Status;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Schema
public class CasinoIssue {
    @NotNull
    Long id;
    @Null
    private Instant updatedAt;
    @NotNull
    private Instant createdAt;
    @NotNull
    private String status;
    @NotNull
    Long customerId;
    @Null
    Long assignedToId;
    @NotNull
    String description;
    @NotNull
    String headline;
    @NotNull
    String topic;

    List<CasinoIssueComment> comments = new ArrayList<>();

    public CasinoIssue() {
    }

    public CasinoIssue(Issue issue) {
        this.id = issue.getId();
        this.customerId = issue.getCustomer().getId();
        if (issue.getAssignee() != null) {
            this.assignedToId = issue.getAssignee().getId();
        }
        this.description = issue.getIssueText();
        this.status = issue.getStatus().name();
        this.createdAt = issue.getCreatedAt();
        this.updatedAt = issue.getUpdatedAt();
        for (var ic : issue.getComments()) {
            this.comments.add(new CasinoIssueComment(ic));
        }
        this.headline = issue.getHeadline();
        this.topic = issue.getTopic();
    }

    public Issue createNewIssue() {
        var nIssue = new Issue();
        nIssue.setIssueText(description);
        nIssue.setTopic(topic);
        nIssue.setHeadline(headline);
        if (status != null) {
            nIssue.setStatus(Status.valueOf(status));
        } else {
            nIssue.setStatus(Status.NEW);
        }
        return nIssue;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getAssignedToId() {
        return assignedToId;
    }

    public void setAssignedToId(Long assignedToId) {
        this.assignedToId = assignedToId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getHeadline() {
        return headline;
    }

    public void setHeadline(String headline) {
        this.headline = headline;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<CasinoIssueComment> getComments() {
        return comments;
    }

    public void setComments(List<CasinoIssueComment> comments) {
        this.comments = comments;
    }
}

package com.ously.gamble.api.slotfinder;

import org.javatuples.Pair;

import java.io.Serializable;
import java.util.List;

public class RecommendationWrapper implements Serializable {
    List<Integer> val1;
    List<Integer> val2;

    public RecommendationWrapper() {
    }

    public RecommendationWrapper(List<Integer> v1, List<Integer> v2) {
        val1 = v1;
        val2 = v2;
    }

    public Pair<List<Integer>, List<Integer>> getPair() {
        return Pair.with(val1, val2);
    }

    public List<Integer> getVal1() {
        return val1;
    }

    public void setVal1(List<Integer> val1) {
        this.val1 = val1;
    }

    public List<Integer> getVal2() {
        return val2;
    }

    public void setVal2(List<Integer> val2) {
        this.val2 = val2;
    }
}

package com.ously.gamble.api.videoads;

import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

public interface VideoAdDailyStatsService {
    List<VideoAdDailyStatsDto> findByRDateBetween(LocalDate startDate, LocalDate endDate);

    VideoAdDailyStatsAggDto sumByRDateAndPosition(LocalDate startDate, LocalDate endDate, String position);

    @Transactional
    void archiveVideoAds();
}

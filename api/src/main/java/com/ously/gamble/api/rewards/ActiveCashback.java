package com.ously.gamble.api.rewards;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.InstantSerializer;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActiveCashback implements Serializable {
    BigDecimal depositSum;
    BigDecimal depositFactor;
    BigDecimal currentAmount;

    @JsonSerialize(using = InstantSerializer.class)
    Instant startedAt;
    @JsonSerialize(using = InstantSerializer.class)
    Instant endsAt;

    public BigDecimal getDepositSum() {
        return depositSum;
    }

    public void setDepositSum(BigDecimal depositSum) {
        this.depositSum = depositSum;
    }

    public BigDecimal getDepositFactor() {
        return depositFactor;
    }

    public void setDepositFactor(BigDecimal depositFactor) {
        this.depositFactor = depositFactor;
    }

    public BigDecimal getCurrentAmount() {
        return currentAmount;
    }

    public void setCurrentAmount(BigDecimal currentAmount) {
        this.currentAmount = currentAmount;
    }

    public Instant getStartedAt() {
        return startedAt;
    }

    public void setStartedAt(Instant startedAt) {
        this.startedAt = startedAt;
    }

    public Instant getEndsAt() {
        return endsAt;
    }

    public void setEndsAt(Instant endsAt) {
        this.endsAt = endsAt;
    }
}

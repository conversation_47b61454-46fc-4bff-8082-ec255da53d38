package com.ously.gamble.api.leaderboard;

import com.ously.gamble.persistence.projections.LeaderboardEntryPJ;

import java.io.Serial;
import java.io.Serializable;
import java.util.Collection;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

public class Leaderboard implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    public enum LeaderboardType {MAX_WIN, MULTIPLIER, PROFIT}

    LeaderboardType type;
    int days;
    Set<LeaderboardEntry> entries;

    public Leaderboard() {
    }

    public Leaderboard(LeaderboardType type, int days, Collection<LeaderboardEntryPJ> entries) {
        this.type = type;
        this.days = days;
        this.entries = entries.stream().map(LeaderboardEntry::new).collect(Collectors.toCollection(TreeSet::new));
    }

    public LeaderboardType getType() {
        return type;
    }

    public void setType(LeaderboardType type) {
        this.type = type;
    }

    public int getDays() {
        return days;
    }

    public void setDays(int days) {
        this.days = days;
    }

    public Set<LeaderboardEntry> getEntries() {
        return entries;
    }

    public void setEntries(Set<LeaderboardEntry> entries) {
        this.entries = entries;
    }

}

package com.ously.gamble.api.crm;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

public class CRMDelayedUserUpdate {
    List<Long> ids = new ArrayList<>(15);
    Instant selectionTime;

    public CRMDelayedUserUpdate() {
        this.selectionTime = Instant.now();
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public Instant getSelectionTime() {
        return selectionTime;
    }

    public void setSelectionTime(Instant selectionTime) {
        this.selectionTime = selectionTime;
    }

    @JsonIgnore
    public void addId(Long id) {
        this.ids.add(id);
    }

    @Override
    public String toString() {
        return "CRMDelayedUserUpdate{" +
                "ids=" + ids +
                ", selectionTime=" + selectionTime +
                '}';
    }
}

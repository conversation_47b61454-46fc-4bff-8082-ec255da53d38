package com.ously.gamble.api.session;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.ously.gamble.api.achievements.Reward;
import com.ously.gamble.api.leaderboards.GameLeaderboards;
import com.ously.gamble.persistence.model.session.SessionStatisticsDao;
import com.ously.gamble.persistence.model.session.SessionStatisticsRD;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import static java.math.BigDecimal.ZERO;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class SessionStatistics {

    private static final BigDecimal BD100 = BigDecimal.valueOf(100);

    public SessionStatistics(SessionStatisticsDao ss) {
        this.startedAt = ss.getStartAt();
        this.lastAt = ss.getEndAt();
        this.sessionId = ss.getSessionId();
        this.sumWins = BigDecimal.valueOf(ss.getSumWin()).divide(BD100, 2, RoundingMode.UP);
        this.sumBets = BigDecimal.valueOf(ss.getSumBet()).divide(BD100, 2, RoundingMode.UP);
        this.numberOfPlays = ss.getNumBets();
        this.numberOfWins = ss.getNumWins();
        this.maxWin = BigDecimal.valueOf(ss.getMaxWin()).divide(BD100, 2, RoundingMode.UP);
        this.maxBet = BigDecimal.valueOf(ss.getMaxBet()).divide(BD100, 2, RoundingMode.UP);
        this.maxMultiplier = ss.getMaxMult();
        this.totalWin = sumWins.subtract(sumBets).max(ZERO);
    }

    public void createAchievement(Integer level, SessionAchievement a, BigDecimal bonus) {
        var sa = a;
        if (sa == null) {
            sa = new SessionAchievement();
        }
        sa.forLevel = level;
        sa.bonus = bonus;
        achievements.add(sa);
    }

    public void addAchievements(List<SessionAchievement> achs) {
        this.achievements.addAll(achs);
    }

    @JsonAutoDetect(fieldVisibility = Visibility.ANY)
    @JsonInclude(Include.NON_NULL)
    public static class SessionAchievement {
        List<Reward> rewards;
        @Schema(format = "double")
        BigDecimal bonus;
        long forLevel;
        String secret;
        long achievementId;

        public SessionAchievement() {
        }

        public SessionAchievement(long achievementId, String secret, String priceDef, int level, BigDecimal bonus) {
            this.achievementId = achievementId;
            this.secret = secret;
            this.forLevel = level;
            this.bonus = bonus;
            this.rewards = Reward.createRewardsFromDefinition(priceDef);
        }

        public long getAchievementId() {
            return achievementId;
        }

        public void setAchievementId(long achievementId) {
            this.achievementId = achievementId;
        }

        public List<Reward> getRewards() {
            return rewards;
        }

        public void setRewards(List<Reward> rewards) {
            this.rewards = rewards;
        }

        public BigDecimal getBonus() {
            return bonus;
        }

        public void setBonus(BigDecimal bonus) {
            this.bonus = bonus;
        }

        public long getForLevel() {
            return forLevel;
        }

        public void setForLevel(long forLevel) {
            this.forLevel = forLevel;
        }

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }
    }


    @NotNull
    private Long sessionId = -1L;
    @NotNull
    private long numberOfPlays;
    @NotNull
    private long numberOfWins;

    @Schema(format = "double")
    @NotNull
    private BigDecimal sumWins = ZERO;


    @Schema(format = "double")
    @NotNull
    private BigDecimal sumBoosts = ZERO;
    @Schema(format = "double")
    @NotNull
    private BigDecimal sumBets = ZERO;


    @Schema(format = "double")
    @NotNull
    private BigDecimal maxWin = ZERO;
    @Schema(format = "double")
    @NotNull
    private BigDecimal maxBet = ZERO;

    @Schema(format = "double")
    @NotNull
    private BigDecimal maxBonus = ZERO;

    @Schema(format = "double")
    private BigDecimal maxMultiplier = ZERO;

    @Schema(format = "double")
    @NotNull
    private BigDecimal sumBonus = ZERO;

    @NotNull
    private int startLevel;

    @Schema(format = "double")
    @NotNull
    private BigDecimal startPercNl = ZERO;

    @NotNull
    private int endLevel;

    @Schema(format = "double")
    @NotNull
    private BigDecimal endPercNl = ZERO;

    @NotNull
    private Instant startedAt = Instant.MIN;

    @NotNull
    private Instant lastAt = Instant.MIN;

    @Schema(format = "double")
    @NotNull
    private BigDecimal totalWin = ZERO;

    @NotNull
    private long totalXP;

    @Schema(format = "double")
    @NotNull
    private BigDecimal maxDoubleAmount = ZERO;

    @Deprecated
    @Schema(format = "double")
    @NotNull
    private BigDecimal sumBbets = ZERO;
    @Deprecated
    @Schema(format = "double")
    @NotNull
    private BigDecimal sumBwins = ZERO;

    List<SessionAchievement> achievements = new ArrayList<>(4);

    GameLeaderboards leaderboards;

    public SessionStatistics() {
    }


    public SessionStatistics(long sessionId) {
        this.sessionId = sessionId;
        this.lastAt = Instant.now();
        this.startedAt = Instant.now();
    }

    public SessionStatistics(SessionStatisticsRD aS) {
        this.startedAt = aS.startAt();
        this.lastAt = aS.endAt();

        this.maxBet = BigDecimal.valueOf(aS.maxBet()).divide(BD100, 2, RoundingMode.HALF_UP);
        this.maxWin = BigDecimal.valueOf(aS.maxWin()).divide(BD100, 2, RoundingMode.HALF_UP);

        this.maxMultiplier = aS.maxMult();

        this.numberOfPlays = aS.numBets();
        this.numberOfWins = aS.numWins();

        this.sumBets = BigDecimal.valueOf(aS.sumBet()).divide(BD100, 2, RoundingMode.HALF_UP);
        this.sumWins = BigDecimal.valueOf(aS.sumWin()).divide(BD100, 2, RoundingMode.HALF_UP);

        this.sessionId = aS.sessionId();
        this.totalWin = sumWins.subtract(sumBets).max(ZERO);

    }

    public GameLeaderboards getLeaderboards() {
        return leaderboards;
    }

    public void setLeaderboards(GameLeaderboards leaderboards) {
        this.leaderboards = leaderboards;
    }

    public long getNumberOfWins() {
        return numberOfWins;
    }

    public void setNumberOfWins(long numberOfWins) {
        this.numberOfWins = numberOfWins;
    }

    public Instant getLastAt() {
        return lastAt;
    }

    public void setLastAt(Instant lastAt) {
        this.lastAt = lastAt;
    }

    public BigDecimal getMaxBonus() {
        return maxBonus;
    }

    public void setMaxBonus(BigDecimal maxBonus) {
        this.maxBonus = maxBonus;
    }

    public long getNumberOfPlays() {
        return numberOfPlays;
    }

    public void setNumberOfPlays(long numberOfPlays) {
        this.numberOfPlays = numberOfPlays;
    }

    public BigDecimal getSumWins() {
        return sumWins;
    }

    public void setSumWins(BigDecimal sumWins) {
        this.sumWins = sumWins;
    }

    public BigDecimal getSumBets() {
        return sumBets;
    }

    public void setSumBets(BigDecimal sumBets) {
        this.sumBets = sumBets;
    }

    public BigDecimal getMaxWin() {
        return maxWin;
    }

    public void setMaxWin(BigDecimal maxWin) {
        this.maxWin = maxWin;
    }

    public BigDecimal getMaxBet() {
        return maxBet;
    }

    public void setMaxBet(BigDecimal maxBet) {
        this.maxBet = maxBet;
    }

    public BigDecimal getMaxMultiplier() {
        return maxMultiplier;
    }

    public void setMaxMultiplier(BigDecimal maxMultiplier) {
        this.maxMultiplier = maxMultiplier;
    }

    public BigDecimal getSumBonus() {
        return sumBonus;
    }

    public void setSumBonus(BigDecimal sumBonus) {
        this.sumBonus = sumBonus;
    }

    public int getStartLevel() {
        return startLevel;
    }

    public void setStartLevel(int startLevel) {
        this.startLevel = startLevel;
    }

    public BigDecimal getStartPercNl() {
        return startPercNl;
    }

    public void setStartPercNl(BigDecimal startPercNl) {
        this.startPercNl = startPercNl;
    }

    public int getEndLevel() {
        return endLevel;
    }

    public void setEndLevel(int endLevel) {
        this.endLevel = endLevel;
    }

    public BigDecimal getEndPercNl() {
        return endPercNl;
    }

    public void setEndPercNl(BigDecimal endPercNl) {
        this.endPercNl = endPercNl;
    }

    public BigDecimal getTotalWin() {
        return totalWin;
    }

    public void setTotalWin(BigDecimal totalWin) {
        this.totalWin = totalWin;
    }

    public Long getTotalXP() {
        return totalXP;
    }

    public void setTotalXP(Long totalXP) {
        this.totalXP = totalXP;
    }

    public List<SessionAchievement> getAchievements() {
        return achievements;
    }

    public void setAchievements(List<SessionAchievement> achievements) {
        this.achievements = achievements;
    }

    public Instant getStartedAt() {
        return startedAt;
    }

    public void setStartedAt(Instant startedAt) {
        this.startedAt = startedAt;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public BigDecimal getMaxDoubleAmount() {
        return maxDoubleAmount;
    }

    public void setMaxDoubleAmount(BigDecimal maxDoubleAmount) {
        this.maxDoubleAmount = maxDoubleAmount;
    }

    public BigDecimal getSumBoosts() {
        return sumBoosts;
    }

    public void setSumBoosts(BigDecimal sumBoosts) {
        this.sumBoosts = sumBoosts;
    }

    @Deprecated
    public BigDecimal getSumBwins() {
        return sumBwins;
    }

    @Deprecated
    public BigDecimal getSumBbets() {
        return sumBbets;
    }

    @Override
    public String toString() {
        return "SessionStatistics{" +
                "sessionId=" + sessionId +
                ", numberOfPlays=" + numberOfPlays +
                ", numberOfWins=" + numberOfWins +
                ", sumWins=" + sumWins +
                ", sumBoosts=" + sumBoosts +
                ", sumBets=" + sumBets +
                ", maxWin=" + maxWin +
                ", maxBet=" + maxBet +
                ", maxBonus=" + maxBonus +
                ", maxMultiplier=" + maxMultiplier +
                ", startedAt=" + startedAt +
                ", lastAt=" + lastAt +
                ", totalWin=" + totalWin +
                ", totalXP=" + totalXP +
                ", achievements=" + achievements +
                '}';
    }
}

package com.ously.gamble.api.bridge.exceptions;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class VendorBridgeException extends Exception {
    private GameSessionDetails details;
    private GameErrorType type;

    VendorBridgeException() {
        super();
    }

    VendorBridgeException(String message) {
        super(message);
    }

    VendorBridgeException(String message, Throwable cause) {
        super(message, cause);
    }

    public VendorBridgeException(Throwable causeException, GameErrorType type, GameSessionDetails details, String message) {
        super(message, causeException);
        this.details = details;
        this.type = type;
    }

    public VendorBridgeException(GameErrorType type, GameSessionDetails details, String message) {
        super(message);
        this.details = details;
        this.type = type;
    }

    public VendorBridgeException(Throwable causeException, GameErrorType type, GameSessionDetails details) {
        this(causeException, type, details, "no message");
    }

    public VendorBridgeException(GameErrorType type, GameSessionDetails details) {
        this(type, details, "no message");
    }

    public void setGameSessionDetails(GameSessionDetails gsd) {
        this.details = gsd;
    }

    public GameSessionDetails getDetails() {
        return details;
    }

    public GameErrorType getType() {
        return type;
    }

    @JsonIgnore
    public String getReason() {
        return getMessage() + " ->" + details.getDetailsAsString();
    }
}

package com.ously.gamble.api.features;

import com.ously.gamble.api.achievements.Reward;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

public class SpinWheelResponse {

    @Schema(description = "the slots of the wheel, reward description")
    String[] slots;
    @Schema(description = "the types of the slots")
    WheelSegmentType[] types;
    @Schema(description = "index of slot hit")
    int hitindex;
    @Schema(description = "secret which needs to be send for collect call")
    String secret;
    @Schema(description = "number of wheelspin tokens available")
    int currentTokenCount;
    @Schema(description = "number of diamonds used for spinning")
    int usedDiamonds;

    @Schema(description = "when true, the wheelspin was performed")
    boolean spinned = true;

    @Schema(description = "minimum win amount")
    BigDecimal minWin;
    @Schema(description = "medium win amount")
    BigDecimal medWin;
    @Schema(description = "max win amount")
    BigDecimal maxWin;

    Reward[] rewards;

    public Reward[] getRewards() {
        return rewards;
    }

    public void setRewards(Reward... rewards) {
        this.rewards = rewards;
    }

    public String[] getSlots() {
        return slots;
    }

    public void setSlots(String... slots) {
        this.slots = slots;
    }

    public int getHitindex() {
        return hitindex;
    }

    public void setHitindex(int hitindex) {
        this.hitindex = hitindex;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public int getCurrentTokenCount() {
        return currentTokenCount;
    }

    public void setCurrentTokenCount(int currentTokenCount) {
        this.currentTokenCount = currentTokenCount;
    }

    public boolean isSpinned() {
        return spinned;
    }

    public void setSpinned(boolean spinned) {
        this.spinned = spinned;
    }

    public WheelSegmentType[] getTypes() {
        return types;
    }

    public void setTypes(WheelSegmentType... types) {
        this.types = types;
    }

    public BigDecimal getMinWin() {
        return minWin;
    }

    public void setMinWin(BigDecimal minWin) {
        this.minWin = minWin;
    }

    public BigDecimal getMedWin() {
        return medWin;
    }

    public void setMedWin(BigDecimal medWin) {
        this.medWin = medWin;
    }

    public BigDecimal getMaxWin() {
        return maxWin;
    }

    public void setMaxWin(BigDecimal maxWin) {
        this.maxWin = maxWin;
    }

    public int getUsedDiamonds() {
        return usedDiamonds;
    }

    public void setUsedDiamonds(int usedDiamonds) {
        this.usedDiamonds = usedDiamonds;
    }
}

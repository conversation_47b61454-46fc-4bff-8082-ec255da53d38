package com.ously.gamble.api.issues;

import java.util.List;

public interface IssueService {
    CasinoIssue getIssue(Long id);

    /**
     * @param cic the payload for the comment
     * @param id  the id of the issue
     * @return the complete issue
     */
    CasinoIssue addComment(CasinoIssueComment cic, Long id, Long uid);

    CasinoIssue changeStatus(Long id, String status, Long uid);

    CasinoIssue createCasinoIssue(CasinoIssue ci);

    CasinoIssue reassignToUserId(Long id, Long uid, Long assignerId);

    List<String> getStatusValues();

    CasinoIssue newIssueForUser(CasinoIssue ci, Long userId);
}

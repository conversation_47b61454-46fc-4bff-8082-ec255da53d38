package com.ously.gamble.api.missions;

public class UserProgressDto {
    long missionId;
    long value;
    long changedAtEs;

    public UserProgressDto() {
    }

    public UserProgressDto(long missionId, long value, long caES) {
        this.missionId = missionId;
        this.value = value;
        this.changedAtEs = caES;
    }

    public long getMissionId() {
        return missionId;
    }

    public void setMissionId(long missionId) {
        this.missionId = missionId;
    }

    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }

    public long getChangedAtEs() {
        return changedAtEs;
    }

    public void setChangedAtEs(long changedAtEs) {
        this.changedAtEs = changedAtEs;
    }
}

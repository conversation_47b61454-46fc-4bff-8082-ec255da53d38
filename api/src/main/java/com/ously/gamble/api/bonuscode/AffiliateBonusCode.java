package com.ously.gamble.api.bonuscode;

import com.ously.gamble.payload.TxPrice;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class AffiliateBonusCode {

    @NotNull
    private Integer id = -1;

    @NotNull
    private Integer affiliateId;

    @NotNull
    private String bonusCode;

    @NotNull
    private BonusCodeType type;

    @NotNull
    private boolean active = true;

    @NotNull
    private LocalDateTime validFrom;
    @NotNull
    private LocalDateTime validUntil;

    @NotNull
    private int maxCount;

    @NotNull
    private int usedCount;

    List<TxPrice> rewards = new ArrayList<>(0);

    /**
     * Userid as string when bound to a user! maxusage = 1, can only be taken by the user!!
     * Usually handed out from support.
     */
    private Long userId;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAffiliateId() {
        return affiliateId;
    }

    public void setAffiliateId(Integer affiliateId) {
        this.affiliateId = affiliateId;
    }

    public String getBonusCode() {
        return bonusCode;
    }

    public void setBonusCode(String bonusCode) {
        this.bonusCode = bonusCode;
    }

    public BonusCodeType getType() {
        return type;
    }

    public void setType(BonusCodeType type) {
        this.type = type;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public LocalDateTime getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(LocalDateTime validFrom) {
        this.validFrom = validFrom;
    }

    public LocalDateTime getValidUntil() {
        return validUntil;
    }

    public void setValidUntil(LocalDateTime validUntil) {
        this.validUntil = validUntil;
    }

    public int getMaxCount() {
        return maxCount;
    }

    public void setMaxCount(int maxCount) {
        this.maxCount = maxCount;
    }

    public int getUsedCount() {
        return usedCount;
    }

    public void setUsedCount(int usedCount) {
        this.usedCount = usedCount;
    }

    public List<TxPrice> getRewards() {
        return rewards;
    }

    public void setRewards(List<TxPrice> rewards) {
        this.rewards = rewards;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}

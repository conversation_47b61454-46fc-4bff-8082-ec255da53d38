package com.ously.gamble.api.slotcatalog;

import jakarta.validation.constraints.NotNull;

public class SCLinkRequest {

    @NotNull
    private Integer gameInfoId;
    @NotNull
    private Long gameId;

    public Integer getGameInfoId() {
        return gameInfoId;
    }

    public void setGameInfoId(Integer gameInfoId) {
        this.gameInfoId = gameInfoId;
    }

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }
}

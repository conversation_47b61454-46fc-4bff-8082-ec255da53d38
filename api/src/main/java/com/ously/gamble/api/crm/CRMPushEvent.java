package com.ously.gamble.api.crm;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CRMPushEvent {
    @JsonProperty("delivery_id" )
    String deliveryId;

    Long timestamp;

    String event = "opened";

    @JsonProperty("device_id" )
    String deviceId;


    public String getDeliveryId() {
        return deliveryId;
    }

    public void setDeliveryId(String deliveryId) {
        this.deliveryId = deliveryId;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
}

package com.ously.gamble.api.achievements;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Schema
@JsonInclude(Include.NON_NULL)
public class Reward implements Serializable {
    private static final Reward[] EMPTY_REWARD_ARRAY = new Reward[0];
    @NotNull
    String type;

    Double multiplier;

    Double amount;

    Long minutes;

    int count = 1;


    public Reward(Reward a) {
        this.type = a.type;
        this.minutes = a.minutes;
        this.multiplier = a.multiplier;
        this.count = a.count;
        this.amount = a.amount;
    }

    @SuppressWarnings("unchecked")
    public static List<Reward> createRewardsFromDefinition(String priceDefinition) {
        if (StringUtils.isEmpty(priceDefinition)) {
            return Collections.EMPTY_LIST;
        }
        return Arrays.stream(priceDefinition.split(",")).map(a -> {
            try {
                return new Reward(a);
            } catch (Exception e) {
                //
            }
            return null;
        }).filter(Objects::nonNull).toList();
    }

    public static Reward[] createRewardsArrayFromDefinition(String priceDefinition) {
        if (StringUtils.isEmpty(priceDefinition)) {
            return EMPTY_REWARD_ARRAY;
        }
        return Arrays.stream(priceDefinition.split(",")).map(a -> {
            try {
                return new Reward(a);
            } catch (Exception e) {
                //
            }
            return null;
        }).filter(Objects::nonNull).toArray(Reward[]::new);
    }

    public Reward() {
    }

    public Reward(String pd) {
        // "M" - Multiplier [WX]2x15
        // MX XP multiplier, MS win multiplier
        // "X" - add XP ######
        // "S" - add Spinz ######

        var comps = pd.split("#");
        type = comps[0];
        if (type.charAt(0) == 'M') {
            if (!"MS".equals(type)) {
                type = "MX";
            }
            if (comps.length > 1) {
                try {
                    if (comps[1].contains(".")) {
                        multiplier = Double.parseDouble(comps[1]) * 100;
                    } else {
                        multiplier = Double.valueOf(comps[1]);
                    }
                } catch (Exception e) {
                    multiplier = 120.0d;
                }
            } else {
                multiplier = 120.0d;
                minutes = "MX".equals(type) ? 30L : 15L;
                count = 1;
            }
            if (comps.length > 2) {
                minutes = Long.valueOf(comps[2]);
            } else {
                minutes = "MX".equals(type) ? 30L : 15L;
            }
            if (comps.length == 4) {
                count = Integer.parseInt(comps[3]);
            }
        } else if ("WF".equals(type) || "SWF".equals(type) || "KS".equals(type) || "KV".equals(type) || "D".equals(type)
                || "TCS".equals(type) || "TCM".equals(type) || "TCL".equals(type) || "LU".equals(type)
        ) {
            if (comps.length == 2) {
                count = Integer.parseInt(comps[1]);
            }
        } else {
            if (comps[1].contains(".")) {
                comps[1] = comps[1].substring(0, comps[1].indexOf('.'));
            }

            amount = Double.valueOf(comps[1]);
        }
    }

    public String getStringDefinition() {
        switch (type) {
            case "MS", "MX" -> {
                var mult = multiplier.longValue();
                return type + '#' + mult + '#' + minutes + '#' + count;
            }
            case "S" -> {
                return type + '#' + amount.longValue();
            }
            default -> {
                return type + '#' + count;
            }
        }
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Double getMultiplier() {
        return multiplier;
    }

    public void setMultiplier(Double multiplier) {
        this.multiplier = multiplier;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Long getMinutes() {
        return minutes;
    }

    public void setMinutes(Long minutes) {
        this.minutes = minutes;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }


}

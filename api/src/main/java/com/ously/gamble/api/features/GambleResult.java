package com.ously.gamble.api.features;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;

import java.math.BigDecimal;

public class GambleResult {
    @Null
    String nextSecret;
    @NotNull
    Long nextRound;
    @Schema(format = "double")
    @NotNull
    BigDecimal maxBetAmount;
    @Schema(format = "double")
    @NotNull
    BigDecimal amountWon;
    @Schema(format = "double")
    @NotNull
    BigDecimal walletAmount;
    @NotNull
    boolean won;

    public String getNextSecret() {
        return nextSecret;
    }

    public void setNextSecret(String nextSecret) {
        this.nextSecret = nextSecret;
    }

    public Long getNextRound() {
        return nextRound;
    }

    public void setNextRound(Long nextRound) {
        this.nextRound = nextRound;
    }

    public BigDecimal getMaxBetAmount() {
        return maxBetAmount;
    }

    public void setMaxBetAmount(BigDecimal maxBetAmount) {
        this.maxBetAmount = maxBetAmount;
    }

    public BigDecimal getAmountWon() {
        return amountWon;
    }

    public void setAmountWon(BigDecimal amountWon) {
        this.amountWon = amountWon;
    }

    public BigDecimal getWalletAmount() {
        return walletAmount;
    }

    public void setWalletAmount(BigDecimal walletAmount) {
        this.walletAmount = walletAmount;
    }

    public boolean isWon() {
        return won;
    }

    public void setWon(boolean won) {
        this.won = won;
    }
}

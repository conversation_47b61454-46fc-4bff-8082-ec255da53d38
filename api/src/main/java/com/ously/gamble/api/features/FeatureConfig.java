package com.ously.gamble.api.features;

import com.ously.gamble.persistence.model.session.Jurisdiction;

import java.time.Duration;

public interface FeatureConfig {

    Duration getLowBalanceLimitPeriod();

    boolean isLevelMultShopItems();

    Boolean getShowExceptionsOnError();

    String getAssetBase();

    String getThumbsBase();

    Float getImageCompressionQuality();

    Boolean getTesting();

    Boolean getAutoActive();

    String getMailfrom();

    String getBaseUrl();

    Boolean getFakeValidation();

    String getStage();

    String getAppBaseUrl();

    Boolean getMailVerification();

    boolean isPopupsEnabled();

    boolean isSocialFeaturesEnabled();

    boolean isMessagesEnabled();

    boolean isWheelEnabled();

    boolean arePerksEnabled();

    Jurisdiction getJurisdiction();

    boolean isCustombetsizes();

    String getPlatform();

    String getAggregationKey();
}

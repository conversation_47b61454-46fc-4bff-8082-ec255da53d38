package com.ously.gamble.api.kyc;


public class KYCDocument {

    final KYCDataFormat format;
    final KYCDataType type;
    final String filename;
    final byte[] data;

    public KYCDocument(String filename, byte[] data) {
        this(filename, data, KYCDataType.OTHER);
    }


    public KYCDocument(String filename, byte[] data, KYCDataType type) {
        this.filename = filename;
        this.data = data;
        this.type = type;
        this.format = guessFormat(filename);
    }

    private static KYCDataFormat guessFormat(String filename) {
        return KYCDataFormat.ofFilename(filename);
    }


    public KYCDocument(String filename, byte[] data, KYCDataType type, KYCDataFormat format) {
        this.filename = filename;
        this.data = data;
        this.type = type;
        this.format = format;
    }

    public KYCDataFormat getFormat() {
        return format;
    }

    public KYCDataType getType() {
        return type;
    }

    public String getFilename() {
        return filename;
    }

    public byte[] getData() {
        return data;
    }
}

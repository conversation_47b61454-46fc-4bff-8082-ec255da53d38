package com.ously.gamble.api.auth;

import com.ously.gamble.api.security.UserPrincipal;
import com.ously.gamble.api.user.*;
import org.springframework.http.ResponseEntity;

import java.util.Map;
import java.util.Optional;

public interface AuthenticationService {

    ResponseEntity<JwtAuthenticationResponse> authenticate(LoginRequest loginRequest, String hst);

    ResponseEntity<JwtAuthenticationResponse> refreshToken(RefreshRequest refreshRequest,
                                                           String remAddrStr);

    Optional<JwtAuthenticationResponse> authenticateAdmin(LoginRequest loginRequest,
                                                          String hst);

    Optional<JwtAuthenticationResponse> refreshTokenAdmin(RefreshRequest refreshRequest,
                                                          String remAddrStr);

    DeviceAnonUser getDeviceAnonUser(String id, long nonce);

    DeviceAnonUser getFacebookUser(String fbUid, UserPrincipal currentUser);

    RegistrationResult registerUser(RegistrationRequest req, String hostIP);

    void sendVerificationEmail(String email, long userId, String username, boolean recreateCode);

    void sendVerificationEmail(String email, boolean recreateCode);

    boolean isVerificationPending(String email);

    JwtAuthenticationResponse registrationVerificationGet(String bCode) throws Exception;

    JwtAuthenticationResponse registrationVerification(String code, String email);

    boolean isUsernameAvailable(String username);

    boolean isEmailUsedAlready(String email);

    void expireRegistrationVerificationRequests(boolean force);

    void expirePasswordChangeRequests(boolean force);

    PasswordChangeResponse passwordChangeRequest(PasswordChangeRequest req);

    PasswordChangeResponse doPasswordChangeGet(ExecutePasswordChangeRequest req);

    PasswordChangeResponse doPasswordChange(String code, String email, String newPW);

    void deleteAccount(long id, String localId, String tokenKey);

    void cleanupAfterRemovalAccount(long id);

    Map<String, String> updateAuthProviderUID();

    void saveAuthProviderUID(Map<String, String> uids);

    void triggerAuthRecordUpdate();

    DeviceAnonUser getOrCreateAnonUser(String fbu);

    DeviceAnonUser getOrCreateAnonUserD(String fbu, StringBuilder bld);
}

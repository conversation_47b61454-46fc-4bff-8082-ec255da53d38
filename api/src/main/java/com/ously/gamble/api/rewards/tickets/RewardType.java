package com.ously.gamble.api.rewards.tickets;

import com.ously.gamble.api.achievements.AchievementType;

public enum RewardType {
    TOURNAMENT_WIN(AchievementType.TOURNAMENT_WIN),
    MILESTONE_STAGE(AchievementType.MILESTONE_STAGE),
    JACKPOT_WIN(AchievementType.JACKPOT_WIN),
    CUSTOM(AchievementType.CUSTOM);


    private final AchievementType aType;

    RewardType(AchievementType aType) {
        this.aType = aType;
    }

    public final AchievementType aType() {
        return aType;
    }

}

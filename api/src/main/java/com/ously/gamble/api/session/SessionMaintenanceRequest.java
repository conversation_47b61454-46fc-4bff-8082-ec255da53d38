package com.ously.gamble.api.session;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * Bean used to trigger session Maintenance via queues
 */
public class SessionMaintenanceRequest implements Serializable {
    SessionMaintenanceType type;
    Long userId;
    Long sessionId;
    Long gameId;
    boolean force;

    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    ZonedDateTime from;
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    ZonedDateTime to;
    int numTxCopied;
    int numRoundsCreated;

    public SessionMaintenanceRequest() {
    }

//    public SessionMaintenanceRequest(GameSessionStatistics gss) {
//        this.userId = gss.getUserId();
//        this.gameId = gss.getGameId();
//        this.sessionId = gss.getId();
//        this.from = gss.getStartedAt().atZone(ZoneOffset.UTC);
//        this.to = gss.getEndedAt().atZone(ZoneOffset.UTC);
//        this.numTxCopied = 0;
//        this.numRoundsCreated = 0;
//    }

    public static SessionMaintenanceRequest forDeleteOldTransactions(long userId, long sessionId,
                                                                     Long gameId) {
        var req = new SessionMaintenanceRequest();
        req.type = SessionMaintenanceType.CTX_REMOVAL;
        req.userId = userId;
        req.sessionId = sessionId;
        req.gameId = gameId;
        return req;
    }

    public static SessionMaintenanceRequest forMigration(long userId, long sessionId, Long gameId) {
        return forMigration(userId, sessionId, gameId, false);
    }

    public static SessionMaintenanceRequest forMigration(long userId, long sessionId, Long gameId,
                                                         boolean force) {
        var req = new SessionMaintenanceRequest();
        req.type = SessionMaintenanceType.MIGRATE;
        req.userId = userId;
        req.force = force;
        req.sessionId = sessionId;
        req.gameId = gameId;
        return req;
    }

    public static SessionMaintenanceRequest forStatisticsRefresh(long userId, long sessionId,
                                                                 long gameId) {
        return forStatisticsRefresh(userId, sessionId, gameId, false);
    }

    public static SessionMaintenanceRequest forStatisticsRefresh(long userId, long sessionId,
                                                                 long gameId,
                                                                 boolean forceMigration) {
        var req = new SessionMaintenanceRequest();
        req.type = SessionMaintenanceType.STATSREFRESH;
        req.userId = userId;
        req.force = forceMigration;
        req.sessionId = sessionId;
        req.gameId = gameId;
        return req;
    }


    public SessionMaintenanceType getType() {
        return type;
    }

    public void setType(SessionMaintenanceType type) {
        this.type = type;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }

    public ZonedDateTime getFrom() {
        return from;
    }

    public void setFrom(ZonedDateTime from) {
        this.from = from;
    }

    public ZonedDateTime getTo() {
        return to;
    }

    public void setTo(ZonedDateTime to) {
        this.to = to;
    }

    public int getNumTxCopied() {
        return numTxCopied;
    }

    public void setNumTxCopied(int numTxCopied) {
        this.numTxCopied = numTxCopied;
    }

    public int getNumRoundsCreated() {
        return numRoundsCreated;
    }

    public void setNumRoundsCreated(int numRoundsCreated) {
        this.numRoundsCreated = numRoundsCreated;
    }

    public boolean isForce() {
        return force;
    }

    public void setForce(boolean force) {
        this.force = force;
    }

    @Override
    public String toString() {
        return "SessionMaintenanceRequest{" +
                "type=" + type +
                ", userId=" + userId +
                ", sessionId=" + sessionId +
                ", gameId=" + gameId +
                ", force=" + force +
                ", from=" + from +
                ", to=" + to +
                ", numTxCopied=" + numTxCopied +
                ", numRoundsCreated=" + numRoundsCreated +
                '}';
    }
}

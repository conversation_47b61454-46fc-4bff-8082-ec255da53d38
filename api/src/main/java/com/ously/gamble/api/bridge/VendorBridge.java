package com.ously.gamble.api.bridge;

import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.model.session.Jurisdiction;

import java.util.List;
import java.util.Map;

public interface VendorBridge {

    CreateSessionResponse createGameSession(CreateSessionRequest req);

    GameInstance createGameInstance(Long userId, CasinoGame game, GamePlatform platform,
                                    GameSettings settings, Long nonce, String crypt,
                                    Jurisdiction jd) throws Exception;

    List<String> getProviderNames();

    List<AvailableGame> getAvailableGamesFromVendor(String vendorName);


    Map<String, List<AvailableGame>> getAvailableGames();

    CreateFreespinsResponse createFreespins(CreateFreespinsRequest req);
}

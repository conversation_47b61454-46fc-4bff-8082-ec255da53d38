package com.ously.gamble.api.gamemanager;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;

public class GMGameImage {

    Long gameId;
    @JsonProperty("iType")
    String iType;
    @JsonProperty("realFormat")
    String realType;
    @JsonProperty("mimeType")
    String mimetype;
    LocalDateTime updatedAt;
    LocalDateTime createdAt;

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }

    public String getiType() {
        return iType;
    }

    public void setiType(String iType) {
        this.iType = iType;
    }

    public String getRealType() {
        return realType;
    }

    public void setRealType(String realType) {
        this.realType = realType;
    }

    public String getMimetype() {
        return mimetype;
    }

    public void setMimetype(String mimetype) {
        this.mimetype = mimetype;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}

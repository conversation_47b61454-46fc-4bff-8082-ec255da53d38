package com.ously.gamble.api.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;

import java.util.Map;

public class WebhookAddPopupRequest extends WebhookRequest {
    @NotEmpty
    @JsonProperty(required = true, value = "userid")
    String userId;
    Long popupDefinitionId;
    String selector;
    Map<String, String> variables;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSelector() {
        return selector;
    }

    public void setSelector(String selector) {
        this.selector = selector;
    }

    public Long getPopupDefinitionId() {
        return popupDefinitionId;
    }

    public void setPopupDefinitionId(Long popupDefinitionId) {
        this.popupDefinitionId = popupDefinitionId;
    }

    public Map<String, String> getVariables() {
        return variables;
    }

    public void setVariables(Map<String, String> variables) {
        this.variables = variables;
    }


    @Override
    public String toString() {
        return "WebhookAddPopupRequest{" +
                "userId='" + userId + '\'' +
                ", popupDefinitionId=" + popupDefinitionId +
                ", selector='" + selector + '\'' +
                ", variables=" + variables +
                "} " + super.toString();
    }
}

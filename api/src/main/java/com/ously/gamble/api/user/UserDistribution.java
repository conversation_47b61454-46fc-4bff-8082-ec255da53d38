package com.ously.gamble.api.user;

import com.ously.gamble.persistence.projections.UserAgeBucketPJ;

import java.time.LocalDate;
import java.util.Objects;

public class UserDistribution {

    private LocalDate date;
    private Integer b0;
    private Integer b1;
    private Integer b5;
    private Integer b10;
    private Integer b20;
    private Integer b30;
    private Integer b40;
    private Integer b50;
    private Integer bmore;


    public UserDistribution(UserAgeBucketPJ userAgeBucketForDay) {
        this.date = LocalDate.parse(userAgeBucketForDay.getDay());
        this.b0 = getValueOrZero(userAgeBucketForDay.getB0());
        this.b1 = getValueOrZero(userAgeBucketForDay.getB1());
        this.b5 = getValueOrZero(userAgeBucketForDay.getB5());
        this.b10 = getValueOrZero(userAgeBucketForDay.getB10());
        this.b20 = getValueOrZero(userAgeBucketForDay.getB20());
        this.b30 = getValueOrZero(userAgeBucketForDay.getB30());
        this.b40 = getValueOrZero(userAgeBucketForDay.getB40());
        this.b50 = getValueOrZero(userAgeBucketForDay.getB50());
        this.bmore = getValueOrZero(userAgeBucketForDay.getBmore());
    }

    public UserDistribution() {
    }

    private static Integer getValueOrZero(Integer b0) {
        return Objects.requireNonNullElse(b0, 0);
    }

    public LocalDate getDate() {
        return date;
    }


    public Integer getB0() {
        return b0;
    }


    public Integer getB1() {
        return b1;
    }


    public Integer getB5() {
        return b5;
    }


    public Integer getB10() {
        return b10;
    }


    public Integer getB20() {
        return b20;
    }


    public Integer getB30() {
        return b30;
    }


    public Integer getB40() {
        return b40;
    }

    public Integer getB50() {
        return b50;
    }


    public Integer getBmore() {
        return bmore;
    }

}

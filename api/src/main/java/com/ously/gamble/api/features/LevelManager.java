package com.ously.gamble.api.features;

import com.ously.gamble.api.achievements.AwardedBonus;
import com.ously.gamble.persistence.model.Wallet;
import org.javatuples.Pair;

import java.math.BigDecimal;
import java.util.List;

public interface LevelManager {

    Pair<Boolean, LevelUpBean> applyLevels(Wallet w, BigDecimal bet, double xpMult);

    List<LevelInfo> getRules();

    AwardedBonus createDevLevelupAchievement(long userId, int level);

    AwardedBonus createAchievementsForLevel(LevelInfo r, Long id);
}

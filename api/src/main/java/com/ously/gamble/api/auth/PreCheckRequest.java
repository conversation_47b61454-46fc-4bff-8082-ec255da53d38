package com.ously.gamble.api.auth;

public class PreCheckRequest {
    String di;
    Long nonce;

    public String getDi() {
        return di;
    }

    public void setDi(String di) {
        this.di = di;
    }

    public Long getNonce() {
        return nonce;
    }

    public void setNonce(Long nonce) {
        this.nonce = nonce;
    }


    @Override
    public String toString() {
        return "PreCheckRequest{" + "di='" + di + '\'' + ", nonce=" + nonce + '}';
    }
}

package com.ously.gamble.api.kyc;

public enum KYCUserAction {
    NONE(""),

    NONE_IN_REVIEW("In review"),
    SEND_DOCUMENT("Please send required documents"),
    CONTACT_SUPPORT("Please contact support"),
    CONTINUE_KYC("Continue/start KYC verification.");


    private final String comment;

    KYCUserAction(String comment) {
        this.comment = comment;
    }

    public String comment() {
        return comment;
    }
}

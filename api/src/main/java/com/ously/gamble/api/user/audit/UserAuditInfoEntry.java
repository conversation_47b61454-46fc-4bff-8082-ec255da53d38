package com.ously.gamble.api.user.audit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.InstantSerializer;

import java.time.Instant;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record UserAuditInfoEntry(@JsonSerialize(using = InstantSerializer.class) Instant createdAt, Long createdBy,
                                 Map<String, Object> infos) {

    @Override
    public String toString() {
        return createdAt.toString() + "(" + createdBy + ") -> " + infos.toString();
    }
}

package com.ously.gamble.api.consumable;

public record LevelCoinMultRange(int startlevel, int multiplier) implements Comparable<LevelCoinMultRange>{

    public static LevelCoinMultRange fromString(String setting){
        String[] split = setting.split("=");
        if(split.length==2) {
            return new LevelCoinMultRange(Integer.parseInt(split[0].trim()), Integer.parseInt(split[1].trim()));
        }else{
            return new LevelCoinMultRange(0,100);
        }
    }

    public String toString(){
        return startlevel+"="+multiplier;
    }

    @Override
    public int compareTo(LevelCoinMultRange o) {
        return Integer.compare(this.startlevel,o.startlevel);
    }
}

package com.ously.gamble.api.games;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.ously.gamble.persistence.projections.DailyGameRankDto;

import java.time.LocalDate;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class GameRankEntry {
    Integer rank;
    Integer previousRank;
    LocalDate date;
    long gameId;
    String name;
    String vendorName;
    int sessions;
    int spins;
    int totalSessionTimeSec;
    double sumBet;
    double sumWin;
    float rtp;

    public GameRankEntry() {
    }

    public GameRankEntry(LocalDate ld, DailyGameRankDto pj) {
        this.date = ld;
        this.gameId = pj.gameId();
        this.name = pj.name();
        this.vendorName = pj.vendorName();
        this.sessions = pj.sessions();
        this.spins = pj.spins();
        this.totalSessionTimeSec = pj.sessionTimeTotal();
        this.sumBet = pj.sumBet();
        this.sumWin = pj.sumWin();
        this.rtp = pj.rtp();
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public Integer getPreviousRank() {
        return previousRank;
    }

    public void setPreviousRank(Integer previousRank) {
        this.previousRank = previousRank;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public long getGameId() {
        return gameId;
    }

    public void setGameId(long gameId) {
        this.gameId = gameId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public int getSessions() {
        return sessions;
    }

    public void setSessions(int sessions) {
        this.sessions = sessions;
    }

    public int getSpins() {
        return spins;
    }

    public void setSpins(int spins) {
        this.spins = spins;
    }

    public int getTotalSessionTimeSec() {
        return totalSessionTimeSec;
    }

    public void setTotalSessionTimeSec(int totalSessionTimeSec) {
        this.totalSessionTimeSec = totalSessionTimeSec;
    }

    public double getSumBet() {
        return sumBet;
    }

    public void setSumBet(double sumBet) {
        this.sumBet = sumBet;
    }

    public double getSumWin() {
        return sumWin;
    }

    public void setSumWin(double sumWin) {
        this.sumWin = sumWin;
    }

    public float getRtp() {
        return rtp;
    }

    public void setRtp(float rtp) {
        this.rtp = rtp;
    }
}

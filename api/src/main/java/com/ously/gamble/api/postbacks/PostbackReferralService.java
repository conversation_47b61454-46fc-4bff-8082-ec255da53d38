package com.ously.gamble.api.postbacks;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

public interface PostbackReferralService {
    String createPostbackReferral(CreatePostbackReferralPayload payload);

    void addUserId(long postbackId, long num, long userId);

    Optional<PostbackReferralDto> getPostbackReferral(Long postbackId, Long num);

    Optional<PostbackReferralDto> findByUserId(Long userId);

    Optional<PostbackReferralDto> getPostbackReferralByCode(String code);

    Page<PostbackReferralDto> getAllPostbackReferrals(long postbackId, Pageable pageable);
}

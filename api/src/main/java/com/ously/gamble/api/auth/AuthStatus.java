package com.ously.gamble.api.auth;

public enum AuthStatus {
    OK(true),
    OK_BUT_CHANGE_PW(true),
    FAILED_RETRY(false),
    FAILED_BLOCKED(false),
    FAILED_BANNED(false),
    TEMP_EXCLUDED(false),
    TEMP_DISABLED(false);

    private final boolean success;

    AuthStatus(boolean authSuccess) {
        success = authSuccess;
    }

    public boolean isSuccess() {
        return success;
    }

}

package com.ously.gamble.api.session;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ously.gamble.persistence.projections.EmptyGameInstancePJ;

import java.time.LocalDateTime;

public record EmptyGameInstance(
        @JsonProperty("sessionId") long sessionId,
        @JsonProperty("gameId") long gameId,
        @JsonProperty("platform") String platform,
        @JsonProperty("startAt") LocalDateTime startAt,
        @JsonProperty("numtrans") long numtrans
) {
    public EmptyGameInstance(EmptyGameInstancePJ pj) {
        this(pj.getSessionId(), pj.getGameId(), pj.getPlatform(), pj.getStartAt(), pj.getNumtrans());
    }
}

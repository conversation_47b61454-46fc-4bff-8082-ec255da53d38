package com.ously.gamble.payload.purchase;

import java.math.BigDecimal;

public class DropInPaymentRequest {

    Long consumableId;
    BigDecimal amount;
    String stateData;
    String currency;
    String locale;

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public Long getConsumableId() {
        return consumableId;
    }

    public void setConsumableId(Long consumableId) {
        this.consumableId = consumableId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getStateData() {
        return stateData;
    }

    public void setStateData(String stateData) {
        this.stateData = stateData;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @Override
    public String toString() {
        return "DropInPaymentRequest{" +
                "consumableId=" + consumableId +
                ", amount=" + amount +
                ", stateData='" + stateData + '\'' +
                ", currency='" + currency + '\'' +
                ", locale='" + locale + '\'' +
                '}';
    }
}

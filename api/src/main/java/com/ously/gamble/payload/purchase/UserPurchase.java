package com.ously.gamble.payload.purchase;

import com.ously.gamble.api.achievements.Reward;
import com.ously.gamble.persistence.model.Purchase;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

public class UserPurchase {

    @NotNull
    private final Instant createdAt;
    @NotNull
    private final List<Reward> rewards;
    @NotNull
    private final String txId;
    @Schema(format = "double")
    @NotNull
    private final BigDecimal price;
    @NotNull
    private final Long id;
    @NotNull
    private final String platform;
    @NotNull
    private final Long consumableId;

    private final Long userId;

    @NotNull
    private final String paymentStatus;

    @NotNull
    private final String paymentMethod;
    private final String orderReference;
    private final String abortReason;
    private final String pspId;


    public UserPurchase(Purchase p) {
        this.createdAt = p.getCreatedAt();
        this.rewards = Reward.createRewardsFromDefinition(p.getAppliedItems());
        this.txId = p.getTransactionId();
        this.price = p.getCost();
        this.id = p.getId();
        this.platform = p.getPlatform().toString();
        this.consumableId = p.getConsumableId();
        this.paymentStatus = p.getStatus().toString();
        this.paymentMethod = p.getPaymentMethod();
        this.orderReference = p.getOrderRef();
        this.abortReason = p.getAbortReason();
        this.pspId = p.getPsp();
        this.userId = p.getUserId();
    }

    public Long getUserId() {
        return userId;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public String getOrderReference() {
        return orderReference;
    }

    public String getAbortReason() {
        return abortReason;
    }

    public String getPspId() {
        return pspId;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public List<Reward> getRewards() {
        return rewards;
    }

    public String getTxId() {
        return txId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public Long getId() {
        return id;
    }

    public String getPlatform() {
        return platform;
    }

    public Long getConsumableId() {
        return consumableId;
    }
}

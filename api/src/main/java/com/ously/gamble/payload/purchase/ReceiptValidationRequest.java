package com.ously.gamble.payload.purchase;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;

import java.util.Base64;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class ReceiptValidationRequest {

    public ReceiptValidationRequest() {
    }

    public ReceiptValidationRequest(byte... data) {
        this.receiptdata = data;
    }

    public ReceiptValidationRequest(String aStr) {
        this.receiptdata = Base64.getDecoder().decode(aStr);

    }

    @JsonProperty(value = "receipt-data", required = true)
    @NotNull
    private byte[] receiptdata;

    @JsonProperty(value = "password")
    @NotNull
    private String password;

    @JsonProperty(value = "exclude-old-transactions")
    @NotNull
    private Boolean excludeOldTransactions;

    public byte[] getReceiptdata() {
        return receiptdata;
    }

    public void setReceiptdata(byte... receiptdata) {
        this.receiptdata = receiptdata;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Boolean getExcludeOldTransactions() {
        return excludeOldTransactions;
    }

    public void setExcludeOldTransactions(Boolean excludeOldTransactions) {
        this.excludeOldTransactions = excludeOldTransactions;
    }
}

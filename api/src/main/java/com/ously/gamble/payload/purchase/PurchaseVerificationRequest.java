package com.ously.gamble.payload.purchase;

/**
 * { “purchaseID”: “string, “productID”: string, “receipt”: string }
 * String purchaseId, String id, String productId, String receipt, String platform
 */
public class PurchaseVerificationRequest {

    /**
     * the productInfoId (id of consumable)
     */
    private String id;

    /**
     * The new id of the purchase (whatever dieter meant)
     */
    private String purchaseId;

    /**
     * reference to the actual product (TODO: needs clarification)
     */
    private String productId;

    /**
     * the receipt which is unique and is needed for verification
     */
    private String receipt;

    private String transactionId;


    /**
     * the "platform" (ios,google, web,...)
     */
    private String platform;

    public String getPurchaseId() {
        return purchaseId;
    }

    public void setPurchaseId(String purchaseId) {
        this.purchaseId = purchaseId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getReceipt() {
        return receipt;
    }

    public void setReceipt(String receipt) {
        this.receipt = receipt;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    @Override
    public String toString() {
        return "PurchaseVerificationRequest{" +
               "id='" + id + '\'' +
               ", purchaseId='" + purchaseId + '\'' +
               ", productId='" + productId + '\'' +
               ", receipt='" + receipt + '\'' +
               ", transactionId='" + transactionId + '\'' +
               ", platform='" + platform + '\'' +
               '}';
    }
}

package com.ously.gamble.payload.purchase;

import com.ously.gamble.persistence.model.PaymentStatus;

public class CheckPaymentResult {
    PaymentStatus status;
    String message;
    Long consumableId;


    public PaymentStatus getStatus() {
        return status;
    }

    public void setStatus(PaymentStatus status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getConsumableId() {
        return consumableId;
    }

    public void setConsumableId(Long consumableId) {
        this.consumableId = consumableId;
    }
}

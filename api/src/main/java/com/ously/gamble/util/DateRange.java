package com.ously.gamble.util;

import java.time.LocalDate;

public class DateRange {

    final LocalDate from;
    final LocalDate to;

    public static DateRange of(String from, String to) {
        return new DateRange(from, to);
    }

    DateRange(String from, String to) {
        this.from = LocalDate.parse(from);
        this.to = LocalDate.parse(to).plusDays(1);
    }

    public LocalDate getFrom() {
        return from;
    }

    public LocalDate getTo() {
        return to;
    }
}

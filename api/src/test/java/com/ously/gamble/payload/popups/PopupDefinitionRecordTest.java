package com.ously.gamble.payload.popups;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.popups.CasinoPopupDefinition;
import com.ously.gamble.api.popups.CasinoPopupType;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class PopupDefinitionRecordTest {

    /**
     * Testing json serialisation/deserialisation of records
     *
     * @throws JsonProcessingException on any exception
     */
    @Test
    public void testSerDeserWithRecords() throws JsonProcessingException {
        var cpd = new CasinoPopupDefinition(1L, "title", "body", "DE,EN", CasinoPopupType.NEWSLOT, "bgAsset", "cRef", "gameId=122,provider=APOLLO", "gameId=The gameId,provider=Provider name", false, "OK,SHOW,PLAY", null, null);

        var om = new ObjectMapper();
        var s = om.writeValueAsString(cpd);
        var cpd2 = om.readValue(s, CasinoPopupDefinition.class);
        assertEquals(cpd, cpd2);
    }

}

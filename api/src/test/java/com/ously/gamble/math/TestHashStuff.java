package com.ously.gamble.math;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;

public class TestHashStuff {


    @Test
    public void basicHashTests() {

        long[] result = new long[10_000];
        long startId = 0;
        for (int i = 0; i < 10_000; i++) {
            startId = startId + rand.nextInt(10) + 1;
            result[i] = createHash(startId);
        }

        Arrays.sort(result);
        OptionalLong max = Arrays.stream(result).max();
        for (int i = 0; i < result.length - 1; i++) {
            if (result[i] == result[i + 1]) {
                fail();
            }
        }

    }

    final Random rand = new Random(System.currentTimeMillis());
    byte[] randBytes = new byte[16];
    byte[] randBytes2 = new byte[6];

    private long createHash(long i) {
        long highestOneBit = (Long.highestOneBit(i) << 4);
        long r = (i ^ (0x00fffffffffffffL)) * 31 >> 4;
        r = (r & (highestOneBit - 1)) % highestOneBit;
        return r;
    }


    @Test
    void testRandomHash() {
        Set<String> results = new HashSet<>(1_000);

        long startId = 0;
        for (int i = 0; i < 1_000; i++) {
            startId = startId + rand.nextInt(10) + 1;
            results.add(createHashStr(6));
        }

        assertEquals(1_000, results.size());
    }


    /**
     * Create a random base64 String without any correlation.
     *
     * @param numBytes numBytes the number of bytes which should be used to create random id
     * @return a base64 String where "+","/" and "=" chars are replaced with random chars (so that string is url safe)
     */
    private String createHashStr(int numBytes) {
        Random rand = new Random(System.nanoTime());
        byte[] randomBytes = new byte[numBytes];
        rand.nextBytes(randomBytes);
        var rndStr = Base64.getEncoder().encodeToString(randomBytes);
        char[] repl = new char[]{(char) (65 + rand.nextInt(24)), (char) (65 + rand.nextInt(24)), (char) (65 + rand.nextInt(24))};
        return StringUtils.replaceChars(rndStr, "+/=", String.copyValueOf(repl));
    }


}

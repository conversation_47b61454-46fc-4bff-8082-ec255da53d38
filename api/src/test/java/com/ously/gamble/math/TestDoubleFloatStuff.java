package com.ously.gamble.math;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestDoubleFloatStuff {


    @Test
    public void basicMathTests() {

        var vdbl = 6.1d;
        var vbd = new BigDecimal("6.1");

        assertEquals(vdbl, vbd.doubleValue());
        var vflt = 6.1f;
        assertEquals(vflt, vbd.floatValue());
        assertEquals("6.1", "" + vdbl);
        assertEquals("6.1", "" + vflt);
        assertEquals("6.1", vbd.toString());

    }

}

plugins {
    id 'java-library'
}

repositories {
    mavenCentral()
}

dependencies {
    compileOnly("org.springframework.data:spring-data-commons")
    implementation "org.springframework:spring-context"
    implementation "com.querydsl:querydsl-core"
    implementation "com.querydsl:querydsl-collections"
    implementation "com.google.guava:guava:33.2.0-jre"
    implementation "org.apache.commons:commons-lang3"
    implementation "org.apache.commons:commons-collections4:4.4"
    implementation "org.slf4j:slf4j-api"
    implementation "jakarta.servlet:jakarta.servlet-api"
}

test {
    useJUnitPlatform()
}
{"notificationType": "Received", "mail": {"timestamp": "2020-04-30T11:31:17.556Z", "source": "j<PERSON><EMAIL>", "messageId": "i6c81640558lfeesjm307sv8h1ki8obghluetk01", "destination": ["<EMAIL>"], "headersTruncated": false, "headers": [{"name": "Return-Path", "value": "<<EMAIL>>"}, {"name": "Received", "value": "from www90.your-server.de (www90.your-server.de [**************]) by inbound-smtp.us-east-1.amazonaws.com with SMTP id i6c81640558lfeesjm307sv8h1ki8obghluetk01 for <EMAIL>; Thu, 30 Apr 2020 11:31:17 +0000 (UTC)"}, {"name": "X-SES-Spam-Verdict", "value": "PASS"}, {"name": "X-SES-Virus-Verdict", "value": "PASS"}, {"name": "Received-SPF", "value": "none (spfCheck: ************** is neither permitted nor denied by domain of ktb-systeme.de) client-ip=**************; envelope-from=j<PERSON><PERSON>@ktb-systeme.de; helo=www90.your-server.de;"}, {"name": "Authentication-Results", "value": "amazonses.com; spf=none (spfCheck: ************** is neither permitted nor denied by domain of ktb-systeme.de) client-ip=**************; envelope-from=j<PERSON><PERSON>@ktb-systeme.de; helo=www90.your-server.de; dmarc=none header.from=ktb-systeme.de;"}, {"name": "X-SES-RECEIPT", "value": "AEFBQUFBQUFBQUFHUXhQMkNBSnRPMjhuR2dFMHNQZmZySkJFdFZod2l0a3ZEM0o4ZzJDMm9NMi93V3B3VCtiSW5kU29Xclg5V0xHaHlCQUtOejBpZUhlU2RKT0hHbFNTMU9RSnVGQkVRenY0aGU3NFEycDBLL3lCZTRuUFgzNjhjZDI5RCtWOE9NMXV2T0VrR0FrM1lkMWFrVVlENkxoOWYzbHZiOEthbi91K2tCSmFhUDNTU2JOOEs2M3owSlhveGxZUXFtNmpKWHUzaFp1c0VHdDZhbFYyY08rYmlabTczZ0xRbHhoa0laYWI0cnNENG10MmxndW90QVExaVFxbTRYdjE0VGQyclZzVXhGOGpzK0N6Y3Y5NlBxeDlKMi94RmllR3NodmN4ek9JS0JKSUgxZUg5YWc9PQ=="}, {"name": "X-SES-DKIM-SIGNATURE", "value": "a=rsa-sha256; q=dns/txt; b=BdGArmJZBlvvHYJmbMIFwwN1dZUKTP/MaL+nkqq30dh9tIilzgADiTWCZ5HLhTx90VLG3H03ZVQRR8GQmF4TkWnfUdoGlvKOuBq9/XioPXdHT6Cq0ABLePPWf79Y7g1T4erDTkQpWhPogodAcIPuNuqIPnGoVMt+ML2IY5As6ns=; c=relaxed/simple; s=224i4yxa5dv7c2xz3womw6peuasteono; d=amazonses.com; t=1588246278; v=1; bh=XRGV3oCQgnhR+y4KUI9Tk/HAjXoe1FvcUxcuKzHt2Ak=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;"}, {"name": "Received", "value": "from sslproxy02.your-server.de ([************]) by www90.your-server.de with esmtpsa (TLSv1.2:DHE-RSA-AES256-GCM-SHA384:256) (Exim 4.89_1) (envelope-from <<EMAIL>>) id 1jU7PN-0000iO-7<NAME_EMAIL>; Thu, 30 Apr 2020 13:31:13 +0200"}, {"name": "Received", "value": "from [************] (helo=jupiter.fritz.box) by sslproxy02.your-server.de with esmtpsa (TLSv1.2:ECDHE-RSA-AES256-GCM-SHA384:256) (Exim 4.92) (envelope-from <<EMAIL>>) id 1jU7PN-000CTM-47 for <EMAIL>; Thu, 30 Apr 2020 13:31:13 +0200"}, {"name": "From", "value": "<PERSON><PERSON> <j<PERSON><PERSON>@ktb-systeme.de>"}, {"name": "Content-Type", "value": "text/plain; charset=us-ascii"}, {"name": "Content-Transfer-Encoding", "value": "7bit"}, {"name": "Mime-Version", "value": "1.0 (Mac OS X Mail 12.4 \\(3445.104.14\\))"}, {"name": "Subject", "value": "TEST2"}, {"name": "Message-Id", "value": "<<EMAIL>>"}, {"name": "Date", "value": "Thu, 30 Apr 2020 13:31:12 +0200"}, {"name": "To", "value": "<EMAIL>"}, {"name": "X-Mailer", "value": "Apple Mail (2.3445.104.14)"}, {"name": "X-Authenticated-Sender", "value": "j<PERSON><EMAIL>"}, {"name": "X-Virus-Scanned", "value": "Clear (ClamAV 0.102.2/25797/Wed Apr 29 14:06:14 2020)"}], "commonHeaders": {"returnPath": "j<PERSON><EMAIL>", "from": ["<PERSON><PERSON> <j<PERSON><PERSON>@ktb-systeme.de>"], "date": "Thu, 30 Apr 2020 13:31:12 +0200", "to": ["<EMAIL>"], "messageId": "<<EMAIL>>", "subject": "TEST2"}}, "receipt": {"timestamp": "2020-04-30T11:31:17.556Z", "processingTimeMillis": 1270, "recipients": ["<EMAIL>"], "spamVerdict": {"status": "PASS"}, "virusVerdict": {"status": "PASS"}, "spfVerdict": {"status": "GRAY"}, "dkimVerdict": {"status": "GRAY"}, "dmarcVerdict": {"status": "GRAY"}, "action": {"type": "SNS", "topicArn": "arn:aws:sns:us-east-1:666067033667:mail_info_ouslycloud_net", "encoding": "UTF8"}}, "content": "Return-Path: <<EMAIL>>\r\nReceived: from www90.your-server.de (www90.your-server.de [**************])\r\n by inbound-smtp.us-east-1.amazonaws.com with SMTP id i6c81640558lfeesjm307sv8h1ki8obghluetk01\r\n for <EMAIL>;\r\n Thu, 30 Apr 2020 11:31:17 +0000 (UTC)\r\nX-SES-Spam-Verdict: PASS\r\nX-SES-Virus-Verdict: PASS\r\nReceived-SPF: none (spfCheck: ************** is neither permitted nor denied by domain of ktb-systeme.de) client-ip=**************; envelope-from=<EMAIL>; helo=www90.your-server.de;\r\nAuthentication-Results: amazonses.com;\r\n spf=none (spfCheck: ************** is neither permitted nor denied by domain of ktb-systeme.de) client-ip=**************; envelope-from=jkle<PERSON>@ktb-systeme.de; helo=www90.your-server.de;\r\n dmarc=none header.from=ktb-systeme.de;\r\nX-SES-RECEIPT: AEFBQUFBQUFBQUFHUXhQMkNBSnRPMjhuR2dFMHNQZmZySkJFdFZod2l0a3ZEM0o4ZzJDMm9NMi93V3B3VCtiSW5kU29Xclg5V0xHaHlCQUtOejBpZUhlU2RKT0hHbFNTMU9RSnVGQkVRenY0aGU3NFEycDBLL3lCZTRuUFgzNjhjZDI5RCtWOE9NMXV2T0VrR0FrM1lkMWFrVVlENkxoOWYzbHZiOEthbi91K2tCSmFhUDNTU2JOOEs2M3owSlhveGxZUXFtNmpKWHUzaFp1c0VHdDZhbFYyY08rYmlabTczZ0xRbHhoa0laYWI0cnNENG10MmxndW90QVExaVFxbTRYdjE0VGQyclZzVXhGOGpzK0N6Y3Y5NlBxeDlKMi94RmllR3NodmN4ek9JS0JKSUgxZUg5YWc9PQ==\r\nX-SES-DKIM-SIGNATURE: a=rsa-sha256; q=dns/txt; b=BdGArmJZBlvvHYJmbMIFwwN1dZUKTP/MaL+nkqq30dh9tIilzgADiTWCZ5HLhTx90VLG3H03ZVQRR8GQmF4TkWnfUdoGlvKOuBq9/XioPXdHT6Cq0ABLePPWf79Y7g1T4erDTkQpWhPogodAcIPuNuqIPnGoVMt+ML2IY5As6ns=; c=relaxed/simple; s=224i4yxa5dv7c2xz3womw6peuasteono; d=amazonses.com; t=1588246278; v=1; bh=XRGV3oCQgnhR+y4KUI9Tk/HAjXoe1FvcUxcuKzHt2Ak=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;\r\nReceived: from sslproxy02.your-server.de ([************])\r\n\tby www90.your-server.de with esmtpsa (TLSv1.2:DHE-RSA-AES256-GCM-SHA384:256)\r\n\t(Exim 4.89_1)\r\n\t(envelope-from <<EMAIL>>)\r\n\tid 1jU7PN-0000iO-7J\r\n\tfor <EMAIL>; Thu, 30 Apr 2020 13:31:13 +0200\r\nReceived: from [************] (helo=jupiter.fritz.box)\r\n\tby sslproxy02.your-server.de with esmtpsa (TLSv1.2:ECDHE-RSA-AES256-GCM-SHA384:256)\r\n\t(Exim 4.92)\r\n\t(envelope-from <<EMAIL>>)\r\n\tid 1jU7PN-000CTM-47\r\n\tfor <EMAIL>; Thu, 30 Apr 2020 13:31:13 +0200\r\nFrom: Jens Kleemann <<EMAIL>>\r\nContent-Type: text/plain;\r\n\tcharset=us-ascii\r\nContent-Transfer-Encoding: 7bit\r\nMime-Version: 1.0 (Mac OS X Mail 12.4 \\(3445.104.14\\))\r\nSubject: TEST2\r\nMessage-Id: <<EMAIL>>\r\nDate: Thu, 30 Apr 2020 13:31:12 +0200\r\nTo: <EMAIL>\r\nX-Mailer: Apple Mail (2.3445.104.14)\r\nX-Authenticated-Sender: <EMAIL>\r\nX-Virus-Scanned: Clear (ClamAV 0.102.2/25797/Wed Apr 29 14:06:14 2020)\r\n\r\nTEST NEU\r\n"}
package com.ously.gamble.util;

import org.apache.commons.codec.digest.DigestUtils;
import org.junit.jupiter.api.Test;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.assertEquals;

class PlayerNameGenerationTest {


    @Test
    void testNameGeneration() {
        var localId = "23h8OgZaKdf4lhXtCiJ2MdHAmYn1";
        var expUsername = "Player-460267";

        var s = DigestUtils.sha1Hex(localId.getBytes(StandardCharsets.UTF_8));
        var hx = s.substring(0, 16);
        var l = new BigInteger("00" + hx, 16).remainder(BigInteger.valueOf(1000000)).longValue();
        var dispName = "Player-" + l;
        assertEquals(expUsername, dispName);

    }
}

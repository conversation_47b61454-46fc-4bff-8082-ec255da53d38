package com.ously.gamble.services.missions;

class MissionDrainageTest {

//    @Test
//    void reduceAndSumWin() {
//        var md = new MissionDrainage();
//        List<MonitoredTransaction> txs = new ArrayList<>();
//
//        txs.add(new MonitoredTransaction(GamePlatform.WEB, 0L, 3L, TransactionType.DIRECTWIN, 0.0, 5.0, 0L));
//        txs.add(new MonitoredTransaction(GamePlatform.WEB, 0L, 1L, TransactionType.DIRECTWIN, 0.0, 2.0, 0L));
//        txs.add(new MonitoredTransaction(GamePlatform.WEB, 0L, 1L, TransactionType.DIRECTWIN, 0.0, 2.0, 0L));
//        txs.add(new MonitoredTransaction(GamePlatform.WEB, 0L, 2L, TransactionType.DIRECTWIN, 0.0, 2.0, 0L));
//        txs.add(new MonitoredTransaction(GamePlatform.WEB, 0L, 3L, TransactionType.DIRECTWIN, 0.0, 2.0, 0L));
//
//        var longDoubleMap = md.reduceAndSumWin(txs);
//        assertEquals(4.0, longDoubleMap.get(1L), 0.001);
//        assertEquals(2.0, longDoubleMap.get(2L), 0.001);
//        assertEquals(7.0, longDoubleMap.get(3L), 0.001);
//    }
}
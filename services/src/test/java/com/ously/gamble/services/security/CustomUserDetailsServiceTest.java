package com.ously.gamble.services.security;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class CustomUserDetailsServiceTest {

    @Test
    void testPlayerNameCreation() {

        assertEquals("<PERSON>", CustomUserDetailsService.createDisplayname("012345", "<EMAIL>", "<PERSON>" ));
        assertEquals("j<PERSON><PERSON>", CustomUserDetailsService.createDisplayname("012345", "<EMAIL>", null));
        assertEquals("012345", CustomUserDetailsService.createDisplayname("012345", null, null));
    }

}
package com.ously.gamble.services.affiliate;

class NewAffiliateServiceImplTest {

    /*  @Test*/
  /*  void makeSeamlessDailyStats() {

        List<UserCampaignDailyLinkStats> links = new ArrayList<>();
        links.add(new UserCampaignDailyLinkStats(LocalDate.parse("2022-01-31"), 0));
        links.add(new UserCampaignDailyLinkStats(LocalDate.parse("2022-02-02"), 1));
        links.add(new UserCampaignDailyLinkStats(LocalDate.parse("2022-02-12"), 2));
        links.add(new UserCampaignDailyLinkStats(LocalDate.parse("2022-02-22"), 4));
        links.add(new UserCampaignDailyLinkStats(LocalDate.parse("2022-02-25"), 5));

        List<UserCampaignDailyPerfStats> perfs = new ArrayList<>();
        perfs.add(new UserCampaignDailyPerfStats(LocalDate.parse("2022-01-31"), 0,
                0));
        perfs.add(new UserCampaignDailyPerfStats(LocalDate.parse("2022-02-02"),
                200,400));
        perfs.add(new UserCampaignDailyPerfStats(LocalDate.parse("2022-02-12"),
                700,400));
        perfs.add(new UserCampaignDailyPerfStats(LocalDate.parse("2022-02-16"),
                1500,3400));
        perfs.add(new UserCampaignDailyPerfStats(LocalDate.parse("2022-02-23"),
                2000,400));

        List<CampaignFactors> factors = new ArrayList<>();
        factors.add(new CampaignFactors(LocalDate.parse("2022-01-01"),0.01 ,
                0.01,0.01,0.01));
        factors.add(new CampaignFactors(LocalDate.parse("2022-02-15"),0.05,
                0.05,0.05,0.05));

        var from = LocalDate.parse("2022-02-01");
        var to = LocalDate.parse("2022-03-01");
        var userCampaignDailyStats = AffiliateClaimRewardsServiceImpl.makeSeamlessDailyStats(from
                , to, links, perfs,factors);

        assertEquals(28,userCampaignDailyStats.size());
        assertEquals(5, userCampaignDailyStats.get(24).numLinked());
        assertEquals(2000, userCampaignDailyStats.get(22).wagers(),0.0001);
        assertEquals(0.05, userCampaignDailyStats.get(18).rsDeposits(),0.01);
    }
    */
}
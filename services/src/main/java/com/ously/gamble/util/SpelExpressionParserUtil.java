package com.ously.gamble.util;

import com.github.benmanes.caffeine.cache.LoadingCache;
import com.ously.gamble.api.cache.LCacheFactory;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Locale;
import java.util.stream.Collectors;

@Component
public class SpelExpressionParserUtil {
    private final LoadingCache<String, Expression> expressions;
    // Parser can be reused (even for diff. Threads)
    private final ExpressionParser spParser = new SpelExpressionParser();

    // parsed Expressions are also thread safe. So we can cache them
    // LoadingCache should be fine

    public SpelExpressionParserUtil(LCacheFactory<String, Expression> lcf) {
        this.expressions = lcf.registerCacheLoader("SpelExpressionCache", 200, 5, 60 * 60,
                key -> {
                    if (key.charAt(0) == '!') {
                        return createSpelExpressionInternal(key.substring(1));
                    }
                    return createWeekdayExpressionInternal(key);
                });
    }

    /**
     * @param zDt            the zonedDateTime
     * @param spElExpression the expression ("!{'MONDAY','THURSDAY'}.contains(dayOfWeek.name)"). "!" at the start denotes a raw spel expression. Otherwise we
     *                       use the weekday expression and the filter should contain a comma separated list of english weekdays: "MONDAY,TUESDAY,....,SUNDAY"
     * @return expression result as Boolean
     */
    public Boolean matchSpelFilter(ZonedDateTime zDt, String spElExpression) {
        var expression1 = expressions.get(spElExpression);
        return expression1.getValue(zDt, Boolean.class);
    }

    // We should use caching to reuse expressions
    // the getOrCreate should be called inside the cacheLoader

    private Expression createWeekdayExpressionInternal(String weekdays) {
        var terms = Arrays.stream(weekdays.split(",")).map(a -> '\'' + a.trim().toUpperCase(Locale.ROOT) + '\'').collect(Collectors.joining(","));
        terms = terms.replace("'WEEKEND'", "'SATURDAY','SUNDAY'");
        var expStr = '{' + terms + "}.contains(dayOfWeek.name)";
        return spParser.parseExpression(expStr);
    }

    private Expression createSpelExpressionInternal(String spElExpression) {
        return spParser.parseExpression(spElExpression);
    }


}

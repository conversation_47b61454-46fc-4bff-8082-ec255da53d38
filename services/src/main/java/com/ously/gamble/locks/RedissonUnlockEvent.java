package com.ously.gamble.locks;

import org.redisson.api.RLock;

public class RedissonUnlockEvent {

    final String id;
    final Long nid;
    final RLock lock;
    final long lts;

    public RedissonUnlockEvent(String id, Long nid, RLock lk) {
        this.id = id;
        this.nid = nid;
        this.lock = lk;
        this.lts = System.currentTimeMillis();
    }

    public void unlock() {
        lock.forceUnlock();
    }

}

package com.ously.gamble.services.configuration;

import com.ously.gamble.api.clouddb.CloudDbAdapter;
import com.ously.gamble.api.clouddb.CloudDbEntryRequest;
import com.ously.gamble.api.configuration.FrontendConfigurationService;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Component
@ConditionalOnOffloader
public class FrontendConfigFirestoreSyncer {
    private final Logger log = LoggerFactory.getLogger(FrontendConfigFirestoreSyncer.class);

    private final CloudDbAdapter cdbAdapter;
    private final FrontendConfigurationService feConfigService;
    private Map<String, String> lastState = Collections.emptyMap();

    public FrontendConfigFirestoreSyncer(CloudDbAdapter cdbAdapter, FrontendConfigurationService feConfSrv) {
        this.cdbAdapter = cdbAdapter;
        this.feConfigService = feConfSrv;
    }

    @Scheduled(fixedRateString = "300000", initialDelayString = "87000")
    @SchedulerLock(name = "resyncFEConf", lockAtLeastFor = "PT1M")
    public void syncConfig() {
        Map<String, String> frontendConfig = feConfigService.getFrontendConfig();
        if (!frontendConfig.equals(lastState)) {
            Map<String, Object> reqMap = new HashMap<>(frontendConfig);
            cdbAdapter.addEntry(new CloudDbEntryRequest("conf/{ENV}", reqMap));
            log.info("Synced FrontendConfig:{}", frontendConfig);
            this.lastState = frontendConfig;
        }
    }
}

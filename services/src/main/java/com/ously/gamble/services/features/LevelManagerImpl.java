package com.ously.gamble.services.features;

import com.ously.gamble.api.achievements.AchievementType;
import com.ously.gamble.api.achievements.AwardedBonus;
import com.ously.gamble.api.achievements.Reward;
import com.ously.gamble.api.features.*;
import com.ously.gamble.conditions.ConditionalOnNotMonitor;
import com.ously.gamble.config.LevelConfiguration;
import com.ously.gamble.config.PiggyBankConfiguration;
import com.ously.gamble.persistence.model.Wallet;
import com.ously.gamble.persistence.model.session.Jurisdiction;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * see <a href="https://docs.jboss.org/drools/release/5.2.0.Final/drools-expert-docs/html/ch06.html#d0e6792">...</a> for excel syntax
 * <p>
 * Level information is loaded from excel "/rules/Levels.xsl"
 * <p>
 * This Manager also calculates the percentage of the bet which is added to the piggybank.
 */

@Component
@ConditionalOnNotMonitor
public class LevelManagerImpl implements LevelManager, EntityChangeListener<Integer> {
    private final Logger log = LoggerFactory.getLogger(LevelManagerImpl.class);


    private static final Pair<Boolean, LevelUpBean> EMPTY_LUBEAN_PAIR = Pair.with(Boolean.FALSE, new LevelUpBean(0L,
            0L));

    static final BigDecimal BETMULT = BigDecimal.valueOf(100L);

    private List<LevelInfo> rules;

    private final Optional<PiggyBankConfiguration> pbc;
    private final LevelManagerService lvlMgr;
    private final LevelConfiguration eConfig;

    public LevelManagerImpl(Optional<PiggyBankConfiguration> pbc, LevelManagerService lvlMgr,
                            LevelConfiguration eConfig, FeatureConfig fConf) {
        this.pbc = pbc;
        this.lvlMgr = lvlMgr;
        this.eConfig = eConfig;

        rules = lvlMgr.getLevelInfos();
        if (fConf.getJurisdiction() == Jurisdiction.SOC) {
            scaleRules();
        }
        if (fConf.getJurisdiction() != Jurisdiction.SOC || rules.size() > 49) {
            log.info("LevelManager initialised from db with {} levels!", rules.size());
            return;
        }

        if (fConf.getJurisdiction() == Jurisdiction.SOC) {
            try {
                readFromExcel();
            } catch (IOException e) {
                log.error("Error reading rules Excel", e);
            }
            log.info("LevelManager initialised from excel with {} levels!", rules.size());
        }
        lvlMgr.addEntityChangeListener(this);
    }

    private void scaleRules() {
        for (var li : rules) {
            li.setAchievementCoins(eConfig.getScaledLevelUpCoins(li.getAchievementCoins()));
        }

    }

    private void readFromExcel() throws IOException {
        var resourceAsStream = LevelManagerImpl.class.getResourceAsStream("/rules/Levels.xls");
        if (resourceAsStream == null) {
            log.error("No Levels excel found");
            return;
        }

        Workbook workbook = new HSSFWorkbook(resourceAsStream);
        var datatypeSheet = workbook.getSheetAt(0);
        var iterator = datatypeSheet.iterator();

        long xpMin = 0;

        while (iterator.hasNext()) {
            var cRow = iterator.next();
            if (cRow.getRowNum() == 0) {
                continue;
            }
            // iterate over the cells ( level, xp-max, maxBet)
            var level = cRow.getCell(0);
            var xpMax = cRow.getCell(1);
            var mpb = cRow.getCell(2);
            var aBonus = cRow.getCell(3);
            var sAchPrices = cRow.getCell(4);

            if (level == null) {
                break;
            }

            var nLevel = (int) level.getNumericCellValue();
            var nxpMax = ((long) (xpMax.getNumericCellValue())) * 100;
            var nmpb = (long) mpb.getNumericCellValue();
            long nBonus = (int) aBonus.getNumericCellValue();
            nBonus = eConfig.getScaledLevelUpCoins(nBonus);
            String priceDef = null;
            if (sAchPrices != null) {
                priceDef = sAchPrices.getStringCellValue();
            }
            if (nxpMax > 0 && nmpb > 0) {
                var rule = new LevelInfo(nLevel, xpMin, nxpMax, 0, 0, nBonus, priceDef);
                rules.add(rule);
                xpMin = nxpMax;
            } else {
                break;
            }
        }

    }


    @Override
    public AwardedBonus createDevLevelupAchievement(long userId, int level) {
        LevelInfo levelInfo = rules.get(level);
        List<AwardedBonus> res = new ArrayList<>(1);
        addAchievement(levelInfo, res, userId);
        return res.getFirst();
    }

    private static void addAchievement(LevelInfo r, List<AwardedBonus> achievements, Long id) {
        if (r.getLevel() == 0) {
            return;
        }

        List<Reward> rewards;
        if (r.getRewards() != null && !r.getRewards().isEmpty()) {
            rewards = r.getRewards().stream().map(Reward::new).collect(Collectors.toList());
        } else {
            rewards = new ArrayList<>(1);
        }

        var coinReward = findCoinReward(rewards);
        double coinBonus = r.getAchievementCoins();
        if (coinBonus < 5) {
            coinBonus = 10;
        }
        if (coinReward == null) {
            coinReward = new Reward();
            coinReward.setType("S");
            coinReward.setAmount(coinBonus);
            rewards.add(coinReward);
        } else {
            coinReward.setAmount(coinReward.getAmount() + coinBonus);
        }
        // now create a specific LEVEL UP Achievement
        var a = new AwardedBonus();
        a.setDescription(AchievementType.LEVELUP_BONUS.getDescriptionLiteral());
        a.setTitle(AchievementType.LEVELUP_BONUS.getTitleLiteral());
        a.setType(AchievementType.LEVELUP_BONUS);
        a.setVariables("level=" + r.getLevel());
        a.setQualifier(Integer.toString(r.getLevel()+1));
        var pDef = rewards.stream().map(Reward::getStringDefinition).collect(Collectors.joining(","));
        a.setPriceDef(pDef);
        a.setUid(id);
        // now add to list
        achievements.add(a);
    }

    /**
     * Changes the wallet according to the earned XP. Even if no levelup is due, the wallet gets the new XP and the scalar which shows how near the user is to get a new level(%, 0-1)
     *
     * @param w   the wallet
     * @param bet the last bet (already included in wallet)
     * @return Pair of Bool/LevelUpBean
     */
    @Override
    public Pair<Boolean, LevelUpBean> applyLevels(Wallet w, BigDecimal bet, double xpMult) {
        // calculate saveUp amount
        calculateSaveAmount(w, bet);

        var bet2 = bet.multiply(BETMULT).multiply(BigDecimal.valueOf(xpMult));
        // apply XP
        var xpDiff = bet2.longValue();
        if (xpDiff == 0) {
            return EMPTY_LUBEAN_PAIR;
        }

        // calc Level
        long oldLevel = w.getLevel();
        w.setXp(w.getXp() + xpDiff);
        var lub = new LevelUpBean(w.getXp(), w.getGamesPlayed() + 1);
        deriveLevel(lub, (int) oldLevel, w.getId());
        var newLevel = lub.getLevel();
        w.setPercnl(BigDecimal.valueOf(lub.getPercNext()));
        if (newLevel > oldLevel) {
            w.setLevel(newLevel);
            if(lub.getResetXp()>0){
                w.setXp(0);
            }
            return Pair.with(true, lub);
        }
        var b = new LevelUpBean(0, 0);
        b.setLastEarned(xpDiff);
        return Pair.with(false, b);

    }

    @Override
    public List<LevelInfo> getRules() {
        return rules;
    }

    private void calculateSaveAmount(Wallet w, BigDecimal bet) {
        if (pbc.isPresent()) {
            var addSave = bet.multiply(pbc.get().getRate());
            // add and apply max
            var currentSaveup = Objects.requireNonNullElse(w.getSaveup(), BigDecimal.ZERO);
            w.setSaveup(currentSaveup.add(addSave).min(pbc.get().getMaxAmount()));
        }
    }

    private void deriveLevel(LevelUpBean lub, int oldLevel, Long id) {
        // find the entry for the current xp
        // 1. When level is at top, just return!!
        var xp = lub.getXp();
        var spins = lub.getSpins();

        if (oldLevel >= rules.size()) {
            // get last entry
            lub.setLevel(oldLevel);
            lub.setPercNext(0.0);
            lub.setAchievements(Collections.emptyList());
            return;
        }

        // Check if oldLevel is spin rule
        var cL = rules.get(oldLevel);
        if (cL.getEndXp() == 0) {
            if (cL.getEndSpins() > spins) {
                var perc =
                        (double) (spins - cL.getStartSpins()) / (double) (cL.getEndSpins() - cL.getStartSpins());
                lub.setPercNext(perc);
                return;
            }
        }

        // Thats the way to go
        for (var i = oldLevel; i < rules.size(); i++) {
            var r = rules.get(i);

            if (r.getEndXp() == 0) {
                if (spins >= r.getStartSpins()) {
                    if (r.getLevel() > oldLevel) {
                        // is current level ended

                        lub.setLevel(r.getLevel());
                        if (r.getLevel() > 0) {
                            lub.addAchievement(createAchievementsForLevel(r, id));
                        }
                        var nextLevel = rules.get(i + 1);
                        if (nextLevel.getEndXp() > 0) {
                            long xpN = 0;
                            lub.setResetXp(1);
                            xp = xpN;
                            var perc = 0;
                            lub.setPercNext(perc);
                            break;
                        } else {
                            var perc =
                                    (double) (spins - nextLevel.getStartSpins()) / (double) (nextLevel.getEndSpins() - nextLevel.getStartSpins());
                            lub.setPercNext(perc);
                        }

                        if (spins < r.getEndSpins()) {
                            break;
                        }

                    } else if (spins >= r.getEndSpins()) {
                        var perc = (double) (spins - r.getStartSpins()) / (double) (r.getEndSpins() - r.getStartSpins());
                        lub.setPercNext(perc);
                    } else {
                        var perc = (double) (spins - r.getStartSpins()) / (double) (r.getEndSpins() - r.getStartSpins());
                        lub.setPercNext(perc);
                        break;
                    }
                }

            }

            // XP BASED LEVELLING
            if (r.getEndXp() > 0) {
                if (r.getEndXp() > 0 && xp >= r.getStartXp()) {

                    if (r.getLevel() > oldLevel) {
                        lub.setLevel(r.getLevel());
                        lub.addAchievement(createAchievementsForLevel(r, id));
                        var perc = (double) (xp - r.getStartXp()) / (double) (r.getEndXp() - r.getStartXp());
                        lub.setPercNext(perc);
                        if (xp < r.getEndXp()) {
                            break;
                        }

                    } else if (xp >= r.getEndXp()) {
                        var perc = (double) (xp - r.getStartXp()) / (double) (r.getEndXp() - r.getStartXp());
                        lub.setPercNext(perc);
                    } else {
                        var perc = (double) (xp - r.getStartXp()) / (double) (r.getEndXp() - r.getStartXp());
                        lub.setPercNext(perc);
                        break;
                    }
                }else{
                    break;
                }
            }
        }
        if ((lub.getLevel() + 1) >= rules.size()) {
            lub.setPercNext(0.0);
        }
        if(lub.getPercNext()<0){
            lub.setPercNext(0.0);
        }
    }

    @Override
    public AwardedBonus createAchievementsForLevel(LevelInfo r, Long id) {

        if (r.getLevel() == 0) {
            return null;
        }

        List<Reward> rewards;

        if (r.getRewards() != null && !r.getRewards().isEmpty()) {
            rewards = r.getRewards().stream().map(Reward::new).collect(Collectors.toList());
        } else {
            rewards = new ArrayList<>(1);
        }

        var coinReward = findCoinReward(rewards);
        double coinBonus = r.getAchievementCoins();
        if (coinBonus < 5) {
            coinBonus = 10;
        }
        if (coinReward == null) {
            coinReward = new Reward();
            coinReward.setType("S");
            coinReward.setAmount(coinBonus);
            rewards.add(coinReward);

        } else {
            coinReward.setAmount(coinReward.getAmount() + coinBonus);
        }
        // now create a specific LEVEL UP Achievement
        var a = new AwardedBonus();
        int targetLevel = r.getLevel()+1;
        a.setDescription(AchievementType.LEVELUP_BONUS.getDescriptionLiteral());
        a.setTitle(AchievementType.LEVELUP_BONUS.getTitleLiteral());
        a.setType(AchievementType.LEVELUP_BONUS);
        a.setVariables("level=" + targetLevel);
        a.setQualifier(Integer.toString(targetLevel));
        var pDef = rewards.stream().map(Reward::getStringDefinition).collect(Collectors.joining(
                ","));
        a.setPriceDef(pDef);
        a.setUid(id);

        return a;
    }

    private static Reward findCoinReward(List<Reward> rewards) {
        if (rewards == null || rewards.isEmpty()) {
            return null;
        }

        for (var r : rewards) {
            if ("S".equalsIgnoreCase(r.getType())) {
                return r;
            }
        }
        return null;
    }

    @Override
    public void entityChanged(Integer id) {
        log.info("reloading levels");
        rules = lvlMgr.getLevelInfos();
    }
}

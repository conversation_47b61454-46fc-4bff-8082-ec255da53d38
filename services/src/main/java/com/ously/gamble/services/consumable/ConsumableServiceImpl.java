package com.ously.gamble.services.consumable;

import com.github.benmanes.caffeine.cache.LoadingCache;
import com.ously.gamble.api.cache.LCacheFactory;
import com.ously.gamble.api.cache.LCacheLoader;
import com.ously.gamble.api.consumable.ConsumableService;
import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.payload.purchase.CasinoConsumable;
import com.ously.gamble.payload.purchase.ProductInfo;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.model.social.Consumable;
import com.ously.gamble.persistence.model.social.ConsumableType;
import com.ously.gamble.persistence.repository.ConsumableRepository;
import com.ously.gamble.util.SpelExpressionParserUtil;
import com.ously.gamble.util.ZoneOffsetHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@ConditionalOnProperty(prefix = "features", name = "jurisdiction", havingValue = "SOC",
        matchIfMissing = true)
public class ConsumableServiceImpl implements ConsumableService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final ConsumableRepository cRepo;
    private final SpelExpressionParserUtil spUtil;
    private final String assetBaseUrl;
    private final LoadingCache<String, List<ProductInfo>> allConsumables;

    @SuppressWarnings({"rawtypes", "unchecked"})
    public ConsumableServiceImpl(ConsumableRepository cRepo, FeatureConfig fConfig,
                                 SpelExpressionParserUtil spUtil, LCacheFactory lcf) {
        this.cRepo = cRepo;
        this.spUtil = spUtil;
        this.assetBaseUrl = fConfig.getAssetBase() + '/' + fConfig.getStage() + "/assets/";

        allConsumables = lcf.registerCacheLoader("specialoffers", 48L, 8, 5 * 60,
                (LCacheLoader<String, List<ProductInfo>>) this::getAllConsumablesInternal
        );
    }

    private List<ProductInfo> getAllConsumablesInternal(String key) {
        try {
            return cRepo.findAll().stream().filter(a -> isActive(a, key)).map(ProductInfo::new).toList();
        } catch (Exception e) {
            log.error("Error getting consumables:{}", key, e);
        }
        return null;
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductInfo> getAllActiveSpecialoffers(GamePlatform gp, String tz) {
        List<ProductInfo> collect = getAllActiveInternal(gp, tz);
        return collect.stream().filter(a -> a.getCategory() == ConsumableType.SPECIALOFFER).toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductInfo> getAllActive(GamePlatform gp, String tz) {
        return getAllActiveInternal(gp, tz);
    }


    private List<ProductInfo> getAllActiveInternal(GamePlatform gp, String tz) {
        List<ProductInfo> collect;
        try {
            collect = allConsumables.get("ALL" + ':' + tz).stream().map(ProductInfo::new).sorted().toList();
        } catch (Exception e) {
            log.warn("Error getting consumables from cache, falling back to repo:{}", e.getMessage());
            collect = cRepo.findAll().stream().filter(a -> isActive(a, tz)).map(ProductInfo::new).toList();
        }

        collect = filterForPlatform(collect, gp);

        for (var productInfo : collect) {
            productInfo.setBonusActive(false);
            productInfo.setRewardMultiplier(100);
        }
        return collect;
    }


    private static List<ProductInfo> filterForPlatform(List<ProductInfo> collect, GamePlatform gp) {

        var products =
                collect.stream().filter(a -> a.getCategory() != ConsumableType.SPECIALOFFER).collect(Collectors.toList());

        switch (gp) {
            case IOS, ANDROID -> {
                return products;
            }
            default -> {
            }
        }

        // WEB: if we have a special-offer type, then check if the "DEFAULT" type is in the list. If so, then replace with
        // SPECIALOFFER, set "oldPrice" on it, if not, just leave it there but sort it to front!
        // The consumables should have matching title!!
        var specialOffers = collect.stream().filter(a -> a.getCategory() == ConsumableType.SPECIALOFFER).toList();
        for (var special : specialOffers) {
            var hit = products.stream().filter(a -> a.getCategory() != ConsumableType.SPECIALOFFER && a.getTitle().equals(special.getTitle())).findFirst();
            if (hit.isPresent()) {
                var i = products.indexOf(hit.get());
                special.setOldCost(hit.get().getCost());
                products.remove(i);
                products.add(i, special);
            }
        }
        return products;
    }


    @Override
    @Transactional
    public CasinoConsumable getConsumable(Long id) {
        return new CasinoConsumable(cRepo.findById(id).get(), assetBaseUrl);
    }

    @Override
    @Transactional
    public CasinoConsumable updateConsumable(CasinoConsumable cConsumable) {

        try {
            var consumable = cRepo.findById(cConsumable.getId()).get();
            cConsumable.updateModel(consumable);
            cRepo.save(consumable);
            return new CasinoConsumable(consumable, assetBaseUrl);
        } catch (Exception e) {
            log.warn("Error trying to update consumbale with: {}", cConsumable, e);
            return null;
        }
    }

    @Override
    @Transactional
    public CasinoConsumable newConsumable(CasinoConsumable cConsumable) {
        var consumable = new Consumable();
        cConsumable.updateModel(consumable);
        consumable = cRepo.saveAndFlush(consumable);
        return new CasinoConsumable(consumable, assetBaseUrl);
    }

    @Override
    @Transactional
    public void deleteConsumable(CasinoConsumable cConsumable) {
        deleteConsumable(cConsumable.getId());
    }

    @Override
    @Transactional
    public void deleteConsumable(Long id) {
        cRepo.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CasinoConsumable> getConsumables() {
        return cRepo.findAll().stream().map(a -> new CasinoConsumable(a, assetBaseUrl)).toList();
    }

    boolean isActive(Consumable a, String timezone) {
        var ldt = LocalDateTime.now();
        var vF = a.getValidFrom();
        var vT = a.getValidTo();

        if (timezone != null && !"ALL".equals(timezone)) {
            var tzComp = timezone.split(":");
            if (tzComp.length == 2) {

                if (StringUtils.isNotEmpty(a.getFilterExpression())) {
                    var zdtClient = Instant.now().atZone(ZoneOffsetHelper.getZoneOffset(tzComp[1]));
                    var aBoolean = spUtil.matchSpelFilter(zdtClient, a.getFilterExpression());
                    if (!aBoolean) {
                        return false;
                    }
                }
            }
        }

        return (vF == null || !vF.isAfter(ldt)) && (vT == null || !vT.isBefore(ldt));

    }

}

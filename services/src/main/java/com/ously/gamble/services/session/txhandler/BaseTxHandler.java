package com.ously.gamble.services.session.txhandler;

import com.ously.gamble.api.bridge.UserSessionBonusStatus;
import com.ously.gamble.api.session.JurisdictionHandler;
import com.ously.gamble.api.session.TxChanges;
import com.ously.gamble.api.session.exception.BetLimitException;
import com.ously.gamble.api.session.exception.BonusBetLimitException;
import com.ously.gamble.api.session.exception.GameTxException;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class BaseTxHandler {
    static final BigDecimal BD0S4 = BigDecimal.ZERO.setScale(4, RoundingMode.DOWN);
    static final BigDecimal BD5PERC = new BigDecimal("0.05").setScale(4, RoundingMode.DOWN);
    static final TxChanges NOCHANGE = new TxChanges(BD0S4, BD0S4, BD0S4);

    private final JurisdictionHandler jdHandler;

    public BaseTxHandler(JurisdictionHandler jdH) {
        this.jdHandler = jdH;
    }


    /**
     * @param bet         bet
     * @param bBet        bonusBet
     * @param bonusStatus the current users session bonus status
     * @throws GameTxException when maxBet restriction or bonusMaxBet restrictions are violated
     */
    void areBetsInLimit(BigDecimal bet, BigDecimal bBet, UserSessionBonusStatus bonusStatus) throws GameTxException {

        if (bonusStatus == UserSessionBonusStatus.ALLOWED && bBet.signum() > 0 && jdHandler.getBonusMaxBet().compareTo(bBet) < 0) {
            // TODO: only throw when user has not an exemption via tag
            throw new BonusBetLimitException("bonusBet of " + bBet + " is higher than limit " +
                    jdHandler.getBonusMaxBet());
        }
        if (bet.signum() > 0 && jdHandler.getJurisdictionOptions().maxBet().compareTo(bet) < 0) {
            throw new BetLimitException("Bet of " + bet + " is higher than limit " +
                    jdHandler.getJurisdictionOptions().maxBet());
        }
    }

    /**
     * Calculate the bonusWager based on the game rtp.
     *
     * @param bBet the bonus bet
     * @param rtp  the rtp of the game
     * @return the bonusWager amount
     */
    BigDecimal getBonusWager(BigDecimal bBet, float rtp) {

        if (bBet.signum() < 1) {
            return BigDecimal.ZERO;
        }

        // if rtp is less than the jurisdiction bonusWagerRtpMax and >0 then the bonus bet is
        // 100% accounted as bonusWager
        if (rtp < jdHandler.getJurisdictionOptions().bonusWagerRtpMax() && rtp > 0.5f) {
            return bBet;
        }
        // if rtp is either unset or higher than bonus rtp max, then only 5% of the bet is
        // accounted for
        return bBet.multiply(BD5PERC);
    }
}

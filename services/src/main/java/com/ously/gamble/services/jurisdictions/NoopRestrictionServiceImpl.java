package com.ously.gamble.services.jurisdictions;


import com.ously.gamble.api.jurisdiction.CasinoJurisdictionCountry;
import com.ously.gamble.api.jurisdiction.RestrictionResponse;
import com.ously.gamble.api.jurisdiction.RestrictionService;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * Dummy Restriction Service used when restrictions are turned off
 */
@Service
@ConditionalOnProperty(prefix = "restrictions", name = "enabled", havingValue = "false",
                       matchIfMissing = true)
public class NoopRestrictionServiceImpl implements RestrictionService {

    public NoopRestrictionServiceImpl() {
        LoggerFactory.getLogger(NoopRestrictionServiceImpl.class).info("Running without global region restrictions");
    }


    @Override
    public boolean isBlocked(String countryCode) {
        return false;
    }

    @Override
    public List<CasinoJurisdictionCountry> getAllCountries() {
        return Collections.emptyList();
    }

    @Override
    public Optional<CasinoJurisdictionCountry> getCountryForCode(String code) {
        return Optional.empty();
    }

    @Override
    @Transactional
    public Optional<CasinoJurisdictionCountry> updateCountry(CasinoJurisdictionCountry cnt) {
        return Optional.empty();
    }

    @Override
    @Transactional
    public Optional<CasinoJurisdictionCountry> addCountry(CasinoJurisdictionCountry cnt) {
        return Optional.empty();
    }

    @Override
    public Optional<RestrictionResponse> getByIp(String ip) {
        return Optional.empty();
    }
}

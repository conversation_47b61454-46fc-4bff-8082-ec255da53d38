package com.ously.gamble.services.common;

import com.ously.gamble.api.OuslyJurisdictionViolationException;
import com.ously.gamble.api.achievements.AwardedBonus;
import com.ously.gamble.api.achievements.HandleAchAddEvent;
import com.ously.gamble.api.crm.CRMUserEvent;
import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.api.features.LevelUpBean;
import com.ously.gamble.api.features.LevelUpEvent;
import com.ously.gamble.api.monitoring.MonitoredGameTx;
import com.ously.gamble.api.session.*;
import com.ously.gamble.api.session.exception.GameTxException;
import com.ously.gamble.api.user.Perk;
import com.ously.gamble.events.WalletRecacheEvent;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.Wallet;
import com.ously.gamble.persistence.model.session.Jurisdiction;
import com.ously.gamble.services.jurisdictions.SocialJurisdictionHandler;
import com.ously.gamble.services.session.txhandler.StandardTxHandler;
import org.apache.commons.lang3.StringUtils;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Common SessionTransaction baseclass.
 * Contains common handling code for "old" and "new" TransactionService
 */
public class BaseSessionTransactionService extends BaseOuslyService {
    private static final Logger log = LoggerFactory.getLogger(BaseSessionTransactionService.class);
    public static final Pair<Boolean, LevelUpBean> NOLEVELUP_PAIR = Pair.with(false, new LevelUpBean(0, 0));
    private static final BigDecimal BD100 = BigDecimal.valueOf(100);

    private final Map<Jurisdiction, JurisdictionHandler> jdHandler;
    private final boolean socialEnabled;
    private final boolean levellingEnabled;

    private final GameTxHandler gmTxHandler;


    protected BaseSessionTransactionService(FeatureConfig fCfg,
                                            List<JurisdictionHandler> handlers
    ) {
        this.jdHandler = handlers.stream().collect(Collectors.toMap(JurisdictionHandler::getJurisdiction, Function.identity()));
        // Set jurisdiction options
        var jurisdictionHandler = jdHandler.getOrDefault(fCfg.getJurisdiction(), null);
        if (jurisdictionHandler == null) {
            this.socialEnabled = fCfg.isSocialFeaturesEnabled();
            this.levellingEnabled = true;
            this.gmTxHandler = new StandardTxHandler(new SocialJurisdictionHandler());
        } else {
            this.socialEnabled = jurisdictionHandler.isSocial() && fCfg.isSocialFeaturesEnabled();
            this.levellingEnabled = jurisdictionHandler.isLevellingEnabled();
            this.gmTxHandler = new StandardTxHandler(jurisdictionHandler);
        }

        log.info("Transactions are using {} for managing bonus/social/levelling", jurisdictionHandler != null ? jurisdictionHandler.getDescription() : "no handler");
    }


    public void sendLowBalanceEventIfNeeded(long userId, BigDecimal balanceAfter, BigDecimal bet) {
        if (socialEnabled) {
            if (balanceAfter.signum() == 0 || balanceAfter.compareTo(bet) < 1)
                publishEvent(new CRMUserEvent(userId, "LOW_BALANCE", "balanceAfter", balanceAfter, "bet", bet));
        }
    }

    public void sendLowBalanceEvent(long userId, BigDecimal bet) {
        sendLowBalanceEventIfNeeded(userId, BigDecimal.ZERO, bet);
    }


    public TxSplit checkRequest(Wallet w, TxRequest txReq) throws GameTxException {
        return gmTxHandler.calculateSplit(w, txReq);
    }


    public BigDecimal calculateTax(TxRequest req) {
        var jurisdictionHandler = jdHandler.get(req.getJurisdiction());
        return (jurisdictionHandler == null) ? BigDecimal.ZERO : jurisdictionHandler.calculateTax(req);
    }

    public void checkJurisdiction(
            TxRequest req) throws OuslyJurisdictionViolationException {
        var jurisdictionHandler = jdHandler.get(req.getJurisdiction());
        var result = (jurisdictionHandler == null) ? JurisdictionStatus.UNKNOWN_JURISDICTION :
                jurisdictionHandler.isValidForJurisdiction(req);
        if (result != JurisdictionStatus.OK) {
            throw new OuslyJurisdictionViolationException(HttpStatus.BAD_REQUEST, result);
        }
    }

    public boolean isSocialEnabled() {
        return socialEnabled;
    }

    public boolean isLevellingEnabled() {
        return levellingEnabled;
    }

    private static final Pair<Double, Double> PAIR_OF_MULT_1_1 = Pair.with(1.0d, 1.0d);

    protected static Pair<Double, Double> getMultiplicators(String aPerks) {
        if (StringUtils.isAllBlank(aPerks)) {
            return PAIR_OF_MULT_1_1;
        }
        var perks = Perk.parsePerks(aPerks);
        if (perks.isEmpty()) {
            return PAIR_OF_MULT_1_1;
        }
        var winMult = 1.0d;
        var xpMult = 1.0d;
        for (var p : perks) {
            var pMult = p.getMultiplier() / 100.0d;
            switch (p.getType()) {
                case MS -> {
                    if (winMult <= 1.0d) {
                        winMult = pMult;
                    } else {
                        winMult *= pMult;
                    }
                }
                case MX -> {
                    if (xpMult <= 1.0d) {
                        xpMult = pMult;
                    } else {
                        xpMult *= pMult;
                    }
                }
                default -> {
                }
            }
        }
        return Pair.with(winMult, xpMult);
    }

    protected void publishEvent(Object event) {
        super.publishEvent(event);
    }

    protected void recacheWalletUserStats(Wallet w) {
        publishEvent(new WalletRecacheEvent(w));
    }

    /**
     * Deduct bet from the openWager
     *
     * @param req    the tx requested
     * @param wallet the wallet entity
     */
    protected static void applyWagerChanges(TxRequest req, Wallet wallet) {
//        if (req.getJurisdiction() != Jurisdiction.SOC) {
//            if (wallet.getOpenWager().signum() > 0 && req.getBet().signum() > 0) {
//                var nWager = wallet.getOpenWager().subtract(req.getBet());
//                if (nWager.signum() < 1) {
//                    wallet.setOpenWager(BigDecimal.ZERO);
//                } else {
//                    wallet.setOpenWager(nWager);
//                }
//            }
//            if (req.isFreespin()) {
//                wallet.setOpenWager(wallet.getOpenWager().add(req.getWin()));
//            }
//        }
    }

    /**
     * Deduct bet from open wager (both real and bonus)
     *
     * @param req    the txRequest
     * @param split  the split values (real/bonus win/bet)
     * @param wallet the current wallet
     * @return true if the bonus wager reached zero (rollover of bonusBalance to real money balance)
     */
    protected static boolean applyWagerChanges(TxRequest req, TxSplit split, Wallet wallet) {
//        if (req.getJurisdiction() != Jurisdiction.SOC) {
//
//
//            if (!req.isFreespin() && wallet.getOpenWager().signum() > 0 && split.real().bet().signum() > 0) {
//                var nWager = wallet.getOpenWager().subtract(split.real().bet());
//                if (nWager.signum() < 1) {
//                    wallet.setOpenWager(BigDecimal.ZERO);
//                } else {
//                    wallet.setOpenWager(nWager);
//                }
//            }
//        }
        return false;
    }


    protected static BigDecimal addBetWinFigures(TxRequest req, TxSplit split, Wallet wallet, double winmult) {
        return switch (req.getType()) {
            case BET -> addBet(split.real().bet(), wallet);
            case WIN -> addWin(split.real().win(), wallet, winmult);
            case DIRECTWIN -> addBet(split.real().bet(), wallet).add(addWin(split.real().win(), wallet, winmult));
            default -> BigDecimal.ZERO;
        };
    }


    protected static BigDecimal addBetWinFigures(TxRequest req, Wallet wallet, double winmult) {
        return switch (req.getType()) {
            case BET -> addBet(req, wallet);
            case WIN -> addWin(req.getWin(), wallet, winmult);
            case DIRECTWIN -> addBet(req, wallet).add(addWin(req.getWin(), wallet, winmult));

            default -> BigDecimal.ZERO;
        };
    }

    protected static BigDecimal addBet(TxRequest req, Wallet wallet) {
        return addBet(req.getBet(), wallet);
    }

    protected static BigDecimal addBet(BigDecimal bet, Wallet wallet) {
        wallet.setGamesPlayed(wallet.getGamesPlayed() + 1);
        wallet.setSumBet(wallet.getSumBet().add(bet));
        return BigDecimal.ZERO;
    }

    protected static BigDecimal addWin(BigDecimal win, Wallet wallet, double mult) {
        if (win.signum() == 1) {
            wallet.setGamesWon(wallet.getGamesWon() + 1);
            var boost = win;
            if (mult > 1) {
                boost = win.multiply(BigDecimal.valueOf(mult));
            }
            wallet.setSumWin(wallet.getSumWin().add(boost));
            return boost;
        }
        return BigDecimal.ZERO;
    }

    // could be relevant for cash also

    protected LevelUpEvent createUserLevelEvent(Wallet wallet) {
        return new LevelUpEvent(wallet, null);
    }


    protected static TxResponse buildIgnoreResponse(Wallet w,
                                                    SessionRoundHandlingStatus sessionRoundHandlingStatus) {
        var resp = new TxResponse();
        resp.setWin(BigDecimal.ZERO);
        resp.setBet(BigDecimal.ZERO);
        resp.setNewBalance(w.getBalance());
        resp.setType(TransactionType.IGNORE);
        resp.setwVersion(w.getVersion());
        if (sessionRoundHandlingStatus != null) {
            resp.setSrhs(sessionRoundHandlingStatus);
        }
        return resp;
    }

    protected void handleAchievements(List<AwardedBonus> achievements, Long sessionId) {
        if (achievements == null || achievements.isEmpty()) {
            return;
        }
        publishEvent(new HandleAchAddEvent(achievements, sessionId));
    }

    /**
     * publish monitoring bean of transaction
     *
     * @param req      the txRequest
     * @param earnedXp xp for that tx
     */
    protected void publishMonitoredTransaction(TxRequest req, int earnedXp, short betlevel) {
        if (req.getGp() != null && req.getGameId() >= 0) {

//            // Old style for missions and rankings
//            publishEvent(new GameTransactionEvent(req.getUserId(), req.getGp(),
//                    req.getGameId().intValue(), req.getVendorId(), req.getType(), req.getBet().doubleValue(),
//                    req.getWin().doubleValue(), earnedXp, req.getEffRtp(), false));

            // new style for monitoring container
            publishEvent(new MonitoredGameTx(req.getUserId(), req.getGameId().intValue(), req.getVendorId(), req.getGp(), req.getType(),
                    req.getBet().signum() == 0 ? 0L : req.getBet().multiply(BD100).longValue(),
                    req.getWin().signum() == 0 ? 0L : req.getWin().multiply(BD100).longValue(),
                    earnedXp, betlevel, req.isInBonus(), req.getEffRtp()));
        }
    }

}

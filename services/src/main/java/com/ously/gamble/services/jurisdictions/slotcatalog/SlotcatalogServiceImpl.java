package com.ously.gamble.services.jurisdictions.slotcatalog;

import com.ously.gamble.api.games.GameManagementService;
import com.ously.gamble.api.slotcatalog.SCGameInfo;
import com.ously.gamble.api.slotcatalog.SlotcatalogService;
import com.ously.gamble.persistence.model.game.Game;
import com.ously.gamble.persistence.model.game.GameCategory;
import com.ously.gamble.persistence.model.game.GameCategory.CategoryType;
import com.ously.gamble.persistence.model.game.GameInfo;
import com.ously.gamble.persistence.repository.game.GameCategoryRepository;
import com.ously.gamble.persistence.repository.game.GameInfoRepository;
import com.querydsl.core.types.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.stereotype.Indexed;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;


@Indexed
public class SlotcatalogServiceImpl implements SlotcatalogService {

    private final GameCategoryRepository gcRepo;

    private final GameInfoRepository giRepo;

    private final GameManagementService gmMgmt;

    public SlotcatalogServiceImpl(
            GameManagementService gmMgmt,
            GameInfoRepository giRepo, GameCategoryRepository gcRepo) {
        this.gmMgmt = gmMgmt;
        this.giRepo = giRepo;
        this.gcRepo = gcRepo;
    }


    @Override
    @Transactional(readOnly = true)
    public List<SCGameInfo> findAllGameInfosForPredicate(Predicate predicate) {
        return StreamSupport.stream(giRepo.findAll(predicate).spliterator(), false).map(SCGameInfo::new).toList();
    }

    @Override
    @Transactional
    public Game unlinkGameInfo(Long gameId) {
        return unlinkGameInfo(gameId, false);
    }

    @Override
    @Transactional
    public Game unlinkGameInfo(Long gameId, boolean flush) {
        var game = gmMgmt.getByIdUncached(gameId);
        if (game == null) {
            return null;
        }
        // unlink and delete categories
        if (game.getCategories() != null) {
            game.getCategories().clear();
        }
        game.setGameInfo(null);
        game = gmMgmt.saveAndFlush(game);
        return game;
    }


    @Override
    @Transactional
    public SCGameInfo linkGameInfoToGame(Integer gameInfoId, Long gameId) {
        return linkGameInfoToGame(gameInfoId, gameId, false);
    }

    @Override
    @Transactional
    public SCGameInfo linkGameInfoToGame(Integer gameInfoId, Long gameId, boolean flush) {
        var g = unlinkGameInfo(gameId);
        if (g == null) {
            return null;
        }
        if (gameInfoId == null) {
            return null;
        }
        var gameInfo = giRepo.findById(gameInfoId);
        if (gameInfo.isEmpty()) {
            return null;
        }
        var gi = gameInfo.get();

        g.setGameInfo(gi);
        if (flush) {
            g = gmMgmt.saveAndFlush(g);
        } else {
            g = gmMgmt.save(g);
        }
        // now change/rebuild gameCategories
        applyNewCategories(g, gi, flush);

        return new SCGameInfo(gi);
    }

    private void applyNewCategories(Game g, GameInfo gi, boolean flush) {
        // Delete old categories if any
        g.getCategories().clear();
        gmMgmt.save(g, flush);

        // get all Categories

        var catByType = gcRepo.findAllByType(CategoryType.CATEGORY);

        var catMap = catByType.stream().collect
                (Collectors.toMap(GameCategory::getName, Function.identity()));

        var filterByType = gcRepo.findAllByType(CategoryType.FILTER);
        var filterMap = filterByType.stream().collect
                (Collectors.toMap(GameCategory::getName, Function.identity()));

        // Add volatility/type categories
        if (StringUtils.isNotEmpty(gi.getType())) {
            g.getCategories().add(catMap.get(gi.getType()));
        }
        if (StringUtils.isNotEmpty(gi.getVolatility())) {
            g.getCategories().add(catMap.get(gi.getVolatility()));
        }
        // Iterate through the gameInfos Tags
        var gInfo = new SCGameInfo(gi);
        for (var tag : gInfo.getInfo().getTags()) {
            var groupName = StringEscapeUtils.unescapeHtml4(tag.getGroup());
            var category = StringEscapeUtils.unescapeHtml4(tag.getValues());
            if ("Features".equals(groupName)) {
                // simple iterating
                var features = category.split(";");
                for (var feature : features) {
                    var gc = filterMap.get(feature.trim());
                    if (gc != null) {
                        g.getCategories().add(gc);
                    }
                }
            } else {
                // the rest is done as usual
                var categories = category.split(";");
                for (var cat : categories) {
                    var trim = cat.trim();
                    if (trim.isEmpty()) {
                        continue;
                    }
                    var gc = catMap.get(trim);
                    if (gc != null) {
                        g.getCategories().add(gc);
                    }
                }
            }
        }
        gmMgmt.save(g, flush);
    }

    @Transactional
    @Override
    public void rebuildCategoryLinkage() {

        var allLinked1 = gmMgmt.findAllLinked();

        for (var g : allLinked1) {
            linkGameInfoToGame(g.getGameInfo().getId(), g.getId());
        }
    }

    @Override
    public void rebuildCategoryLinkage(Long gameId) {
        var game = gmMgmt.getByIdUncached(gameId);
        if (game != null && game.getGameInfo() != null) {
            linkGameInfoToGame(game.getGameInfo().getId(), gameId);
        }
    }

}

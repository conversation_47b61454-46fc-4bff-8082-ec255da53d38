package com.ously.gamble.services.jurisdictions;

import com.ously.gamble.api.session.JurisdictionOptions;
import com.ously.gamble.persistence.model.session.Jurisdiction;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * Social Gaming jurisdiction handler
 */
@Component
public class SocialJurisdictionHandler extends BaseJurisdictionHandler {

    public SocialJurisdictionHandler() {
        super(Jurisdiction.SOC,
                new JurisdictionOptions(true,
                        BigDecimal.ZERO,
                        true,
                        BigDecimal.valueOf(10_000_000L), false, BigDecimal.ZERO));

    }

}

package com.ously.gamble.services.user;

import com.ously.gamble.api.crm.CRMPlatform;
import com.ously.gamble.api.crm.CRMUserUpdate;
import com.ously.gamble.api.user.UserManagementService;
import com.ously.gamble.persistence.dto.CRMUserDetails;
import com.ously.gamble.persistence.dto.CRMUserRevDetails;
import com.ously.gamble.persistence.repository.WalletRepository;
import com.ously.gamble.persistence.repository.user.UserLoginsRepository;
import com.ously.gamble.services.common.BaseOuslyService;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

public abstract class AbstractUserServiceImpl extends BaseOuslyService {

    final UserManagementService uMgm;
    final WalletRepository walletRepository;
    final UserLoginsRepository ulR;

    public AbstractUserServiceImpl(UserManagementService um,
                                   WalletRepository wRepo,
                                   UserLoginsRepository ulr) {
        this.uMgm = um;
        this.walletRepository = wRepo;
        this.ulR = ulr;
    }


    // CRM Update of a user
    @Transactional
    public CRMUserUpdate updateUserCrm(long userId, boolean sendImmediately) {
            var crmDetail = uMgm.getCRMDetails(userId);
            if (crmDetail != null) {
                CRMUserRevDetails revDetails = null;
                if (crmDetail.getCount_purchases() > 0) {
                    revDetails = uMgm.getCRMRevDetails(userId);
                }
                String country = uMgm.getCountry(userId);

                return updateUserCrm(userId, crmDetail, revDetails, country, sendImmediately);
            }
        return null;
    }

    protected CRMUserUpdate updateUserCrm(long userId,
                                          CRMUserDetails crmDetails,
                                          CRMUserRevDetails revDetails,
                                          String country,
                                          boolean sendImmediately) {
        var crmUUpdt = new CRMUserUpdate(crmDetails, revDetails, country);

        handleCIOUpdate(userId, sendImmediately, crmUUpdt);
        return crmUUpdt;
    }

    private void handleCIOUpdate(long userId, boolean sendImmediately, CRMUserUpdate cioU) {
        try {
            var pf = ulR.getLatestPlatform(userId);
            pf.ifPresent(s -> cioU.setPlatform(CRMPlatform.valueOf(s)));
        } catch (Exception ignored) {
            //
        }

        // send to event handler
        publishEvent(cioU);
        // update user crm date
        uMgm.updateUserCrmTimestamp(Instant.now(), userId);
    }


    public abstract void requestUserEmailVerification(String reqMail, Long id);
}

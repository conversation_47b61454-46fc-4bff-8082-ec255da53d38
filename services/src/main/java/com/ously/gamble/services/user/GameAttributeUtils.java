package com.ously.gamble.services.user;


import com.ously.gamble.api.games.GameList;
import org.apache.commons.lang3.StringUtils;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;


@SuppressWarnings("ALL")
public class GameAttributeUtils {

    private static final Logger log = LoggerFactory.getLogger(GameAttributeUtils.class);

    private GameAttributeUtils() {
    }

    /**
     * parse "a=v1,b=v2,...z=v26" into a Gamelist for mostPlayed (sorted by number of plays per game, desc.)
     *
     * @param playlist the string rep of the game tuples
     * @param b
     * @return lastplayed, mostplayed lists
     */
    static List<GameList> parseLastPlayed(String playlist, boolean includePersonalMostPlayed,
                                          Set<Long> activeIds) {
        List<GameList> lhm = new ArrayList<>();
        log.debug("parseLastPlayed: playlist='{}', includePersonalMostPlayed='{}', activeIds='{}'", playlist, includePersonalMostPlayed, activeIds);
        if (StringUtils.isBlank(playlist)) {
            lhm.add(new GameList("lastplayed", Collections.emptyList()));
            // includePersonalMostPlayed is (currently) always false
            // might be relevant for other platforms in the future
            if (includePersonalMostPlayed) {
                lhm.add(new GameList("pmostplayed", Collections.emptyList()));
            }
            return lhm;
        }
        Map<Long, Long> mostPlayed = new HashMap<>();

        var collect = Arrays.stream(playlist.split(",")).map(s -> {
            var comps = s.split("=");
            var gameId = Long.valueOf(comps[0]);
            mostPlayed.put(gameId, Long.valueOf(comps[1]));
            return gameId;
        }).filter(activeIds::contains).toList();

        lhm.add(new GameList("lastplayed", collect));

        // includePersonalMostPlayed is (currently) always false
        // might be relevant for other platforms in the future
        if (includePersonalMostPlayed) {
            var bvc = new ValueComparator(mostPlayed);
            var mostPlayedSorted = new TreeMap<Long, Long>(bvc);
            mostPlayedSorted.putAll(mostPlayed);
            var longs = mostPlayedSorted.keySet().stream().filter(activeIds::contains).toList();
            lhm.add(new GameList("pmostplayed", longs));
        }
        return lhm;
    }


    static String printFavlist(List<Long> favlist) {
        if (favlist.isEmpty()) {
            return "";
        }
        if (favlist.size() == 1) {
            return favlist.get(0).toString();
        }

        var str = new StringBuilder();
        var last = favlist.size() - 1;
        for (var i = 0; i < favlist.size(); i++) {
            Long aLong = favlist.get(i);
            if (aLong != null) {
                str.append(aLong);
                if (i != last) {
                    str.append(',');
                }
            }
        }
        return str.toString();
    }

    static String printPlaylistTuples(List<Pair<Long, Long>> collect) {
        if (collect.isEmpty()) {
            return "";
        }
        if (collect.size() == 1) {
            return printPair(collect.get(0));
        }

        var str = new StringBuilder();
        var last = collect.size() - 1;
        for (var i = 0; i < collect.size(); i++) {
            var cP = collect.get(i);
            str.append(cP.getValue0()).append('=').append(cP.getValue1());
            if (i != last) {
                str.append(',');
            }
        }
        return str.toString();
    }

    private static String printPair(Pair<Long, Long> objects) {
        return Long.toString(objects.getValue0()) + '=' + Long.toString(objects.getValue1());
    }

    static List<Long> parseLongList(String strOfListOfLongs) {
        if (StringUtils.isBlank(strOfListOfLongs)) {
            return new ArrayList<>();
        }
        return Arrays.stream(strOfListOfLongs.split(",")).filter(a -> !"null".equals(a)).
                map(Long::valueOf).collect(Collectors.toList());
    }

    static class ValueComparator implements Comparator<Long> {
        final Map<Long, Long> base;

        public ValueComparator(Map<Long, Long> base) {
            this.base = base;
        }

        // Note: this comparator imposes orderings that are inconsistent with
        // equals.
        @Override
        public int compare(Long a, Long b) {
            if (base.get(a) >= base.get(b)) {
                return -1;
            }
            return 1;
        }
    }

}

package com.ously.gamble.services.slotfinder;

import java.util.HashSet;
import java.util.Set;

public class WeightedCategory implements Comparable<WeightedCategory> {

    double weight;
    int memberCount;
    final int catId;

    final Set<Long> taggedGameIds = new HashSet<>();

    public WeightedCategory(int catId) {
        this.catId = catId;
    }

    void increaseCount() {
        memberCount++;
    }

    void increaseWeight(int addWeight) {
        weight += addWeight;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        var that = (WeightedCategory) o;

        return catId == that.catId;
    }

    @Override
    public int hashCode() {
        return catId;
    }

    @Override
    public int compareTo(WeightedCategory o) {
        if (catId == o.catId) {
            return 0;
        }
        return (int) ((o.weight - this.weight) * 100);
    }
}

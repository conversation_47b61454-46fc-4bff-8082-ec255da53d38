package com.ously.gamble.services.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;


public abstract class BaseOuslyService {

    @Autowired
    ApplicationEventPublisher eventPublisher;

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * Sending an event into the publisher
     *
     * @param event the event to send
     */
    protected void publishEvent(Object event) {
        if (eventPublisher != null) {
            eventPublisher.publishEvent(event);
        }
    }

    /**
     * A fishy action was detected - could be an attempt of fraud!
     * We sleep a bit to hamper the potential attacker
     */
    public void fraudSleep(String sleepMessage, long sleeptime, Long userId) {
        log.warn("FRAUD-ALERT: due to {} - for User {}", sleepMessage, userId);
        sleepAWhile(500L);
    }

    public void possibleFraud(String msg, Long userid) {
        fraudSleep(msg, 1000L, userid);
    }

    protected void sleepAWhile(long sleeptime) {
        // ignore empty method
    }


}

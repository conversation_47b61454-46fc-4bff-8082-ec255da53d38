package com.ously.gamble.services.configuration;

import com.github.benmanes.caffeine.cache.LoadingCache;
import com.ously.gamble.api.cache.LCacheFactory;
import com.ously.gamble.api.configuration.ConfigPropertyService;
import com.ously.gamble.api.configuration.FrontendConfigurationService;
import com.ously.gamble.persistence.dto.ConfigPropertyDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

@Service
public class FrontendConfigurationServiceImpl implements FrontendConfigurationService {

    private final static Logger log = LoggerFactory.getLogger(FrontendConfigurationServiceImpl.class);

    private final String[] prefixWhitelist = new String[]{
            "slotoptions.",
            "frontend.",
            "purchase."
    };

    private final ConfigPropertyService cfgPropService;
    private final LoadingCache<Long, Map<String, String>> propertyCache;

    public FrontendConfigurationServiceImpl(ConfigPropertyService cfgPropSrv, LCacheFactory<Long, Map<String, String>> lcf) {
        this.cfgPropService = cfgPropSrv;
        propertyCache = lcf.registerCacheLoader("frontendprops", 2, 10, 60 * 5, this::getFrontendConfigInternal);
        log.info("Initializing fe-properties:{}", propertyCache.get(1L));
    }

    private Map<String, String> getFrontendConfigInternal(Long aLong) {
        return cfgPropService.getAllProperties().stream().filter(this::checkForWhitelistProperties).collect(Collectors.toUnmodifiableMap(ConfigPropertyDto::key, ConfigPropertyDto::value));
    }

    private boolean checkForWhitelistProperties(ConfigPropertyDto configPropertyDto) {
        for (var prefix : prefixWhitelist) {
            if (configPropertyDto.key().startsWith(prefix)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Map<String, String> getFrontendConfig() {
        return propertyCache.get(1L);
    }
}

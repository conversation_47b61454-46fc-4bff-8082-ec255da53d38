package com.ously.gamble.services.ranking;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.ously.gamble.api.cache.CachedMap;
import com.ously.gamble.api.cache.LCacheFactory;
import com.ously.gamble.api.cache.LCacheLoader;
import com.ously.gamble.api.leaderboard.Leaderboard;
import com.ously.gamble.api.leaderboard.Leaderboard.LeaderboardType;
import com.ously.gamble.api.leaderboard.LeaderboardService;
import com.ously.gamble.api.leaderboard.Leaderboards;
import com.ously.gamble.conditions.ConditionalOnNotMonitor;
import com.ously.gamble.persistence.repository.session.SessionStatisticsRepository;
import com.ously.gamble.util.CacheItemWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@SuppressWarnings("ALL")
@Service
@ConditionalOnNotMonitor
public class LeaderboardServiceImpl implements LeaderboardService {

    private final SessionStatisticsRepository gssRO;
    private final CachedMap<String, Leaderboards> leaderboardsCachedMap;
    private final ObjectMapper om;
    private final LoadingCache<String, CacheItemWrapper<Leaderboards>> leaderboardsLoadingCache;


    @SuppressWarnings({"rawtypes", "unchecked"})
    @Autowired
    public LeaderboardServiceImpl(ObjectMapper om,
                                  LCacheFactory lcf,
                                  CachedMap<String, Leaderboards> leaderboardsCachedMap,
                                  SessionStatisticsRepository gssRO) {
        this.om = om;
        this.leaderboardsCachedMap = leaderboardsCachedMap;
        this.gssRO = gssRO;
        leaderboardsLoadingCache = lcf.registerCacheLoader("leadboardsLocal", 2, 10, 5 * 60, (LCacheLoader<String, CacheItemWrapper<Leaderboards>>) this::getLeaderboardsInternal);
    }

    @Override
    public CacheItemWrapper<Leaderboards> getLeaderboards() {
        return leaderboardsLoadingCache.get("ALL");
    }

    private CacheItemWrapper<Leaderboards> getLeaderboardsInternal(String key) {
        var all = leaderboardsCachedMap.get("key");
        if (all == null) {
            Map<String, Leaderboard> maxwins = new HashMap<>();
            maxwins.put("7", getLeaderboardMaxWin(7, 50));
            maxwins.put("14", getLeaderboardMaxWin(14, 50));
            maxwins.put("30", getLeaderboardMaxWin(30, 50));
            Map<String, Leaderboard> maxmults = new HashMap<>();
            maxmults.put("7", getLeaderboardMaxMult(7, 50));
            maxmults.put("14", getLeaderboardMaxMult(14, 50));
            maxmults.put("30", getLeaderboardMaxMult(30, 50));
            Map<String, Leaderboard> maxprofits = new HashMap<>();
            maxprofits.put("7", getLeaderboardMaxProfitable(7, 50));
            maxprofits.put("14", getLeaderboardMaxProfitable(14, 50));
            maxprofits.put("30", getLeaderboardMaxProfitable(30, 50));
            all = new Leaderboards(new Integer[]{7, 14, 30}, maxwins, maxmults, maxprofits);
            leaderboardsCachedMap.putFast("ALL", all, 5, TimeUnit.MINUTES);
        }
        try {
            return new CacheItemWrapper<>(all, om.writeValueAsString(all), true);
        } catch (JsonProcessingException e) {
            return new CacheItemWrapper<>(all, null, false);
        }
    }


    private Leaderboard getLeaderboardMaxWin(int days, int limit) {
        return new Leaderboard(LeaderboardType.MAX_WIN, days, gssRO.getTopMaxwinsForDaysWithLimitNew(days, limit));
    }

    private Leaderboard getLeaderboardMaxMult(int days, int limit) {
        return new Leaderboard(LeaderboardType.MULTIPLIER, days, gssRO.getTopMultipliersForDaysWithLimitNew(days, limit));
    }

    private Leaderboard getLeaderboardMaxProfitable(int days, int limit) {
        return new Leaderboard(LeaderboardType.PROFIT, days, gssRO.getTopProfitsForDaysWithLimitNew(days, limit));
    }
}

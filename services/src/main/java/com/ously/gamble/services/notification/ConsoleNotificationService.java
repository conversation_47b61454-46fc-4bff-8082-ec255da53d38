package com.ously.gamble.services.notification;

import com.ously.gamble.api.notification.Message;
import com.ously.gamble.api.notification.NotificationSendService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Component
@Profile("itest")
public class ConsoleNotificationService implements NotificationSendService {
    final Logger log = LoggerFactory.getLogger(ConsoleNotificationService.class);

    @Override public void sendNotification(Message msg) {
        log.info("**** to: user={},{} msg:{}", msg.getuId(), msg.getDestination(), msg.getMsg());
    }

    public void sendNotification(String msg) {
        log.info("**** msg:{}", msg);
    }


}

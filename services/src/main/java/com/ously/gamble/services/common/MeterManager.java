package com.ously.gamble.services.common;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class MeterManager {

    private final MeterRegistry mReg;

    final Map<String, Counter> counters = new ConcurrentHashMap<>();

    public MeterManager(MeterRegistry mReg) {
        this.mReg = mReg;
    }

    public void counter(String name, String... tags) {
        var counter = counters.computeIfAbsent(name, key -> mReg.counter(key, tags));
        counter.increment();
    }


}

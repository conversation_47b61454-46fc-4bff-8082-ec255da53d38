package com.ously.gamble.services.payments;

import com.ously.gamble.api.PurchaseManagementService;
import com.ously.gamble.api.consumable.LevelCoinMultRanges;
import com.ously.gamble.configprops.PurchaseServiceConfig;
import com.ously.gamble.persistence.model.PaymentStatus;
import com.ously.gamble.persistence.model.Purchase;
import com.ously.gamble.persistence.repository.PurchaseRepository;
import jakarta.annotation.PostConstruct;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;

@Service
@ConditionalOnProperty(prefix = "features", name = "jurisdiction", havingValue = "SOC", matchIfMissing = true)
public class PurchaseManagementServiceImpl implements PurchaseManagementService {

    private static final LevelCoinMultRanges ERROR_LEVELCOINMULTRANGE = new LevelCoinMultRanges("0=100");
    private final PurchaseRepository pRepo;
    private final PurchaseServiceConfig psConfig;

    public PurchaseManagementServiceImpl(PurchaseRepository pRepo,
                                         PurchaseServiceConfig pServConfig) {
        this.pRepo = pRepo;
        this.psConfig = pServConfig;
    }

    @PostConstruct
    public void setup(){
        String coinMultSettings = psConfig.getCoinMultSettings();
        System.out.println(coinMultSettings);
    }

    @Override
    public LevelCoinMultRanges getCoinMultRanges() {
        try {
            return new LevelCoinMultRanges(psConfig.getCoinMultSettings());
        } catch (Exception e) {
            return ERROR_LEVELCOINMULTRANGE;
        }
    }

    @Override
    public LevelCoinMultRanges setCoinMultRanges(LevelCoinMultRanges lcmr){
        LevelCoinMultRanges levelCoinMultRanges = lcmr.checkRanges();
        if(levelCoinMultRanges.message()==null){
            psConfig.setCoinMultSettings(levelCoinMultRanges.toString());
        }
        return levelCoinMultRanges;
    }


    @Override
    public int getCoinMultiplierForLevel(int level) {
        return getCoinMultRanges().multForLevel(level);
    }

    @Override
    public Optional<Purchase> findById(Long id) {
        return pRepo.findById(id);
    }

    @Override
    public Optional<Purchase> findByTransactionIdAndUserId(String transactionId, Long userId) {
        return pRepo.findByTransactionIdAndUserId(transactionId, userId);
    }

    @Override
    public Optional<Purchase> findByUserIdAndPsp(Long userId, String psp) {
        return pRepo.findByUserIdAndPsp(userId, psp);
    }

    @Override
    public Optional<Purchase> findByPsp(String psp) {
        return pRepo.findByPsp(psp);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Purchase> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable) {
        return pRepo.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    @Override
    public Page<Purchase> findAllPageableWithinPeriod(LocalDateTime from, LocalDateTime to, Pageable pageable) {
        return pRepo.findAllPageableWithinPeriod(from.toInstant(ZoneOffset.UTC), to.toInstant(ZoneOffset.UTC), pageable);
    }

    @Override
    public List<Purchase> getAllByUserId(Long userId) {
        return pRepo.getAllByUserId(userId);
    }

    @Override
    public List<Purchase> getAllByUserIdOrderByCreatedAtDesc(Long userId) {
        return pRepo.getAllByUserIdOrderByCreatedAtDesc(userId);
    }

    @Override
    public Purchase save(Purchase p) {
        pRepo.save(p);
        if (p.getStatus() == PaymentStatus.SUCCESS || p.getStatus() == PaymentStatus.AUTHORIZED) {
            pRepo.updatePurchaseStatistics(p.getUserId());
        }
        return p;
    }

    @Override
    public Purchase saveAndFlush(Purchase p) {
        pRepo.saveAndFlush(p);
        if (p.getStatus() == PaymentStatus.SUCCESS || p.getStatus() == PaymentStatus.AUTHORIZED) {
            pRepo.updatePurchaseStatistics(p.getUserId());
        }
        return p;
    }

    @Override
    public Optional<Purchase> findByUserIdAndOrderRef(Long id, String orderRef) {
        return pRepo.findByUserIdAndOrderRef(id, orderRef);
    }

}

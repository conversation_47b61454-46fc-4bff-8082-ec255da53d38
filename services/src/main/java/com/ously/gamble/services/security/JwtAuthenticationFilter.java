package com.ously.gamble.services.security;


import com.github.benmanes.caffeine.cache.LoadingCache;
import com.ously.gamble.api.auth.TokenExpireBlockEvent;
import com.ously.gamble.api.cache.CachedMap;
import com.ously.gamble.api.cache.LCacheFactory;
import com.ously.gamble.api.security.UserPrincipal;
import com.ously.gamble.api.user.exclusion.AuthExclusionService;
import com.ously.gamble.api.user.exclusion.UserExclusionStatus;
import com.ously.gamble.config.SlotCatalogConfig;
import com.ously.gamble.config.WebhookConfig;
import com.ously.gamble.locks.RedissonLockProvider;
import com.ously.gamble.services.common.MeterManager;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Indexed;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.security.MessageDigest;
import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.TimeUnit;


/**
 * Using Firebase to check tokens
 */
@Indexed
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final boolean CONTINUE_WHEN_BEARER_FAILS = true;

    @Autowired
    MeterManager mMgr;

    @Autowired
    SlotCatalogConfig scConfig;

    @Autowired
    WebhookConfig whConfig;

    @Autowired
    private CustomUserDetailsService customUserDetailsService;

    @Autowired
    RedissonLockProvider lkProvider;

    @Autowired
    LCacheFactory<TokenCheckCarrier, UsernamePasswordAuthenticationToken> lcf;

    private static final Logger log = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    @Autowired
    private CachedMap<String, UsernamePasswordAuthenticationToken> tokenMap;

    @Autowired
    private CachedMap<Long, String> idToMd5Map;

    private LoadingCache<TokenCheckCarrier, UsernamePasswordAuthenticationToken> tokenBarrier;

    @Autowired(required = false)
    private AuthExclusionService authExclService;

    @PostConstruct
    public void setupCaching() {
        this.tokenBarrier = lcf.registerCacheLoader("tokenBarrier", 25000, 50, 20,
                this::findToken);
    }

    @EventListener(TokenCheckCarrier.class)
    public void clearTokensFromCache(TokenCheckCarrier md5) {
        tokenMap.removeFast(md5.getMd5());
        tokenBarrier.invalidate(md5);
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void invalidateTokensAndAddToBlocklist(TokenExpireBlockEvent md5) {
        if (md5.md5() != null) {
            tokenMap.removeFast(md5.md5());
            tokenBarrier.invalidate(new TokenCheckCarrier("none", md5.md5()));
        } else {
            String rMd5 = idToMd5Map.get(md5.userId(), null);
            if (rMd5 != null) {
                var md5Arr = rMd5.split(",");
                for (String md5S : md5Arr) {
                    tokenMap.removeFast(md5S);
                    tokenBarrier.invalidate(new TokenCheckCarrier("none", md5S));
                }
            }
        }
        customUserDetailsService.resetRefreshTokens(md5.userId());
    }

    public UsernamePasswordAuthenticationToken findToken(TokenCheckCarrier tcc) {
        var upat = tokenMap.get(tcc.getMd5());
        if (upat == null) {
            var principal = (UserPrincipal) customUserDetailsService.loadUserByFBToken(tcc.getToken(), tcc.getMd5());

            if (principal != null) {
                var authentication = new UsernamePasswordAuthenticationToken(principal, null, principal.getAuthorities());

                // invalid jwt
                if (principal.getEpochExpiry() == 0) {
                    log.warn("Setting a 5 min invalid auth into cache to avoid constant rechecks: {}", tcc.getMd5());
                    tokenMap.putFast(tcc.getMd5(), authentication, 5 * 60, TimeUnit.SECONDS);
                    return authentication;
                }

                if (authExclService != null) {
                    Optional<UserExclusionStatus> activeExclusion = authExclService.getActiveExclusion(principal.getId());
                    if (activeExclusion.isPresent()) {
                        UserExclusionStatus userExclusionStatus = activeExclusion.get();
                        log.warn("Excluded, setting a 5 min invalid auth into cache to avoid constant rechecks:{} - {}", userExclusionStatus, tcc.getMd5());
                        authentication.setAuthenticated(false);
                        principal.setEpochExpiry(0L);
                        tokenMap.putFast(tcc.getMd5(), authentication, 5 * 60, TimeUnit.SECONDS);
                        return authentication;
                    }
                }

//                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(tcc.getRequest()));
                var secondsToExpiry = (principal.getEpochExpiry() - Instant.now().getEpochSecond()) + (5 * 60);
                if (secondsToExpiry > 0) {
                    log.trace("putting auth into cache with TTL {}/User {}", secondsToExpiry, principal.getId());
                    tokenMap.putFast(tcc.getMd5(), authentication, secondsToExpiry, TimeUnit.SECONDS);
                    String oldMd5 = idToMd5Map.get(principal.getId(), null);
                    if (oldMd5 == null) {
                        idToMd5Map.putFast(principal.getId(), tcc.md5, 600, TimeUnit.SECONDS);
                    } else {
                        idToMd5Map.putFast(principal.getId(), oldMd5 + "," + tcc.md5, 600, TimeUnit.SECONDS);
                    }
                    return authentication;
                }
                log.debug("Token already expired, remaining seconds {}/User {}", secondsToExpiry, principal.getId());
            } else {
                return null;
            }
        } else {
            return upat;
        }
        return null;
    }

    @Override
    protected void doFilterInternal(@NonNull final HttpServletRequest request,
                                    @NonNull final HttpServletResponse response,
                                    @NonNull final FilterChain filterChain) throws ServletException, IOException {
        try {
            var authHeaderValue = request.getHeader("Authorization");
            log.debug("Auth:'{}'", authHeaderValue);
            if (authHeaderValue != null && !StringUtils.endsWithIgnoreCase(authHeaderValue, "null") && StringUtils.startsWithIgnoreCase(authHeaderValue, "Bearer ")) {
                log.trace("Got bearer '{}'", authHeaderValue);
                var md5Token = getMD5OfToken(authHeaderValue);
                log.trace("Got md5 '{}'", md5Token);

                var oAuth = tokenMap.get(md5Token);
                if (oAuth == null) {
                    oAuth = tokenBarrier.get(new TokenCheckCarrier(authHeaderValue.substring(7), md5Token));
                }
                if (oAuth != null && (((UserPrincipal) oAuth.getPrincipal()).getEpochExpiry() > 0)) {
                    SecurityContextHolder.getContext().setAuthentication(oAuth);
                } else {
                    if (!CONTINUE_WHEN_BEARER_FAILS) {
                        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                        return;
                    }
                }
            } else {
                var header = request.getHeader("X-Api-key");
                if (header != null) {
                    var tkKey = "XA-KEY-" + getMD5OfToken(header);
                    var uu = tokenMap.get(tkKey);
                    if (uu != null) {
                        SecurityContextHolder.getContext().setAuthentication(uu);
                    } else if (header.equals(scConfig.getRemoteApiKey())) {
                        // Allow remote access for Slotcatalog
                        var up = UserPrincipal.getSlotcatalogPrincipal();
                        var aa = new UsernamePasswordAuthenticationToken(up, null, up.getAuthorities());
                        aa.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        SecurityContextHolder.getContext().setAuthentication(aa);
                        tokenMap.putFast(tkKey, aa, 5 * 60, TimeUnit.SECONDS);
                    } else if (whConfig.getXapikey().contains(header)) {
                        // Allow remote access for CRM Webhook
                        var up = UserPrincipal.getWebhookPrincipal();
                        var aa = new UsernamePasswordAuthenticationToken(up, null, up.getAuthorities());
                        aa.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        SecurityContextHolder.getContext().setAuthentication(aa);
                        tokenMap.putFast(tkKey, aa, 5 * 60, TimeUnit.SECONDS);
                    } else {
                        log.error("Wrong X-Api Key sent->{}", request.getRequestURI());
                        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                        return;
                    }
                }
            }
        } catch (Exception ex) {
            if (log.isDebugEnabled()) {
                log.error("Could not set user authentication in security context:", ex);
            } else {
                log.error("Could not set user authentication in security context (use debuglvl to get full trace):{}", ex.getMessage());
            }
            if (!CONTINUE_WHEN_BEARER_FAILS) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return;
            }
        }
        filterChain.doFilter(request, response);
    }

    private static final ThreadLocal<MessageDigest> digestTL =
            ThreadLocal.withInitial(DigestUtils::getMd5Digest);

    private static String getMD5OfToken(String tk) {
        var digest = digestTL.get();
        try {
            return Hex.encodeHexString(digest.digest(tk.getBytes()));
        } catch (Exception e) {
            log.error("Error using md5 digest", e);
            return null;
        } finally {
            digest.reset();
        }
    }

}
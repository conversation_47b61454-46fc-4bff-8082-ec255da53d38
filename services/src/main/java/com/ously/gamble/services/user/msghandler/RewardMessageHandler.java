package com.ously.gamble.services.user.msghandler;

import com.ously.gamble.api.achievements.AchievementService;
import com.ously.gamble.api.achievements.AchievementType;
import com.ously.gamble.api.achievements.AwardedBonus;
import com.ously.gamble.api.user.UserMessageActionResult;
import com.ously.gamble.api.user.UserMessageHandler;
import com.ously.gamble.persistence.model.messages.UserMessage;
import com.ously.gamble.persistence.model.messages.UserMessageType;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Set;

@Service
@ConditionalOnProperty(prefix = "features", name = "messages", havingValue = "true")
@ConditionalOnBean(AchievementService.class)
public class RewardMessageHandler implements UserMessageHandler {


    private final AchievementService achServ;

    public RewardMessageHandler(AchievementService aS) {
        this.achServ = aS;
    }

    @Override
    public Set<UserMessageType> supportedTypes() {
        return Set.of(UserMessageType.REWARD);
    }

    @Override
    public UserMessageActionResult handleMessage(UserMessage msg, String action) {

        var claimQualifier = "MSGREW:" + msg.getQualifier();
        var userId = msg.getUserId();
        var pDef = msg.getContent().variables().getOrDefault("rewards", "");
        var type = AchievementType.MESSAGE;

        if (msg.isRead()) {
            if (!achServ.findAchievement(userId, type, claimQualifier).isEmpty()) {
                return new UserMessageActionResult(action, false);
            }
        }

        var ab = new AwardedBonus();
        ab.setPriceDef(pDef);
        ab.setUid(userId);
        ab.setType(type);
        ab.setTitle(type.getTitleLiteral());
        ab.setDescription(type.getDescriptionLiteral());
        ab.setQualifier(claimQualifier);
        ab.setVariables("");
        if (achServ.addNewBonus(ab, true)) {
            return new UserMessageActionResult(action, true);
        }
        return new UserMessageActionResult(action, false);
    }
}

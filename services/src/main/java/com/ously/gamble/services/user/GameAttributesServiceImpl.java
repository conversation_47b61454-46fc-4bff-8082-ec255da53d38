package com.ously.gamble.services.user;

import com.ously.gamble.api.crm.CRMUserEvent;
import com.ously.gamble.api.games.GameList;
import com.ously.gamble.api.games.GameManagementService;
import com.ously.gamble.api.games.GameRankingService;
import com.ously.gamble.api.games.GameService;
import com.ously.gamble.api.monitoring.MonitoredAction;
import com.ously.gamble.api.monitoring.MonitoredActionType;
import com.ously.gamble.api.user.*;
import com.ously.gamble.api.util.LockProvider;
import com.ously.gamble.conditions.ConditionalOnNotMonitor;
import com.ously.gamble.events.AddUserTxEvent;
import com.ously.gamble.events.UserStatsHistoryFavsTimestampUpdateEvent;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.model.json.UnlockInfo;
import com.ously.gamble.persistence.model.user.UserGameAttributes;
import com.ously.gamble.persistence.model.user.UserTransactionType;
import com.ously.gamble.services.common.BaseOuslyService;
import org.apache.commons.lang3.StringUtils;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@ConditionalOnNotMonitor
public class GameAttributesServiceImpl extends BaseOuslyService implements UserGameAttributesService {

    final Logger log = LoggerFactory.getLogger(GameAttributesServiceImpl.class);


    private final UserGameAttributesManagementService ugaMgmt;

    private final GameManagementService gmMgmt;

    private final GameRankingService gRankSrv;
    private final GameService gameService;

    private final LockProvider lkProvider;
    private final ApplicationEventPublisher eventPublisher;


    public GameAttributesServiceImpl(
            UserGameAttributesManagementService ugaMgmt,
            GameManagementService gmMgmt,
            GameRankingService gRankSrv, GameService gameService,
            LockProvider lkProvider, ApplicationEventPublisher eventPublisher) {

        this.ugaMgmt = ugaMgmt;
        this.gmMgmt = gmMgmt;
        this.gRankSrv = gRankSrv;
        this.gameService = gameService;
        this.lkProvider = lkProvider;

        this.eventPublisher = eventPublisher;
    }


    @Override
    @Transactional
    public void setFavorite(long userId, SetFavoriteRequest request) throws Exception {
        lkProvider.getLockedLockTx("uga", userId);
        // Get (or create) Row
        var uga = getUserGameAtts(userId);
        // now parse  favlist and either remove or add new favorite
        var favlist = GameAttributeUtils.parseLongList(uga.getFavlist());

        var changed = false;
        if (!request.isFavorite()) {
            if (favlist.contains(request.getGameId())) {
                favlist.remove(request.getGameId());
                changed = true;
            }
        } else {
            if (!favlist.contains(request.getGameId())) {
                favlist.add(request.getGameId());
                changed = true;
            }
            while (favlist.size() > UserGameAttributes.MAXIMUM_FAVS) {
                favlist.removeFirst();
                changed = true;
            }
        }
        if (changed) {
            uga.setFavlist(GameAttributeUtils.printFavlist(favlist));
            ugaMgmt.saveUGA(uga);

            eventPublisher.publishEvent(new UserStatsHistoryFavsTimestampUpdateEvent(userId));

            var eventName = request.isFavorite() ? "favorite_set" : "favorite_unset";

            var casinoGame = gameService.getCasinoGame(request.getGameId());
            eventPublisher.publishEvent(new CRMUserEvent(userId, eventName,
                    "game_id", request.getGameId(),
                    // FIXME: only temporary solution. The event should only contain the
                    //  gameId. The name should be queried by CRM by other means.
                    "game_name", casinoGame == null ? "unknown" : casinoGame.getName()));

        }
    }

    private UserGameAttributes getUserGameAtts(long userId) {
        var ugaO = ugaMgmt.getByIdUncached(userId);
        if (ugaO == null) {
            ugaO = new UserGameAttributes();
            ugaO.setUserId(userId);
            ugaO.setFavlist("");
            ugaO.setPlaylist("");
            ugaO.setUnlocks(null);
        }
        return ugaO;
    }

    @Override
    public List<Long> getFavorites(long userId, GamePlatform gp) {
        var uga = ugaMgmt.findById(userId);
        if (uga.isEmpty()) {
            return Collections.emptyList();
        }
        return filterListByPlatform(GameAttributeUtils.parseLongList(uga.get().getFavlist()), gp);
    }

    private List<Long> filterListByPlatform(List<Long> parseLongList, GamePlatform gp) {
        var activeIdForPlatform = gmMgmt.getActiveIdForPlatform(gp);
        return parseLongList.stream().filter(activeIdForPlatform::contains).toList();
    }

    /**
     * Return the most played and last played list. Last played is kept in order of insertion (the last game played is first)
     * the mostPlayed order is by #plays desc!
     *
     * @param userId userId of caller
     */
    @Override
    public List<GameList> getHistory(long userId, GamePlatform gp) {
        var uga = ugaMgmt.findById(userId);
        var gameLists = uga.map(casinoUserGameAttributes ->
                        GameAttributeUtils.parseLastPlayed(casinoUserGameAttributes.getPlaylist(), false, gmMgmt.getActiveIdForPlatform(gp)))
                .orElseGet(() -> new ArrayList<>(1));
        gameLists.add(new GameList("mostplayed", gRankSrv.getCurrentGameRankingsId(gp)));
        return gameLists;
    }

    @Override
    @Transactional
    public void increaseGameCount(long userId, long ngameId) throws Exception {
        lkProvider.getLockedLockTx("uga", userId);
        var uga = getUserGameAtts(userId);
        var playlist = uga.getPlaylist();

        List<Pair<Long, Long>> gameCounts;
        if (StringUtils.isBlank(playlist)) {
            gameCounts = new ArrayList<>(5);
        } else {
            gameCounts = Arrays.stream(playlist.split(",")).map(s -> {
                var comps = s.split("=");
                var gameId = Long.valueOf(comps[0]);
                var count = Long.valueOf(comps[1]);
                return Pair.with(gameId, count);
            }).collect(Collectors.toList());
        }
        // find matching tuple
        long gameCount = getGameCount(ngameId, gameCounts);
        gameCounts.remove(Pair.with(ngameId,gameCount));
        long gameCountIncremented = gameCount + 1;
        gameCounts.addFirst(Pair.with(ngameId, gameCountIncremented));
        while (gameCounts.size() > UserGameAttributes.MAXIMUM_PLAYS) {
            gameCounts.removeLast();
        }
        // now print tuples to str. and update uga
        var playlistStr = GameAttributeUtils.printPlaylistTuples(gameCounts);
        if (playlistStr.length() >= 1200) {
            log.info("Playlist too long:{}", playlistStr);
            gameCounts.removeLast();
            playlistStr = GameAttributeUtils.printPlaylistTuples(gameCounts);
        }
        uga.setPlaylist(playlistStr);
        ugaMgmt.saveUGA(uga);
        Pair<Long, Long> mostPlayed = getMaxPlayed(gameCounts);
        long mostPlayedGameId = mostPlayed.getValue0();
        eventPublisher.publishEvent(new GameCountUpdatedEvent(userId,
                ngameId,
                gameCountIncremented,
                mostPlayedGameId,
                mostPlayed.getValue1(),
                // FIXME: only temporary solution. The event should only contain the
                //  gameId. The name should be queried by CRM by other means.
                gameService.getCasinoGame(mostPlayedGameId).getName()));

        eventPublisher.publishEvent(new UserStatsHistoryFavsTimestampUpdateEvent(userId));
    }

    private static Pair<Long, Long> getMaxPlayed(List<Pair<Long, Long>> gameCounts) {
        return gameCounts.stream().max(Comparator.comparing(Pair::getValue1))
                .orElseThrow(() -> new RuntimeException("gameCounts is empty? Should not happen."));
    }

    @Override
    public CasinoUserUnlockInfo getUnlocks(long userId, GamePlatform gp) {
        var uga = ugaMgmt.findById(userId);
        if (uga.isEmpty()) {
            return new CasinoUserUnlockInfo();
        }
        return uga.get().getUnlocks();
    }


    @Transactional
    @Override
    public void unlockGame(long userId, int gameId) {
        try {
            lkProvider.getLockedLockTx("uga", userId);
            var uga = getUserGameAtts(userId);
            var unlocks = Objects.requireNonNullElseGet(uga.getUnlocks(), GameAttributesServiceImpl::createNewUnlockInfo);
            unlocks = unlocks.addUnlockedGame(gameId);
            uga.setUnlocks(unlocks);
            ugaMgmt.saveUGA(uga);

            // create userTx to
            UserTxRequest req = new UserTxRequest();
            req.setType(UserTransactionType.UNLOCK_GAME);
            req.setUserId(userId);
            req.setStorePrices(false);
            req.setDescription(Integer.toString(gameId));
            req.setTxRef("UNLOCK:" + gameId + ":" + UUID.randomUUID());
            req.setPiggyPossible(false);
            eventPublisher.publishEvent(new AddUserTxEvent(req));

            eventPublisher.publishEvent(new CRMUserEvent(userId, "unlocked_game",
                    "game_id", gameId));
            eventPublisher.publishEvent(new UserStatsHistoryFavsTimestampUpdateEvent(userId));
            eventPublisher.publishEvent(new MonitoredAction(userId, MonitoredActionType.UNLOCKED_GAME, 1));
        } catch (Exception e) {
            log.warn("Error unlocking game {} for user {}", gameId, userId, e);
        }
    }

    @Transactional
    @Override
    public void lockGame(long userId, int gameId) {
        try {
            lkProvider.getLockedLockTx("uga", userId);
            var uga = getUserGameAtts(userId);
            var unlocks = Objects.requireNonNullElseGet(uga.getUnlocks(), GameAttributesServiceImpl::createNewUnlockInfo);
            unlocks = unlocks.removeUnlockedGame(gameId);
            uga.setUnlocks(unlocks);
            ugaMgmt.saveUGA(uga);
            eventPublisher.publishEvent(new UserStatsHistoryFavsTimestampUpdateEvent(userId));
        } catch (Exception e) {
            log.warn("Error locking game {} for user {}", gameId, userId, e);
        }
    }

    private static UnlockInfo createNewUnlockInfo() {
        return new UnlockInfo(null,
                null, null, null);
    }

    @Transactional
    @Override
    public void unlockVendor(long userId, int vendorId) throws Exception {
        lkProvider.getLockedLockTx("uga", userId);
        var uga = getUserGameAtts(userId);
        var unlocks = Objects.requireNonNullElseGet(uga.getUnlocks(), GameAttributesServiceImpl::createNewUnlockInfo);
        unlocks = unlocks.addUnlockedVendor(vendorId);
        uga.setUnlocks(unlocks);
        ugaMgmt.saveUGA(uga);
        eventPublisher.publishEvent(new CRMUserEvent(
                userId, "unlocked_vendor",
                "vendor_id", vendorId));
        eventPublisher.publishEvent(new UserStatsHistoryFavsTimestampUpdateEvent(userId));
    }

    private long getGameCount(Long ngameId, List<Pair<Long, Long>> collect) {
        for (var t : collect) {
            if (t.getValue0().equals(ngameId)) {
                return t.getValue1();
            }
        }
        return 0L;
    }
}
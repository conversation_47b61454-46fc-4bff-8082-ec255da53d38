package com.ously.gamble.api.session;

import com.ously.gamble.persistence.dto.SessionStatDto;
import com.ously.gamble.persistence.dto.SessionTxDto;
import com.ously.gamble.persistence.model.session.SessionStatisticsDao;
import com.ously.gamble.persistence.model.session.SessionStatisticsRD;
import com.ously.gamble.persistence.projections.UserSessionInfo;
import org.springframework.data.domain.Page;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface SessionStatisticService {

    List<SessionTxDto> getTxForUserAndSession(long userId, long sessionId);

    Page<SessionTxDto> getRoundsForUser(long userId, int page, int size);

    /**
     * aggregate a session and optionally replace/save. Before saving/updating the main entry is checked if
     * any changes are in, if the aggregated entry is the same as in the db, we do not delete/insert and just ignore!
     *
     * @param sessionId      sessionId
     * @param userId         userId
     * @param insertOrUpdate when true, the entries are inserted/updated into session_transaction_rd
     * @return List of SessionStatisticsRD entries (one for each rdate)
     */
    List<SessionStatisticsRD> aggregateSession(long sessionId, long userId, boolean insertOrUpdate);

    Optional<SessionStatisticsDao> findSessionStatistics(long sessionId);

    /**
     * @param sessionId sessionId
     * @param userId    userId
     * @return the rows of the aggregated session
     */
    List<SessionStatisticsRD> aggregateSession(long sessionId, long userId);


    /**
     * save (if not in db or the aggregated entry differs from the entry in the db)
     *
     * @param sessionId sessionId
     * @param userId    userId
     * @param rows      the rows returned from aggregate session
     * @return the aggregated statistics for the session
     */
    SessionStatisticsRD saveOrUpdateSessionStatistics(long sessionId, long userId, List<SessionStatisticsRD> rows);

    /**
     * add batches of session_recalc_statistics for a date range.
     *
     * @param fromDate     date from
     * @param toDateIncl   date to
     * @param batchsize    size of batch
     * @param saveOrUpdate if true then differing entries are updated/inserted
     * @return number of sessions batched
     */
    int enqueueSessionsForRecalc(LocalDate fromDate, LocalDate toDateIncl, int batchsize, boolean saveOrUpdate);

    long getUserRoundCount(long userId);

    long getUserSessionCount(long userId);

    long getUserSessionInfoCount(long userId);

    List<UserSessionInfo> getUserSessionInfosPaged(long userId, long offset, long size);

    List<SessionStatDto> getUserSessionDtoPaged(long userId, long offset, long size);
}

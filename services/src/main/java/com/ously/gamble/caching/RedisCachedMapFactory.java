package com.ously.gamble.caching;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.cache.*;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.nustaq.serialization.FSTConfiguration;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.client.codec.IntegerCodec;
import org.redisson.client.codec.LongCodec;
import org.redisson.client.codec.StringCodec;
import org.redisson.codec.CompositeCodec;
import org.redisson.codec.FstCodec;
import org.redisson.codec.FuryCodec;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@SuppressWarnings("rawtypes")
@Component
public class RedisCachedMapFactory<K, V extends Serializable> implements CachedMapFactory<K, V> {
    final Logger log = LoggerFactory.getLogger(RedisCachedMapFactory.class);

    private final RedissonClient client;
    private final Jackson2ObjectMapperBuilder omBuilder;
    private final MeterRegistry registry;

    private final Map<String, CachedMap<K, V>> activeMaps = new ConcurrentHashMap<>();
    private final Map<String, CachedMap<K, V>> activelocalMaps = new ConcurrentHashMap<>();
    private final Map<String, CachedQueue<V>> activeQueues = new ConcurrentHashMap<>();
    private final Map<String, ScoredSet<V>> activeSets = new ConcurrentHashMap<>();
    private final Map<String, com.ously.gamble.api.cache.RedisRMap<K, V>> activeRMaps = new ConcurrentHashMap<>();
    private final Map<String, CachedSet<V>> cachedSets = new ConcurrentHashMap<>();

    private Codec defFst;
    private ObjectMapper rOm;
    private Codec defFury;


    public RedisCachedMapFactory(RedissonClient rclnt, Jackson2ObjectMapperBuilder j2omb,
                                 MeterRegistry mReg) {
        this.client = rclnt;
        this.omBuilder = j2omb;
        this.registry = mReg;
    }

    @PostConstruct
    public void setupCodecs() {

        // FST default optimized
        var fstConfiguration1 = FSTConfiguration.createDefaultConfiguration();
        fstConfiguration1.setPreferSpeed(true);
        fstConfiguration1.setShareReferences(true);

        defFst = new FstCodec(fstConfiguration1);
        // setup jackson codecs
        rOm = omBuilder.findModulesViaServiceLoader(true).build();

        // Fury codecs
        defFury = new FuryCodec();

    }

    @Override
    public RedisRMap<K, V> createRedisMap(String mapName, Class<K> keyType, Class<V> valueType) {
        synchronized (activeRMaps) {

            var rmAp = activeRMaps.get(mapName);
            if (rmAp != null) {
                return rmAp;
            }
            rmAp = new RedisRMapImpl<>(client, mapName, registry, selectCodecForType(keyType, valueType, null));
            activeRMaps.put(mapName, rmAp);
            return rmAp;
        }
    }


    @Override
    public CachedSet<V> createCachedSet(String setName, CodecType codecType, Class<V> valueType,
                                        int ttlInSeconds) {
        synchronized (cachedSets) {
            var set = cachedSets.get(setName);
            if (set == null) {
                set = new RedisCachedSet<>(client, setName, registry, selectCodecForType(null,
                        valueType, CodecType.FST), ttlInSeconds);
                cachedSets.put(setName, set);
            }
            return set;
        }
    }

    @Override
    public CachedQueue<V> createBlockingQueue(String queueName, CodecType codecType,
                                              Class<V> valueType) {
        synchronized (activeQueues) {
            var blockingQueue = activeQueues.get(queueName);
            if (blockingQueue == null) {
                blockingQueue = new RedisBlockingQueue<>(client, queueName, registry, selectCodecForType(null, valueType, codecType));
                activeQueues.put(queueName, blockingQueue);
            }
            return blockingQueue;
        }
    }


    @Override
    public CachedMap<K, V> createLocalCachedMap(String mapname, CodecType codecType,
                                                Class<K> keyType, Class<V> valueType,
                                                int ttlInSeconds) {
        return createWrappedLocalIMap(mapname, codecType, keyType, valueType, ttlInSeconds);
    }

    @Override
    public CachedMap<K, V> createCachedMap(String mapname, CodecType codecType, Class<K> keyType,
                                           Class<V> valueType, int ttlInSeconds) {
        return createWrappedIMap(mapname, codecType, keyType, valueType, ttlInSeconds);
    }

    @Override
    public ScoredSet<V> createScoredSet(String setname, Class<V> valueType) {
        synchronized (activeSets) {
            var set = activeSets.get(setname);
            if (set == null) {
                set = new RedisScoredSet<>(client, setname, registry, selectCodecForType(null, valueType, CodecType.FST));
                activeSets.put(setname, set);
            }
            return set;
        }
    }

    Codec selectCodecForType(Class<K> keyType, Class<V> valueType, CodecType codecType) {

        if (codecType == CodecType.JSON) {
            if (keyType != null) {
                return new TypedJsonJacksonCodec(valueType, keyType, valueType, rOm);
            }
            return new TypedJsonJacksonCodec(valueType, valueType, valueType, rOm);
        }

        var valueCodec = getCodec(valueType, codecType);
        var keyCodec = getCodec(keyType, codecType);

        // queues/sets
        if (keyCodec == null) {
            return valueCodec;
        }

        return new CompositeCodec(keyCodec, valueCodec);

    }

    private Codec getCodec(Class type, CodecType codecType) {
        if (type == null) {
            return null;
        }
        if (type == String.class) {
            return StringCodec.INSTANCE;
        }
        if (type == Integer.class) {
            return IntegerCodec.INSTANCE;
        }
        if (type == Long.class) {
            return LongCodec.INSTANCE;
        }
        if (codecType == CodecType.JSON) {
            return new TypedJsonJacksonCodec(type, rOm);
        }
        if (codecType == CodecType.FURY) {
            return defFury;
        }
        return defFst;
    }

    CachedMap<K, V> createWrappedLocalIMap(String name, CodecType codecType, Class<K> keyType,
                                           Class<V> valueType, int ttlInSeconds) {
        synchronized (activelocalMaps) {
            var cName = "LC" + name;
            var rmAp = activelocalMaps.get(cName);
            if (rmAp != null) {
                return rmAp;
            }
            rmAp = new RedisCachedLocalCacheMap<>(cName, registry, client, ttlInSeconds, selectCodecForType(keyType, valueType, codecType));
            activelocalMaps.put(cName, rmAp);
            return rmAp;
        }
    }

    CachedMap<K, V> createWrappedIMap(String name, CodecType codecType, Class<K> keyType,
                                      Class<V> valueType, int defTTLSeconds) {
        synchronized (activeMaps) {

            var rmAp = activeMaps.get(name);
            if (rmAp != null) {
                return rmAp;
            }
            rmAp = new RedisCachedMap<>(client, name, registry, selectCodecForType(keyType, valueType, codecType), defTTLSeconds);
            activeMaps.put(name, rmAp);
            return rmAp;
        }
    }

    @Override
    public synchronized void clearAllMaps() {
        for (var rm : activeMaps.values()) {
            rm.clear();
        }
        for (var rm : activelocalMaps.values()) {
            rm.clear();
        }

    }

    @Override
    public Map<String, CacheStatus> getCacheMapSizes() {
        Map<String, CacheStatus> result = new ConcurrentHashMap<>();
        for (var e : activeMaps.entrySet()) {
            result.put(e.getKey(), new CacheStatus(e.getKey(), e.getValue()));
        }
        for (var e : activelocalMaps.entrySet()) {
            result.put(e.getKey(), new CacheStatus(e.getKey(), e.getValue()));
        }

        return result;
    }

    @Override
    public synchronized NamedAtomicLong getAtomicCounter(String nName) {
        var redisAtomicLong = new RedisAtomicLong(nName, client);
        try {
            var value = redisAtomicLong.getValue();
            if (value < 100000L) {
                redisAtomicLong.setValue(System.currentTimeMillis() + 1);
            }
        } catch (Exception e) {
            redisAtomicLong.setValue(System.currentTimeMillis() + 1);
        }
        return redisAtomicLong;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void clearSpecificCache(String cacheName, Boolean clearAll, String clearKey) {
        synchronized (activeMaps) {
            List<CachedMap> cachesToClear = new ArrayList<>();
            if (cacheName.endsWith("*")) {
                var selector = cacheName.replace("*", "");
                activeMaps.keySet().stream().filter(a -> a.startsWith(selector)).forEach(a -> cachesToClear.add(activeMaps.get(a)));
                activelocalMaps.keySet().stream().filter(a -> a.startsWith(selector)).forEach(a -> cachesToClear.add(activeMaps.get(a)));
            } else {
                var vCachedMap = activeMaps.get(cacheName);
                if (vCachedMap == null) {
                    vCachedMap = activelocalMaps.get(cacheName);
                }
                if (vCachedMap != null) {
                    cachesToClear.add(vCachedMap);
                }
            }
            for (var map : cachesToClear) {
                if (clearAll) {
                    log.info("Cache {} cleared", map.getCacheName());
                    long size = map.size();
                    long localSize = map.localSize();
                    map.clear();
                    log.info("Cache {} cleared, size was={}, localSize was={}", map.getCacheName(), size, localSize);
                } else {
                    if (!StringUtils.isEmpty(clearKey)) {
                        map.removeFast(clearKey);
                    }
                }
            }
        }
    }


    public void expireEntries() {
        for (CachedMap cm : activelocalMaps.values()) {
            try {
                var entriesRemoved = cm.expireEntries();
                log.debug("Expired {} entries from localCacheMap '{}'", entriesRemoved, cm.getCacheName());
            } catch (Exception e) {
                log.info("Expired all entries from localCacheMap '{}' due to expiry exception", cm.getCacheName());
                cm.clear();
            }
        }
    }

}

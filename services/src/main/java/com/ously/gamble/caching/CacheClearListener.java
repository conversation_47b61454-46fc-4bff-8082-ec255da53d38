package com.ously.gamble.caching;

import com.ously.gamble.api.cache.CacheModificationRequest;
import com.ously.gamble.api.cache.CachedMapFactory;
import com.ously.gamble.api.cache.LCacheFactory;
import jakarta.annotation.PreDestroy;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Component
public class CacheClearListener {

    final Logger log = LoggerFactory.getLogger(CacheClearListener.class);

    private final CachedMapFactory<String, Serializable> cmFact;
    private final LCacheFactory<Object, Object> lcFact2;
    private final RTopic clearCacheTopic;

    public CacheClearListener(RedissonClient rc, CachedMapFactory<String, Serializable> cmFact,
                              LCacheFactory<Object, Object> lcFact2) {
        this.cmFact = cmFact;
        this.lcFact2 = lcFact2;
        clearCacheTopic = rc.getTopic("cacheClearCommand");
        clearCacheTopic.addListener(CacheModificationRequest.class,
                (channel, message) -> clearCaches(message));
    }

    @PreDestroy
    public void reapListener() {
        clearCacheTopic.removeAllListeners();
    }

    private void clearCaches(CacheModificationRequest req) {
        try {
            if ("ALL".equalsIgnoreCase(req.getCacheName())) {
                log.info("All local caches clear was requested via topic");
                lcFact2.clearAllMaps();
                log.info("All local caches cleared as requested via topic");
            } else {
                lcFact2.clearSpecificCache(req.getCacheName(), req.getClearAll(),
                        req.getClearKey());
            }
        } catch (Exception e) {
            log.error("error trying to execute: {}", req, e);
        }
    }

    @EventListener
    public void publishClearCachesRequest(CacheModificationRequest req) {
        log.info("Clearing cache then publishing CacheClearReq: {}", req);
        try {
            clearCachesLocal(req);
        } catch (Exception ignored) {
        }
        log.info("Publishing CacheClearReq: {}", req);
        clearCacheTopic.publish(req);
    }

    private void clearCachesLocal(CacheModificationRequest req) {
        try {
            if ("ALL".equalsIgnoreCase(req.getCacheName())) {
                log.info("All caches cleared was requested via topic");
                cmFact.clearAllMaps();
                lcFact2.clearAllMaps();
                log.info("All caches cleared as requested via topic");
            } else {
                cmFact.clearSpecificCache(req.getCacheName(), req.getClearAll(), (req.getClearKey() != null) ? req.getClearKey().toString() : null);
                lcFact2.clearSpecificCache(req.getCacheName(), req.getClearAll(),
                        req.getClearKey());
            }
        } catch (Exception e) {
            log.error("error trying to execute: {}", req, e);
        }
    }


}

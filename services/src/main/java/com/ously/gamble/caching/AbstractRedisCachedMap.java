package com.ously.gamble.caching;

import com.ously.gamble.api.cache.CachedMap;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;

import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

public abstract class AbstractRedisCachedMap<K, V> implements CachedMap<K, V> {
    protected final Counter putCounter;
    protected final Counter getCounter;
    protected final Counter removeCounter;
    protected final String name;
    protected final RMapCache<K, V> map;
    protected final long defTTLSeconds;

    protected AbstractRedisCachedMap(String name, MeterRegistry registry, RedissonClient rClient,
                                     Codec codec, long defTtlSec) {
        this.name = name;
        this.defTTLSeconds = defTtlSec;
        this.putCounter = registry.counter(name + "put" );
        this.getCounter = registry.counter(name + "get" );
        this.removeCounter = registry.counter(name + "del" );
        map = rClient.getMapCache(name, codec);
    }

    @Override
    public String getCacheName() {
        return name;
    }

    @Override
    public V put(K key, V value) {
        putCounter.increment();
        return map.put(key, value, defTTLSeconds, TimeUnit.SECONDS);
    }

    @Override
    public void putFast(K key, V value) {
        putCounter.increment();
        map.fastPut(key, value, defTTLSeconds, TimeUnit.SECONDS);
    }


    @Override
    public V put(K key, V value, long ttl, TimeUnit ttlUnit) {
        putCounter.increment();
        return map.put(key, value, ttl, ttlUnit);
    }

    @Override
    public void putFast(K key, V value, long ttl, TimeUnit ttlUnit) {
        putCounter.increment();
        map.fastPut(key, value, ttl, ttlUnit);
    }

    @Override
    public V putIfAbsent(K key, V value) {
        putCounter.increment();
        return map.putIfAbsent(key, value, defTTLSeconds, TimeUnit.SECONDS);
    }

    @Override
    public void putIfAbsentFast(K key, V value) {
        putCounter.increment();
        map.fastPutIfAbsent(key, value, defTTLSeconds, TimeUnit.SECONDS);
    }

    @Override
    public V putIfAbsent(K key, V value, long ttl, TimeUnit ttlUnit) {
        putCounter.increment();
        return map.putIfAbsent(key, value, ttl, ttlUnit);
    }

    @Override
    public void putIfAbsentFast(K key, V value, long ttl, TimeUnit ttlUnit) {
        putCounter.increment();
        map.fastPutIfAbsent(key, value, ttl, ttlUnit);
    }

    @Override
    public int size() {
        return map.size();
    }

    @Override
    public int localSize() {
        return 0;
    }

    @Override
    public Set<K> getKeys() {
        return map.keySet();
    }

    @Override
    public V remove(K key) {
        removeCounter.increment();
        return map.remove(key);
    }

    @Override
    @SuppressWarnings("unchecked" )
    public void removeFast(K key) {
        removeCounter.increment();
        try {
            map.fastRemove(key);
        } catch (Exception ignored) {
        }
    }

    @Override
    public boolean contains(K key) {
        return map.containsKey(key);
    }

    @Override
    public void clear() {
        map.clear();
    }

    @Override
    public int expireEntries() {
        return 0;
    }

    @Override
    public V getOrComputeIfAbsent(K key, Function<? super K, ? extends V> mappingFunction) {
        getCounter.increment();
        try {
            return map.computeIfAbsent(key, mappingFunction);
        } catch (Exception e) {
            return null;
        }
    }

}

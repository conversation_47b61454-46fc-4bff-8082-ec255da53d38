package com.ously.gamble.caching;

import com.ously.gamble.api.cache.ScoredSet;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.javatuples.Pair;
import org.redisson.api.BatchOptions;
import org.redisson.api.BatchOptions.ExecutionMode;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RScoredSortedSetAsync;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class RedisScoredSet<V> implements ScoredSet<V> {
    final RScoredSortedSet<V> set;
    final RedissonClient rClient;
    final Codec vCodec;
    final String name;

    protected final Counter changeCounter;
    protected final Counter getCounter;
    protected final Counter getRangeCounter;

    public RedisScoredSet(RedissonClient client, String setName, MeterRegistry registry,
                          Codec valueCodec) {
        this.name = setName;
        rClient = client;
        vCodec = valueCodec;
        set = client.getScoredSortedSet(setName, valueCodec);
        changeCounter = registry.counter("scd-set-" + setName + "-chg");
        getCounter = registry.counter("scd-set-" + setName + "-get");
        getRangeCounter = registry.counter("scd-set-" + setName + "-rng");
    }

    @Override
    public List<ScoredEntry<V>> getTopEntries(int n) {
        getRangeCounter.increment(n);
        return set.entryRangeReversed(0, n - 1).stream().map(a -> new ScoredEntry<>(a.getValue(), a.getScore())).toList();
    }

    @Override
    public List<ScoredEntry<V>> getRankEntries(int from, int to) {
        getRangeCounter.increment((double) to - from);
        return set.entryRangeReversed(from, to).stream().map(a -> new ScoredEntry<>(a.getValue(), a.getScore())).toList();
    }

    @Override
    public int getRank(V key) {
        getCounter.increment();
        var integer = set.revRank(key);
        return Objects.requireNonNullElse(integer, -1);
    }

    @Override
    public double getScore(V key) {
        getCounter.increment();
        if (set.contains(key)) {
            return set.getScore(key);
        } else {
            return 0.0;
        }
    }

    @Override
    public Pair<Double, Integer> getScoreAndRank(
            V key) throws ExecutionException, InterruptedException, TimeoutException {
        getCounter.increment();
        var batch = rClient.createBatch(BatchOptions.defaults().responseTimeout(3000, TimeUnit.SECONDS).retryAttempts(5).executionMode(ExecutionMode.REDIS_READ_ATOMIC));
        RScoredSortedSetAsync<V> bSet = batch.getScoredSortedSet(name, vCodec);
        var scoreAsync = bSet.getScoreAsync(key);
        var integerRFuture = bSet.revRankAsync(key);
        batch.execute();
        return Pair.with(scoreAsync.toCompletableFuture().get(1000, TimeUnit.MILLISECONDS),
                integerRFuture.toCompletableFuture().get(1000, TimeUnit.MILLISECONDS));
    }

    @Override
    public int addScore(Double score, V entry) {
        changeCounter.increment();
        return set.addScoreAndGetRevRank(entry, score);
    }

    @Override
    public void addScoresAsync(Map<V, Double> scores) {
        if (scores.size() > 1) {
            changeCounter.increment(scores.size());
            var batch = rClient.createBatch(BatchOptions.defaults().skipResult().executionMode(ExecutionMode.IN_MEMORY_ATOMIC));
            var bSet = batch.getScoredSortedSet(name, vCodec);
            scores.forEach(bSet::addScoreAsync);
            batch.executeAsync();
        } else {
            changeCounter.increment();
            scores.forEach(set::addScoreAsync);
        }
    }

    @Override
    public void clear() {
        set.clear();
    }

    @Override
    public int size() {
        return set.size();
    }

    @Override
    public void addScores(Map<V, Double> scores) {
        if (scores.size() > 1) {
            changeCounter.increment(scores.size());
            var batch = rClient.createBatch(BatchOptions.defaults().skipResult().executionMode(ExecutionMode.IN_MEMORY_ATOMIC));
            var bSet = batch.getScoredSortedSet(name, vCodec);
            scores.forEach(bSet::addScoreAsync);
            batch.execute();
        } else {
            changeCounter.increment();
            scores.forEach(set::addScoreAsync);
        }
    }

    @Override
    public void removeKey(V key) {
        set.remove(key);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setScore(V key, double value) {
        set.remove(key);
        set.addScore(key, value);
    }

}

package com.ously.gamble.shared.service;

import com.ously.gamble.api.events.GeoIpLookup;
import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.api.geoip.UserEventRouter;
import com.ously.gamble.api.user.HelloRequest;
import com.ously.gamble.api.user.HelloResponse;
import com.ously.gamble.api.user.HelloService;
import com.ously.gamble.api.user.UserManagementService;
import com.ously.gamble.persistence.model.user.UserInfo;
import jakarta.transaction.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;

@Service
public class HelloServiceImpl implements HelloService {

    final Logger log = LoggerFactory.getLogger(this.getClass());

    private final UserManagementService uMgmt;
    private final UserEventRouter geoService;
    private final FeatureConfig fConfig;
    private final GeoIpLookup geoIpService;

    private String gitShortCommit = "unknown";
    private String gitCommitTime = "";

    public HelloServiceImpl(UserManagementService uMgmt, UserEventRouter geoSrv,
                            FeatureConfig fConfig, GeoIpLookup geoIpService) {
        this.uMgmt = uMgmt;
        this.geoService = geoSrv;
        this.fConfig = fConfig;
        this.geoIpService = geoIpService;
        try {
            var props = new Properties();
            props.load(HelloServiceImpl.class.getResourceAsStream("/git.properties"));

            this.gitShortCommit = props.getProperty("git.commit.id.abbrev", "unknown");
            this.gitCommitTime = props.getProperty("git.commit.time", "");
        } catch (Exception e) {
            log.warn("Cannot load git.properties: {}", e.getMessage());
        }

    }

    @Override
    @Transactional
    public ResponseEntity<HelloResponse> hello(HelloRequest hr, long userId, String hst,
                                               Map<String, String> headers) {
        log.debug("Incoming Hello for user {} originating from {} with req:{} and headers:{}", userId, hst, hr, headers);
        geoService.saveLoginEvent(userId, hst, hr.any(), headers);
        var resp = new HelloResponse();
        // default values
        resp.setPreferredLocale("en");
        resp.setSoundVolume(0);
        resp.setStage(fConfig.getStage());
        resp.setBackendVersion(gitShortCommit + '@' + gitCommitTime);
        resp.setId(userId);

        try {
            // try to set country
            var inet = InetAddress.getByName(hst);
            resp.setCountry(geoIpService.getCountry(inet));

            // Try to load userinfo and get set language
            var infoById = uMgmt.getCasinoUserById(userId);
            if (infoById.isPresent()) {
                var langCode = infoById.get().getLangCode();
                if (StringUtils.isAllEmpty(langCode) || langCode.equalsIgnoreCase("XX")) {
                    langCode = massageAndSetLangcode(hr.any(), userId);
                }
                if (StringUtils.isNotEmpty(langCode)) {
                    resp.setPreferredLocale(langCode.trim().toLowerCase(Locale.ROOT));
                }
            }
        } catch (Exception ignored) {
            log.error("Error eval device language code from:{}", hr);
        }
        return ResponseEntity.ok(resp);
    }

    private String massageAndSetLangcode(Map<String, Object> any, long userId) throws Exception {
        String effDevLang = "en";
        if (any.containsKey("deviceLang")) {
            String deviceLang = any.get("deviceLang").toString().toLowerCase(Locale.ROOT);
            effDevLang = switch (deviceLang) {
                case "en", "fr", "it", "es", "pt" -> deviceLang;
                default -> "en";
            };
            // try to save langcode
            UserInfo infoByIdUncached = uMgmt.getInfoByIdUncached(userId);
            if (infoByIdUncached != null) {
                infoByIdUncached.setLangCode(effDevLang);
                uMgmt.saveUserInfoAndFlush(infoByIdUncached);
            }
        }
        return effDevLang;
    }

}

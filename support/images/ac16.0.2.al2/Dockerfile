FROM amazoncorretto:16.0.2-al2

RUN jlink --no-header-files --no-man-pages --compress=2 --strip-java-debug-attributes --add-modules  jdk.crypto.ec,jdk.jcmd,java.sql.rowset,jdk.management.agent,jdk.management,jdk.unsupported,java.base,java.logging,java.sql,java.naming,java.management,java.instrument,java.desktop,java.security.jgss,jdk.charsets --output /usr/lib/jvm/spring-boot-runtime

FROM amazonlinux:2
COPY --from=0 /usr/lib/jvm/spring-boot-runtime /usr/lib/jvm/spring-boot-runtime
RUN ln -s /usr/lib/jvm/spring-boot-runtime/bin/java /usr/bin/java \
    && yum install -y fontconfig \
    && yum clean all \
    && rm -rf /var/cache/yum

package com.ously.gamble.router.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.bridge.RouterTransportService;
import com.ously.gamble.bridge.pragmatic.PragmaticConfiguration;
import com.ously.gamble.bridge.pragmatic.PragmaticController;
import com.ously.gamble.bridge.pragmatic.PragmaticService;
import com.ously.gamble.bridge.pragmatic.PragmaticServiceRouterImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PragmaticRouterConfiguration {

    @Bean
    PragmaticService pragmaticService(RouterTransportService rTransportService) {
        return new PragmaticServiceRouterImpl(rTransportService);
    }

    @Bean
    PragmaticConfiguration pragmaticConfiguration() {
        return new PragmaticConfiguration();
    }

    @Bean
    PragmaticController pragmaticController(RouterTransportService rTransportService, ObjectMapper om) {
        return new PragmaticController(pragmaticConfiguration(), pragmaticService(rTransportService), om);
    }

}

<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="run backend with swagger libs" type="GradleRunConfiguration" factoryName="Gradle" activateToolWindowBeforeRun="false">
    <ExternalSystemSettings>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value="-x test -Pswagger=on -Dspring.profiles.active=dev" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list>
          <option value="clean" />
          <option value="backend:bootRun" />
        </list>
      </option>
      <option name="vmOptions" value="-Xmx1024m " />
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>true</DebugAllEnabled>
    <method v="2" />
  </configuration>
</component>
<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="gamble [clean build no test]" type="GradleRunConfiguration" factoryName="Gradle" activateToolWindowBeforeRun="false">
    <ExternalSystemSettings>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value="-x test --warning-mode all --stacktrace" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list>
          <option value="clean" />
          <option value="build" />
        </list>
      </option>
      <option name="vmOptions" value="-Xms2048M -Xmx4096M" />
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>false</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>false</DebugAllEnabled>
    <ForceTestExec>false</ForceTestExec>
    <method v="2" />
  </configuration>
</component>
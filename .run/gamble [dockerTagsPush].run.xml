<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="gamble [dockerTagsPush]" type="GradleRunConfiguration" factoryName="Gradle" activateToolWindowBeforeRun="false">
    <selectedOptions>
      <option name="environmentVariables" visible="false" />
    </selectedOptions>
    <ExternalSystemSettings>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value="dockerTagsPush" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list />
      </option>
      <option name="vmOptions" />
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>false</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>false</DebugAllEnabled>
    <method v="2" />
  </configuration>
</component>
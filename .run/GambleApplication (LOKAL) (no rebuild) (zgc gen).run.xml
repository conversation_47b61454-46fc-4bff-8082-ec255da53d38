<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="GambleApplication (LOKAL) (no rebuild) (zgc gen)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" folderName="Spinarena" activateToolWindowBeforeRun="false">
    <option name="ACTIVE_PROFILES" value="dev" />
    <option name="ALTERNATIVE_JRE_PATH" value="corretto-21" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <envs>
      <env name="SPRINGFOX_DOCUMENTATION_AUTO-STARTUP" value="false" />
    </envs>
    <module name="gamble.backend.main" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.ously.gamble.BackendApplication" />
    <option name="VM_PARAMETERS" value="-Dspring.config.location=file:backend/src/main/resources/ @backend/src/main/resources/java-21-args -Xms786m -Xmx786m -XX:+UseZGC -XX:+ZGenerational -XX:+UseCompressedOops  -XX:+UseStringDeduplication -Xss256k" />
    <extension name="com.egoshard.intellij.k8s-runtime-config-plugin">
      <option name="ENABLED" value="false" />
      <entries />
    </extension>
    <extension name="software.aws.toolkits.jetbrains.core.execution.JavaAwsConnectionExtension">
      <option name="credential" />
      <option name="region" />
      <option name="useCurrentConnection" value="false" />
    </extension>
    <RunnerSettings RunnerId="Profile ">
      <option name="myExternalizedOptions" value="additional-options2=onexit\=snapshot&#10;startup=0" />
    </RunnerSettings>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>
package com.ously.gamble.configurations;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.auth.JwtManager;
import com.ously.gamble.api.bridge.BridgeTools;
import com.ously.gamble.api.bridge.VendorBridge;
import com.ously.gamble.api.cache.CachedMap;
import com.ously.gamble.api.cache.LCacheFactory;
import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.api.features.LevelManager;
import com.ously.gamble.api.features.LevelManagerService;
import com.ously.gamble.api.gamemanager.GameManagerService;
import com.ously.gamble.api.games.*;
import com.ously.gamble.api.jackpots.JackpotService;
import com.ously.gamble.api.session.JurisdictionHandler;
import com.ously.gamble.api.session.NewTransactionService;
import com.ously.gamble.api.session.TransactionService;
import com.ously.gamble.api.slotcatalog.SlotcatalogService;
import com.ously.gamble.api.statistics.FTDEntry;
import com.ously.gamble.api.statistics.SessionStatsDay;
import com.ously.gamble.api.statistics.StatCacheKeyFromTo;
import com.ously.gamble.api.statistics.StatisticsService;
import com.ously.gamble.api.user.UserAdminService;
import com.ously.gamble.api.user.UserDistribution;
import com.ously.gamble.api.user.UserManagementService;
import com.ously.gamble.api.util.LockProvider;
import com.ously.gamble.api.vendor.VendorManagementService;
import com.ously.gamble.persistence.dto.RevRolling30Days;
import com.ously.gamble.persistence.repository.ColdTransactionRepository;
import com.ously.gamble.persistence.repository.TransactionRepository;
import com.ously.gamble.persistence.repository.WalletRepository;
import com.ously.gamble.persistence.repository.game.GameCategoryRepository;
import com.ously.gamble.persistence.repository.game.GameInfoRepository;
import com.ously.gamble.persistence.repository.game.GamePromotionRepository;
import com.ously.gamble.persistence.repository.game.GameViewRepository;
import com.ously.gamble.persistence.repository.session.SessionRoundRepository;
import com.ously.gamble.persistence.repository.session.SessionStatisticsRepository;
import com.ously.gamble.persistence.repository.session.SessionTransactionRepository;
import com.ously.gamble.persistence.repository.statistics.GameSessionStatisticsAdminRepository;
import com.ously.gamble.persistence.repository.statistics.PurchaseStatisticsRepository;
import com.ously.gamble.persistence.repository.statistics.UserStatisticsRepository;
import com.ously.gamble.persistence.repository.user.UserLoginsRepository;
import com.ously.gamble.persistence.repository.user.UserMailRepository;
import com.ously.gamble.persistence.repository.user.UserViewRepository;
import com.ously.gamble.services.common.TransactionServiceImpl;
import com.ously.gamble.services.game.GameAdminServiceImpl;
import com.ously.gamble.services.jurisdictions.slotcatalog.SlotcatalogServiceImpl;
import com.ously.gamble.services.session.NewTransactionServiceImpl;
import com.ously.gamble.services.statistics.StatisticsServiceImpl;
import com.ously.gamble.services.user.UserAdminServiceImpl;
import com.ously.gamble.util.CacheItemWrapper;
import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

import java.util.List;
import java.util.Optional;

@Configuration(proxyBeanMethods = false)
public class AdminToolsConfiguration {

    @Bean
    public UserAdminService userAdminServiceImpl(UserManagementService um, WalletRepository wRepo,
                                                 LockProvider lkp,
                                                 ActiveGameManagementService gRepo,
                                                 UserStatisticsRepository usro,
                                                 UserLoginsRepository ulr, UserMailRepository umRep,
                                                 UserViewRepository uvRepo,
                                                 JwtManager jwtManager,
                                                 GameSessionStatisticsAdminRepository gssAdmR,
                                                 Optional<LevelManagerService> lvlMgt,
                                                 LCacheFactory<StatCacheKeyFromTo, List<UserDistribution>> lcFact4
    ) {

        return new UserAdminServiceImpl(um, wRepo, lkp, gRepo,
                usro, ulr, umRep, uvRepo, jwtManager, gssAdmR, lvlMgt.orElse(null), lcFact4);
    }


    @Bean
    public SlotcatalogService slotcatalogService(GameManagementService gmMgmt,
                                                 GameInfoRepository giRepo,
                                                 GameCategoryRepository gcRepo
    ) {
        return new SlotcatalogServiceImpl(gmMgmt, giRepo, gcRepo);
    }

    @Bean
    public GameAdminService gameAdminService(GameCategoryRepository gcR,
                                             GamePromotionRepository gpR, GameManagementService gmM,
                                             FeatureConfig fConfig,
                                             Optional<SlotcatalogService> scS,
                                             VendorManagementService vRepo,
                                             GameInfoRepository giRepo, VendorBridge vBr,
                                             GameManagerService gmMgr,
                                             PlatformTransactionManager txm,
                                             GameViewRepository gvRepo, GameTagService gtService,
                                             @Autowired(required = false) JackpotService jpService) {
        return new GameAdminServiceImpl(gcR, gpR, gmM, fConfig, scS.orElse(null), vRepo, giRepo, vBr,
                gmMgr, txm, gvRepo, gtService, jpService);
    }

    @Bean
    public StatisticsService statisticsService(PurchaseStatisticsRepository pRepo,
                                               UserAdminService uMgmt,
                                               UserLoginsRepository ulRepo, BridgeTools bt,
                                               SessionStatisticsRepository roGss,
                                               UserStatisticsRepository usr,
                                               GameSessionStatisticsAdminRepository gssAdmRep,
                                               UserViewRepository uvRep,
                                               LCacheFactory<StatCacheKeyFromTo, CacheItemWrapper<List<GameRtp>>> lcFact,
                                               LCacheFactory<StatCacheKeyFromTo, List<FTDEntry>> lcFact2,
                                               LCacheFactory<StatCacheKeyFromTo, List<SessionStatsDay>> lcFact4,
                                               LCacheFactory<String, List<RevRolling30Days>> lcFact5
            , ObjectMapper om) {
        return new StatisticsServiceImpl(lcFact, lcFact2, lcFact4, lcFact5, gssAdmRep, pRepo, uMgmt, ulRepo, bt, roGss, usr, uvRep, om);
    }



    @Bean
    public TransactionService transactionService(WalletRepository walletRepository,
                                                 TransactionRepository txRepository,
                                                 LevelManager lm,
                                                 ColdTransactionRepository coldTxRepo,
                                                 FeatureConfig fCfg,
                                                 List<JurisdictionHandler> handlers) {
        return new TransactionServiceImpl(walletRepository, txRepository, lm,
                coldTxRepo, fCfg, handlers);
    }

    @Bean
    public NewTransactionService newTransactionService(WalletRepository walletRepository,
                                                       SessionTransactionRepository txRepository,
                                                       LevelManager lm,
                                                       SessionRoundRepository srRepo,
                                                       List<JurisdictionHandler> handlers,
                                                       EntityManager em,
                                                       CachedMap<Long, String> openRoundsCachedMap,
                                                       FeatureConfig fCfg) {
        return new NewTransactionServiceImpl(walletRepository,
                txRepository, lm, srRepo, handlers, em, openRoundsCachedMap, fCfg);
    }
}

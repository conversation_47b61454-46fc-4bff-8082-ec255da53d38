# Use "cpp/fatass" on localhost:3306 using db "cpp" for direct connection to db
# localhost:8080 for appserver in docker, 8081 for locally started appserver

version: '3.7'
name: stake77
services:

  dbstake77:
    image: mysql:8.0.31
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    ports:
      - "3311:3306"
    environment:
      MYSQL_ROOT_PASSWORD: fatass
      MYSQL_DATABASE: ebdb
      MYSQL_USER: cpp
      MYSQL_PASSWORD: fatass
    networks:
      - backendstake77


#
#  redis-master:
#    image: 'bitnami/redis:latest'
#    ports:
#      - '6385:6379'
#    environment:
#      - REDIS_REPLICATION_MODE=master
#      - REDIS_PASSWORD=fatass69
#    networks:
#      - backend
#
#  redis-replica1:
#    image: 'bitnami/redis:latest'
#    ports:
#      - '6386:6379'
#    depends_on:
#      - redis-master
#    environment:
#      - REDIS_REPLICATION_MODE=slave
#      - REDIS_MASTER_HOST=redis-master
#      - REDIS_MASTER_PORT_NUMBER=6379
#      - REDIS_MASTER_PASSWORD=fatass69
#      - REDIS_PASSWORD=fatass69
#    networks:
#      - backend
#
#  redis-replica2:
#    image: 'bitnami/redis:latest'
#    ports:
#      - '6387:6379'
#    depends_on:
#      - redis-master
#    environment:
#      - REDIS_REPLICATION_MODE=slave
#      - REDIS_MASTER_HOST=redis-master
#      - REDIS_MASTER_PORT_NUMBER=6379
#      - REDIS_MASTER_PASSWORD=fatass69
#      - REDIS_PASSWORD=fatass69
#    networks:
#      - backend
#



  redisstake77:
    image: bitnami/redis:6.2.3-debian-10-r0
    restart: always
    ports:
      - "6383:6379"
    environment:
      REDIS_PASSWORD: fatass69
    networks:
      - backendstake77



  rabbitstake77:
    image: rabbitmq:3.8-management-alpine
    restart: always
    ports:
      - "5676:5672"
      - "15676:15672"
    networks:
      - backendstake77



networks:
  backendstake77:

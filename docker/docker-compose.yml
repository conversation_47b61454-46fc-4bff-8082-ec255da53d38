# Use "cpp/fatass" on localhost:3306 using db "cpp" for direct connection to db
# localhost:8080 for appserver in docker, 8081 for locally started appserver

version: '3.7'
name: heatz
services:

  dbheatz:
    image: mysql:8.0.31
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    ports:
      - "3310:3306"
    environment:
      MYSQL_ROOT_PASSWORD: fatass
      MYSQL_DATABASE: ebdb
      MYSQL_USER: cpp
      MYSQL_PASSWORD: fatass
    networks:
      - backendheatz


#
#  redis-master:
#    image: 'bitnami/redis:latest'
#    ports:
#      - '6385:6379'
#    environment:
#      - REDIS_REPLICATION_MODE=master
#      - REDIS_PASSWORD=fatass69
#    networks:
#      - backend
#
#  redis-replica1:
#    image: 'bitnami/redis:latest'
#    ports:
#      - '6386:6379'
#    depends_on:
#      - redis-master
#    environment:
#      - REDIS_REPLICATION_MODE=slave
#      - REDIS_MASTER_HOST=redis-master
#      - REDIS_MASTER_PORT_NUMBER=6379
#      - REDIS_MASTER_PASSWORD=fatass69
#      - REDIS_PASSWORD=fatass69
#    networks:
#      - backend
#
#  redis-replica2:
#    image: 'bitnami/redis:latest'
#    ports:
#      - '6387:6379'
#    depends_on:
#      - redis-master
#    environment:
#      - REDIS_REPLICATION_MODE=slave
#      - REDIS_MASTER_HOST=redis-master
#      - REDIS_MASTER_PORT_NUMBER=6379
#      - REDIS_MASTER_PASSWORD=fatass69
#      - REDIS_PASSWORD=fatass69
#    networks:
#      - backend
#



  redisheatz:
    image: bitnami/redis:6.2.3-debian-10-r0
    restart: always
    ports:
      - "6381:6379"
    environment:
      REDIS_PASSWORD: fatass69
    networks:
      - backendheatz



  rabbitheatz:
    image: rabbitmq:3.8-management-alpine
    restart: always
    ports:
      - "5675:5672"
      - "15675:15672"
    networks:
      - backendheatz



networks:
  backendheatz:

# Use "cpp/fatass" on localhost:3306 using db "cpp" for direct connection to db
# localhost:8080 for appserver in docker, 8081 for locally started appserver

version: '3.7'
name: ously-social
services:
  db:
    image: mysql:8.0.31
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: fatass
      MYSQL_DATABASE: cpp
      MYSQL_USER: cpp
      MYSQL_PASSWORD: fatass
    networks:
      - backend

  dbgm:
    image: mysql:8.0.31
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    ports:
      - "3309:3306"
    environment:
      MYSQL_ROOT_PASSWORD: fatass
      MYSQL_DATABASE: ebdb
      MYSQL_USER: cpp
      MYSQL_PASSWORD: fatass
    networks:
      - backend

  pulsar:
    image: apachepulsar/pulsar
    ports:
      - "8080:8080"
      - "6650:6650"
    expose:
      - 8080
      - 6650
    environment:
      - PULSAR_MEM=" -Xms512m -Xmx512m -XX:MaxDirectMemorySize=1g"
    command: >
      /bin/bash -c
      "bin/apply-config-from-env.py conf/standalone.conf
      && bin/pulsar standalone"
    networks:
      - backend
#
#  redis-master:
#    image: 'bitnami/redis:latest'
#    ports:
#      - '6385:6379'
#    environment:
#      - REDIS_REPLICATION_MODE=master
#      - REDIS_PASSWORD=fatass69
#    networks:
#      - backend
#
#  redis-replica1:
#    image: 'bitnami/redis:latest'
#    ports:
#      - '6386:6379'
#    depends_on:
#      - redis-master
#    environment:
#      - REDIS_REPLICATION_MODE=slave
#      - REDIS_MASTER_HOST=redis-master
#      - REDIS_MASTER_PORT_NUMBER=6379
#      - REDIS_MASTER_PASSWORD=fatass69
#      - REDIS_PASSWORD=fatass69
#    networks:
#      - backend
#
#  redis-replica2:
#    image: 'bitnami/redis:latest'
#    ports:
#      - '6387:6379'
#    depends_on:
#      - redis-master
#    environment:
#      - REDIS_REPLICATION_MODE=slave
#      - REDIS_MASTER_HOST=redis-master
#      - REDIS_MASTER_PORT_NUMBER=6379
#      - REDIS_MASTER_PASSWORD=fatass69
#      - REDIS_PASSWORD=fatass69
#    networks:
#      - backend



  redis:
    image: bitnami/redis:7.4.2-debian-12-r0
    restart: always
    ports:
      - "6379:6379"
    environment:
      REDIS_PASSWORD: fatass69
    networks:
      - backend

  rabbitmq:
    image: rabbitmq:3.8-management-alpine
    restart: always
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - backend

#  hazelcast-cluster:
#    image: hazelcast/hazelcast:5.0.3
#    volumes:
#      - type: bind
#        source: ./hazelcast
#        target: /opt/hazelcast/config_ext
#    restart: always
#    environment:
#      JAVA_OPTS: "-Dhazelcast.config=/opt/hazelcast/config_ext/hazelcast.yaml"
#    scale: 3
#    networks:
#      - hazelcast
#
#  hazelcast-management:
#    image: hazelcast/management-center:5.1.2
#    environment:
#      MC_DEFAULT_CLUSTER: local
#      MC_DEFAULT_CLUSTER_MEMBERS: docker_hazelcast-cluster_1,docker_hazelcast-cluster_2,docker_hazelcast-cluster_3
#      MC_ADMIN_USER: admin
#      MC_ADMIN_PASSWORD: xSec!!99
#    ports:
#      - "7995:8080"
#    networks:
#      - hazelcast

networks:
  backend:
#  hazelcast:
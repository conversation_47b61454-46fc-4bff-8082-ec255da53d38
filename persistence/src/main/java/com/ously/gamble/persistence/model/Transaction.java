package com.ously.gamble.persistence.model;

import com.ously.gamble.persistence.model.audit.CreationDateAudit;
import jakarta.persistence.*;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * for all providers, each call which potentially changes the waller (bet/deposit/win) is reflected here. I add attributes only when i need them in dependent
 * code - so while the game/provider/... information is needed sometimes - it will be added as soon as i implement the feature or the export to the crm.
 * This entity is WORM, and should be the center of any optimisation later - together with the game/user cache.
 * <p>
 * Bet and win/deposits differ by the sign of the amount, that can change or i will add the enum type. I can deduct the type (bet,win,deposit) by the other attributes for now.
 */
@Entity
@Table(name = "transactions",
        indexes = {
                @Index(columnList = "created_at", name = "created_at_idx"),
                @Index(columnList = "session_id", name = "session_id_idx"),
        }, uniqueConstraints = {
        @UniqueConstraint(columnNames = {
                "user_id", "vendor_name", "ext_tx_numid"
        })
})
public class Transaction extends CreationDateAudit {
    private static final BigDecimal BD0S4 = BigDecimal.ZERO.setScale(4, RoundingMode.DOWN);
    private static final BigDecimal BD0S2 = BigDecimal.ZERO.setScale(2, RoundingMode.DOWN);
    @Id
    @GeneratedValue(generator = "pooledTx")
    @GenericGenerator(name = "pooledTx",  type = org.hibernate.id.enhanced.TableGenerator.class,
            parameters = {
                    @Parameter(name = "table_name", value = "custom_sequences"),
                    @Parameter(name = "value_column_name",
                            value = "sequence_next_hi_value"),
                    @Parameter(name = "prefer_entity_table_as_segment_value",
                            value = "true"),
                    @Parameter(name = "optimizer", value = "pooled-lo"),
                    @Parameter(name = "initial_value", value = "100000"),
                    @Parameter(name = "increment_size", value = "1000"),
                    @Parameter(name = "segment_value", value = "transactions")
            })
    Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "user_id")
    Long userId;

    @Column(name = "game_id")
    Long gameId;

    @Column(name = "bet")
    BigDecimal bet = BD0S4;

    @Column(name = "win")
    BigDecimal win = BD0S4;

    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    TransactionType type;

    @Column(name = "round_ref", length = 100)
    String roundReference;

    @Column(name = "session_id")
    Long sessionId;

    @Column(name = "ext_tx_numid", length = 100)
    String externalOrigTxId;

    @Column(name = "vendor_name", length = 50)
    String vendor;

    @Column(name = "ext_tx_id", length = 100)
    String externalTransactionRef;

    @Column(name = "balance_after")
    BigDecimal balanceAfter = BD0S4;

    @Column(name = "level_after", nullable = false)
    int level;

    @Column(name = "percnl_after")
    BigDecimal percnl = BD0S2;

    @Column(name = "earned_xp")
    long earnedXp;

    @Column(name = "cancelled")
    boolean cancelled;

    @Column(name = "boost")
    BigDecimal boost = BD0S4;

    public BigDecimal getBoost() {
        return boost;
    }

    public void setBoost(BigDecimal boost) {
        if (boost == null || boost.signum() == 0) {
            this.boost = BD0S4;
        } else if (boost.scale() != 4) {
            this.boost = boost.setScale(4, RoundingMode.DOWN);
        } else {
            this.boost = boost;
        }
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public BigDecimal getPercnl() {
        return percnl;
    }

    public void setPercnl(BigDecimal percnl) {
        if ((percnl == null || percnl.signum() == 0)) {
            this.percnl = BD0S2;
        } else if (percnl.scale() != 2) {
            this.percnl = percnl.setScale(2, RoundingMode.DOWN);
        } else {
            this.percnl = percnl;
        }
    }

    public long getEarnedXp() {
        return earnedXp;
    }

    public void setEarnedXp(long earnedXp) {
        this.earnedXp = earnedXp;
    }

    public String getExternalOrigTxId() {
        return externalOrigTxId;
    }

    public void setExternalOrigTxId(String externalOrigTxId) {
        this.externalOrigTxId = externalOrigTxId;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public BigDecimal getBalanceAfter() {
        return balanceAfter;
    }

    public void setBalanceAfter(BigDecimal balanceAfter) {
        if (balanceAfter == null || balanceAfter.signum() == 0) {
            this.balanceAfter = BD0S4;
        } else if (balanceAfter.scale() != 4) {
            this.balanceAfter = balanceAfter.setScale(4, RoundingMode.DOWN);
        } else {
            this.balanceAfter = balanceAfter;
        }
    }

    public String getExternalTransactionRef() {
        return externalTransactionRef;
    }

    public void setExternalTransactionRef(String externalTransactionRef) {
        this.externalTransactionRef = externalTransactionRef;
    }

    public TransactionType getType() {
        return type;
    }

    public void setType(TransactionType type) {
        this.type = type;
    }

    public BigDecimal getBet() {
        return bet;
    }

    public void setBet(BigDecimal bet) {
        if (bet == null || bet.signum() == 0) {
            this.bet = BD0S4;
        } else if (bet.scale() != 4) {
            this.bet = bet.setScale(4, RoundingMode.DOWN);
        } else {
            this.bet = bet;
        }
    }

    public BigDecimal getWin() {
        return win;
    }

    public void setWin(BigDecimal win) {
        if (win == null || win.signum() == 0) {
            this.win = BD0S4;
        } else if (win.scale() != 4) {
            this.win = win.setScale(4, RoundingMode.DOWN);
        } else {
            this.win = win;
        }
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }

    public String getRoundReference() {
        return roundReference;
    }

    public void setRoundReference(String roundReference) {
        this.roundReference = roundReference;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public boolean isCancelled() {
        return cancelled;
    }

    public void setCancelled(boolean cancelled) {
        this.cancelled = cancelled;
    }
}

package com.ously.gamble.persistence.repository.statistics;

import com.ously.gamble.persistence.dto.UserBalanceStatsDto;
import com.ously.gamble.persistence.model.user.User;
import com.ously.gamble.persistence.projections.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;

@Repository
@ConditionalOnBean(name = "statisticsConfiguration")
public interface UserStatisticsRepository extends JpaRepository<User, Long> {

    @Query(nativeQuery = true, value = """
            select rdate,
                   min(LEAST(endB, startB))    as minBal,
                   max(GREATEST(startB, endB)) as maxBal,
                   sum(depos)                  as depos,
                   sum(pouts)                  as pouts
            from (select DATE(gs.start_at)                            as rdate,
                         s.start_balance                             as startB,
                         (s.start_balance + (gs.sum_win - gs.sum_bet)/100) as endB,
                         0                                            as depos,
                         0                                            as pouts
                  from session_statistics gs
                           join session s on gs.session_id = s.session_id
                  where s.user_id = ?1
                    and (gs.sum_win > 0 or gs.sum_bet > 0)
                    and s.start_balance > 0
                    and gs.start_at > DATE_SUB(current_timestamp, INTERVAL ?2 DAY)
                  union all
                  select DATE(created_at)                               as rdate,
                         balance_after + withdraw - credit              as startB,
                         balance_after                                  as endB,
                         IF(type IN ('DEPOSIT', 'PURCHASE'), credit, 0) as depos,
                         IF(type = 'PAYOUT_RESERVE', withdraw, 0)       as pouts
                  from user_transaction
                  where user_id = ?1
                    and cancel_id = 0
                    and created_at > DATE_SUB(current_timestamp, INTERVAL ?2 DAY)) bal
            group by rdate;
            """)
    List<BalanceStatsPJ> getBalanceStats(long userId, int maxDays);

    @Query(nativeQuery = true, value = """
             select rdate,
                   sum(saldo) as saldo,
                   sum(bets) as bets,
                   sum(wins) as wins,
                   sum(levelup) as levelup,
                   sum(wheelspin) as wheelspin,
                   sum(loyalty) as loyalty,
                   sum(bonuscode)as bonuscode,
                   sum(other) as other,
                   sum(doubleup)as doubleup,
                   sum(mission) as mission,
                   sum(purchase) as purchase
             from (
             select
                DATE(created_at) as rdate,
                sum(credit-withdraw) as saldo,
                0 as wins,
                0 as bets,
                sum(if(type IN ('LEVELUP'),credit,0)) as levelup,
                sum(if(type IN ('WHEELSPIN'),credit,0)) as wheelspin,
                sum(if(type IN ('LOYALTY'),credit,0)) as loyalty,
                sum(if(type IN ('BONUSCODE'),credit,0)) as bonuscode,
                sum(if(type IN ('ACHIEVMENT'),credit,0)) as other,
                sum(if(type IN ('DOUBLEUP'),credit-withdraw,0)) as doubleup,
                sum(if(type IN ('MISSION'),credit,0)) as mission,
                sum(if(type IN ('PURCHASE'),credit,0)) as purchase
             from user_transaction ut
             where created_at>= ?1 and created_at <= ?2
             group by DATE(created_at)
             union all
             select rdate, sum(wins-bets) as saldo, sum(wins) as wins,sum(bets) as bets,
                   0 as levelup,0 as wheelspin,0 as loyalty,0 as bonuscode,0 as other,0 as doubleup,0 as mission, 0 as purchase
             from user_stats_daily
             where user_stats_daily.rdate>= ?1 and user_stats_daily.rdate <= ?2
             group by rdate) xx group by rdate
            """)
    List<WalletChangeEntryPJ> getWalletChangeBreakdown(ZonedDateTime from, ZonedDateTime to);

    @Query(nativeQuery = true, value = """
            SELECT distinct `u`.`id`
            FROM users u join user_logins ul on u.id = ul.user_id
            WHERE
                ul.created_at BETWEEN DATE_SUB(CURRENT_TIMESTAMP, INTERVAL ?1 HOUR) AND DATE_SUB(CURRENT_TIMESTAMP, INTERVAL ?2 HOUR)
               AND `u`.`crm_update` < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL ?2 HOUR)
            ORDER BY `id`
            LIMIT ?3,?4
            """)
//
//    @Query(nativeQuery = true, value = "select u.id from users u where u.crm_update is null or u.id in (" + " select user_id from user_login where created_at between DATE_SUB(CURRENT_TIME, INTERVAL ?1 hour) and DATE_SUB(CURRENT_TIME, INTERVAL ?2 hour))" + " and u.crm_update < DATE_SUB(CURRENT_TIME,INTERVAL ?2 hour ) order by id  LIMIT ?3,?4 ")
    List<Long> findIdsForCRMUpdate(int lowerBound, int upperBound, int position, int maxBatchsize);

    @Query(nativeQuery = true, value = """
            select min(us.rdate) as firstLogin,
                   max(us.rdate) as lastLogin,
                   count(distinct rdate) as activeDays
            from user_logins us
            where user_id = ?1""")
    UserLoginStatsPJ getLoginStatistics(Long userId);

    @Query(nativeQuery = true, value = """
            select day, sum( if (age=0,1,0)) as b0,sum( if (age=1,1,0)) as b1,sum( if (age between 2 and 5,1,0)) as b5,
            sum( if (age between 6 and 10,1,0)) as b10,
            sum( if (age between 11 and 20,1,0)) as b20,
            sum( if (age between 21 and 30,1,0)) as b30,
            sum( if (age between 31 and 40,1,0)) as b40,
            sum( if (age between 41 and 50 ,1,0)) as b50,
            sum( if (age>50,1,0)) as bmore
            from (
                select ue.created as day,
                       DATEDIFF(ue.created, u.created_at)  as age
                from users u join
                     (select user_id, max(rdate) as created from user_logins where rdate = ?1  group by user_id) ue on u.id = ue.user_id
                where u.created_at >= '2021-04-01'
                group by day,u.id
            ) aa group by day""")
    UserAgeBucketPJ getUserAgeBucketForDayOpt(LocalDate day);


    @Query(nativeQuery = true, value = """
            select day, sum( if (age=0,acnt,0)) as b0,
                      sum( if (age=1,acnt,0)) as b1,
                      sum( if (age between 2 and 5,acnt,0)) as b5,
                        sum( if (age between 6 and 10,acnt,0)) as b10,
                        sum( if (age between 11 and 20,acnt,0)) as b20,
                        sum( if (age between 21 and 30,acnt,0)) as b30,
                        sum( if (age between 31 and 40,acnt,0)) as b40,
                        sum( if (age between 41 and 50 ,acnt,0)) as b50,
                        sum( if (age>50,acnt,0)) as bmore
                        from (
               select ue.created as day,
                DATEDIFF(ue.created, u.first_login) as age,count(*) as acnt
                            from user_statistics u join
                                 (select user_id, rdate as created from user_logins where rdate >= ?1 and user_logins.rdate < DATE_ADD(?2, INTERVAL 1 DAY) group by user_id) ue on u.user_id = ue.user_id
                            group by  day,age
                        ) aa group by day""")
    List<UserAgeBucketPJ> getUserAgeBucketsFromTo(LocalDate from, LocalDate to);


    @Query(nativeQuery = true, value = """
            select age,count(*) as cnt from (
                           select u.id,
                           DATE_FORMAT(u.created_at, '%y-%m-%d')    as created,
                   DATE_FORMAT(us.last_login, '%y-%m-%d') as maxDate,
                   coalesce(ABS(DATEDIFF(us.last_login, u.created_at) ) +1,0) as age
                   from
                   users u left join user_statistics us on u.id = us.user_id
                   where u.created_at >= '2021-04-01' and u.created_at < CURRENT_DATE
                   and u.email not like '%ously.de' and u.system_user = false
                   group by u.id
                                        ) aa group by age""")
    List<UserAgeDistributionPJ> getUserAgeDistribution();

    @Query(nativeQuery = true,
            value = """
                    select rdate as date, ftds as ftd from stats_daily where rdate >= ?1 and rdate < ?2
                    """)
    List<FirstTimeDepositorsPJ> findFTDUserCountsPerDay(LocalDate from, LocalDate to);


    @Query(nativeQuery = true)
    List<UserBalanceStatsDto> getUserBalanceStat(Long userId, Instant from, Instant to);

    @Query(nativeQuery = true,
            value = "select rdate as date, sign_ups as count from stats_daily where rdate BETWEEN DATE(?1) and DATE(?2)")
    List<RegistrationStatisticPJ> getNewUserCountByRange(LocalDateTime from, LocalDateTime to);

}
package com.ously.gamble.persistence.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ously.gamble.persistence.converters.UserTagsConverter;
import com.ously.gamble.persistence.model.AccountStatus;
import com.ously.gamble.persistence.model.user.User;
import com.ously.gamble.persistence.model.user.UserAddress;
import com.ously.gamble.persistence.model.user.UserInfo;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;

public class CasinoUser implements Serializable {

    private String langCode = "EN";

    private String surname;
    private String name;



    private String city;
    private String street;
    private String zipcode;
    private String birthplace;


    private Boolean agbSigned;
    private String gender;

    @NotNull
    private Boolean anonymous;

    @NotNull
    private Long id;

    private String extId;
    @NotNull
    private String displayName;
    @NotNull
    private String email;

    private String mobile;

    @NotNull
    private String status;

    private LocalDate birthdate;

    private Instant updatedAt;

    @NotNull
    private Instant createdAt;

    private Boolean admin;

    private String fbUid;

    private String affCode;

    private Set<String> tags;

    private String country;

    public CasinoUser() {
    }


    public CasinoUser(User user) {
        id = user.getId();
        extId = user.getExtId();
        displayName = user.getDisplayName();
        email = user.getEmail();
        status = user.getStatus().name();
        admin = user.getSystemUser();
        updatedAt = user.getUpdatedAt();
        createdAt = user.getCreatedAt();
        this.fbUid = user.getLocalId();
        this.country = user.getCountry();
    }

    public CasinoUser(Long id, String extId, String displayName, String email, String status, Boolean admin,
                      String mobile, LocalDate birthdate, String gender,
                      Boolean agbSigned, String name, String surname, String langCode,
                      Instant createdAt, Instant updatedAt, String localId, String affCode) {
        this(id, extId, displayName, email, status, admin, mobile, birthdate, gender, agbSigned, name, surname,
                langCode, createdAt, updatedAt, localId, affCode, null, "XX", null,null,null,null);
    }

    public CasinoUser(Long id, String extId, String displayName, String email, String status, Boolean admin,
                      String mobile, LocalDate birthdate, String gender,
                      Boolean agbSigned, String name, String surname, String langCode,
                      Instant createdAt, Instant updatedAt, String localId, String
                              affCode, String tags, String country, String city,String street, String postcode,String birthplace) {
        this.id = id;
        this.extId = extId;
        this.displayName = displayName;
        this.email = email;
        this.status = status;
        this.admin = admin;
        this.mobile = mobile;
        this.birthdate = birthdate;
        this.gender = gender;
        this.agbSigned = agbSigned;
        this.name = name;
        this.surname = surname;
        this.langCode = langCode;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.fbUid = localId;
        this.affCode = affCode;
        this.tags = UserTagsConverter.convertToSet(tags);
        this.country = country;
        this.city=city;
        this.street=street;
        this.zipcode=postcode;
        this.birthplace=birthplace;
    }

    public CasinoUser(User user, UserInfo userInfo) {
        id = user.getId();
        extId = user.getExtId();
        displayName = user.getDisplayName();
        email = user.getEmail();
        status = user.getStatus().name();
        this.country = user.getCountry();
        admin = user.getSystemUser();
        if (userInfo != null) {
            mobile = userInfo.getMobileNumber();
            birthdate = userInfo.getBirthDate();
            gender = userInfo.getGender();
            agbSigned = userInfo.getAgbSigned();
            name = userInfo.getName();
            surname = userInfo.getSurname();
            birthplace=userInfo.getBirthPlace();
            langCode = Objects.requireNonNullElse(userInfo.getLangCode(), "EN");
            if(userInfo.getAddress() != null) {
                city = userInfo.getAddress().getCity();
                street= userInfo.getAddress().getStreet();
                country=Objects.requireNonNullElse(userInfo.getAddress().getCountry(), this.country);
                zipcode=userInfo.getAddress().getZipCode();
            }
        } else {
            langCode = "EN";
        }
        this.tags = Collections.emptySet();
        updatedAt = user.getUpdatedAt();
        createdAt = user.getCreatedAt();
        this.fbUid = user.getLocalId();
    }

    @JsonIgnore
    public void updateUser(User u) {
        u.setDisplayName(displayName);
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFbUid() {
        return fbUid;
    }

    public void setFbUid(String fbUid) {
        this.fbUid = fbUid;
    }

    public String getStatus() {
        return status;
    }

    public Long getId() {
        return id;
    }

    public String getEmail() {
        return email;
    }

    public String getMobile() {
        return mobile;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public LocalDate getBirthdate() {
        return birthdate;
    }


    public void setEmail(String email) {
        this.email = email;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setBirthdate(LocalDate birthdate) {
        this.birthdate = birthdate;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Boolean getAdmin() {
        return admin;
    }

    public void setAdmin(Boolean admin) {
        this.admin = admin;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    public String getSurname() {
        return surname;
    }

    public void setSurname(String surname) {
        this.surname = surname;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getAgbSigned() {
        return agbSigned;
    }

    public void setAgbSigned(Boolean agbSigned) {
        this.agbSigned = agbSigned;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Boolean getAnonymous() {
        return anonymous;
    }

    public void setAnonymous(Boolean anonymous) {
        this.anonymous = anonymous;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public boolean isBlocked() {
        return (AccountStatus.BLOCKED.name().equals(status) || AccountStatus.BANNED.name().equals(status));
    }

    public String getExtId() {
        return extId;
    }

    public void setExtId(String extId) {
        this.extId = extId;
    }

    public String getAffCode() {
        return affCode;
    }

    public void setAffCode(String affCode) {
        this.affCode = affCode;
    }

    public Set<String> getTags() {
        return tags;
    }

    public void setTags(final Set<String> tags) {
        this.tags = tags;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    public String getBirthplace() {
        return birthplace;
    }

    public void setBirthplace(String birthplace) {
        this.birthplace = birthplace;
    }

    @JsonIgnore
    public Map<String,Object> updateUserInfo(UserInfo ui) {
        Map<String,Object> result=new HashMap<>(0);

        if(ui.getAddress()==null){
            ui.setAddress(new UserAddress());
        }

        if(!StringUtils.isAllBlank(city)){
            if(StringUtils.compare(ui.getAddress().getCity(),city) != 0){
                result.put("city", "Changed city from '"+ui.getAddress().getCity()+"' to '"+city+"'");
                ui.getAddress().setCity(StringUtils.abbreviate(city,99));
            }
        }

        if(!StringUtils.isAllBlank(country)){
            if(StringUtils.compare(ui.getAddress().getCountry(),country) != 0){
                result.put("country", "Changed country from '"+ui.getAddress().getCountry()+"' to '"+country+"'");
                ui.getAddress().setCountry(country);
            }
        }

        if(!StringUtils.isAllBlank(street)){
            if(StringUtils.compare(ui.getAddress().getStreet(),street) != 0){
                result.put("street", "Changed street from '"+ui.getAddress().getStreet()+"' to '"+street+"'");
                ui.getAddress().setStreet(StringUtils.abbreviate(street,99));
            }
        }

        if(!StringUtils.isAllBlank(zipcode)){
            if(StringUtils.compare(ui.getAddress().getZipCode(),zipcode) != 0){
                result.put("zipcode", "Changed zipcode from '"+ui.getAddress().getZipCode()+"' to '"+zipcode+"'");
                ui.getAddress().setZipCode(StringUtils.abbreviate(zipcode,20));
            }
        }

        if(birthdate != null){
            if(ui.getBirthDate() == null || !birthdate.isEqual(ui.getBirthDate())){
                result.put("birthdate", "Changed birthdate from '"+ui.getBirthDate()+"' to '"+birthdate+"'");
                ui.setBirthDate(birthdate);
            }
        }

        return result;
    }
}

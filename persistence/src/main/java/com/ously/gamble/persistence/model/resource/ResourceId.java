package com.ously.gamble.persistence.model.resource;

import java.io.Serializable;
import java.util.Objects;

public class ResourceId implements Serializable {
    String component;
    String name;

    public ResourceId() {
    }

    public ResourceId(String component, String name) {
        this.component = component;
        this.name = name;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ResourceId that = (ResourceId) o;

        if (!Objects.equals(component, that.component)) return false;
        return Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        int result = component != null ? component.hashCode() : 0;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        return result;
    }
}

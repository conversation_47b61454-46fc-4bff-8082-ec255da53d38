package com.ously.gamble.persistence.model.idclasses;

import java.io.Serializable;

public class SessionRoundId implements Serializable {

    Long userId;
    Long sessionId;
    String roundReference;

    public SessionRoundId() {
    }

    public SessionRoundId(Long userId, Long sessionId, String roundRef) {
        this.userId = userId;
        this.sessionId = sessionId;
        this.roundReference = roundRef;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        var that = (SessionRoundId) o;

        return userId.equals(that.userId) && sessionId.equals(that.sessionId) && roundReference.equals(that.roundReference);
    }

    @Override
    public int hashCode() {
        var result = userId.hashCode();
        result = 31 * result + sessionId.hashCode();
        result = 31 * result + roundReference.hashCode();
        return result;
    }

    public String getRoundReference() {
        return roundReference;
    }

    public void setRoundReference(String roundReference) {
        this.roundReference = roundReference;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public Long getUserId() {
        return userId;
    }
}

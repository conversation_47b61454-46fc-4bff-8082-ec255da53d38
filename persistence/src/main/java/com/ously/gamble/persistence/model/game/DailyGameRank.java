package com.ously.gamble.persistence.model.game;

import com.ously.gamble.persistence.model.idclasses.DailyGameRankId;
import com.ously.gamble.persistence.projections.DailyGameRankDto;
import jakarta.persistence.*;
import org.hibernate.annotations.Immutable;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * Daily game rank (TOP N)
 */
@Entity
@Table(name = "game_rank_daily")
@IdClass(DailyGameRankId.class)
@Immutable
@NamedNativeQuery(name = "DailyGameRank.getDailyGameRanks", query = """
        select dgr.gameId,g.name as name, v.vendor_name as vendorName, dgr.sessions,dgr.spins,dgr.sessionTimeTotal,dgr.sumBet,dgr.sumWin,dgr.rtp from
                (select gs.game_id as gameId,
                       count(*)                                                    as sessions,
                       sum(gs.num_bets)                                           as spins,
                       sum(UNIX_TIMESTAMP(gs.end_at) - UNIX_TIMESTAMP(gs.start_at)) as sessionTimeTotal,
                       sum(gs.sum_bet) /100                                            as sumBet,
                       sum(gs.sum_win) /100                                            as sumWin,
                       1 + (sum(gs.sum_win) - sum(gs.sum_bet)) / (sum(gs.sum_bet)) as rtp
                from session_statistics_lb sb join session_statistics gs on sb.session_id=gs.session_id join users u on gs.user_id= u.id
                where
                   u.system_user = false
                  and u.email not like '%@ously.de'
                  and gs.num_bets > 0
                  and sb.rdate = ?1
                group by gs.game_id) dgr join games g on dgr.gameId=g.id join vendors v on g.vendor_id=v.id
                order by sessions DESC,spins DESC,sessionTimeTotal DESC
                limit ?2""", resultSetMapping = "DailyGameRank.DailyGameRankDto")

@NamedNativeQuery(name = "DailyGameRank.getDailyGameRanksTest", query = """
        select dgr.gameId,g.name as name, v.vendor_name as vendorName, dgr.sessions,dgr.spins,dgr.sessionTimeTotal,dgr.sumBet,dgr.sumWin,dgr.rtp from
        (select gs.game_id as gameId,
               count(*)                                                    as sessions,
               sum(gs.num_bets)                                           as spins,
               sum(UNIX_TIMESTAMP(gs.end_at) - UNIX_TIMESTAMP(gs.start_at)) as sessionTimeTotal,
               sum(gs.sum_bet) /100                                            as sumBet,
               sum(gs.sum_win) /100                                            as sumWin,
               1 + (sum(gs.sum_win) - sum(gs.sum_bet)) / (sum(gs.sum_bet)) as rtp
        from session_statistics_lb sb join session_statistics gs on sb.session_id=gs.session_id join users u on gs.user_id= u.id
        where
          gs.num_bets > 0
          and sb.rdate = ?1
        group by gs.game_id) dgr join games g on dgr.gameId=g.id join vendors v on g.vendor_id=v.id
        order by sessions DESC,spins DESC,sessionTimeTotal DESC
        limit ?2""", resultSetMapping = "DailyGameRank.DailyGameRankDto")

@NamedNativeQuery(name = "DailyGameRank.getRankingFrom",
        query = """
                select grd.game_id as gameId,g.name as name,v.vendor_name as vendorName,sum(sessions) as sessions,sum(spins) as spins,
                          sum(session_time_total) as sessionTimeTotal,
                          sum(sum_bet)  as sumBet,
                          sum(sum_win)  as sumWin,
                          0 as rtp
                          from game_rank_daily grd join games g on grd.game_id = g.id join vendors v on g.vendor_id = v.id
                          where
                          rdate >= ?1 and rdate <= ?2
                  group by grd.game_id,g.name,v.vendor_name
                  order by sessions desc, spins desc LIMIT ?3""",
        resultSetMapping = "DailyGameRank" +
                ".DailyGameRankDto")


@SqlResultSetMapping(name = "DailyGameRank.DailyGameRankDto",
        classes = @ConstructorResult(targetClass = DailyGameRankDto.class,
                columns = {
                        @ColumnResult(name = "gameId",
                                type = long.class),
                        @ColumnResult(name = "name",
                                type = String.class),
                        @ColumnResult(name = "vendorName",
                                type = String.class),
                        @ColumnResult(name = "sessions",
                                type = int.class),
                        @ColumnResult(name = "spins",
                                type = int.class),
                        @ColumnResult(name = "sessionTimeTotal",
                                type = int.class),
                        @ColumnResult(name = "sumBet",
                                type = double.class),
                        @ColumnResult(name = "sumWin",
                                type = double.class),
                        @ColumnResult(name = "rtp",
                                type = float.class)}))
@EntityListeners(AuditingEntityListener.class)
public class DailyGameRank implements Persistable<DailyGameRankId>, Serializable {

    @Transient
    boolean wasLoaded;

    @PostLoad
    @PostPersist
    public void setTransientLoaded() {
        this.wasLoaded = true;
    }


    @Id
    @Column(name = "rdate")
    LocalDate date;

    @Id
    @Column(name = "game_id")
    long gameId;

    @Column(name = "sessions")
    int sessions;
    @Column(name = "spins")
    int spins;
    @Column(name = "session_time_total")
    int totalSessionTime;
    @Column(name = "sum_bet")
    double sumBet;
    @Column(name = "sum_win")
    double sumWin;
    @Column(name = "rtp")
    float rtp;

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public long getGameId() {
        return gameId;
    }

    public void setGameId(long gameId) {
        this.gameId = gameId;
    }

    public int getSessions() {
        return sessions;
    }

    public void setSessions(int sessions) {
        this.sessions = sessions;
    }

    public int getSpins() {
        return spins;
    }

    public void setSpins(int spins) {
        this.spins = spins;
    }

    public int getTotalSessionTime() {
        return totalSessionTime;
    }

    public void setTotalSessionTime(int totalSessionTime) {
        this.totalSessionTime = totalSessionTime;
    }

    public double getSumBet() {
        return sumBet;
    }

    public void setSumBet(double sumBet) {
        this.sumBet = sumBet;
    }

    public double getSumWin() {
        return sumWin;
    }

    public void setSumWin(double sumWin) {
        this.sumWin = sumWin;
    }

    public float getRtp() {
        return rtp;
    }

    public void setRtp(float rtp) {
        this.rtp = rtp;
    }

    @Override
    public DailyGameRankId getId() {
        return new DailyGameRankId(this.date, this.gameId);
    }

    @Override
    public boolean isNew() {
        return !wasLoaded;
    }
}

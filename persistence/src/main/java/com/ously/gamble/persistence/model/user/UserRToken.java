package com.ously.gamble.persistence.model.user;

import com.ously.gamble.persistence.model.idclasses.UserRTokenId;
import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.time.Instant;

@Entity
@EntityListeners(AuditingEntityListener.class)
@IdClass(UserRTokenId.class)
@Table(name = "user_rtoken")
public class UserRToken implements Persistable<UserRTokenId>, Serializable {

    @Transient
    boolean wasLoaded;

    @PostLoad
    @PostPersist
    public void setTransientLoaded() {
        this.wasLoaded = true;
    }


    @Id
    @Column(name = "user_id")
    Long userId;

    @Id
    @Column(name = "realm", length = 20)
    String realm;

    @Id
    @Column(name = "dcode")
    String deviceCode;

    @Column(name = "token", length = 100)
    String token;

    @Column(name = "email", length = 128)
    String email;

    @CreatedDate
    @Column(name = "created_at")
    Instant createdAt;

    @Column(name = "expire_at")
    Instant expireAt;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getRealm() {
        return realm;
    }

    public void setRealm(String realm) {
        this.realm = realm;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getExpireAt() {
        return expireAt;
    }

    public void setExpireAt(Instant expireAt) {
        this.expireAt = expireAt;
    }

    @Override
    public UserRTokenId getId() {
        return new UserRTokenId(userId, realm, deviceCode);
    }

    @Override
    public boolean isNew() {
        return !this.wasLoaded;
    }
}

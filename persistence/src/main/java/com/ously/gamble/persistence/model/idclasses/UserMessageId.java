package com.ously.gamble.persistence.model.idclasses;

import java.io.Serializable;

public class UserMessageId implements Serializable {
    private long userId;
    private String qualifier;

    public UserMessageId() {
    }

    public UserMessageId(long uid, String qualifier) {
        this.userId = uid;
        this.qualifier = qualifier;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }


    public String getQualifier() {
        return qualifier;
    }

    public void setQualifier(String qualifier) {
        this.qualifier = qualifier;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        var that = (UserMessageId) o;

        return userId == that.userId && qualifier.equals(that.qualifier);
    }

    @Override
    public int hashCode() {
        var result = Long.hashCode(userId);
        result = 31 * result + qualifier.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return "UserMessageId{" +
                "userId=" + userId +
                ", qualifier='" + qualifier + '\'' +
                '}';
    }
}

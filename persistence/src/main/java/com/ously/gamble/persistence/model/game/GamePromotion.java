package com.ously.gamble.persistence.model.game;


import com.ously.gamble.persistence.model.PromotionType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;

@Entity
@Table(name = "game_promotion")
public class GamePromotion implements Comparable<GamePromotion> {

    public boolean isActive() {
        var today = LocalDate.now();
        // null or future day
        return this.validFrom != null && !this.validFrom.isAfter(today) && this.validTo != null && !this.validTo.isBefore(today);

    }

    @Override
    public int compareTo(GamePromotion o) {
        return this.importance.compareTo(o.importance);
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "game_id")
    private Long gameId;

    @Column(name = "valid_from")
    private LocalDate validFrom;

    @Column(name = "valid_to")
    private LocalDate validTo;

    @Column(name = "importance")
    private Integer importance = 50;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private PromotionType type;

    @Column(name = "feature_text")
    private String featureText;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }

    public LocalDate getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(LocalDate validFrom) {
        this.validFrom = validFrom;
    }

    public LocalDate getValidTo() {
        return validTo;
    }

    public void setValidTo(LocalDate validTo) {
        this.validTo = validTo;
    }

    public int getImportance() {
        return importance;
    }

    public void setImportance(int importance) {
        this.importance = importance;
    }

    public PromotionType getType() {
        return type;
    }

    public void setType(PromotionType type) {
        this.type = type;
    }

    public String getFeatureText() {
        return featureText;
    }

    public void setFeatureText(String featureText) {
        this.featureText = featureText;
    }
}

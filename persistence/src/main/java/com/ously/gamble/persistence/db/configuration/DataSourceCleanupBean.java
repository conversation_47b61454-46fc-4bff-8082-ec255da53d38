package com.ously.gamble.persistence.db.configuration;

import com.ously.gamble.persistence.db.routingds.RoutingDataSource;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DataSourceCleanupBean implements DisposableBean {
    @Autowired
    private RoutingDataSource routingDataSource;

    @Override
    public void destroy() throws Exception {
        if (routingDataSource != null) {
            routingDataSource.close();
        }
    }
}
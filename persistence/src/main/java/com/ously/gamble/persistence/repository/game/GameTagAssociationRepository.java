package com.ously.gamble.persistence.repository.game;

import com.ously.gamble.persistence.model.game.GameTagAssociation;
import com.ously.gamble.persistence.model.game.GameTagAssociationId;
import com.ously.gamble.persistence.projections.GameTaglistPJ;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface GameTagAssociationRepository extends JpaRepository<GameTagAssociation, GameTagAssociationId> {

    @Query(nativeQuery = true, value = "SELECT * FROM game_tags  where game_id = :gid ")
    List<GameTagAssociation> findAllAssociationsByGameId(@Param("gid") long gameId);

    @Query(nativeQuery = true, value = "SELECT tag_id FROM game_tags  where game_id = :gid ")
    Set<Integer> findAllTagIdsForGameId(@Param("gid") long gameId);

    @Query(nativeQuery = true, value = "SELECT COALESCE(group_concat(tag_id),'') as tidc FROM  " +
                                       "game_tags  where game_id = :gid")
    String findAllTagIdsConcatForGameId(@Param("gid") long gameId);

    @Query(nativeQuery = true, value = "SELECT game_id as gameId,COALESCE(group_concat(tag_id)," +
                                       "'') as taglist " +
                                       "FROM  " +
                                       "game_tags  group by game_id")
    List<GameTaglistPJ> findAllTagIdsConcat();


    @Query(nativeQuery = true, value = "delete from game_tags where game_id = :gid")
    @Modifying
    void deleteAllForGameId(@Param("gid") long gameId);
}
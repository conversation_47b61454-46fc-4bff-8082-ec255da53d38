//package com.ously.gamble.persistence.model.freespins;
//
//import java.io.Serializable;
//import java.util.Objects;
//
//public class UserBonusId implements Serializable {
//    Long fsId;
//    Long userId;
//
//    public UserBonusId() {
//    }
//
//    public UserBonusId(Long fsId, Long userId) {
//        this.userId = userId;
//        this.fsId = fsId;
//    }
//
//    public Long getFsId() {
//        return fsId;
//    }
//
//    public void setFsId(Long fsId) {
//        this.fsId = fsId;
//    }
//
//    public Long getUserId() {
//        return userId;
//    }
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }
//
//    @Override
//    public boolean equals(Object o) {
//        if (this == o) {
//            return true;
//        }
//        if (o == null || getClass() != o.getClass()) {
//            return false;
//        }
//        var that = (UserBonusId) o;
//
//        if (!Objects.equals(fsId, that.fsId)) {
//            return false;
//        }
//        return userId != null ? userId.equals(that.userId) : that.userId == null;
//    }
//
//    @Override
//    public int hashCode() {
//        var result = fsId != null ? fsId.hashCode() : 0;
//        result = 31 * result + (userId != null ? userId.hashCode() : 0);
//        return result;
//    }
//}

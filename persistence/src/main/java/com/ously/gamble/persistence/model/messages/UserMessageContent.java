package com.ously.gamble.persistence.model.messages;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import com.ously.gamble.persistence.dto.JsonViews.JVAdmin;
import com.ously.gamble.persistence.dto.JsonViews.JVUser;

import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.stream.Collectors;

@JsonView(JVUser.class)
public record UserMessageContent(
        UserMessageType type,
        @JsonView(JVAdmin.class)
        String title,
        @JsonView(JVAdmin.class)
        String content,
        @JsonView(JVAdmin.class)
        String ttl,
        Map<String, String> variables,
        Map<String, String> actions
) {
    public UserMessageContent(UserMessageType type, Map<String, String> variables, Map<String,
            String> actions) {
        this(type, type.title(), type.body(), type.expiry(), variables, actions);
    }

    public UserMessageContent(UserMessageType type, String ttl, Map<String, String> variables,
                              Map<String,
                                      String> actions) {
        this(type, type.title(), type.body(), Optional.ofNullable(ttl).orElseGet(type::expiry), variables, actions);
    }

    @JsonIgnore
    public UserMessageContent getFiltered() {

        if (variables == null || variables.isEmpty()) {
            return this;
        }

        var nVars =
                variables.entrySet().stream().filter(a -> !a.getKey().startsWith("@") && a.getValue() != null).collect(Collectors.toMap(Entry::getKey,
                        Entry::getValue));

        return new UserMessageContent(type, title, content, ttl,
                nVars, actions);
    }
}

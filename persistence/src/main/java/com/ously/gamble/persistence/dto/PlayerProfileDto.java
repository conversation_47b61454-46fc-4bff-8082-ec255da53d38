package com.ously.gamble.persistence.dto;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


public record PlayerProfileDto(
        String extId, String username, int level, LocalDate activeSince, long sessions, long spins,
        BigDecimal sumBets, BigDecimal sumWins, List<Integer> favorites, List<Integer> lastGamesPlayed,
        BigDecimal maxBet, BigDecimal maxWin, BigDecimal maxMult, int slotsPlayed, List<Integer> mostPlayed
) {

    public PlayerProfileDto(String extId, String username, int level, String activeSince, long sessions, long spins,
                            BigDecimal sumBets, BigDecimal sumWins, String favorites, String lastGamesPlayed, BigDecimal maxBet, BigDecimal maxWin, BigDecimal maxMult, int slotsPlayed, String mostGamesPlayed) {
        this(extId, username, level, LocalDate.parse(activeSince), sessions, spins, sumBets, sumWins, parseList(favorites, 10),
                parseList(lastGamesPlayed, 10), maxBet, maxWin, maxMult, slotsPlayed, parseList(mostGamesPlayed, 10));
    }

    static List<Integer> parseList(String favorites, int i) {
        if (StringUtils.isAllEmpty(favorites)) {
            return Collections.EMPTY_LIST;
        }
        return Arrays.stream(favorites.split(",")).limit(i)
                .map(a -> (a.contains("=") ? (a.split("=")[0]) : a))
                .map(Integer::parseInt).toList();
    }


}

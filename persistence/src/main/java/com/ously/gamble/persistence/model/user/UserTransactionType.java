package com.ously.gamble.persistence.model.user;

public enum UserTransactionType {

    IGNORE(true),

    /**
     * @deprecated "sc-12454 remove later."
     */
    @Deprecated
    DEPOSIT(false),
    /**
     * @deprecated "sc-12454 remove later."
     */
    @Deprecated
    PAYOUT_RESERVE(false),
    /**
     * @deprecated "sc-12454 remove later."
     */
    @Deprecated
    PAYOUT_CANCEL(false),
    /**
     * @deprecated "sc-12454 remove later."
     */
    @Deprecated
    PAYOUT_COMMIT(false),
    /**
     * @deprecated "sc-12454 remove later."
     */
    @Deprecated
    REFUND(false),

    /**
     * used when deduction or bonus balance changes are applied
     * @deprecated "sc-12454 remove later."
     */
    @Deprecated
    CHANGE(false),

    /**
     * bonus balance rolled over to real money balance
     * @deprecated "sc-12454 remove later."
     */
    @Deprecated
    BONUS_ROLLOVER(false),

    /**
     * @deprecated "sc-12454 remove later."
     */
    @Deprecated
    BONUS_ZERO_OUT(false),
    /**
     * @deprecated "sc-12454 remove later."
     */
    @Deprecated
    BONUS_ADDED(false),
    /**
     * @deprecated "sc-12454 remove later."
     */
    @Deprecated
    BONUS_OVERRIDE(false),
    /**
     * @deprecated "sc-12454 remove later."
     */
    @Deprecated
    BONUS_RESET(false),

    /**
     * @deprecated "sc-12454 remove later."
     */
    @Deprecated
    AFF_REWARD(false),

    /**
     * @deprecated "sc-12454 remove later."
     */
    @Deprecated
    RAKEBACK_BONUS(false),

    UNLOCK_GAME(true),

    SDEPOSIT(true),
    PURCHASE(true),
    ACHIEVEMENT(true),
    WHEELSPIN(true),
    DOUBLEUP(true),
    LEVELUP(true),
    LOYALTY(true),
    MILESTONE(true),
    VIDEOAD(true),
    BOOSTER(true),
    MISSION(true),
    TUTORIAL(true),
    BONUSCODE(true),
    REGBONUS(true);


    final boolean social;

    UserTransactionType(boolean social) {
        this.social = social;
    }

    public boolean isSocial() {
        return social;
    }

}

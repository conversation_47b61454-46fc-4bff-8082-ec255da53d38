package com.ously.gamble.persistence.model.messages;

public enum UserMessageType {
    IGNORE(false, false, false, "MSG_IGNORE", null),
    CUSTOM(false, false, true, "MSG_CUSTOM", null, "P30D", "P60D"),

    WEE<PERSON><PERSON><PERSON><PERSON>WARD(true, false, true, "MSG_WEEKLYAWARD", null),
    REWARD(true, false, true, "MSG_REWARD", null, "PT48H", "PT72H"),
    NEWSLOT(false, false, true, "MSG_NEWSLOT", null, "P7D", "P14D"),
    SHOPITEM(true, false, true, "MSG_SHOPITEM", null, "P2D", "P2D"),

    PURCHASE_SUCCESS(false, false, true, "MSG_PURCHASE_SUCCESS", null, "P60D", "P180D"),

    DEPOSIT_RECEIVED(true, true, true, "MSG_DEPOSIT_RECEIVED", "deposit_received"),
    DEPOSIT_PROBLEM(true, false, true, "MSG_DEPOSIT_PROBLEM", null),
    PAYOUT_CREATED(false, false, true, "MSG_PAYOUT_CREATED", "payout_created"),
    PAYOUT_SENT(true, true, true, "MSG_PAYOUT_SENT", "payout_sent"),
    PAYOUT_CONFIRMED(true, false, true, "MSG_PAYOUT_CONFIRMED", "payout_confirmed"),
    PAYOUT_PROBLEM(true, false, true, "MSG_PAYOUT_PROBLEM", null),
    PAYOUT_CONTACT_SUPPORT(true, false, true, "MSG_CONTACT_SUPPORT", "payout_contact_support"),

    SUPPORT_MESSAGE(false, false, true, "MSG_SUPPORT_MESSAGE", null),

    RAKEBACK_AVAILABLE(true, false, true, "MSG_RAKEBACK_AVAILABLE", null),
    RAKEBACK_CLAIMED(false, false, true, "MSG_RAKEBACK_CLAIMED", null),

    REGVERIFICATION(false, true, false, "AUTH_REGVERIFICATION", null),
    PWCHANGE(false, true, false, "AUTH_PWCHANGE", null),

    AFF_STATEMENT_BOOKED(true, false, true, "MSG_AFF_STATEMENT_BOOKED", null),


    SESSION_BET_TO_HIGH(true, false, true, "BET_TO_HIGH", null),

    BONUS_BET_TO_HIGH(true, false, true, "BONUS_BET_TO_HIGH", null),

    BONUS_NOT_ALLOWED(true, false, true, "BONUS_NOT_ALLOWED", null),
    BONUS_WIN_IGNORED(true, false, true, "BONUS_WIN_IGNORED", null),
    BONUS_ENTERED(true, false, true, "BONUS_ENTERED", null),
    BONUS_WAGERED(true, false, true, "BONUS_WAGERED", null),

    CONFIRM_MARKETING_EMAIL(false, true, false, "MSG_CONFIRM_MARKETING_EMAIL", null);


    private final boolean popup;
    private final boolean email;

    private final boolean inbox;
    private final String title;
    private final String body;
    private final String eventName;

    private final String expiry;

    private final String expiryRead;


    UserMessageType(boolean isPopup, boolean email, boolean inbox, String literal, String crmName, String expiry,
                    String expiryRead) {
        this.popup = isPopup;
        this.email = email;
        this.inbox = inbox;
        this.title = literal + "_TITLE";
        this.body = literal + "_BODY";
        this.eventName = crmName;
        this.expiry = expiry;
        this.expiryRead = expiryRead;
    }

    UserMessageType(boolean isPopup, boolean email, boolean inbox, String literal, String crmName) {
        this(isPopup, email, inbox, literal, crmName, "P30D", "P60D");
    }

    public boolean isPopup() {
        return popup;
    }

    public boolean isInbox() {
        return inbox;
    }

    public boolean isEmail() {
        return email;
    }

    public String title() {
        return title;
    }

    public String body() {
        return body;
    }

    public String eventName() {
        return eventName;
    }

    public String expiry() {
        return expiry;
    }

    public String expiryRead() {
        return expiryRead;
    }
}

package com.ously.gamble.persistence.repository.user;

import com.ously.gamble.persistence.dto.CRMUserDetails;
import com.ously.gamble.persistence.dto.CRMUserRevDetails;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.dto.PlayerProfileDto;
import com.ously.gamble.persistence.model.AccountStatus;
import com.ously.gamble.persistence.model.user.User;
import com.ously.gamble.persistence.projections.UserAuthCompositePJ;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    @Query(nativeQuery = true)
    Optional<PlayerProfileDto> loadPlayerProfile(String extId);

    Optional<User> findByEmail(String email);

    Optional<User> findByLocalId(String localId);

    Boolean existsByEmail(String email);

    @Query(nativeQuery = true, value = "select * from users u where id in :ids")
    List<User> findByUserIds(@Param("ids") List<Long> userIds);

    @Query(nativeQuery = true,
            value = "select count(*) from (select um.email as m from user_mail um where um.email = ?1 union all select u.email as m from users u where u.email = ?1) xx ")
    int mailCount(String email);

    @Query(nativeQuery = true,
            value = "select count(*) from (select um.local_id as m from user_mail um where um.local_id = ?1 union all select u.display_name as m from users u where u.display_name = ?1) xx ")
    int usernameCount(String username);


    @Modifying
    @Query("update User u set u.crmUpdateTimestamp = ?1,u.updatedAt=?1 where u.id = ?2")
    int setNewCrmTimestamp(Instant updTime, Long id);

    @Modifying
    @Query("update User u set u.status = ?3,u.updatedAt=?1 where u.id = ?2")
    int setNewAccountStatus(Instant updTime, Long id, AccountStatus status);

    @Query(nativeQuery = true, value = "select count(*) from users u where u.display_name = ?1")
    int checkUsernameAvailability(String username);

    @Query(nativeQuery = true, value = "select count(*) from users u where u.extid = ?1")
    int hashUsageCount(long extId);

    @Query("select ur as userRegistration, u as user,ui as userInfo from UserRegistration ur inner join User u on ur.userId = u.id LEFT JOIN UserInfo ui on u.id = ui.userId  where ur.localId = (:localId)")
    Optional<UserAuthCompositePJ> findUserAuthComposite(@Param("localId") String localId);

    @Query(nativeQuery = true)
    Optional<CasinoUser> loadCasinoUser(long userId);

    @Query(nativeQuery = true)
    Optional<CRMUserDetails> loadCRMData(long userId);

    @Query(nativeQuery = true)
    Optional<CRMUserRevDetails> loadCRMRevData(long userId);


    @Query(nativeQuery = true, value = "select id from users where ext_id <0 limit ?1")
    List<Long> getUnhashedUserIds(int limit);

    @Query(nativeQuery = true,
            value = "select id from users where status = 'DISABLED' and updated_at < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL ?1 HOUR)")
    List<Long> getUserIdsForFinalCleanup(int hoursBack);

    @Query(nativeQuery = true, value = "select CONCAT(local_id,'##',id) from users u")
    List<String> getAllLocalUidTuples();

    @Query(nativeQuery = true, value = "select coalesce(sum(applied_cost), 0) as sumPurchases from purchases where user_id = " +
            ":uid and status = 'SUCCESS'")
    Optional<BigDecimal> getRevTotal(@Param("uid") long userId);

    @Query(nativeQuery = true, value = """
            select country from (
            select country, num
            from (select country, count(*) as num from user_logins where user_id = 549 group by country order by num desc limit 1) y
            union all
            select 'EN' as country, 1 as num
            limit 1) x""")
    String getUsersCountry(@Param("uid") long userId);
}
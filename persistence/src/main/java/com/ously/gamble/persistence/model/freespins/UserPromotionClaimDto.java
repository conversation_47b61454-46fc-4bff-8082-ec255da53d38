//package com.ously.gamble.persistence.model.freespins;
//
//import com.fasterxml.jackson.databind.annotation.JsonSerialize;
//import com.fasterxml.jackson.datatype.jsr310.ser.InstantSerializer;
//
//import java.math.BigDecimal;
//import java.time.Instant;
//
//public record UserPromotionClaimDto(
//        Number id,
//        Number gameId,
//        Number level,
//        Number amount,
//        Number wagerFactor,
//        Number bonusAmount,
//        @JsonSerialize(using = InstantSerializer.class)
//        Instant selectedAt,
//        @JsonSerialize(using = InstantSerializer.class)
//        Instant createdAt,
//        @JsonSerialize(using = InstantSerializer.class)
//        Instant expireAt,
//        BigDecimal maxCashout,
//        BigDecimal zeroOutThreshold,
//        String depositId,
//        String createdBy,
//        PromotionDto promotion
//) {
//        public UserPromotionClaimDto(UserBonusClaim claim, PromotionDto promotion) {
//                this(
//                        claim.getClaimId(),
//                        claim.getFsGameId(),
//                        claim.getFsBetLevel(),
//                        claim.getFsAmount(),
//                        claim.getWagerFactor(),
//                        claim.getBonusAmount(),
//                        claim.getSelectedAt(),
//                        claim.getCreatedAt(),
//                        claim.getExpireAt(),
//                        claim.getMaxCashout(),
//                        claim.getZeroOutThreshold(),
//                        claim.getDepositId(),
//                        claim.getCreatedBy(),
//                        promotion);
//        }
//}

package com.ously.gamble.persistence.model.session;

import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.audit.CreationDateAudit;
import com.ously.gamble.persistence.model.idclasses.SessionTransactionId;
import jakarta.persistence.*;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;
import org.springframework.data.domain.Persistable;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;

@NamedNativeQuery(name = "SessionTransaction.loadArchivedTransactions", query = """
        select       a.id as id,
                     a.created_at as ts,
                     a.ext_tx_id as extid,
                     a.round_ref as rndid,
                     a.type as type,
                     a.cancelled as canc,
                     a.earned_xp as xp,
                     a.level_after as lvl,
                     a.boost as boost,
                     a.percnl_after as pnl,
                     a.bet as bet,
                     a.win as win,
                     a.balance_after as bal
              from session_transaction_archive a
              where a.session_id = ?1""", resultSetMapping = "Mapping.archivedSessionTransaction")
@SqlResultSetMapping(name = "Mapping.archivedSessionTransaction",
        classes = @ConstructorResult(targetClass = ArchivedTransactionDto.class,
                columns = {
                        @ColumnResult(name = "id",
                                type = long.class),
                        @ColumnResult(name = "ts",
                                type = Instant.class),
                        @ColumnResult(name = "extid",
                                type = String.class),
                        @ColumnResult(name = "rndid",
                                type = String.class),
                        @ColumnResult(name = "type",
                                type = String.class),
                        @ColumnResult(name = "canc",
                                type = boolean.class),
                        @ColumnResult(name = "xp",
                                type = long.class),
                        @ColumnResult(name = "lvl",
                                type = int.class),
                        @ColumnResult(name = "boost",
                                type = BigDecimal.class),
                        @ColumnResult(name = "pnl",
                                type = BigDecimal.class),
                        @ColumnResult(name = "bet", type =
                                BigDecimal.class),
                        @ColumnResult(name = "win", type =
                                BigDecimal.class),
                        @ColumnResult(name = "bal", type =
                                BigDecimal.class)
                }))


@Entity
@Table(name = "session_transaction")
@IdClass(SessionTransactionId.class)
public class SessionTransaction extends CreationDateAudit implements Serializable
        , Persistable<SessionTransactionId> {
    private static final BigDecimal BD0S4 = BigDecimal.ZERO.setScale(4, RoundingMode.DOWN);
    private static final BigDecimal BD0S2 = BigDecimal.ZERO.setScale(2, RoundingMode.DOWN);

    @Transient
    boolean wasLoaded;

    @PostLoad
    @PostPersist
    public void setTransientLoaded() {
        this.wasLoaded = true;
    }


    @Id
    @GeneratedValue(generator = "sessionTx")
    @GenericGenerator(name = "sessionTx", type = org.hibernate.id.enhanced.TableGenerator.class,
            parameters = {
                    @Parameter(name = "table_name", value = "custom_sequences"),
                    @Parameter(name = "value_column_name",
                            value = "sequence_next_hi_value"),
                    @Parameter(name = "prefer_entity_table_as_segment_value",
                            value = "true"),
                    @Parameter(name = "optimizer", value = "pooled-lo"),
                    @Parameter(name = "initial_value", value = "2000000000"),
                    @Parameter(name = "increment_size", value = "1000"),
                    @Parameter(name = "segment_value", value = "sessiontx")
            })
    @Column(name = "id", updatable = false)
    Long txId;

    @Id
    @Column(name = "user_id", updatable = false)
    long userId;

    @Id
    @Column(name = "session_id")
    Long sessionId;

    @Column(name = "game_id")
    Long gameId;

    @Column(name = "ext_tx_id", length = 100, updatable = false)
    String externalOrigTxId;

    @Column(name = "bet", updatable = false)
    BigDecimal bet = BD0S4;

    @Column(name = "win", updatable = false)
    BigDecimal win = BD0S4;

//    @Column(name = "tax", updatable = false)
//    BigDecimal tax = BD0S4;

    @Column(name = "balance_after", updatable = false)
    BigDecimal balanceAfter = BD0S4;

    @Enumerated(EnumType.STRING)
    @Column(length = 20, updatable = false)
    TransactionType type;

    @Column(name = "round_ref", length = 100, updatable = false)
    String roundReference;

    @Column(name = "level_after", updatable = false)
    int level;

    @Column(name = "percnl_after", updatable = false)
    BigDecimal percnl = BD0S2;

    @Column(name = "earned_xp", updatable = false)
    int earnedXp;

    @Column(name = "cancelled")
    boolean cancelled;

    @Column(name = "boost", updatable = false)
    BigDecimal boost = BD0S4;

    public Long getTxId() {
        return txId;
    }

    @Override
    public SessionTransactionId getId() {
        return new SessionTransactionId(this.txId, this.userId, this.sessionId);
    }

    @Override
    public boolean isNew() {
        return !this.wasLoaded;
    }

    public void setTxId(Long id) {
        this.txId = id;
    }

    public BigDecimal getBoost() {
        return boost;
    }

    public void setBoost(BigDecimal boost) {
        if (boost == null || boost.signum() == 0) {
            this.boost = BD0S4;
        } else if (boost.scale() != 4) {
            this.boost = boost.setScale(4, RoundingMode.DOWN);
        } else {
            this.boost = boost;
        }
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public BigDecimal getPercnl() {
        return percnl;
    }

    public void setPercnl(BigDecimal percnl) {
        if (percnl == null || percnl.signum() == 0) {
            this.percnl = BD0S2;
        } else if (percnl.scale() != 2) {
            this.percnl = percnl.setScale(2, RoundingMode.DOWN);
        } else {
            this.percnl = percnl;
        }
    }

    public int getEarnedXp() {
        return earnedXp;
    }

    public void setEarnedXp(int earnedXp) {
        this.earnedXp = earnedXp;
    }

    public String getExternalOrigTxId() {
        return externalOrigTxId;
    }

    public void setExternalOrigTxId(String externalOrigTxId) {
        this.externalOrigTxId = externalOrigTxId;
    }

    public BigDecimal getBalanceAfter() {
        return balanceAfter;
    }

    public void setBalanceAfter(BigDecimal balanceAfter) {
        if (balanceAfter == null || balanceAfter.signum() == 0) {
            this.balanceAfter = BD0S4;
        } else if (balanceAfter.scale() != 4) {
            this.balanceAfter = balanceAfter.setScale(4, RoundingMode.DOWN);
        } else {
            this.balanceAfter = balanceAfter;
        }
    }

    public TransactionType getType() {
        return type;
    }

    public void setType(TransactionType type) {
        this.type = type;
    }

    public BigDecimal getBet() {
        return bet;
    }

    public void setBet(BigDecimal bet) {
        if (bet == null || bet.signum() == 0) {
            this.bet = BD0S4;
        } else if (bet.scale() != 4) {
            this.bet = bet.setScale(4, RoundingMode.DOWN);
        } else {
            this.bet = bet;
        }
    }

    public BigDecimal getWin() {
        return win;
    }

    public void setWin(BigDecimal win) {
        if (win == null || win.signum() == 0) {
            this.win = BD0S4;
        } else if (win.scale() != 4) {
            this.win = win.setScale(4, RoundingMode.DOWN);
        } else {
            this.win = win;
        }
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }

    public String getRoundReference() {
        return roundReference;
    }

    public void setRoundReference(String roundReference) {
        this.roundReference = roundReference;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public boolean isCancelled() {
        return cancelled;
    }

    public void setCancelled(boolean cancelled) {
        this.cancelled = cancelled;
    }

//    public BigDecimal getTax() {
//        return tax;
//    }
//
//    public void setTax(BigDecimal tax) {
//        if (tax == null || tax.signum() == 0) {
//            this.tax = BD0S4;
//        } else if (tax.scale() != 4) {
//            this.tax = tax.setScale(4, RoundingMode.DOWN);
//        } else {
//            this.tax = tax;
//        }
//    }
}

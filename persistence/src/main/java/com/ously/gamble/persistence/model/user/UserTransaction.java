package com.ously.gamble.persistence.model.user;

import com.ously.gamble.persistence.model.idclasses.UserTransactionId;
import jakarta.persistence.*;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;

/**
 * All transactions unrelated to games or game-sessions are kept in UserTransaction. Normal usecase
 * is to insert,update balance,commit.
 * <p>
 * PK unclear. In most cases dupl. check will be by userId,type,txRefId.
 * When needed to scan, it is in a user context and seldom by other means.
 * so PK(user_id, type,txRefId) PARTITIONED by hash(user_id).
 * and evtl. an index on created_at and/or id.
 * id is unique on itself - but is used only when another entity uses this as fk - and then userId is a given and type is given in most (social) cases.
 */


@Entity
@Table(name = "user_transaction")
@EntityListeners(AuditingEntityListener.class)
@IdClass(UserTransactionId.class)
public class UserTransaction implements
        Serializable {
    private static final BigDecimal BD0S4 = BigDecimal.ZERO.setScale(4, RoundingMode.DOWN);

    // numerical id -> more practical as fk, used to reference from other entities
    // e.g. an achievement could store the id to quickly reference the transaction
    // generated.
    @Id
    @GeneratedValue(generator = "pooledUTx")
    @GenericGenerator(name = "pooledUTx", type = org.hibernate.id.enhanced.TableGenerator.class,
            parameters = {
                    @Parameter(name = "table_name", value = "custom_sequences"),
                    @Parameter(name = "value_column_name",
                            value = "sequence_next_hi_value"),
                    @Parameter(name = "prefer_entity_table_as_segment_value",
                            value = "true"),
                    @Parameter(name = "optimizer", value = "pooled-lo"),
                    // set initial high to avoid clashes with old txs
                    @Parameter(name = "initial_value", value = "2000000000"),
                    @Parameter(name = "increment_size", value = "100"),
                    @Parameter(name = "segment_value", value = "usertx")
            })
    Long id;

    // the user id
    @Id
    @Column(name = "user_id", nullable = false, updatable = false)
    long userId;

    // type of transaction (deposit, bonus, wheelspin,....)
    @Enumerated(EnumType.STRING)
    @Id
    @Column(length = 20, name = "type", nullable = false, updatable = false)
    UserTransactionType type;

    // unique per user, a textual id which also acts as a unique index
    @Id
    @Column(name = "ref_id", length = 80, nullable = false, updatable = false)
    String txRefid;

    // a more descr. description (short form), opt.
    @Column(name = "description", length = 100, nullable = false, updatable = false)
    String txDescription = "";

    // creation
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    Instant createdAt;

    // plus balance (scale =4)
    @Column(name = "credit", nullable = false, updatable = false)
    BigDecimal credit = BD0S4;

    // minus balance (scale =4)
    @Column(name = "withdraw", nullable = false, updatable = false)
    BigDecimal withdraw = BD0S4;

    // balance after that tx  (scale =4)
    @Column(name = "balance_after", nullable = false, updatable = false)
    BigDecimal balanceAfter = BD0S4;

    // reference to the ROLLBACK transaction, if >0 -> this tx has been cancelled. Reason can be found inside cancel-tx tx_descr.
    @Column(name = "cancel_id", nullable = false)
    long cancelId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public UserTransactionType getType() {
        return type;
    }

    public void setType(UserTransactionType type) {
        this.type = type;
    }

    public String getTxRefid() {
        return txRefid;
    }

    public void setTxRefid(String txRefid) {
        this.txRefid = txRefid;
    }

    public String getTxDescription() {
        return txDescription;
    }

    public void setTxDescription(String txDescription) {
        this.txDescription = txDescription;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public BigDecimal getCredit() {
        return credit;
    }

    public void setCredit(BigDecimal credit) {
        this.credit = credit;
    }

    public BigDecimal getWithdraw() {
        return withdraw;
    }

    public void setWithdraw(BigDecimal withdraw) {
        this.withdraw = withdraw;
    }

    public BigDecimal getBalanceAfter() {
        return balanceAfter;
    }

    public void setBalanceAfter(BigDecimal balanceAfter) {
        this.balanceAfter = balanceAfter;
    }

    public Long getCancelId() {
        return cancelId;
    }

    public void setCancelId(Long cancelId) {
        this.cancelId = cancelId;
    }
}

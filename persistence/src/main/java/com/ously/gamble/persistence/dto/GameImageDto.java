package com.ously.gamble.persistence.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

public record GameImageDto(
        @JsonProperty("gameId")
        long gameId,
        @JsonProperty("iType")
        String iType,
        @JsonProperty("realFormat")
        String realFormat,
        @JsonProperty("mimeType")
        String mimeType,
        @JsonProperty("updatedAt")
        LocalDateTime updatedAt,
        @JsonProperty("createdAt")
        LocalDateTime createdAt) implements Serializable {

    @Override
    public String toString() {
        return getClass().getSimpleName() + '(' +
                "gameId = " + gameId + ", " +
                "iType = " + iType + ", " +
                "realFormat = " + realFormat + ", " +
                "mimeType = " + mimeType + ", " +
                "createdAt = " + createdAt + ", " +
                "updatedAt = " + updatedAt + ')';
    }
}

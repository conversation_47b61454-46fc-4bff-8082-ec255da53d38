package com.ously.gamble.persistence.repository;

import com.ously.gamble.persistence.model.idclasses.UserPopupId;
import com.ously.gamble.persistence.model.popups.UserPopup;
import com.ously.gamble.persistence.projections.PopupKeyPJ;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface UserPopupRepository extends JpaRepository<UserPopup, UserPopupId> {

    String POPUPKEYSQL = """
            select concat(up.popup_id,':',up.selector) as 'key', DATE_FORMAT(up.expires_at,'%Y-%m-%dT%H:%i:%s.%fZ') as expiresAt, DATE_FORMAT(TIMESTAMPADD(MINUTE ,3, coalesce(ns.nextShow, FROM_UNIXTIME(0))),'%Y-%m-%dT%H:%i:%s.%fZ') as nextShow
            from user_popups up left join (select user_id,max(shown_at) as nextShow from user_popups where user_id= :userId and shown_at is not null group by user_id) ns on up.user_id=ns.user_id
            where up.user_id = :userId and up.shown_at is null and up.expires_at > CURRENT_TIMESTAMP order by created_at asc LIMIT 1
            """;


    List<UserPopup> findAllByUserId(long userId);


    /**
     * returns the next popup key + the earliest timestamp to show it!
     *
     * @param userId userid to use for query
     * @return a projection containing the key ("popupId:selector", expiresAt instant, earliest time to show it)
     */
    @Query(nativeQuery = true, value = POPUPKEYSQL)
    Optional<PopupKeyPJ> getPopupKeyForUserId(@Param("userId") long userId);

    @Modifying
    void deleteExpired();

    @Query(nativeQuery = true, value = "delete from user_popups where user_id = ?1")
    @Modifying
    void deleteAllByUserId(long id);
}

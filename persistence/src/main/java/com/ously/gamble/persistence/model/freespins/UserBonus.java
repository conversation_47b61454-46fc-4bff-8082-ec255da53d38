//package com.ously.gamble.persistence.model.freespins;
//
//import jakarta.persistence.*;
//import org.hibernate.annotations.GenericGenerator;
//import org.hibernate.annotations.Parameter;
//import org.springframework.data.domain.Persistable;
//import org.springframework.data.jpa.domain.support.AuditingEntityListener;
//
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.time.Instant;
//
//@Entity
//@EntityListeners(AuditingEntityListener.class)
//@Table(name = "user_bonuses")
//@IdClass(UserBonusId.class)
//public class UserBonus implements Serializable
//        , Persistable<UserBonusId> {
//
//    @Transient
//    boolean wasLoaded;
//
//    @PostLoad
//    @PostPersist
//    public void setTransientLoaded() {
//        this.wasLoaded = true;
//    }
//
//    @Override
//    public UserBonusId getId() {
//        return new UserBonusId(this.fsId, this.userId);
//    }
//
//    @Override
//    public boolean isNew() {
//        return !this.wasLoaded;
//    }
//
//    @Id
//    @GeneratedValue(generator = "userFS")
//    @GenericGenerator(name = "userFS", type = org.hibernate.id.enhanced.TableGenerator.class,
//            parameters = {
//                    @Parameter(name = "table_name", value = "custom_sequences"),
//                    @Parameter(name = "value_column_name",
//                            value = "sequence_next_hi_value"),
//                    @Parameter(name = "prefer_entity_table_as_segment_value",
//                            value = "true"),
//                    @Parameter(name = "optimizer", value = "pooled-lo"),
//                    @Parameter(name = "initial_value", value = "500"),
//                    @Parameter(name = "increment_size", value = "10"),
//                    @Parameter(name = "segment_value", value = "userfs")
//            })
//    @Column(name = "id")
//    Long fsId;
//    @Id
//    @Column(name = "user_id")
//    Long userId;
//
//    @Column(name = "game_id")
//    Long gameId;
//
//    @Column(name = "amount")
//    Integer amount;
//
//    @Column(name = "level")
//    Integer level;
//
//    @Column(name = "activated_at")
//    Instant activatedAt;
//
//    @Column(name = "created_at")
//    Instant createdAt;
//
//    @Column(name = "expire_at")
//    Instant expireAt;
//
//    @Column(name = "ext_fs_id", length = 80)
//    String extFsId;
//
//    @Column(name = "played_at")
//    Instant playedAt;
//
//    @Column(name = "bet")
//    BigDecimal bet;
//
//    @Column(name = "win")
//    BigDecimal win;
//
//    @Column(name = "wager_factor")
//    Integer wagerFactor;
//
//    @Column(name = "bonus_amount")
//    Integer bonusAmount;
//
//    @Column(name = "claim_id")
//    Long claimId;
//
//    @Column(name = "max_cashout")
//    BigDecimal maxCashout;
//
//    @Column(name = "zero_out_threshold")
//    BigDecimal zeroOutThreshold;
//
//    @Column(name = "bets")
//    BigDecimal bets = BigDecimal.ZERO;
//
//    @Column(name = "wins")
//    BigDecimal wins = BigDecimal.ZERO;
//
//    @Column(name = "rollover_amount")
//    BigDecimal rolloverAmount = BigDecimal.ZERO;
//
//    @Column(name = "finished_at")
//    Instant finishedAt;
//
//    @Column(name = "status")
//    @Enumerated(EnumType.STRING)
//    UserBonusStatus status;
//
//    public String getExtFsId() {
//        return extFsId;
//    }
//
//    public void setExtFsId(String extFsId) {
//        this.extFsId = extFsId;
//    }
//
//    public Long getFsId() {
//        return fsId;
//    }
//
//    public void setFsId(Long fsId) {
//        this.fsId = fsId;
//    }
//
//    public Long getUserId() {
//        return userId;
//    }
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }
//
//    public Long getGameId() {
//        return gameId;
//    }
//
//    public void setGameId(Long gameId) {
//        this.gameId = gameId;
//    }
//
//    public Integer getAmount() {
//        return amount;
//    }
//
//    public void setAmount(Integer amount) {
//        this.amount = amount;
//    }
//
//    public Integer getLevel() {
//        return level;
//    }
//
//    public void setLevel(Integer level) {
//        this.level = level;
//    }
//
//    public Instant getActivatedAt() {
//        return activatedAt;
//    }
//
//    public void setActivatedAt(Instant activatedAt) {
//        this.activatedAt = activatedAt;
//    }
//
//    public Instant getCreatedAt() {
//        return createdAt;
//    }
//
//    public void setCreatedAt(Instant createdAt) {
//        this.createdAt = createdAt;
//    }
//
//    public Instant getExpireAt() {
//        return expireAt;
//    }
//
//    public void setExpireAt(Instant expireAt) {
//        this.expireAt = expireAt;
//    }
//
//    public Instant getPlayedAt() {
//        return playedAt;
//    }
//
//    public void setPlayedAt(Instant playedAt) {
//        this.playedAt = playedAt;
//    }
//
//    public BigDecimal getBet() {
//        return bet;
//    }
//
//    public void setBet(BigDecimal bet) {
//        this.bet = bet;
//    }
//
//    public BigDecimal getWin() {
//        return win;
//    }
//
//    public void setWin(BigDecimal win) {
//        this.win = win;
//    }
//
//    public Integer getWagerFactor() {
//        return wagerFactor;
//    }
//
//    public void setWagerFactor(Integer wagerFactor) {
//        this.wagerFactor = wagerFactor;
//    }
//
//    public Integer getBonusAmount() {
//        return bonusAmount;
//    }
//
//    public void setBonusAmount(Integer bonusAmount) {
//        this.bonusAmount = bonusAmount;
//    }
//
//    public Long getClaimId() {
//        return claimId;
//    }
//
//    public void setClaimId(Long claimId) {
//        this.claimId = claimId;
//    }
//
//    public BigDecimal getMaxCashout() {
//        return maxCashout;
//    }
//
//    public void setMaxCashout(BigDecimal maxCashout) {
//        this.maxCashout = maxCashout;
//    }
//
//    public BigDecimal getZeroOutThreshold() {
//        return zeroOutThreshold;
//    }
//
//    public void setZeroOutThreshold(BigDecimal zeroOutThreshold) {
//        this.zeroOutThreshold = zeroOutThreshold;
//    }
//
//    public BigDecimal getBets() {
//        return bets;
//    }
//
//    public void setBets(BigDecimal bets) {
//        this.bets = bets;
//    }
//
//    public BigDecimal getWins() {
//        return wins;
//    }
//
//    public void setWins(BigDecimal wins) {
//        this.wins = wins;
//    }
//
//    public BigDecimal getRolloverAmount() {
//        return rolloverAmount;
//    }
//
//    public void setRolloverAmount(BigDecimal rolloverAmount) {
//        this.rolloverAmount = rolloverAmount;
//    }
//
//    public Instant getFinishedAt() {
//        return finishedAt;
//    }
//
//    public void setFinishedAt(Instant finishedAt) {
//        this.finishedAt = finishedAt;
//    }
//
//    public UserBonusStatus getStatus() {
//        return status;
//    }
//
//    public void setStatus(UserBonusStatus status) {
//        this.status = status;
//    }
//}
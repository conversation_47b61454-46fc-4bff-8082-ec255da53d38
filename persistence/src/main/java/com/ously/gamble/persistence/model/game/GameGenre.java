package com.ously.gamble.persistence.model.game;

public enum GameGenre {
    ANDAR_BAHAR("Andar Bahar"),
    BACCARAT("Baccarat"),
    BINGO("Bingo"),
    BLACKJACK("Blackjack"),
    CRAPS("Craps"),
    CRASH("Crash"),
    DICE("Di<PERSON>"),
    <PERSON><PERSON><PERSON><PERSON>TIGER("Dragon Tiger"),
    HI_LO("Hi Lo"),
    KENO("Keno"),
    LIVE_BLACKJACK("Live Blackjack"),
    LIVE_ROULETTE("Live Roulette"),
    LOBBY("Lobby"),
    MINE("Mine"),
    PLINKO("<PERSON>linko"),
    <PERSON><PERSON>ER("Poker"),
    ROULETTE("Roulette"),
    SCRATCHCARDS("Scratchcards"),
    SETTE_E_MEZZO("Sette e Mezzo"),
    <PERSON>OW("Show"),
    SIC_BO("Sic Bo"),
    SLOTS("Slots"),
    VIDEO_POKER("Video Poker"),
    <PERSON><PERSON><PERSON>("Other"),
    <PERSON>DE<PERSON>NE<PERSON>("Undefined");

    private final String aval;

    GameGenre(String aval) {
        this.aval = aval;
    }

    String aval() {
        return aval;
    }

}

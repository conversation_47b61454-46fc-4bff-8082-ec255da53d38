package com.ously.gamble.persistence.dto;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

public class DuplicateDeviceItem {

    long latestuid;
    String fcmToken;
    List<Long> uids;

    public DuplicateDeviceItem() {
    }

    public DuplicateDeviceItem(String fcmToken, String uids, Long cntUids) {
        this.fcmToken = fcmToken;
        try {
            this.uids = Arrays.stream(uids.split(",")).map(Long::valueOf).toList();
            this.latestuid = this.uids.getFirst();
        } catch (Exception ignore1) {
            //;
        }
    }

    public long getLatestuid() {
        return latestuid;
    }

    public void setLatestuid(long latestuid) {
        this.latestuid = latestuid;
    }

    public String getFcmToken() {
        return fcmToken;
    }

    public void setFcmToken(String fcmToken) {
        this.fcmToken = fcmToken;
    }

    public List<Long> getUids() {
        return uids;
    }

    public void setUids(List<Long> uids) {
        this.uids = uids;
    }


    @Override
    public String toString() {
        return "DuplicateDeviceItem{" +
                "latestuid=" + latestuid +
                ", fcmToken='" + StringUtils.abbreviate(fcmToken, 30) +
                ", countTks=" + uids.size() +
                '}';
    }
}

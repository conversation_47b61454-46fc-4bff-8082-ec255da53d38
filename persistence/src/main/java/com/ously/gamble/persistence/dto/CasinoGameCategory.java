package com.ously.gamble.persistence.dto;

import com.ously.gamble.persistence.model.game.GameCategory;
import com.ously.gamble.persistence.model.game.GameCategory.CategoryType;
import com.ously.gamble.persistence.projections.CategoryGameDto;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.util.Objects;

public class CasinoGameCategory implements Serializable {
    @NotNull
    int id;
    @NotNull
    String name;
    @NotNull
    CategoryType type;

    Integer groupId;
    @NotNull
    boolean hidden;

    public CasinoGameCategory() {
    }

    public CasinoGameCategory(int id, String name, CategoryType type, Integer groupId,
                              boolean hidden) {
        this.id = id;
        this.name = name;
        this.type = type;
        if (groupId < 0) {
            this.groupId = null;
        } else {
            this.groupId = groupId;
        }
        this.hidden = hidden;
    }

    public CasinoGameCategory(GameCategory gc) {
        this.id = gc.getId();
        this.name = gc.getName();
        this.type = gc.getType();
        if (gc.getParentId() != null) {
            this.groupId = gc.getParentId();
        } else {
            this.groupId = null;
        }
        this.hidden = gc.isHidden();
    }

    public CasinoGameCategory(CategoryGameDto pj) {
        this.id = pj.categoryId();
        this.name = pj.categoryName();
        this.type = pj.categoryType();
        this.hidden = pj.hidden();
        this.groupId = (pj.parentId() == null) ? null : pj.parentId();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        var that = (CasinoGameCategory) o;
        return id == that.id &&
               Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        var result = id;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        return result;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isHidden() {
        return hidden;
    }

    public void setHidden(boolean hidden) {
        this.hidden = hidden;
    }

    public CategoryType getType() {
        return type;
    }

    public void setType(CategoryType type) {
        this.type = type;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }
}

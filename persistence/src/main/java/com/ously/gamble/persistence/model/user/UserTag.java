package com.ously.gamble.persistence.model.user;

import com.ously.gamble.persistence.dto.CasinoUserTag;
import jakarta.persistence.*;

@Entity
@Table(name = "user_tag")
@SqlResultSetMapping(
        name = "Mapping.CasinoUserTag", classes = @ConstructorResult(
        targetClass = CasinoUserTag.class,
        columns = {
                @ColumnResult(name = "id", type = Integer.class),
                @ColumnResult(name = "name", type = String.class),
                @ColumnResult(name = "description", type = String.class),
                @ColumnResult(name = "rule", type = String.class),
                @ColumnResult(name = "num", type = int.class)
        }
)
)
@NamedNativeQuery(name = "UserTag.findAllCasinoUserTags",
                  query = """
select ut.id as id, ut.name as name, ut.description as description, ut.rules as rule, coalesce(utc.num,0) as num from user_tag ut left join (
        select tag_id as id, count(*) as num from user_tags group by tag_id
        ) utc on ut.id=utc.id
                                        """,
                  resultSetMapping = "Mapping.CasinoUserTag")
@NamedNativeQuery(name = "UserTag.loadDtoByTagName",
                  query = """
select ut.id as id, ut.name as name, ut.description as description, ut.rules as rule, coalesce(utc.num,0) as num from user_tag ut left join (
        select tag_id as id, count(*) as num from user_tags group by tag_id
        ) utc on ut.id=utc.id where ut.name= ?1
                                        """,
                  resultSetMapping = "Mapping.CasinoUserTag")
@NamedNativeQuery(name = "UserTag.loadDtoByTagId",
                  query = """
select ut.id as id, ut.name as name, ut.description as description, ut.rules as rule, coalesce(utc.num,0) as num from user_tag ut left join (
        select tag_id as id, count(*) as num from user_tags group by tag_id
        ) utc on ut.id=utc.id where ut.id= ?1
                                        """,
                  resultSetMapping = "Mapping.CasinoUserTag")
public class UserTag {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    Integer id;

    @Column(name = "name", length = 20)
    String name;

    @Column(name = "description", length = 100)
    String description;

    @Column(name = "rules")
    String rule;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }
}

package com.ously.gamble.persistence.model.idclasses;

import java.io.Serializable;
import java.time.Instant;

public class ColdSessionRoundId implements Serializable {
    String roundReference;
    Long sessionId;
    Instant createdAt;

    public ColdSessionRoundId() {
    }

    public ColdSessionRoundId(String roundRef, Long sessionId, Instant crAt) {
        this.roundReference = roundRef;
        this.sessionId = sessionId;
        this.createdAt = crAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        var that = (ColdSessionRoundId) o;

        return roundReference.equals(that.roundReference) && sessionId.equals(that.sessionId) && createdAt.equals(that.createdAt);
    }

    @Override
    public int hashCode() {
        var result = roundReference.hashCode();
        result = 31 * result + sessionId.hashCode();
        result = 31 * result + createdAt.hashCode();
        return result;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getRoundReference() {
        return roundReference;
    }

    public void setRoundReference(String roundReference) {
        this.roundReference = roundReference;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }


}

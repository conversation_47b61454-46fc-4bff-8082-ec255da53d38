package com.ously.gamble.persistence.model.user;

import com.ously.gamble.persistence.dto.*;
import com.ously.gamble.persistence.model.AccountStatus;
import com.ously.gamble.persistence.model.audit.DateAudit;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Base64;
import java.util.Random;

@Entity
@Table(name = "users",
        uniqueConstraints = {@UniqueConstraint(columnNames = {"local_id"}), @UniqueConstraint(
                columnNames = {"email"})})


@NamedNativeQuery(name = "User.loadCasinoUser", query = """
                        SELECT u.id,
                        u.extid as extId,
                      u.display_name,
                      u.email,
                      u.status,
                      u.system_user,
                      ui.mobile,
                      ui.birthDate,
                      ui.gender,
                      ui.agb,
                      ui.name,
                      ui.surname,
                      ui.pref_lang,
                      u.created_at,
                      u.updated_at,
                      u.local_id,
                       ac.code                                                            as aff_code,
                      (select GROUP_CONCAT(t.name) as tags from user_tags ut join  user_tag t on ut.tag_id = t.id and ut.user_id=?1 ) as tags,
                      u.country,
                      ui.adr_city as city,
                      ui.adr_street as street,
                      ui.adr_zipcode as postcode,
                      ui.birthplace as birthplace
               FROM users u
                        LEFT JOIN user_info ui on u.id = ui.user_id
                                 LEFT JOIN aff_links al on u.id = al.ref_id
                                 LEFT JOIN aff_campaigns ac on al.user_id = ac.user_id and al.num = ac.num
               where u.id = ?1
        """, resultSetMapping = "Mapping.CasinoUser")

@NamedNativeQuery(name = "User.getUserBalanceStat", query = """
        select userId,
               type,
               detail,
               start,
               startBalance,
               end,
               endBalance,
               endBalance - startBalance as saldo
        from (select gs.user_id                                   as userId,
                     'GameSession'                                as type,
                     g.name                                       as detail,
                     gs.start_at                                  as start,
                     s.start_balance                             as startBalance,
                     gs.end_at                                    as end,
                     (s.start_balance + gs.sum_win/100 - gs.sum_bet/100) as endBalance
              from  session_statistics_ju gb join session_statistics gs on gb.session_id=gs.session_id join session s on gs.user_id=s.user_id and gs.session_id=s.session_id join games g on s.game_id=g.id
              where
                 gs.num_bets > 0
                and gs.start_at >= ?2
                and gs.start_at < ?3
                and gb.rdate >= DATE(?2)
                and gb.rdate <= DATE(?3)
                and gb.user_id = ?1
                and gs.user_id = ?1
              union all
              select t.user_id                                                     as userId,
                     IF(t.ext_tx_id like 'BonusAchievement for level%', 'Level Bonus',
                        IF(t.ext_tx_id like 'coins for ext%', 'UserInfoBonus',
                           IF(t.round_ref like 'SPIN:%', 'WheelSpin',
                              IF(t.type = 'PURCHASE', 'Purchase', 'LoginBonus')))) as type,
                     t.ext_tx_id                                                   as detail,
                     t.created_at                                                  as start,
                     (t.balance_after - t.win)                                     as startBalance,
                     t.created_at                                                  as end,
                     t.balance_after                                               as endBalance
              from ctransactions t
              where t.vendor_name = 'OUSLY'
                and win > 0
                and t.type IN ('BONUS', 'ACHIEVEMENT', 'PURCHASE')
                and created_at >= ?2
                and created_at < ?3
                and user_id = ?1
              union all
              select t.user_id as                                                                                                         userId,
                     IF(t.ext_tx_id like 'BonusAchievement for level%', 'Level Bonus',
                        IF(t.ext_tx_id like 'coins for ext%', 'UserInfoBonus', IF(t.round_ref like 'SPIN:%', 'WheelSpin',
                                                                                  IF(t.type = 'PURCHASE', 'Purchase', 'LoginBonus')))) as type,
                     t.ext_tx_id as                                                                                                       detail,
                     t.created_at as                                                                                                      start,
                     (t.balance_after - t.win) as                                                                                         startBalance,
                     t.created_at as                                                                                                      end,
                     t.balance_after as                                                                                                   endBalance
              from transactions t
              where t.vendor_name = 'OUSLY'
                and win > 0
                and t.type IN ('BONUS', 'ACHIEVEMENT', 'PURCHASE')
                and created_at >= ?2
                and created_at < ?3
                and user_id = ?1
              union all
              select ut.user_id                                   as userId,
                     ut.type                                      as type,
                     ut.description                               as detail,
                     ut.created_at                                as start,
                     (ut.balance_after - ut.credit + ut.withdraw) as startBalance,
                     ut.created_at                                as end,
                     ut.balance_after                             as endBalance
              from user_transaction ut
              where ut.credit > 0
                and ut.created_at >= ?2
                and ut.created_at < ?3
                and user_id = ?1) view
        order by start desc""", resultSetMapping = "Mapping.UserBalanceStatsDtoMapping")


@NamedNativeQuery(name = "User.loadCRMRevData", query = """
             select
            coalesce(sum(case when wkd < 2 then cost else 0 end),0) as revPerWeek1,
            coalesce(sum(case when wkd < 3 then cost else 0 end)/2,0) as revPerWeek2,
            coalesce(sum(case when wkd < 5 then cost else 0 end)/4,0) as revPerWeek4,
            coalesce(sum(case when wkd < 9 then cost else 0 end)/8,0) as revPerWeek8,
            coalesce(sum(case when wkd < 13 then cost else 0 end)/12,0) as revPerWeek12,
            coalesce(sum(case when wkd < 2 then cost else 0 end)/sum(case when wkd < 2 then 1 else 0 end),0) as pkgsAvgPerWeek1,
            coalesce(sum(case when wkd < 3 then cost else 0 end)/sum(case when wkd < 3 then 1 else 0 end),0) as pkgsAvgPerWeek2,
            coalesce(sum(case when wkd < 5 then cost else 0 end)/sum(case when wkd < 5 then 1 else 0 end),0) as pkgsAvgPerWeek4,
            coalesce(sum(case when wkd < 9 then cost else 0 end)/sum(case when wkd < 9 then 1 else 0 end),0) as pkgsAvgPerWeek8,
            coalesce(sum(case when wkd < 13 then cost else 0 end)/sum(case when wkd < 13 then 1 else 0 end),0) as pkgsAvgPerWeek12,
            coalesce(sum(cost),0) as totalRev,
            coalesce(max(wkd),0) as earliestRevWeek,
            coalesce(min(wkd),0) as latestRevWeek
        from (
            select
                ROUND(DATEDIFF(CURRENT_DATE,created_at)/7,0) as wkd,
                applied_cost as cost
            from purchases
            where user_id = ?1
            and status = 'SUCCESS'
            order by created_at desc
        ) purchasesByWeek""",
        resultSetMapping = "Mapping.UserCrmRevMapping")


@SqlResultSetMapping(name = "Mapping.UserCrmRevMapping",
        classes = @ConstructorResult(targetClass = CRMUserRevDetails.class,
                columns = {
                        @ColumnResult(name = "revPerWeek1",
                                type = BigDecimal.class),
                        @ColumnResult(name = "revPerWeek2",
                                type = BigDecimal.class),
                        @ColumnResult(name = "revPerWeek4",
                                type = BigDecimal.class),
                        @ColumnResult(name = "revPerWeek8",
                                type = BigDecimal.class),
                        @ColumnResult(name = "revPerWeek12",
                                type = BigDecimal.class),
                        @ColumnResult(name = "totalRev",
                                type = BigDecimal.class),
                        @ColumnResult(name = "earliestRevWeek",
                                type = Integer.class),
                        @ColumnResult(name = "latestRevWeek",
                                type = Integer.class),
                        @ColumnResult(name = "pkgsAvgPerWeek1",
                                type = BigDecimal.class),
                        @ColumnResult(name = "pkgsAvgPerWeek2",
                                type = BigDecimal.class),
                        @ColumnResult(name = "pkgsAvgPerWeek4",
                                type = BigDecimal.class),
                        @ColumnResult(name = "pkgsAvgPerWeek8",
                                type = BigDecimal.class),
                        @ColumnResult(name = "pkgsAvgPerWeek12",
                                type = BigDecimal.class)
                }
        )
)


@NamedNativeQuery(name = "User.loadCRMData",
        query = """
                select u.id as userId,
                   u.status                                                      as status,
                COALESCE(mkt.email, IF(u.email LIKE '%@nomail.nomail', NULL, u.email)) AS email,
                   u.display_name,
                   CAST(UNIX_TIMESTAMP(u.created_at) AS SIGNED)                  as created_at,
                   COALESCE(u.signin_provider, '')                               as signin_provider,
                   COALESCE(ur.login_provider, '')                               as login_provider,
                   u.local_id                                                    as localId,
                   COALESCE(ui.agb, true)                                        as agb,
                   DATE_FORMAT(ui.birthdate, '%Y-%m-%d')                         as birthdate,
                   COALESCE(ui.directContact, true)                              as directContact,
                   upper(coalesce(ui.gender, 'X'))                               as gender,
                   COALESCE(ui.mobile, '')                                       as mobile,
                   COALESCE(ui.newsletter, true)                                 as newsletter,
                   COALESCE(ui.name, '')                                         as name,
                   COALESCE(ui.surname, '')                                      as surname,
                   w.balance,
                   w.level                                                       as level,
                   CAST(coalesce(w.percnl * 100, 0.0) as SIGNED INTEGER)         as levelprogress,
                   CAST(coalesce(w.saveup, 0.0) as SIGNED INTEGER)               as piggybank,
                    CAST(coalesce(xpu.appcost, 0) as DECIMAL(15, 2))        as sumPurchases,
                    CAST(UNIX_TIMESTAMP(xpulatest) as SIGNED INTEGER) as latestPurchaseDate,
                    CAST(xpucnt as SIGNED INTEGER)              as numPurchases,
                   COALESCE(ui.pref_lang, 'DE')                                  as language,
                   uss.tags                                                      as tags,
                   coalesce(uabs.avgBet,0)/100                                       as avgBet
                    from users u
                     join wallets w on u.id = w.user_id
                    cross join (select  sum(gs.sum_bet)/sum(gs.num_bets)as avgBet from session s join session_statistics gs on s.session_id = gs.session_id and s.user_id=gs.user_id
                         where s.user_id=?1 and s.created_at >= DATE_SUB(current_date,INTERVAL 30 DAY)
                        and gs.num_bets>0) uabs
                      cross join (select sum(applied_cost) as appcost, max(created_at) as xpulatest,count(*) as xpucnt from purchases purch where purch.status = 'SUCCESS' and user_id=?1) xpu
                     left join user_statistics uss on u.id=uss.user_id
                     left join user_registration ur on u.local_id = ur.local_id
                     left join user_info ui on u.id = ui.user_id
                         LEFT JOIN (
                             SELECT
                                 user_id,
                                 email
                             FROM
                                 marketing_emails
                             WHERE verified_at IS NOT NULL
                             ORDER BY num DESC LIMIT 1
                         ) mkt ON u.id = mkt.user_id
                    where u.id = ?1""",
        resultSetMapping = "Mapping.UserCrmMapping")


@NamedNativeQuery(name = "User.loadPlayerProfile",
        query = """
                select
                                     u.extid as extId,
                                     u.display_name as username,
                                     w.level as level,
                                     DATE(us.first_login) as activeSince,
                                     us.ss_session_count as sessions,
                                     us.ss_num_spins as spins,
                                     us.ss_sum_bet as sumBets,
                                     us.ss_sum_win as sumWins,
                                     coalesce(uga.favorites,'') as favorites,
                                     coalesce(uga.playlist,'') as lastGamesPlayed,
                                 coalesce(max(gs.max_bet),0)/100 as maxBet,
                                 coalesce(max(gs.max_win),0)/100 as maxWin,
                                 coalesce(max(gs.max_mult),0) as maxMult,
                                     coalesce(count(distinct gs.game_id),0) as slotsPlayed,
                                     CAST( coalesce( mostPlayed1 ,'') AS CHAR(400) CHARACTER SET utf8) as mostPlayed
                                from users u
                                     join wallets w on u.id = w.user_id
                                     left join user_statistics us on u.id = us.user_id
                                     left join user_game_attributes uga on u.id = uga.id
                                     left join session s on u.id = s.user_id
                                       left join session_statistics gs on s.session_id = gs.session_id
                                     left join (select user_id, group_concat(mostPlayedS) as mostPlayed1 from (
                                     select s.user_id,concat(s.game_id,'=',count( *) )  as mostPlayedS from
                                    session s join session_statistics gss on s.session_id = gss.session_id join users u on s.user_id=u.id where u.extid = ?1 and (sum_bet+sum_win>0) group by s.user_id,s.game_id order by count(*) desc limit 20) x group by x.user_id) x on u.id=x.user_id
                                     where u.extid = ?1 group by u.extid
                """,
        resultSetMapping = "Mapping.PlayerProfile")

@SqlResultSetMapping(name = "Mapping.PlayerProfile",
        classes = @ConstructorResult(targetClass = PlayerProfileDto.class,
                columns = {
                        @ColumnResult(name = "extId",
                                type = String.class),
                        @ColumnResult(name = "username",
                                type = String.class),
                        @ColumnResult(name = "level",
                                type = int.class),
                        @ColumnResult(name = "activeSince",
                                type = String.class),
                        @ColumnResult(name = "sessions",
                                type = long.class),
                        @ColumnResult(name = "spins",
                                type = long.class),
                        @ColumnResult(name = "sumBets",
                                type = BigDecimal.class),
                        @ColumnResult(name = "sumWins",
                                type = BigDecimal.class),
                        @ColumnResult(name = "favorites",
                                type = String.class),
                        @ColumnResult(name = "lastGamesPlayed",
                                type = String.class),
                        @ColumnResult(name = "maxBet",
                                type = BigDecimal.class),
                        @ColumnResult(name = "maxWin",
                                type = BigDecimal.class),
                        @ColumnResult(name = "maxMult",
                                type = BigDecimal.class),
                        @ColumnResult(name = "slotsPlayed",
                                type = int.class),
                        @ColumnResult(name = "mostPlayed",
                                type = String.class)
                }
        )
)


@SqlResultSetMapping(name = "Mapping.UserCrmMapping",
        classes = @ConstructorResult(targetClass = CRMUserDetails.class,
                columns = {
                        @ColumnResult(name = "userId",
                                type = BigInteger.class),
                        @ColumnResult(name = "status",
                                type = String.class),
                        @ColumnResult(name = "email",
                                type = String.class),
                        @ColumnResult(name = "display_name",
                                type = String.class),
                        @ColumnResult(name = "created_at",
                                type = BigInteger.class),
                        @ColumnResult(name = "signin_provider",
                                type = String.class),
                        @ColumnResult(name = "login_provider",
                                type = String.class),
                        @ColumnResult(name = "localId",
                                type = String.class),
                        @ColumnResult(name = "agb",
                                type = Boolean.class),
                        @ColumnResult(name = "birthdate",
                                type = String.class),
                        @ColumnResult(name = "directContact",
                                type = Boolean.class),
                        @ColumnResult(name = "gender",
                                type = String.class),
                        @ColumnResult(name = "mobile",
                                type = String.class),
                        @ColumnResult(name = "newsletter",
                                type = Boolean.class),
                        @ColumnResult(name = "name",
                                type = String.class),
                        @ColumnResult(name = "surname",
                                type = String.class),
                        @ColumnResult(name = "balance",
                                type = BigDecimal.class),
                        @ColumnResult(name = "level",
                                type = BigInteger.class),
                        @ColumnResult(name = "levelprogress",
                                type = BigInteger.class),
                        @ColumnResult(name = "piggybank",
                                type = BigInteger.class),
                        @ColumnResult(name = "sumPurchases",
                                type = BigDecimal.class),
                        @ColumnResult(name = "latestPurchaseDate",
                                type = BigInteger.class),
                        @ColumnResult(name = "numPurchases",
                                type = BigInteger.class),
                        @ColumnResult(name = "language",
                                type = String.class),
                        @ColumnResult(name = "tags",
                                type = String.class),
                        @ColumnResult(name = "avgBet",
                                type = BigDecimal.class)

                }))

@SqlResultSetMapping(
        name = "Mapping.UserBalanceStatsDtoMapping", classes = @ConstructorResult(
        targetClass = UserBalanceStatsDto.class,
        columns = {
                @ColumnResult(name = "userId", type = long.class),
                @ColumnResult(name = "type", type = String.class),
                @ColumnResult(name = "detail", type = String.class),
                @ColumnResult(name = "start", type = Instant.class),
                @ColumnResult(name = "end", type = Instant.class),
                @ColumnResult(name = "startBalance", type = Double.class),
                @ColumnResult(name = "endBalance", type = Double.class),
                @ColumnResult(name = "saldo", type = Double.class),
        }
)
)
@SqlResultSetMapping(
        name = "Mapping.CasinoUser", classes = @ConstructorResult(
        targetClass = CasinoUser.class,
        columns = {
                @ColumnResult(name = "id", type = Long.class),
                @ColumnResult(name = "extId", type = String.class),
                @ColumnResult(name = "display_name", type = String.class),
                @ColumnResult(name = "email", type = String.class),
                @ColumnResult(name = "status", type = String.class),
                @ColumnResult(name = "system_user", type = Boolean.class),
                @ColumnResult(name = "mobile", type = String.class),
                @ColumnResult(name = "birthDate", type = LocalDate.class),
                @ColumnResult(name = "gender", type = String.class),
                @ColumnResult(name = "agb", type = Boolean.class),
                @ColumnResult(name = "name", type = String.class),
                @ColumnResult(name = "surname", type = String.class),
                @ColumnResult(name = "pref_lang", type = String.class),
                @ColumnResult(name = "created_at", type = Instant.class),
                @ColumnResult(name = "updated_at", type = Instant.class),
                @ColumnResult(name = "local_id", type = String.class),
                @ColumnResult(name = "aff_code", type = String.class),
                @ColumnResult(name = "tags", type = String.class),
                @ColumnResult(name = "country", type = String.class),
                @ColumnResult(name = "city", type = String.class),
                @ColumnResult(name = "street", type = String.class),
                @ColumnResult(name = "postcode", type = String.class),
                @ColumnResult(name = "birthplace", type = String.class)
        }
)
)
public class User extends DateAudit {
    @Id
    @GeneratedValue(generator = "pooledUser")
    @GenericGenerator(name = "pooledUser", type = org.hibernate.id.enhanced.TableGenerator.class,
            parameters = {@Parameter(name = "table_name",
                    value = "custom_sequences"), @Parameter(
                    name = "value_column_name",
                    value = "sequence_next_hi_value"), @Parameter(
                    name = "prefer_entity_table_as_segment_value",
                    value = "true"), @Parameter(name = "optimizer",
                    value = "pooled-lo"), @Parameter(
                    name = "initial_value", value = "100000"), @Parameter(
                    name = "increment_size", value = "1000"), @Parameter(
                    name = "segment_value", value = "users")})
    Long id;

    @Column(name = "local_id", length = 150)
    private String localId;

    @NotBlank
    @Size(max = 128)
    @Column(name = "display_name", length = 100)
    private String displayName;

    @Column(name = "system_user")
    private Boolean systemUser = Boolean.FALSE;

    @NotBlank
    @Size(max = 120)
    @Email
    private String email;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    private AccountStatus status = AccountStatus.ACTIVE;

    @Column(name = "signin_provider", length = 70)
    private String signinProvider;

    @Column(name = "email_verified")
    private Boolean emailVerified;

    @Column(name = "crm_update")
    private Instant crmUpdateTimestamp;

    @Column(name = "country")
    private String country = "XX";

    @Column(name = "extid", nullable = false)
    private String extId;

    public User() {
    }

    public User(String username, String email, AccountStatus status) {
        this.displayName = username;
        this.email = email;
        this.status = status;
    }

    @PrePersist
    void preparePersist() {
        if (this.extId == null) {
            this.extId = createHashStr(6);
        }
    }


    public Instant getCrmUpdateTimestamp() {
        return crmUpdateTimestamp;
    }

    public void setCrmUpdateTimestamp(Instant crmUpdateTimestamp) {
        this.crmUpdateTimestamp = crmUpdateTimestamp;
    }

    public AccountStatus getStatus() {
        return status;
    }

    public void setStatus(AccountStatus status) {
        this.status = status;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Transient
    public String createHashStr(int numBytes) {
        Random rand = new Random(System.nanoTime());
        byte[] randomBytes = new byte[numBytes];
        rand.nextBytes(randomBytes);
        var rndStr = Base64.getEncoder().encodeToString(randomBytes);
        char[] repl = new char[]{(char) (65 + rand.nextInt(24)), (char) (65 + rand.nextInt(24)), (char) (65 + rand.nextInt(24))};
        return StringUtils.replaceChars(rndStr, "+/=", String.copyValueOf(repl));
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String username) {
        this.displayName = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Transient
    public boolean isBlocked() {
        return (status == AccountStatus.BLOCKED || status == AccountStatus.BANNED);
    }

    public String getLocalId() {
        return localId;
    }

    public void setLocalId(String localId) {
        this.localId = localId;
    }

    public Boolean getSystemUser() {
        return systemUser;
    }

    public void setSystemUser(Boolean systemUser) {
        this.systemUser = systemUser;
    }

    public String getSigninProvider() {
        return signinProvider;
    }

    public void setSigninProvider(String signinProvider) {
        this.signinProvider = signinProvider;
    }

    public Boolean getEmailVerified() {
        return emailVerified;
    }

    public void setEmailVerified(Boolean emailVerified) {
        this.emailVerified = emailVerified;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getExtId() {
        return extId;
    }

    public void setExtId(String extId) {
        this.extId = extId;
    }
}



package com.ously.gamble.persistence.projections;

import java.math.BigDecimal;

public interface SessionStatsDayPJ {
    Long getGameId();

    String getDate();

    BigDecimal getSaldo();

    BigDecimal getSpins();

    BigDecimal getBets();

    BigDecimal getWins();

    BigDecimal getBonus();

    BigDecimal getMaxWin();

    BigDecimal getMaxBet();

    BigDecimal getMaxMultiplier();

    Long getSessions();
}

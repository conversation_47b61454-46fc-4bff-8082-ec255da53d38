package com.ously.gamble.persistence.repository.statistics;

import com.ously.gamble.persistence.dto.ConsumablePerformanceReportItemDto;
import com.ously.gamble.persistence.dto.PurchaseReportItemDto;
import com.ously.gamble.persistence.dto.PurchaseStatisticsFullDto;
import com.ously.gamble.persistence.dto.RevRolling30Days;
import com.ously.gamble.persistence.model.Purchase;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

@Repository
@ConditionalOnProperty(prefix = "features", name = "jurisdiction", havingValue = "SOC", matchIfMissing = true)
@ConditionalOnBean(name = "statisticsConfiguration")
public interface PurchaseStatisticsRepository extends JpaRepository<Purchase, Long> {

    @Query(nativeQuery = true)
    List<PurchaseStatisticsFullDto> getPurchaseStatisticsUsingRange(LocalDateTime from,
                                                                    LocalDateTime to);

    @Query(nativeQuery = true)
    List<PurchaseStatisticsFullDto> getFullPurchaseStatisticsUsingRange(LocalDateTime from,
                                                                        LocalDateTime to);

    @Query(nativeQuery = true)
    List<PurchaseReportItemDto> getDailyBreakdown(Instant from, Instant to);

    @Query(nativeQuery = true)
    List<PurchaseReportItemDto> getHourlyBreakdown(Instant from, Instant to);

    @Query(nativeQuery = true)
    List<PurchaseReportItemDto> getWeekdayBreakdown(Instant from, Instant to);

    @Query(nativeQuery = true)
    List<ConsumablePerformanceReportItemDto> getConsumablePerformance(Instant from, Instant to);

    @Query(nativeQuery = true)
    List<RevRolling30Days> getRevRolling30Days();

}
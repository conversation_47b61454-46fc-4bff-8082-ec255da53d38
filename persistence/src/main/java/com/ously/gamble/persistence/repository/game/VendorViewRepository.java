package com.ously.gamble.persistence.repository.game;

import com.ously.gamble.persistence.model.game.QVendorView;
import com.ously.gamble.persistence.model.game.VendorView;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.galegofer.spring.data.querydsl.value.operators.ExpressionProviderFactory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.stereotype.Repository;

import java.time.Instant;

@SuppressWarnings("NullableProblems")
@Repository
public interface VendorViewRepository extends JpaRepository<VendorView, Integer>, QuerydslPredicateExecutor<VendorView>, QuerydslBinderCustomizer<QVendorView> {

    @Override
    default void customize(QuerydslBindings bindings, QVendorView root) {
        bindings.bind(String.class).first(
                (SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);

        bindings.bind(String.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Long.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Integer.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Double.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Boolean.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Instant.class).all(ExpressionProviderFactory::getPredicate);
    }


}
package com.ously.gamble.persistence.model.game;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public enum GamePlatform {

    WEB((byte) 1),
    IOS((byte) 2),
    ANDROID((byte) 3),
    TOPSLOTS((byte) 4),
    DESKTOP((byte) 5),
    TEST((byte) 6),
    MOBILE((byte) 7),
    UNKNOWN((byte) 99);

    private final byte shortCode;

    private static final Map<Byte, GamePlatform> mMap = initializeMapping();

    private static Map<Byte, GamePlatform> initializeMapping() {
        return Arrays.stream(GamePlatform.values()).collect(Collectors.toUnmodifiableMap(GamePlatform::shortCode, Function.identity()));
    }

    GamePlatform(byte shortCode){
        this.shortCode=shortCode;
    }

    public byte shortCode(){
        return shortCode;
    }

    public static GamePlatform fromShortCode(byte code){
        return mMap.getOrDefault(code, GamePlatform.UNKNOWN);
    }

}

CREATE TABLE `jd_country`
(
    `code`    CHAR(2)      NOT NULL,
    `name`    varchar(100) NOT NULL,
    `boycot`  bit(1)       NOT NULL DEFAULT b'0',
    `blocked` bit(1)       NOT NULL DEFAULT b'0',
    PRIMARY KEY (`code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

insert into jd_country (code, name, boycot, blocked)
values ('AD', 'Andorra', false, false),
       ('AE', 'United Arab Emirates', false, false),
       ('AF', 'Afghanistan', true, false),
       ('AG', 'Antigua and Barbuda', false, false),
       ('AI', 'Anguilla', false, false),
       ('AL', 'Albania', false, false),
       ('AM', 'Armenia', false, false),
       ('AO', 'Angola', false, false),
       ('AQ', 'Antarctica', false, false),
       ('AR', 'Argentina', false, false),
       ('AS', 'American Samoa', false, false),
       ('AT', 'Austria', false, false),
       ('AU', 'Australia', false, false),
       ('AW', 'Aruba', false, false),
       ('AX', 'Ãland Islands', false, false),
       ('AZ', 'Azerbaijan', false, false),
       ('BA', 'Bosnia and Herzegovina', false, false),
       ('BB', 'Barbados', false, false),
       ('BD', 'Bangladesh', false, false),
       ('BE', 'Belgium', false, false),
       ('BF', 'Burkina Faso', false, false),
       ('BG', 'Bulgaria', false, false),
       ('BH', 'Bahrain', false, false),
       ('BI', 'Burundi', false, false),
       ('BJ', 'Benin', false, false),
       ('BL', 'Saint Barthélemy', false, false),
       ('BM', 'Bermuda', false, false),
       ('BN', 'Brunei Darussalam', false, false),
       ('BO', 'Bolivia, Plurinational State of', false, false),
       ('BQ', 'Bonaire, Sint Eustatius and Saba', false, false),
       ('BR', 'Brazil', false, false),
       ('BS', 'Bahamas', false, false),
       ('BT', 'Bhutan', false, false),
       ('BV', 'Bouvet Island', false, false),
       ('BW', 'Botswana', false, false),
       ('BY', 'Belarus', false, false),
       ('BZ', 'Belize', false, false),
       ('CA', 'Canada', false, false),
       ('CC', 'Cocos (Keeling) Islands', false, false),
       ('CD', 'Congo, the Democratic Republic of the', false, false),
       ('CF', 'Central African Republic', false, false),
       ('CG', 'Congo', false, false),
       ('CH', 'Switzerland', false, false),
       ('CI', 'Cote d''Ivoire', false, false),
       ('CK', 'Cook Islands', false, false),
       ('CL', 'Chile', false, false),
       ('CM', 'Cameroon', false, false),
       ('CN', 'China', false, false),
       ('CO', 'Colombia', false, false),
       ('CR', 'Costa Rica', false, false),
       ('CU', 'Cuba', false, false),
       ('CV', 'Cape Verde', false, false),
       ('CW', 'Curaçao', false, false),
       ('CX', 'Christmas Island', false, false),
       ('CY', 'Cyprus', false, false),
       ('CZ', 'Czech Republic', false, false),
       ('DE', 'Germany', false, false),
       ('DJ', 'Djibouti', false, false),
       ('DK', 'Denmark', false, false),
       ('DM', 'Dominica', false, false),
       ('DO', 'Dominican Republic', false, false),
       ('DZ', 'Algeria', false, false),
       ('EC', 'Ecuador', false, false),
       ('EE', 'Estonia', false, false),
       ('EG', 'Egypt', false, false),
       ('EH', 'Western Sahara', false, false),
       ('ER', 'Eritrea', false, false),
       ('ES', 'Spain', false, false),
       ('ET', 'Ethiopia', false, false),
       ('FI', 'Finland', false, false),
       ('FJ', 'Fiji', false, false),
       ('FK', 'Falkland Islands (Malvinas)', false, false),
       ('FM', 'Micronesia, Federated States of', false, false),
       ('FO', 'Faroe Islands', false, false),
       ('FR', 'France', false, false),
       ('GA', 'Gabon', false, false),
       ('GB', 'United Kingdom', false, false),
       ('GD', 'Grenada', false, false),
       ('GE', 'Georgia', false, false),
       ('GF', 'French Guiana', false, false),
       ('GG', 'Guernsey', false, false),
       ('GH', 'Ghana', false, false),
       ('GI', 'Gibraltar', false, false),
       ('GL', 'Greenland', false, false),
       ('GM', 'Gambia', false, false),
       ('GN', 'Guinea', false, false),
       ('GP', 'Guadeloupe', false, false),
       ('GQ', 'Equatorial Guinea', false, false),
       ('GR', 'Greece', false, false),
       ('GS', 'South Georgia and the South Sandwich Islands', false, false),
       ('GT', 'Guatemala', false, false),
       ('GU', 'Guam', false, false),
       ('GW', 'Guinea-Bissau', false, false),
       ('GY', 'Guyana', false, false),
       ('HK', 'Hong Kong', false, false),
       ('HM', 'Heard Island and McDonald Islands', false, false),
       ('HN', 'Honduras', false, false),
       ('HR', 'Croatia', false, false),
       ('HT', 'Haiti', false, false),
       ('HU', 'Hungary', false, false),
       ('ID', 'Indonesia', false, false),
       ('IE', 'Ireland', false, false),
       ('IL', 'Israel', false, false),
       ('IM', 'Isle of Man', false, false),
       ('IN', 'India', false, false),
       ('IO', 'British Indian Ocean Territory', false, false),
       ('IQ', 'Iraq', true, false),
       ('IR', 'Iran, Islamic Republic of', true, false),
       ('IS', 'Iceland', false, false),
       ('IT', 'Italy', false, false),
       ('JE', 'Jersey', false, false),
       ('JM', 'Jamaica', false, false),
       ('JO', 'Jordan', false, false),
       ('JP', 'Japan', false, false),
       ('KE', 'Kenya', false, false),
       ('KG', 'Kyrgyzstan', false, false),
       ('KH', 'Cambodia', false, false),
       ('KI', 'Kiribati', false, false),
       ('KM', 'Comoros', false, false),
       ('KN', 'Saint Kitts and Nevis', false, false),
       ('KP', 'Korea, Democratic People''s Republic of', true, false),
       ('KR', 'Korea, Republic of', false, false),
       ('KW', 'Kuwait', false, false),
       ('KY', 'Cayman Islands', false, false),
       ('KZ', 'Kazakhstan', false, false),
       ('LA', 'Lao People''s Democratic Republic', false, false),
       ('LB', 'Lebanon', false, false),
       ('LC', 'Saint Lucia', false, false),
       ('LI', 'Liechtenstein', false, false),
       ('LK', 'Sri Lanka', false, false),
       ('LR', 'Liberia', false, false),
       ('LS', 'Lesotho', false, false),
       ('LT', 'Lithuania', false, false),
       ('LU', 'Luxembourg', false, false),
       ('LV', 'Latvia', false, false),
       ('LY', 'Libya', false, false),
       ('MA', 'Morocco', false, false),
       ('MC', 'Monaco', false, false),
       ('MD', 'Moldova, Republic of', false, false),
       ('ME', 'Montenegro', false, false),
       ('MF', 'Saint Martin (French part)', false, false),
       ('MG', 'Madagascar', false, false),
       ('MH', 'Marshall Islands', false, false),
       ('MK', 'Macedonia, the Former Yugoslav Republic of', false, false),
       ('ML', 'Mali', false, false),
       ('MM', 'Myanmar', false, false),
       ('MN', 'Mongolia', false, false),
       ('MO', 'Macao', false, false),
       ('MP', 'Northern Mariana Islands', false, false),
       ('MQ', 'Martinique', false, false),
       ('MR', 'Mauritania', false, false),
       ('MS', 'Montserrat', false, false),
       ('MT', 'Malta', false, false),
       ('MU', 'Mauritius', false, false),
       ('MV', 'Maldives', false, false),
       ('MW', 'Malawi', false, false),
       ('MX', 'Mexico', false, false),
       ('MY', 'Malaysia', false, false),
       ('MZ', 'Mozambique', false, false),
       ('NA', 'Namibia', false, false),
       ('NC', 'New Caledonia', false, false),
       ('NE', 'Niger', false, false),
       ('NF', 'Norfolk Island', false, false),
       ('NG', 'Nigeria', false, false),
       ('NI', 'Nicaragua', false, false),
       ('NL', 'Netherlands', false, false),
       ('NO', 'Norway', false, false),
       ('NP', 'Nepal', false, false),
       ('NR', 'Nauru', false, false),
       ('NU', 'Niue', false, false),
       ('NZ', 'New Zealand', false, false),
       ('OM', 'Oman', false, false),
       ('PA', 'Panama', false, false),
       ('PE', 'Peru', false, false),
       ('PF', 'French Polynesia', false, false),
       ('PG', 'Papua New Guinea', false, false),
       ('PH', 'Philippines', false, false),
       ('PK', 'Pakistan', false, false),
       ('PL', 'Poland', false, false),
       ('PM', 'Saint Pierre and Miquelon', false, false),
       ('PN', 'Pitcairn', false, false),
       ('PR', 'Puerto Rico', false, false),
       ('PS', 'Palestine, State of', false, false),
       ('PT', 'Portugal', false, false),
       ('PW', 'Palau', false, false),
       ('PY', 'Paraguay', false, false),
       ('QA', 'Qatar', false, false),
       ('RE', 'Réunion', false, false),
       ('RO', 'Romania', false, false),
       ('RS', 'Serbia', false, false),
       ('RU', 'Russian Federation', false, false),
       ('RW', 'Rwanda', false, false),
       ('SA', 'Saudi Arabia', false, false),
       ('SB', 'Solomon Islands', false, false),
       ('SC', 'Seychelles', false, false),
       ('SD', 'Sudan', false, false),
       ('SE', 'Sweden', false, false),
       ('SG', 'Singapore', false, false),
       ('SH', 'Saint Helena, Ascension and Tristan da Cunha', false, false),
       ('SI', 'Slovenia', false, false),
       ('SJ', 'Svalbard and Jan Mayen', false, false),
       ('SK', 'Slovakia', false, false),
       ('SL', 'Sierra Leone', false, false),
       ('SM', 'San Marino', false, false),
       ('SN', 'Senegal', false, false),
       ('SO', 'Somalia', false, false),
       ('SR', 'Suriname', false, false),
       ('SS', 'South Sudan', false, false),
       ('ST', 'Sao Tome and Principe', false, false),
       ('SV', 'El Salvador', false, false),
       ('SX', 'Sint Maarten (Dutch part)', false, false),
       ('SY', 'Syrian Arab Republic', false, false),
       ('SZ', 'Swaziland', false, false),
       ('TC', 'Turks and Caicos Islands', false, false),
       ('TD', 'Chad', false, false),
       ('TF', 'French Southern Territories', false, false),
       ('TG', 'Togo', false, false),
       ('TH', 'Thailand', false, false),
       ('TJ', 'Tajikistan', false, false),
       ('TK', 'Tokelau', false, false),
       ('TL', 'Timor-Leste', false, false),
       ('TM', 'Turkmenistan', false, false),
       ('TN', 'Tunisia', false, false),
       ('TO', 'Tonga', false, false),
       ('TR', 'Turkey', false, false),
       ('TT', 'Trinidad and Tobago', false, false),
       ('TV', 'Tuvalu', false, false),
       ('TW', 'Taiwan, Province of China', false, false),
       ('TZ', 'Tanzania, United Republic of', false, false),
       ('UA', 'Ukraine', false, false),
       ('UG', 'Uganda', false, false),
       ('UM', 'United States Minor Outlying Islands', false, false),
       ('US', 'United States', false, false),
       ('UY', 'Uruguay', false, false),
       ('UZ', 'Uzbekistan', false, false),
       ('VA', 'Holy See (Vatican City State)', false, false),
       ('VC', 'Saint Vincent and the Grenadines', false, false),
       ('VE', 'Venezuela, Bolivarian Republic of', true, false),
       ('VG', 'Virgin Islands, British', false, false),
       ('VI', 'Virgin Islands, U.S.', false, false),
       ('VN', 'Viet Nam', false, false),
       ('VU', 'Vanuatu', false, false),
       ('WF', 'Wallis and Futuna', false, false),
       ('WS', 'Samoa', false, false),
       ('YE', 'Yemen', true, false),
       ('YT', 'Mayotte', false, false),
       ('ZA', 'South Africa', false, false),
       ('ZM', 'Zambia', false, false),
       ('ZW', 'Zimbabwe', false, false);
drop trigger if exists on_insert;
@@
drop trigger if exists  on_insert_us;
@@
drop trigger if exists on_update;
@@
drop trigger if exists on_update_us;
@@
DROP TRIGGER on_insert_sessstat_lb;
@@
CREATE  trigger on_insert_sessstat_lb
    after insert
    on session_statistics
    for each row
BEGIN
    insert into session_statistics_lb (rdate,session_id) values (  DATE(NEW.start_at), NEW.session_id);
    CALL REPLACE_SESSION_RANK_ENTRY(NEW.start_at, NEW.session_id);
    CALL UPDATE_SESSION_DATA_USER_STATISTICS(NEW.user_id, 1, NEW.start_at, NEW.num_bets, NEW.num_wins,
                                             NEW.sum_bet/100, NEW.sum_win/100,NEW.sum_boost/100,
                                             ABS(TIME_TO_SEC(TIMEDIFF(NEW.start_at, NEW.end_at))));
END
@@
DROP TRIGGER on_delete_sessstat_lb;
@@
CREATE  trigger on_delete_sessstat_lb
    after delete
                     on session_statistics
                     for each row
BEGIN
delete from session_statistics_lb where rdate=DATE(OLD.start_at) and session_id=OLD.session_id;
CALL REPLACE_SESSION_RANK_ENTRY(OLD.start_at, OLD.session_id);
CALL UPDATE_SESSION_DATA_USER_STATISTICS(OLD.user_id, -1, OLD.start_at, -1*OLD.num_bets,-1 * OLD.num_wins,
                                             -1*OLD.sum_bet/100, -1 * OLD.sum_win/100,-1*OLD.sum_boost/100,
                                         -1*ABS(TIME_TO_SEC(TIMEDIFF(OLD.start_at, OLD.end_at))));
END
@@



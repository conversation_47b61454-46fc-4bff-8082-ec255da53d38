CREATE PROCEDURE `ARCHIVE_SESSION`(IN p_uid BIGINT, IN p_sid BIGINT, IN p_simulate INT, OUT p_success INT)
    modifies sql data
BEGIN
    DECLARE pstatus VARCHAR(20);
    DECLARE ptask_status VARCHAR(20);

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
        BEGIN
            ROLLBACK;
            SELECT 0 into p_success;
        END;


    SELECT coalesce(s.status, 'NONE') into pstatus from session s where s.session_id = p_sid and s.user_id = p_uid;
    IF (pstatus <> 'MIGRATED') THEN
        SELECT 0 into p_success;
    ELSE
        SELECT coalesce(st.status, 'NONE')
        into ptask_status
        from session_task st
        where st.type = 'ARCHIVE'
          and st.session_id = p_sid;
        IF (ptask_status = 'PENDING') THEN
            IF (p_simulate = 1) THEN
                DELETE from session_transaction_archive where session_id = p_sid;
            END IF;
            insert into session_transaction_archive (id, session_id, round_ref, balance_after, bet, win, tax, ext_tx_id,
                                                     type, earned_xp,
                                                     level_after, percnl_after, cancelled, boost, created_at,
                                                     updated_at, bbalance_after, bbet, bwin)
            SELECT st.id,
                   st.session_id,
                   st.round_ref,
                   st.balance_after,
                   st.bet,
                   st.win,
                   st.tax,
                   st.ext_tx_id,
                   st.type,
                   st.earned_xp,
                   st.level_after,
                   st.percnl_after,
                   st.cancelled,
                   st.boost,
                   st.created_at,
                   st.updated_at,
                   st.bbalance_after,
                   st.bbet,
                   st.bwin
            from session_transaction st
            where st.user_id = p_uid
              and st.session_id = p_sid;
            --  Remove and mark session, remove task
            IF (p_simulate = 0) THEN

                delete from session_transaction where user_id = p_uid and session_id = p_sid;

                delete from session_round where user_id = p_uid and session_id = p_sid;

                update session s set status = 'ARCHIVED' where s.user_id = p_uid and s.session_id = p_sid;

                update session_task st
                set st.status='NEW',
                    st.retries=0,
                    st.type='EXTERNALIZE',
                    st.updated_at =CURRENT_TIMESTAMP
                where st.session_id = p_sid;
            END IF;
            SELECT 1 into p_success;
        END IF;
    END IF;
END;


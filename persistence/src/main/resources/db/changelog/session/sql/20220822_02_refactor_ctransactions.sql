CREATE TABLE `ctransactions2`
(
    `id`             bigint                                                            NOT NULL,
    `balance_after`  decimal(18, 4)                          DEFAULT 0                 NOT NULL,
    `bet`            decimal(18, 4)                          DEFAULT 0                 NOT NULL,
    `win`            decimal(18, 4)                          DEFAULT 0                 NOT NULL,
    `bbalance_after` decimal(18, 4)                          DEFAULT 0                 NOT NULL,
    `bbet`           decimal(18, 4)                          DEFAULT 0                 NOT NULL,
    `bwin`           decimal(18, 4)                          DEFAULT 0                 NOT NULL,
    `created_at`     timestamp                               DEFAULT CURRENT_TIMESTAMP NOT NULL,
    `ext_tx_numid`   varchar(100) COLLATE utf8mb4_unicode_ci                           NOT NULL,
    `ext_tx_id`      varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `type`           varchar(20) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `vendor_name`    varchar(30) COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `game_id`        bigint                                  DEFAULT NULL,
    `user_id`        bigint                                                            NOT NULL,
    `earned_xp`      INTEGER                                 DEFAULT 0                 NOT NULL,
    `level_after`    INTEGER                                 DEFAULT 0                 NOT NULL,
    `percnl_after`   decimal(7, 4)                           DEFAULT 0                 NOT NULL,
    `round_ref`      varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `session_id`     bigint                                  DEFAULT NULL,
    `cancelled`      bit(1)                                  DEFAULT b'0',
    `boost`          decimal(18, 4)                          DEFAULT '0.00',
    PRIMARY KEY (`id`, `created_at`),
    KEY `transactions_index_created_at` (`created_at`),
    KEY `ctransactions_index2` (`user_id`, `session_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

insert into ctransactions2
select id,
       balance_after,
       bet,
       win,
       0,
       0,
       0,
       created_at,
       ext_tx_numid,
       ext_tx_id,
       type,
       vendor_name,
       game_id,
       user_id,
       earned_xp,
       level_after,
       percnl_after,
       round_ref,
       session_id,
       cancelled,
       boost
from ctransactions;

rename table ctransactions to ctransactions_tmp,ctransactions2 to ctransactions;

drop table ctransactions_tmp;

databaseChangeLog:
#  - preConditions:
#      - onFail: MARK_RAN
#      - sqlCheck:
#          expectedResult: 0
#          sql: SELECT count(*) FROM information_schema.COLUMNS c WHERE c.TABLE_NAME = 'session_transaction' and c.COLUMN_NAME='bwin'
  - changeSet:
      id: s-20220822-03
      author: j<PERSON><PERSON>
      runOnChange: false
      changes:
        - sqlFile:
            fullDefinition: false
            remarks: add bonus columns
            path: sql/session_transactions_add_bonus_columns.sql
            relativeToChangelogFile: true
            stripComments: true


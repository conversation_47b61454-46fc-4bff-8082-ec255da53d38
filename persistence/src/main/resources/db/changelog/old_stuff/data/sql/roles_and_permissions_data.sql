
INSERT IGNORE INTO sec_role (role_id, description) VALUES ('USER', 'Normal USER role, no admin');
INSERT IGNORE INTO sec_role (role_id, description) VALUES ('ADMIN', 'Display, list, manage and search various adm. entities');
INSERT IGNORE INTO sec_role (role_id, description) VALUES ('SUPPORTER', 'Basic supporter role');
INSERT IGNORE INTO sec_role (role_id, description) VALUES ('TESTER', 'Basic TESTER role');
INSERT IGNORE INTO sec_role (role_id, description) VALUES ('PROVIDER', 'GAME-PROVIDER access');
INSERT IGNORE INTO sec_role (role_id, description) VALUES ('SUPERUSER', 'Full access');
INSERT IGNORE INTO sec_role (role_id, description) VALUES ('TECHNICIAN', 'Full access, but limited display rights reg. users');
INSERT IGNORE INTO sec_role (role_id, description) VALUES ('M<PERSON><PERSON>OR', 'Full read access to statistics, but limited display rights reg. users');

INSERT IGNORE INTO sec_role (role_id, description) VALUES ('SLOTCATALOG', 'Read access to Slotcatalog Data/Game RTP Stats');


INSERT IGNORE INTO sec_permission (permission_id, description) VALUES ('ADMIN_BASE', 'Display, list and search various adm. entities');
INSERT IGNORE INTO sec_permission (permission_id, description) VALUES ('USER_UNMASKED', 'Display all attributes unmasked');
INSERT IGNORE INTO sec_permission (permission_id, description) VALUES ('GAME_MANAGE', 'Update game info, adding games');
INSERT IGNORE INTO sec_permission (permission_id, description) VALUES ('PROMOTION_MANAGE', 'Manage promotions');
INSERT IGNORE INTO sec_permission (permission_id, description) VALUES ('USER_MANAGE', 'Edit user attributes, blocking,...');
INSERT IGNORE INTO sec_permission (permission_id, description) VALUES ('SHOP_MANAGE', 'Manage shop items');
INSERT IGNORE INTO sec_permission (permission_id, description) VALUES ('LEVEL_MANAGE', 'Manage shop items');
INSERT IGNORE INTO sec_permission (permission_id, description) VALUES ('ROLE_MANAGE', 'Manage roles, permission and allow system user creation');
INSERT IGNORE INTO sec_permission (permission_id, description) VALUES ('STATISTIC_VIEW', 'See Statistics');

INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('ADMIN','ADMIN_BASE');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('ADMIN','USER_UNMASKED');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('ADMIN','GAME_MANAGE');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('ADMIN','PROMOTION_MANAGE');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('ADMIN','USER_MANAGE');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('ADMIN','SHOP_MANAGE');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('ADMIN','LEVEL_MANAGE');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('ADMIN','STATISTIC_VIEW');

INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('SUPPORTER','ADMIN_BASE');

INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('SUPERUSER','ADMIN_BASE');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('SUPERUSER','USER_UNMASKED');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('SUPERUSER','GAME_MANAGE');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('SUPERUSER','PROMOTION_MANAGE');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('SUPERUSER','USER_MANAGE');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('SUPERUSER','SHOP_MANAGE');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('SUPERUSER','LEVEL_MANAGE');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('SUPERUSER','ROLE_MANAGE');
INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('SUPERUSER','STATISTIC_VIEW');

INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('MONITOR','STATISTIC_VIEW');

INSERT IGNORE INTO sec_roles_permissions (role_id, permission_id) VALUES ('TECHNICIAN','STATISTIC_VIEW');






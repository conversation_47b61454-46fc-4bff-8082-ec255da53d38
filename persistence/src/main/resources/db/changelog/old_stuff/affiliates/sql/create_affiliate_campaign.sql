create table affiliate_campaign
(
    id           int AUTO_INCREMENT,
    affiliate_id int         not null,
    track_value  VARCHAR(50) NOT NULL,
    usage_count  int         NOT NULL DEFAULT 0,
    active       bit(1)      NOT NULL DEFAULT true,
    created_at   TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE INDEX idx_cmpgn_tval (`track_value`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE PROCEDURE REPLACE_SESSION_RANK_ENTRY(IN created_at TIMESTAMP, IN session_id BIGINT)
    READS SQL DATA MODIFIES SQL DATA
BEGIN
    INSERT into session_ranks (created_at, session_id, maxwin, maxmult, hsaldo, user_id, game_id)
    select *
    from (
             select gs.start_at                          as created_at,
                    gs.session_id                        as sesion_id,
                    gs.max_win * 100                     as maxwin,
                    COALESCE(gs.max_multiplier, 0) * 100 as maxmult,
                    ((gs.sum_win - gs.sum_bet) * 100)    as hsaldo,
                    gs.user_id                           as user_id,
                    gs.game_id                           as game_id
             from game_statistics gs
             where gs.session_id = session_id
               and gs.start_at = created_at
               and gs.num_plays > 0) new
    on duplicate key update maxwin=new.maxwin, maxmult=new.maxmult, hsaldo=new.hsaldo;
END;

create or replace view v_game_search as
select `g`.`id`                              AS `id`,
       `g`.`game_id`                         AS `gameId`,
       `g`.`name`                            AS `name`,
       `g`.`active`                          AS `active`,
       `g`.`ios`                             AS `activeIos`,
       `g`.`mobile`                          AS `activeMobile`,
       `g`.`desktop`                         AS `activeDesktop`,
       `g`.`android`                         AS `activeAndroid`,
       coalesce(`g`.`sort_order`, 99999)     AS `sortOrder`,
       `g`.`updated_at`                      AS `updatedAt`,
       `g`.`created_at`                      AS `createdAt`,
       if(isnull(`gi`.`Id`), FALSE, TRUE)    AS `isLinked`,
       `gi`.`Id`                             AS `infoId`,
       `gi`.`layout`                         AS `layout`,
       `gi`.`rtp`                            AS `officialRtp`,
       coalesce(`gi`.`slot_rank`, 99999)     AS `slotrank`,
       `gi`.`paylines`                       AS `paylines`,
       `gi`.`release_date`                   AS `releaseDate`,
       `gi`.`type`                           AS `slotType`,
       `gi`.`volatility`                     AS `volatility`,
       coalesce(`grd`.`spins`, 0)            AS `spins`,
       `grd`.`rtp`                           AS `realRtp`,
       coalesce(`grd`.`sessions`, 0)         AS `sessions`,
       coalesce(`grd`.`sumBet`, 0)           AS `sumBet`,
       coalesce(`grd`.`sumWin`, 0)           AS `sumWin`,
       coalesce(`grd`.`avgSessionTime`, 0)   AS `avgSessionTime`,
       coalesce(`v`.`active`, FALSE)         AS `vendorActive`,
       coalesce(`v`.`blocked_countries`, '') AS `blockedCountries`,
       `v`.`vendor_name`                     AS `vendorName`,
       `v`.`provider_homepage`               AS `vendorHomepage`,
       `gp`.`type`                           AS `promoType`,
       `i`.`real_type`                       AS `thumbSourceDimension`,
       coalesce(`i`.`hasThumb`, FALSE)       AS `hasThumb`,
       coalesce(`issues`.`numIssues`, 0)     AS `issuesLastMonth`
from ((((((`games` `g` left join `game_info` `gi` on ((`g`.`info_id` = `gi`.`Id`))) left join (select `gr`.`game_id`                                          AS `game_id`,
                                                                                                      sum(`gr`.`sessions`)                                    AS `sessions`,
                                                                                                      sum(`gr`.`spins`)                                       AS `spins`,
                                                                                                      (sum(`gr`.`session_time_total`) / sum(`gr`.`sessions`)) AS `avgSessionTime`,
                                                                                                      sum(`gr`.`sum_bet`)                                     AS `sumBet`,
                                                                                                      sum(`gr`.`sum_win`)                                     AS `sumWin`,
                                                                                                      (sum(`gr`.`sum_win`) / sum(`gr`.`sum_bet`))             AS `rtp`
                                                                                               from `game_rank_daily` `gr`
                                                                                               where (`gr`.`rdate` >= (now() - interval 6 month))
                                                                                               group by `gr`.`game_id`) `grd` on ((`g`.`id` = `grd`.`game_id`)))
    left join `vendors` `v` on ((`g`.`provider_name` = `v`.`bridge_name`)))
    left join (select `gpp`.`game_id` AS `game_id`, group_concat(DISTINCT gpp.type ORDER BY gpp.type ASC) AS `type`
               from `game_promotion` `gpp`
               where ((`gpp`.`valid_from` < now()) and (`gpp`.`valid_to` > now()))
               group by game_id) `gp` on ((`g`.`id` = `gp`.`game_id`))) left join (select `ii`.`game_id`   AS `game_id`,
                                                                                          `ii`.`real_type` AS `real_type`,
                                                                                          1                AS `hasThumb`
                                                                                   from `game_image` `ii`
                                                                                   where (`ii`.`type` = '_1x1')
                                                                                   group by `ii`.`game_id`, `ii`.`real_type`) `i` on ((`g`.`id` = `i`.`game_id`)))
         left join (select `iss`.`game_id` AS `game_id`, count(0) AS `numIssues`
                    from `internal_issues` `iss`
                    where (`iss`.`created_at` > (now() - interval 1 month))
                    group by `iss`.`game_id`) `issues` on ((`g`.`id` = `issues`.`game_id`)));


CREATE TRIGGER on_insert_us
    AFTER INSERT
    ON `game_statistics`
    FOR EACH ROW
    CALL UPDATE_SESSION_DATA_USER_STATISTICS(NEW.user_id, 1, NEW.start_at, NEW.num_plays, NEW.num_wins,
                                             NEW.sum_bet, NEW.sum_win,
                                             ABS(TIME_TO_SEC(TIMEDIFF(NEW.start_at, NEW.end_at))));

CREATE TRIGGER on_update_us
    AFTER UPDATE
    ON `game_statistics`
    FOR EACH ROW
    CALL UPDATE_SESSION_DATA_USER_STATISTICS(NEW.user_id, 0, NEW.start_at, NEW.num_plays - OLD.num_plays,
                                             NEW.num_wins - OLD.num_wins, NEW.sum_bet - OLD.sum_bet,
                                             NEW.sum_win - OLD.sum_win,
                                             ABS(TIME_TO_SEC(TIMEDIFF(NEW.start_at, NEW.end_at))) -
                                             ABS(TIME_TO_SEC(TIMEDIFF(OLD.start_at, OLD.end_at))));

CREATE TABLE `user_verifications`
(
    `user_id`           bigint(20)                             NOT NULL,
    `created_at`        TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `verified_at`       TIMESTAMP                              NULL,
    `code`              varchar(40) COLLATE utf8mb4_unicode_ci NOT NULL,
    `code_short`        varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
    `verification_type` VARCHAR(30)                            NOT NULL,
    `attribute`         VARCHAR(150)                           NOT NULL,
    PRIMARY KEY (`code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

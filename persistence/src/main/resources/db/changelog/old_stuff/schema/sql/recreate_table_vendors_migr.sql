CREATE TABLE `vendors2`
(
    `id`                int                                                          NOT NULL,
    `created_at`        TIMESTAMP                                                             DEFAULT CURRENT_TIMESTAMP,
    `updated_at`        TIMESTAMP                                                             DEFAULT CURRENT_TIMESTAMP,
    `vendor_name`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `bridge_name`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `slotcatalog_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `provider_homepage` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `vendor_order`      int                                                                   DEFAULT 99,
    `active`            bit(1)                                                       NOT NULL DEFAULT b'0',
    `blocked_countries` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT '',
    <PERSON><PERSON>AR<PERSON> KEY (`id`),
    UNIQUE KEY `vendor_name` (`vendor_name`),
    UNIQUE KEY `bridge_name` (`bridge_name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;


insert into vendors2 (id, created_at, updated_at, vendor_name, bridge_name, slotcatalog_name, provider_homepage,
                      vendor_order, blocked_countries)
values (117, '2021-08-10 13:32:12', '2021-08-10 13:32:12', 'Booongo', 'BOOONGO', 'Booongo', 'https://booongo.com', 8,
        ''),
       (118, '2021-08-10 13:32:17', '2021-08-10 13:32:17', 'Push Gaming', 'PUSHGAMING', 'Push Gaming', '', 48, ''),
       (119, '2021-08-10 13:32:17', '2021-08-10 13:32:17', 'Tom Horn Gaming', 'TOMHORN', 'Tom Horn Gaming',
        'https://www.tomhorngaming.com', 34, ''),
       (120, '2021-08-10 13:32:17', '2021-08-10 13:32:17', '5 Men Gaming', '5 MEN GAMING', 'Five Men Games',
        'https://www.netent.com', 2, ''),
       (121, '2021-08-10 13:32:17', '2021-08-10 13:32:17', 'Kalamba Games', 'KALAMBA', 'Kalamba Games',
        'https://kalambagames.com', 21, ''),
       (122, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Spinomenal', 'SPINOMENAL', 'Spinomenal',
        'https://spinomenal.com', 33, ''),
       (123, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Phantasma', 'PHANTASMA', '', '', 43, ''),
       (124, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Wazdan', 'WAZDAN', 'Wazdan', 'https://www.wazdan.com/', 36,
        ''),
       (125, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Oryx Gaming', 'ORYXGAMING', 'Oryx',
        'https://oryxgaming.com', 27, ''),
       (126, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Gamzix', 'GAMZIX', 'Gamzix', 'https://gamzix.com', 17, ''),
       (127, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Edict', 'EDICT', 'edict',
        'https://www.edict.de/spiele/slots/', 9, ''),
       (128, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Mancala Gaming', 'MANCALA', 'Mancala Gaming',
        'https://mancalagaming.com', 23, ''),
       (129, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Lady Luck Games', 'LADYLUCK', 'Lady Luck Games',
        'https://ladyluckgames.io', 22, ''),
       (130, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Green Jade', 'GREEN JADE', 'Green Jade Games', '', 39, ''),
       (131, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Zeus Play', 'ZEUS', 'Zeus Play', 'https://zeusplay.com', 41,
        ''),
       (132, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Quickspin', 'QUICKSPIN', 'Quickspin', '', 49, ''),
       (133, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Netent', 'NETENT', 'NetEnt', 'https://www.netent.com', 1,
        ''),
       (134, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Skywind Group', 'SKYWIND', 'Skywind Group', '', 37, ''),
       (135, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Bally Wulff', 'BALLYWULFF', 'Bally Wulff',
        'https://www.ballywulff.de/online-games/', 6, ''),
       (136, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Peter and Sons', 'PETERANDSONS', 'Peter and Sons',
        'https://peterandsons.org', 70, ''),
       (137, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Gameart', 'GAMEART', 'GameArt', 'https://gameart.net', 14,
        ''),
       (138, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Spadegaming', 'SPADE', 'Spadegaming',
        'https://www.spadegaming.com/', 42, ''),
       (139, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'iSoftBet', 'ISOFTBET', 'iSoftBet', '', 47, ''),
       (140, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Apollo Games', 'APOLLO', 'Apollo Games',
        'https://www.apollogames.com/', 5, ''),
       (141, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Eurasian Gaming', 'EURASIAN GAMING', 'Eurasian Gaming',
        'https://www.eagaming.com', 11, ''),
       (142, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'RNG Gaming', 'RNGGAMING', 'RNGPlay', '', 46, ''),
       (143, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Nolimit City', 'NOLIMIT', 'Nolimit City',
        'https://www.nolimitcity.com', 26, ''),
       (144, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Evoplay', 'EVOPLAY', 'Evoplay', 'https://evoplay.games', 12,
        ''),
       (145, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Hacksaw Gaming', 'HACKSAW GAMING', 'Hacksaw Gaming',
        'https://www.hacksawgaming.com', 20, ''),
       (146, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Fugaso', 'FUGASO', 'Fugaso', 'https://fugaso.com', 13, ''),
       (147, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Givme Games', 'GIVME', 'Givme Games', '', 18, ''),
       (148, '2021-08-10 13:32:18', '2021-08-10 13:32:18', '7 Mojos', '7MOJOS', '7mojos', 'https://7mojos.com', 3, ''),
       (149, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'BGaming', 'BGAMING', 'BGAMING', 'https://bgaming.com', 60,
        ''),
       (150, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Leap Gaming', 'LEAP', 'Leap Gaming', '', 44, ''),
       (151, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Pragmatic Play', 'PRAGMATIC', 'Pragmatic Play',
        'https://www.pragmaticplay.com/', 30, ''),
       (152, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Booming Games', 'BOOMING', 'Booming Games',
        'https://booming-games.com', 7, ''),
       (153, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Platipus', 'PLATIPUS', 'Platipus', '', 45, ''),
       (154, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Mascot Gaming', 'MASCOT', 'Mascot Gaming',
        'https://mascot.games', 38, ''),
       (155, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'KA Gaming', 'KA GAMING', 'KA Gaming',
        'https://www.kaga88.com', 88, ''),
       (156, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Red Rake Gaming', 'REDRAKE', 'Red Rake',
        'https://www.redrakegaming.com', 31, ''),
       (157, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'APPARAT', 'APPARAT', 'Apparat Gaming', ' ', 55, ''),
       (158, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Endorphina', 'ENDORPHINA', 'Endorphina',
        'https://endorphina.com', 10, ''),
       (159, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Playson', 'PLAYSON', 'Playson', 'https://playson.com', 29,
        ''),
       (160, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Triple Cherry', 'TRIPLE CHERRY', 'Triple Cherry',
        'https://www.3cherry.com', 35, ''),
       (161, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Play''n Go', 'PLAYNGO', 'Play''n Go',
        'https://www.playngo.com', 3, ''),
       (162, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Gamomat', 'GAMOMAT', 'Gamomat', 'https://www.gamomat.com',
        15, ''),
       (163, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Microgaming', 'MICROGAMING', 'Microgaming',
        'https://microgaming.co.uk/', 25, ''),
       (164, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Revolver Gaming', 'REVOLVERGAMING', 'Revolver Gaming',
        'https://revolvergaming.com', 32, ''),
       (165, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Gamshy', 'GAMSHY', 'Gamshy', 'https://www.gamshy.com', 16,
        ''),
       (166, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Golden Hero', 'GOLDEN HERO', 'Golden Hero',
        'https://www.goldenhero.com', 19, ''),
       (167, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Pariplay', 'PARIPLAY', 'Pariplay',
        'https://pariplayltd.com', 28, ''),
       (168, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Candle Bets', 'CANDLEBETS', 'Candle Bets',
        'https://candlebets.com', 80, ''),
       (169, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Merkur', 'MERKUR', 'Merkur',
        'https://www.edict.de/spiele/slots/', 24, ''),
       (170, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Playtech', 'PLAYTECH', 'Playtech', '', 40, ''),
       (171, '2021-08-10 13:32:18', '2021-08-10 13:32:18', 'Amatic Industries', 'AMATIC', 'Amatic Industries',
        'https://www.amatic.com', 4, ''),
       (172, '2021-12-29 13:37:21', '2021-12-29 13:37:17', 'Hölle Games', 'HOELLE', 'Hölle Games',
        'https://www.hölle.games', 54, ''),
       (173, '2021-10-11 22:29:18', '2021-10-11 22:29:18', 'Arcadem', 'ARCADEM', 'Arcadem', 'https://arcadem.com', 93,
        ''),
       (174, '2021-12-30 11:34:55', '2021-12-30 11:34:58', 'Spearhead', 'SPEARHEAD', 'Spearhead Studios',
        'https://spearhead.com', 99, ''),
       (175, '2021-12-30 11:36:56', '2021-12-30 11:36:56', 'Reel Kingdom', 'PG_REELKINGDOM', 'Reel Kingdom',
        'https://reelkingdom.com', 100, '');

update vendors2
set active= true
where bridge_name in (select vold.bridge_name from vendors vold where vold.active = true);

RENAME TABLE vendors to vendors_bm, vendors2 to vendors;

DROP TABLE vendors_bm;
ALTER TABLE activations
    CHAR<PERSON><PERSON>R SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE activations
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE activegames CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE activegames
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE affiliates CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE affiliates
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE asset CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE asset
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE bonuscodes CH<PERSON><PERSON><PERSON>R SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE bonuscodes
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE consumables CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE consumables
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE ctransactions CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE ctransactions
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE custom_sequences CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE custom_sequences
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE DATABASECHANGELOG CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE DATABASECHANGELOG
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE DATABASECHANGELOGLOCK CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE DATABASECHANGELOGLOCK
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE deposits CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE deposits
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE doubleup_foes CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE doubleup_foes
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE doubleup_statistics CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE doubleup_statistics
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE doubleup_weapons CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE doubleup_weapons
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE doubleups CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE doubleups
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE game_category CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE game_category
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE game_category_link CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE game_category_link
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE game_image CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE game_image
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE game_info CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE game_info
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE game_promotion CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE game_promotion
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE game_statistics CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE game_statistics
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE games CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE games
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE issue_comments CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE issue_comments
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE issues CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE issues
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE localisations CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE localisations
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE notifications CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE notifications
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE payments CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE payments
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE purchases CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE purchases
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE tokens CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE tokens
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE transactions CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE transactions
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE user_events CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE user_events
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE user_game_attributes CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE user_game_attributes
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE user_info CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE user_info
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE users CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE users
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wallets CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wallets
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wheelspins CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE wheelspins
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

CREATE TABLE `sec_role`
(
    `role_id`     VARCHAR(40)  NOT NULL,
    `description` VA<PERSON>HAR(100) NOT NULL,
    PRIMARY KEY (`role_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `sec_permission`
(
    `permission_id` VARCHAR(40)  NOT NULL,
    `description`   VARCHAR(100) NOT NULL,
    PRIMARY KEY (`permission_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;



CREATE TABLE `sec_roles_permissions`
(
    `role_id`       VARCHAR(40) NOT NULL,
    `permission_id` VARCHAR(40) NOT NULL,
    PRIMARY KEY (`role_id`, `permission_id`),

    CONSTRAINT `sec_role_ibfk_1`
        FOREIGN KEY (`role_id`) REFERENCES `sec_role` (`role_id`),
    CONSTRAINT `sec_permission_ibfk_2`
        FOREIGN KEY (`permission_id`) REFERENCES `sec_permission` (`permission_id`)

) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

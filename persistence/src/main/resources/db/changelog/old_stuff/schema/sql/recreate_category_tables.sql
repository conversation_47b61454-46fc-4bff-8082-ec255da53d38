CREATE TABLE `game_category2`
(
    `id`          int                                     NOT NULL,
    `name`        varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
    `type`        varchar(20) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '',
    `parent_id`   int                                              DEFAULT NULL,
    `hidden`      bit(1)                                           DEFAULT b'0',
    `slotcatalog` bit(1)                                           DEFAULT b'1',
    PRIMARY KEY (`id`),
    KEY `game_category_game_category_id_fk` (`parent_id`),
    KEY `game_category_type_name_index` (`type`, `name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

insert into game_category2 (id, name, type, parent_id, hidden, slotcatalog)
select id, name, type, parent as parent_id, hidden, true as slotcatalog
from game_category;


CREATE TABLE `game_category_link2`
(
    `game_id`     bigint NOT NULL,
    `category_id` int    NOT NULL,
    PRIMARY KEY (`game_id`, `category_id`),
    KEY `game_category_link_game_category_id_fk` (`category_id`),
    CONSTRAINT `game_category_link_game_category_id_fk` FOREIGN KEY (`category_id`) REFERENCES `game_category2` (`id`),
    CONSTRAINT `game_category_link_games_id_fk` FOREIGN KEY (`game_id`) REFERENCES `games` (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

insert into game_category_link2 (game_id, category_id)
select game_id, category_id
from game_category_link;

rename table game_category to game_category_tmp, game_category2 to game_category,
    game_category_link to game_category_link_tmp, game_category_link2 to game_category_link;


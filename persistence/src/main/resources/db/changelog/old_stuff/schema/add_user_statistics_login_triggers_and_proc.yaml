databaseChangeLog:
    - changeSet:
          id: 20211028-01
          author: j<PERSON><PERSON>
          changes:
              - createProcedure:
                    runOnChange: false
                    fullDefinition: false
                    remarks: trigger proc for login dates trigger
                    path: sql/create_user_statistics_update_login_dates_proc.sql
                    relativeToChangelogFile: true
                    procedureName: UPDATE_LOGIN_DATA_USER_STATISTICS
    - changeSet:
          id: 20211028-02
          author: j<PERSON><PERSON>
          changes:
              - sqlFile:
                    runOnChange: false
                    fullDefinition: false
                    remarks: new user statistics mechanic, the triggers on user_login which upd. user_statistics
                    path: sql/create_user_statistics_update_login_dates_triggers.sql
                    relativeToChangelogFile: true

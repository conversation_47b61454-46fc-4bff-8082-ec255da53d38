databaseChangeLog:
  - changeSet:
      id: 20210309-01
      author: j<PERSON><PERSON>
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: BIGINT
              - column:
                  name: created_at
                  type: timestamp
              - column:
                  name: updated_at
                  type: timestamp
              - column:
                  name: game_id
                  type: BIGINT
              - column:
                  name: user_id
                  type: BIGINT
              - column:
                  name: other_id
                  type: VARCHAR(255)
              - column:
                  name: problem
                  type: VARCHAR(1024)
              - column:
                  name: type
                  type: VARCHAR(40)
              - column:
                  name: checked
                  type: BIT(1)
            tableName: internal_issues
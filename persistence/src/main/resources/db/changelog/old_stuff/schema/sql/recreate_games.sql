
CREATE TABLE `games2` (
                          `id` bigint(20) NOT NULL AUTO_INCREMENT,
                          `created_at` datetime DEFAULT NULL,
                          `updated_at` datetime DEFAULT NULL,
                          `active` bit(1) DEFAULT NULL,
                          `desktop` bit(1) DEFAULT NULL,
                          `game_id` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
                          `mobile` bit(1) DEFAULT NULL,
                          `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `provider_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
                          `level` int(11) DEFAULT '0',
                          `info_id` bigint(20) DEFAULT NULL,
                          `ios` bit(1) DEFAULT b'1',
                          `android` bit(1) NOT NULL DEFAULT b'1',
                          `auto_set` bit(1) NOT NULL DEFAULT b'0',
                          `auto_comment` varchar(300) NOT NULL DEFAULT '',
                          PRIMARY KEY (`id`),
                          UNIQUE KEY (`provider_name`,`game_id`),
                          KEY  (`info_id`),
                          CONSTRAINT FOREIGN KEY (`info_id`) REFERENCES `game_info` (`Id`)
) ENGINE=InnoDB AUTO_INCREMENT=2042 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


insert into games2 select id,created_at,updated_at,active,desktop,game_id,mobile,name,provider_name,level,info_id,ios,android,0,'' from games;

RENAME TABLE games to games_tmp, games2 to games;

DROP TABLE games_tmp;

CREATE TABLE `vendors` (
                            `id` bigint NOT NULL AUTO_INCREMENT,
                            `created_at` datetime DEFAULT NULL,
                            `updated_at` datetime DEFAULT NULL,
                            `vendor_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
                            `bridge_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
                            `slotcatalog_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                            `provider_homepage` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                            `comment` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '',
                            `active` bit(1) NOT NULL DEFAULT b'1',
                            `auto_gamelist` bit(1) NOT NULL DEFAULT b'0',
                            `auto_comment` varchar(300) COLLATE utf8mb4_unicode_ci DEFAULT '',
                            `auto_set` bit(1) NOT NULL DEFAULT b'0',
                            `order` int DEFAULT 0,
                            PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
create
 procedure REPLACE_USER_DAILY_STATSTX(IN statsdate date, IN social bit) modifies sql data
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
        BEGIN
            ROLLBACK;
            SELECT 'An error has occurred, operation rollbacked and the stored procedure was terminated';
        END;


    # Delete entries
    START TRANSACTION;
    DELETE from user_stats_daily where rdate = statsdate;
    COMMIT;

    # Insert bet/win stats
    # session_transaction


    START TRANSACTION;
    insert into user_stats_daily (rdate, user_id, bets, wins, boost, hedge, boni)
    select s.rdate                                                             as rdate,
           s.user_id                                                           as user_id,
           sum(s.sum_bet / 100)                                                as bets,
           sum(s.sum_win / 100)                                                as wins,
           sum(s.sum_boost / 100)                                              as boosts,
           sum((s.sum_bet / 100) * (1 - coalesce(g.rtp, gi.rtp, 100.0) / 100)) as hedge,
           0                                                                   as bonus
    from session_statistics_rd s
             join games g on s.game_id = g.id
             left join game_info gi on g.info_id = gi.id
    where s.rdate = statsdate
    group by s.rdate, s.user_id;
    COMMIT;

    # <PERSON><PERSON><PERSON>, RAKEBACKS,... NO PURCH and NO DEPS/PO
    START TRANSACTION ;
    insert into user_stats_daily (rdate, user_id, boni)
    select *
    from (select statsdate as rdate, st.user_id as user_id, sum(st.credit - st.withdraw) as nboni
          from user_transaction st
          where st.created_at >= statsdate
            and st.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
            and st.type NOT in ('PURCHASE', 'DEPOSIT', 'PAYOUT_RESERVE', 'PAYOUT_CANCEL')
          group by st.user_id) new
    on duplicate key update boni=boni + new.nboni;
    COMMIT;
    # insert/update deposits + fees


        # purchases
        START TRANSACTION ;

        INSERT into user_stats_daily (rdate, user_id, depos)
        select *
        from (select statsdate as rdate, p.user_id, sum(p.applied_cost) as ndepos
              from purchases p
              where p.created_at >= statsdate
                and p.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
                and p.status = 'SUCCESS'
              group by p.user_id) new
        on duplicate key update depos=new.ndepos;
        COMMIT;

        START TRANSACTION ;

        INSERT into user_stats_daily (rdate, user_id, depos_total)
        select *
        from (select statsdate as rdate, p.user_id, ROUND(coalesce(sum(p.applied_cost), 0), 4) as ndeposTa
              from purchases p
                       join user_stats_daily usd on p.user_id = usd.user_id and usd.rdate = statsdate
              where p.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
                and p.status = 'SUCCESS'
              group by p.user_id) new
        on duplicate key update depos_total=new.ndeposTa;
        COMMIT;





END;


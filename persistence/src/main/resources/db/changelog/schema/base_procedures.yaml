databaseChangeLog:
  - changeSet:
      id: n-20220201-01pr
      author: j<PERSON><PERSON> (generated)
      failOnError: false
      changes:
          - createProcedure:
                fullDefinition: false
                remarks: PROC
                path: sql/proc_archive_transactions.sql
                relativeToChangelogFile: true
                procedureName: ARCHIVE_TRANSACTIONS
          - createProcedure:
                fullDefinition: false
                remarks: PROC
                path: sql/proc_create_partitions.sql
                relativeToChangelogFile: true
                procedureName: create_new_partitions
          - createProcedure:
                fullDefinition: false
                remarks: PROC
                path: sql/proc_db_maintenance.sql
                relativeToChangelogFile: true
                procedureName: DB_MAINTENANCE
          - createProcedure:
                fullDefinition: false
                remarks: PROC
                path: sql/proc_optimize_partition.sql
                relativeToChangelogFile: true
                procedureName: OPTIMIZE_PARTITION
          - createProcedure:
              fullDefinition: false
              remarks: PROC
              path: sql/proc_reorder.sql
              relativeToChangelogFile: true
              procedureName: REORDER
          - createProcedure:
              fullDefinition: false
              remarks: PROC
              path: sql/proc_replace_session_rank_entry.sql
              relativeToChangelogFile: true
              procedureName: REPLACE_SESSION_RANK_ENTRY
          - createProcedure:
                fullDefinition: false
                remarks: PROC
                path: sql/proc_update_login_data_user_stats.sql
                relativeToChangelogFile: true
                procedureName: UPDATE_LOGIN_DATA_USER_STATISTICS
          - createProcedure:
                fullDefinition: false
                remarks: PROC
                path: sql/proc_update_session_data_user_stats.sql
                relativeToChangelogFile: true
                procedureName: UPDATE_SESSION_DATA_USER_STATISTICS




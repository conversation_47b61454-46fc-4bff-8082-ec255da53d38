create table if not exists activegames
(
    id            bigint                       not null
        primary key,
    active        bit                          not null,
    created_at    datetime                     not null,
    game_url      varchar(4096)                null,
    game_token    varchar(80)                  not null,
    game_id       bigint                       null,
    user_id       bigint                       null,
    auth_token    varchar(80)                  not null,
    expires_at    datetime                     null,
    game_html     varchar(10000)               null,
    start_level   bigint                       null,
    start_percnl  decimal(19, 2)               null,
    vendor_name   varchar(100)   default ''    null,
    platform      varchar(20)    default 'WEB' null,
    country       varchar(5)     default 'DE'  null,
    start_balance decimal(19, 2) default 0.00  null,
    jurisdiction  varchar(5)     default 'SOC' not null,
    hversion      int            default 0     not null,
    index activegames_ug_idx (user_id, game_id),
    constraint UKaggame<PERSON>en unique (game_token)
)
    collate = utf8mb4_unicode_ci;



create table if not exists asset
(
    id         bigint auto_increment
        primary key,
    created_at datetime     null,
    updated_at datetime     null,
    image      mediumblob   null,
    mimetype   varchar(30)  null,
    name       varchar(128) null,
    real_type  varchar(14)  null,
    asset_type varchar(30)  null,
    constraint UKb8bje59o0x1bmjw74fgrdg3ac
        unique (name, asset_type)
)
    collate = utf8mb4_unicode_ci;


create table if not exists consumables
(
    id                bigint auto_increment
        primary key,
    created_at        datetime                  null,
    updated_at        datetime                  null,
    price             decimal(19, 2)            null,
    description       varchar(256)              null,
    item_category     varchar(255)              null,
    item_def          varchar(255)              null,
    product_id        varchar(128)              null,
    title             varchar(128)              null,
    valid_from        datetime                  null,
    valid_to          datetime                  null,
    version           bigint                    null,
    asset_id          bigint                    null,
    bonus_eligible    bit          default b'0' null,
    filter_expression varchar(200) default ''   null
)
    collate = utf8mb4_unicode_ci;

create table if not exists ctransactions
(
    id            bigint                      not null,
    balance_after decimal(19, 2)              null,
    bet           decimal(19, 2)              null,
    created_at    timestamp                   not null,
    ext_tx_numid  varchar(180)                not null,
    ext_tx_id     varchar(180)                null,
    type          varchar(20)                 null,
    vendor_name   varchar(100)                null,
    win           decimal(19, 2)              null,
    game_id       bigint                      null,
    user_id       bigint                      not null,
    earned_xp     bigint                      null,
    level_after   bigint                      null,
    percnl_after  decimal(19, 2)              null,
    round_ref     varchar(128)                null,
    session_id    bigint                      null,
    cancelled     bit            default b'0' null,
    boost         decimal(19, 2) default 0.00 null,
    primary key (id, created_at),
    index ctransactions_index2 (user_id, session_id),
    index transactions_index_created_at (created_at)
)
    collate = utf8mb4_unicode_ci;

create table if not exists custom_sequences
(
    sequence_name          varchar(255) not null
        primary key,
    sequence_next_hi_value bigint       null
)
    collate = utf8mb4_unicode_ci;

create table if not exists db_maintenance_log
(
    id        bigint unsigned auto_increment
        primary key,
    db        varchar(128)  not null,
    statement varchar(1024) not null,
    started   datetime      null,
    finished  datetime      null,
    index idx_started (started)
)
    collate = utf8mb4_unicode_ci;



create table if not exists db_maintenance_table_excludes
(
    database_name varchar(128) not null,
    table_name    varchar(128) not null,
    primary key (database_name, table_name)
)
    collate = utf8mb4_unicode_ci;

create table if not exists game_category
(
    id          int                      not null
        primary key,
    name        varchar(100)             not null,
    type        varchar(20) default ''   not null,
    parent_id   int                      null,
    hidden      bit         default b'0' null,
    slotcatalog bit         default b'1' null,
    index game_category_game_category_id_fk (parent_id),
    index game_category_type_name_index (type, name)

)
    collate = utf8mb4_unicode_ci;


create table if not exists game_integrations
(
    id          int                         not null
        primary key,
    name        varchar(100)                not null,
    description varchar(200) default ''     not null,
    status      varchar(20)  default 'DONE' null,
    constraint game_integrations_name_uindex
        unique (name)
)
    collate = utf8mb4_unicode_ci;

create table if not exists game_promotion
(
    id           bigint auto_increment
        primary key,
    game_id      bigint       not null,
    importance   int          null,
    type         varchar(255) null,
    valid_from   date         null,
    valid_to     date         null,
    feature_text varchar(255) null
)
    collate = utf8mb4_unicode_ci;

create table if not exists game_rank_daily
(
    rdate              date                        not null,
    game_id            bigint                      not null,
    game_name          varchar(100)                not null,
    vendor_name        varchar(128)                not null,
    sessions           bigint         default 0    null,
    spins              bigint         default 0    null,
    session_time_total bigint         default 0    null,
    sum_bet            decimal(19, 2) default 0.00 null,
    sum_win            decimal(19, 2) default 0.00 null,
    rtp                decimal(8, 2)               null,
    primary key (rdate, game_id)
)
    collate = utf8mb4_unicode_ci;

create table if not exists game_statistics
(
    session_id     bigint                                   not null,
    user_id        bigint                                   not null,
    game_id        bigint                                   not null,
    start_at       timestamp      default CURRENT_TIMESTAMP not null,
    end_at         timestamp      default CURRENT_TIMESTAMP not null,
    max_bet        decimal(19, 2) default 0.00              null,
    max_multiplier decimal(19, 2)                           null,
    max_win        decimal(19, 2) default 0.00              null,
    num_plays      bigint         default 0                 null,
    num_wins       bigint         default 0                 null,
    sum_bet        decimal(19, 2) default 0.00              null,
    sum_bonus      decimal(19, 2) default 0.00              null,
    sum_win        decimal(19, 2) default 0.00              null,
    boost          decimal(19, 2) default 0.00              null,
    platform       varchar(30)    default 'ANDROID'         null,
    start_balance  decimal(19, 2) default 0.00              null,
    primary key (start_at, session_id),
    index idx_gs_session_id (session_id)
)
    collate = utf8mb4_unicode_ci partition by range (unix_timestamp(`start_at`)) (
    partition p_first values less than (1609459200),
    partition p202101 values less than (1612137600),
    partition p202102 values less than (1614556800),
    partition p202103 values less than (1617235200),
    partition p202104 values less than (1619827200),
    partition p202105 values less than (1622505600),
    partition p202106 values less than (1625097600),
    partition p202107 values less than (1627776000),
    partition p202108 values less than (1630454400),
    partition p202109 values less than (1633046400),
    partition p202110 values less than (1635724800),
    partition p202111 values less than (1638316800),
    partition p202112 values less than (1640995200),
    partition p_future values less than (MAXVALUE)
    );

create table if not exists issue_comments
(
    id                     bigint auto_increment
        primary key,
    created_at             datetime      null,
    updated_at             datetime      null,
    comment                varchar(2048) null,
    commenter_id           bigint        null,
    issue_id               bigint        null,
    attachment_contenttype varchar(255)  null,
    attachment_name        varchar(255)  null,
    attachment             mediumblob    null,
    index FK1cwv1o145vwja1v6c3gjw28vk (commenter_id),
    index FKnvnj0204928o0w1th5jsx4f28 (issue_id)
)
    collate = utf8mb4_unicode_ci;


create table if not exists issues
(
    id          bigint auto_increment
        primary key,
    created_at  datetime      null,
    updated_at  datetime      null,
    issue_head  varchar(256)  null,
    issue_text  varchar(2048) null,
    status      varchar(30)   not null,
    issue_topic varchar(256)  null,
    assignee_id bigint        null,
    customer_id bigint        null,
    index FK6tkde1c2odhrtreahor01p5fb (assignee_id),
    index FKcf7q73nojgjq16kip3i9ilt1w (customer_id)
)
    collate = utf8mb4_unicode_ci;



create table if not exists levels
(
    level     int                     not null
        primary key,
    xp_start  bigint                  not null,
    xp_end    bigint                  not null,
    rewards   varchar(255) default '' null,
    coinbonus int          default 0  null
)
    collate = utf8mb4_unicode_ci;

create table if not exists localisations
(
    id         bigint auto_increment
        primary key,
    created_at datetime      null,
    updated_at datetime      null,
    langcode   varchar(15)   null,
    literal_id varchar(100)  null,
    template   varchar(1024) null,
    version    bigint        null
)
    collate = utf8mb4_unicode_ci;

create table if not exists maintenance_events
(
    id          bigint auto_increment
        primary key,
    type        varchar(60)                           not null,
    started_at  timestamp   default CURRENT_TIMESTAMP null,
    finished_at timestamp                             null,
    info        varchar(200)                          not null,
    error       text                                  null,
    status      varchar(32) default 'UNKNOWN'         not null
)
    collate = utf8mb4_unicode_ci;

create table if not exists notifications
(
    id           bigint auto_increment
        primary key,
    destination  varchar(200) null,
    msg          varchar(100) null,
    ok           bit          null,
    receipt      varchar(256) null,
    requested_at datetime     null,
    send_at      datetime     null,
    subject      varchar(50)  null,
    type         varchar(255) null,
    user_id      bigint       null
)
    collate = utf8mb4_unicode_ci;

create table if not exists popup_definitions
(
    id              bigint auto_increment
        primary key,
    created_at      timestamp    default CURRENT_TIMESTAMP null,
    updated_at      timestamp                              null,
    title           varchar(128)                           not null,
    body            varchar(512)                           not null,
    languages       varchar(64)                            null,
    type            varchar(64)                            not null,
    assets          varchar(128)                           null,
    campaign_ref    varchar(64)                            null,
    var_default     varchar(128) default ''                null,
    var_definitions varchar(256) default ''                null,
    ingame_allowed  bit          default b'0'              not null,
    buttons         varchar(128) default 'OK'              not null
)
    collate = utf8mb4_unicode_ci;

create table if not exists purchases
(
    id             bigint                         not null,
    created_at     datetime                       not null,
    applied_items  varchar(255)                   null,
    consumable_id  bigint                         null,
    applied_cost   decimal(19, 2)                 null,
    platform       varchar(30)  default 'UNKNOWN' not null,
    receipt        varchar(8192)                  null,
    transaction_id varchar(100) default ''        null,
    user_id        bigint                         not null,
    affiliate_id   bigint                         null,
    payment_method varchar(50)  default ''        null,
    psp            varchar(70)  default ''        null,
    status         varchar(40)  default 'SUCCESS' null,
    abort_reason   varchar(512) default ''        null,
    order_ref      varchar(128) default ''        null,
    primary key (user_id, platform, id)
)
    collate = utf8mb4_unicode_ci partition by hash (user_id) partitions 10;

create table if not exists sec_permission
(
    permission_id varchar(40)  not null
        primary key,
    description   varchar(100) not null
)
    collate = utf8mb4_unicode_ci;

create table if not exists sec_role
(
    role_id     varchar(40)  not null
        primary key,
    description varchar(100) not null
)
    collate = utf8mb4_unicode_ci;

create table if not exists sec_roles_permissions
(
    role_id       varchar(40) not null,
    permission_id varchar(40) not null,
    primary key (role_id, permission_id),
    constraint sec_permission_ibfk_2
        foreign key (permission_id) references sec_permission (permission_id),
    constraint sec_role_ibfk_1
        foreign key (role_id) references sec_role (role_id)
)
    collate = utf8mb4_unicode_ci;

create table if not exists sec_user_roles
(
    user_id    varchar(40)                         not null,
    role_id    varchar(40)                         not null,
    created_at timestamp default CURRENT_TIMESTAMP null,
    created_by varchar(100)                        not null,
    primary key (user_id, role_id)
)
    collate = utf8mb4_unicode_ci;

create table if not exists session
(
    session_id   bigint                                not null,
    user_id      bigint                                not null,
    game_id      bigint                                not null,
    game_token   varchar(80)                           not null,
    status       varchar(10) default 'ACTIVE'          not null,
    platform     varchar(20)                           not null,
    country      varchar(5)                            not null,
    created_at   timestamp                             not null,
    last_action  timestamp   default CURRENT_TIMESTAMP not null,
    jurisdiction varchar(5)  default 'SOC'             not null,
    hversion     int         default 0                 not null,
    primary key (user_id, session_id),
    index SESS_IDX_gametoken (game_token)
)
    collate = utf8mb4_unicode_ci partition by hash (`user_id`) partitions 20;



create table if not exists session_ranks
(
    created_at timestamp not null,
    session_id bigint    not null,
    maxwin     int       not null,
    maxmult    int       not null,
    hsaldo     bigint    not null,
    user_id    bigint    not null,
    game_id    bigint    not null,
    primary key (created_at, session_id),
    index ss_ranks_idx_hsalo (hsaldo),
    index ss_ranks_idx_maxmult (maxmult),
    index ss_ranks_idx_maxwin (maxwin)
);

create table if not exists session_round
(
    user_id     bigint                              not null,
    session_id  bigint                              not null,
    round_ref   varchar(100)                        not null,
    game_id     bigint                              not null,
    created_at  timestamp default CURRENT_TIMESTAMP null,
    last_update timestamp default CURRENT_TIMESTAMP null,
    open        bit       default b'1'              not null,
    primary key (user_id, session_id, round_ref)
)
    collate = utf8mb4_unicode_ci partition by key (`user_id`) partitions 20;

create table if not exists session_transaction
(
    id            bigint                                   not null,
    session_id    bigint                                   not null,
    round_ref     varchar(100)                             not null,
    balance_after decimal(16, 4)                           not null,
    bet           decimal(16, 4) default 0.0000            not null,
    win           decimal(16, 4) default 0.0000            not null,
    tax           decimal(16, 4) default 0.0000            not null,
    ext_tx_id     varchar(100)                             not null,
    type          varchar(20)                              not null,
    game_id       bigint         default -1                not null,
    user_id       bigint                                   not null,
    earned_xp     int unsigned   default 0                 not null,
    level_after   int unsigned   default 0                 not null,
    percnl_after  decimal(5, 2)  default 0.00              not null,
    cancelled     bit            default b'0'              not null,
    boost         decimal(16, 4) default 0.0000            not null,
    created_at    timestamp      default CURRENT_TIMESTAMP not null,
    updated_at    timestamp      default CURRENT_TIMESTAMP not null,
    primary key (user_id, session_id, ext_tx_id)
)
    collate = utf8mb4_unicode_ci partition by key (`user_id`) partitions 20;


create table if not exists transactions
(
    id            bigint                      not null,
    balance_after decimal(19, 2)              null,
    bet           decimal(19, 2)              null,
    created_at    datetime                    null,
    ext_tx_numid  varchar(180)                not null,
    ext_tx_id     varchar(180)                null,
    type          varchar(20)                 null,
    vendor_name   varchar(100)                null,
    win           decimal(19, 2)              null,
    game_id       bigint                      null,
    user_id       bigint                      not null,
    earned_xp     bigint                      null,
    level_after   bigint                      null,
    percnl_after  decimal(19, 2)              null,
    round_ref     varchar(128)                null,
    session_id    bigint                      null,
    cancelled     bit            default b'0' null,
    boost         decimal(19, 2) default 0.00 null,
    primary key (id, user_id),
    index transactions__index_created_at (created_at),
    index transactions_round_ref_index (round_ref),
    index transactions_session_id_index (session_id),
    constraint transaction_main_idx
        unique (user_id, vendor_name, ext_tx_numid)
)
    collate = utf8mb4_unicode_ci partition by hash (user_id) partitions 10;

create table if not exists user_game_attributes
(
    id         bigint                             not null
        primary key,
    favorites  varchar(1200)                      null,
    playlist   varchar(1200)                      null,
    updated_at datetime default CURRENT_TIMESTAMP null
)
    collate = utf8mb4_unicode_ci partition by hash (id) partitions 10;

create table if not exists user_info
(
    user_id         bigint                     not null
        primary key,
    agb             bit                        null,
    birthdate       date                       null,
    crm_update      datetime                   null,
    directContact   bit                        null,
    gender          varchar(10)                null,
    mobile          varchar(100)               null,
    name            varchar(100)               null,
    newsletter      bit                        null,
    surname         varchar(100)               null,
    updated_at      datetime                   null,
    pref_lang       varchar(4)    default 'EN' null,
    slot_sound      bit           default b'1' null,
    game_sound      bit           default b'1' null,
    reg_bonus       datetime                   null,
    app_settings    varchar(1024) default '{}' null,
    username_public bit           default b'0' null,
    email           varchar(150)  default ''   null,
    birthplace      varchar(100)               null,
    nationality     varchar(10)                null,
    adr_city        varchar(100)               null,
    adr_street      varchar(100)               null,
    adr_street_nr   varchar(20)                null,
    adr_zipcode     varchar(20)                null,
    adr_country     varchar(10)                null,
    kyc_timestamp   timestamp                  null,
    kyc_status      varchar(25)                null,
    kyc_id          varchar(40)                null
)
    collate = utf8mb4_unicode_ci partition by hash (user_id) partitions 10;

create table if not exists user_login
(
    user_id    bigint                              not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    platform   varchar(20)                         not null,
    device_id  varchar(50)                         null,
    fcm_token  varchar(180)                        null,
    ip_address varchar(60)                         not null,
    country    varchar(60)                         not null,
    logindata  json                                not null,
    primary key (created_at, user_id, platform),
    index idx_userlogin_uid (user_id)
)
    collate = utf8mb4_unicode_ci;



create table if not exists user_mail
(
    email    varchar(128)           not null
        primary key,
    local_id varchar(128)           not null,
    user_id  bigint                 not null,
    hsh      varchar(96) default '' null,
    ref_tk   varchar(100)           null,
    ref_exp  timestamp              null
)
    collate = utf8mb4_unicode_ci;

create table if not exists user_popups
(
    user_id          bigint                                 not null,
    popup_id         bigint                                 not null,
    selector         varchar(64)                            not null,
    variables        varchar(256) default ''                not null,
    created_at       timestamp    default CURRENT_TIMESTAMP not null,
    expires_at       timestamp                              null,
    shown_at         timestamp                              null,
    action_performed int          default -1                not null,
    primary key (user_id, popup_id, selector)
)
    collate = utf8mb4_unicode_ci partition by hash ( `user_id`) partitions 10;

create table if not exists user_registration
(
    local_id       varchar(128)                        not null
        primary key,
    user_id        bigint                              not null,
    created_at     timestamp default CURRENT_TIMESTAMP null,
    status         varchar(30)                         not null,
    login_provider varchar(200)                        not null
)
    collate = utf8mb4_unicode_ci;

create table if not exists user_removals
(
    id         bigint                                not null
        primary key,
    localid    varchar(64) default ''                null,
    reason     varchar(64) default ''                null,
    stage      varchar(64) default ''                null,
    balance    decimal(18, 4)                        null,
    created_at timestamp   default CURRENT_TIMESTAMP null,
    deleted_at timestamp                             null
)
    collate = utf8mb4_unicode_ci;

create table if not exists user_statistics
(
    user_id              bigint                        not null
        primary key,
    ss_session_count     int            default 0      not null,
    ss_sum_win           decimal(22, 4) default 0.0000 not null,
    ss_sum_bet           decimal(22, 4) default 0.0000 not null,
    ss_num_spins         int            default 0      not null,
    ss_num_wins          int            default 0      not null,
    ss_first_session_at  timestamp                     null,
    ss_last_session_at   timestamp                     null,
    ss_total_session_sec int            default 0      not null,
    first_login          timestamp                     null,
    last_login           timestamp                     null
);

create table if not exists user_transaction
(
    id            bigint                                   not null,
    user_id       bigint                                   not null,
    type          varchar(20)                              not null,
    ref_id        varchar(80)                              not null,
    description   varchar(100)                             not null,
    created_at    timestamp      default CURRENT_TIMESTAMP not null,
    credit        decimal(16, 4) default 0.0000            not null,
    withdraw      decimal(16, 4) default 0.0000            not null,
    balance_after decimal(16, 4) default 0.0000            not null,
    cancel_id     bigint         default 0                 not null,
    primary key (user_id, type, ref_id)
)
    collate = utf8mb4_unicode_ci partition by key (`user_id`) partitions 20;

create table if not exists user_verifications
(
    user_id           bigint                              not null,
    created_at        timestamp default CURRENT_TIMESTAMP null,
    verified_at       timestamp                           null,
    code              varchar(40)                         not null
        primary key,
    code_short        varchar(10)                         not null,
    verification_type varchar(30)                         not null,
    attribute         varchar(150)                        not null
)
    collate = utf8mb4_unicode_ci;

create table if not exists users
(
    id              bigint                                 not null
        primary key,
    created_at      datetime     default CURRENT_TIMESTAMP null,
    updated_at      datetime     default CURRENT_TIMESTAMP null,
    email           varchar(120)                           not null,
    status          varchar(30)                            null,
    local_id        varchar(150)                           not null,
    system_user     bit          default b'0'              null,
    display_name    varchar(128)                           null,
    email_verified  bit                                    null,
    signin_provider varchar(70)                            null,
    crm_update      datetime                               null,
    tags            varchar(100) default ''                null,
    constraint UK_users_email
        unique (email),
    constraint UK_users_lid
        unique (local_id)
)
    collate = utf8mb4_unicode_ci;

create table if not exists aff_campaigns
(
    user_id             bigint                                   not null,
    num                 int                                      not null,
    created_at          timestamp      default CURRENT_TIMESTAMP null,
    code                varchar(32)                              not null,
    revshare            decimal(4, 2)  default 5.00              not null,
    cpa                 decimal(5)     default 0                 not null,
    cpa_baseline        decimal(6, 2)  default 0.00              not null,
    admin_fee           decimal(4, 2)  default 0.00              not null,
    cpas_awarded        int            default 0                 not null,
    cpas_awarded_at     timestamp      default CURRENT_TIMESTAMP not null,
    deposits_awarded    decimal(18, 4) default 0.0000            not null,
    deposits_awarded_at timestamp      default CURRENT_TIMESTAMP not null,
    wagers_awarded      decimal(18, 4) default 0.0000            not null,
    wagers_awarded_at   timestamp      default CURRENT_TIMESTAMP not null,
    primary key (user_id, num),
    constraint code
        unique (code),
    constraint aff_campaigns_ibfk_1
        foreign key (user_id) references users (id)
)
    collate = utf8mb4_unicode_ci;

create table if not exists aff_links
(
    user_id    bigint                              not null,
    num        int                                 not null,
    ref_id     bigint                              not null,
    created_at timestamp default CURRENT_TIMESTAMP null,
    primary key (user_id, num, ref_id),
    index ref_id (ref_id),
    constraint aff_links_ibfk_1
        foreign key (ref_id) references users (id),
    constraint aff_links_ibfk_2
        foreign key (user_id, num) references aff_campaigns (user_id, num)
)
    collate = utf8mb4_unicode_ci;



create table if not exists vendors
(
    id                int                                    not null
        primary key,
    created_at        timestamp    default CURRENT_TIMESTAMP null,
    updated_at        timestamp    default CURRENT_TIMESTAMP null,
    vendor_name       varchar(50)                            not null,
    bridge_name       varchar(50)                            not null,
    slotcatalog_name  varchar(50)                            null,
    provider_homepage varchar(100)                           null,
    vendor_order      int          default 99                null,
    active            bit          default b'0'              not null,
    blocked_countries varchar(100) default ''                null,
    constraint bridge_name
        unique (bridge_name),
    constraint vendor_name
        unique (vendor_name)
)
    collate = utf8mb4_unicode_ci;

create table if not exists game_info
(
    id             int                                     not null
        primary key,
    vendor_id      int                                     null,
    game_name      varchar(100)                            null,
    slot_rank      int                                     null,
    vendor_name    varchar(100)                            null,
    volatility     varchar(10)                             null,
    rtp            decimal(5, 2)                           null,
    release_date   date                                    null,
    type           varchar(40)                             null,
    layout         varchar(50)                             null,
    rtp_comment    varchar(386)                            null,
    paylines       int                                     null,
    modified_at    timestamp     default CURRENT_TIMESTAMP null,
    description    varchar(2048)                           null,
    description_de varchar(2048) default ''                null,
    data           varchar(4096)                           null,
    index idx_vendor_id (vendor_id),
    constraint game_info_ibfk_1
        foreign key (vendor_id) references vendors (id)
)
    collate = utf8mb4_unicode_ci;


create table if not exists game_info_content
(
    info_id    int                                 not null,
    content_id int                                 not null,
    name_cdn   varchar(32)                         not null,
    fetched    bit       default b'0'              null,
    visible    bit       default b'0'              null,
    created_at timestamp default CURRENT_TIMESTAMP null,
    updated_at timestamp default CURRENT_TIMESTAMP null,
    format     varchar(12)                         null,
    primary key (info_id, content_id),
    index idx_info_id (info_id),
    constraint game_info_content_ibfk_1
        foreign key (info_id) references game_info (id)
)
    collate = utf8mb4_unicode_ci;



create table if not exists game_integrations_vendors
(
    integration_id  int         not null,
    vendor_id       int         not null,
    integration_key varchar(50) not null,
    jurisdiction    varchar(10) not null,
    primary key (vendor_id, integration_id, integration_key, jurisdiction),
    constraint game_integrations_vendors_game_integrations_id_fk
        foreign key (integration_id) references game_integrations (id),
    constraint game_integrations_vendors_vendors_id_fk
        foreign key (vendor_id) references vendors (id)
)
    collate = utf8mb4_unicode_ci;

create table if not exists games
(
    id            bigint                             not null
        primary key,
    vendor_id     int                                null,
    name          varchar(100)                       null,
    info_id       int                                null,
    provider_name varchar(128)                       not null,
    game_id       varchar(128)                       not null,
    active        bit      default b'0'              null,
    desktop       bit      default b'0'              null,
    mobile        bit      default b'0'              null,
    ios           bit      default b'1'              null,
    android       bit      default b'0'              not null,
    sort_order    int                                null,
    created_at    datetime default CURRENT_TIMESTAMP null,
    updated_at    datetime default CURRENT_TIMESTAMP null,
    index games_idx_info_id (info_id),
    index games_idx_vendor_id (vendor_id),
    constraint provider_name
        unique (provider_name, game_id),
    constraint games_ibfk_1
        foreign key (info_id) references game_info (id),
    constraint games_ibfk_2
        foreign key (vendor_id) references vendors (id)
)
    collate = utf8mb4_unicode_ci;

create table if not exists game_category_link
(
    game_id     bigint not null,
    category_id int    not null,
    primary key (game_id, category_id),
    constraint game_category_link_game_category_id_fk
        foreign key (category_id) references game_category (id),
    constraint game_category_link_games_id_fk
        foreign key (game_id) references games (id)
)
    collate = utf8mb4_unicode_ci;

create table if not exists game_image
(
    game_id    bigint                             not null,
    type       varchar(30)                        not null,
    created_at datetime default CURRENT_TIMESTAMP null,
    updated_at datetime default CURRENT_TIMESTAMP null,
    image      mediumblob                         not null,
    real_type  varchar(14)                        null,
    mimetype   varchar(30)                        null,
    primary key (game_id, type),
    constraint game_image_ibfk_1
        foreign key (game_id) references games (id)
)
    collate = utf8mb4_unicode_ci;

create table if not exists wallets
(
    balance      decimal(16, 4) null,
    num_played   bigint         null,
    num_won      bigint         null,
    level        bigint         null,
    mbp          bigint         null,
    percnl       decimal(3, 2)  null,
    sum_bet      decimal(20, 2) null,
    sum_win      decimal(20, 2) null,
    version      bigint         null,
    xp           bigint         null,
    user_id      bigint         not null
        primary key,
    active_perks varchar(200)   null,
    saveup       decimal(7, 4)  null,
    spintimer    datetime       null
)
    collate = utf8mb4_unicode_ci partition by hash (user_id) partitions 10;


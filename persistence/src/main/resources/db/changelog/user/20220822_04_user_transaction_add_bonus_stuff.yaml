databaseChangeLog:
#  - preConditions:
#      - onFail: MARK_RAN
#      - sqlCheck:
#          expectedResult: 0
#          sql: SELECT count(*) FROM information_schema.COLUMNS c WHERE c.TABLE_NAME = 'user_transaction' and c.COLUMN_NAME='bcredit'
  - changeSet:
      id: u-20220822-04
      author: j<PERSON><PERSON>
      runOnChange: false
      changes:
        - sqlFile:
            fullDefinition: false
            remarks: add bonus columns
            path: sql/user_transaction_add_bonus_columns.sql
            relativeToChangelogFile: true
            stripComments: true


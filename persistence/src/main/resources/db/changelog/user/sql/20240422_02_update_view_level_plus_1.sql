CREATE OR REPLACE VIEW `v_user_search` AS
select `u`.`id`                                                                AS `id`,
       `u`.`status`                                                            AS `status`,
       `u`.`email`                                                             AS `email`,
       `u`.`local_id`                                                          AS `localId`,
       `u`.`created_at`                                                        AS `createdAt`,
       coalesce(`u`.`system_user`, false)                                      AS `systemUser`,
       `u`.`display_name`                                                      AS `displayName`,
       `w`.`balance`                                                           AS `balance`,
       `w`.`level` +1                                                          AS `level`,
       `w`.`saveup`                                                            AS `saveup`,
       `w`.`xp`                                                                AS `xp`,
       `ui`.`name`                                                             AS `name`,
       `ui`.`surname`                                                          AS `surname`,
       coalesce(`ui`.`game_sound`, false)                                      AS `gameSound`,
       `ui`.`birthdate`                                                        AS `birthdate`,
       coalesce(`ui`.`directContact`, false)                                   AS `directContact`,
       `ui`.`gender`                                                           AS `gender`,
       `ui`.`mobile`                                                           AS `mobile`,
       coalesce(`ui`.`username_public`, false)                                 AS `usernamePublic`,
       coalesce(`us`.`ss_session_count`, 0)                                    AS `sessionCount`,
       coalesce(`us`.`ss_sum_win`, 0)                                          AS `sumWin`,
       coalesce(`us`.`ss_sum_bet`, 0)                                          AS `sumBet`,
       coalesce(`us`.`ss_num_spins`, 0)                                        AS `sumSpins`,
       `us`.`first_login`                                                      AS `firstLogin`,
       `us`.`last_login`                                                       AS `lastLogin`,
       coalesce((to_days(`us`.`last_login`) - to_days(`us`.`first_login`)), 0) AS `activeDays`,
       us.logins_ios                                                           AS `iosLogins`,
       us.logins_web                                                           AS `webLogins`,
       us.logins_android                                                       AS `androidLogins`,
       ''                                                                      AS `devices`,
       us.num_purchases                                                        AS `countPurchases`,
       us.sum_purchases                                                        AS `sumPurchases`,
       us.first_purchase                                                       AS `firstPurchase`,
       us.last_purchase                                                        AS `lastPurchase`,
       concat(`u`.`email`, ',', `u`.`display_name`)                            AS `usernameAndEmail`,
       coalesce(us.tags, '')                                                   AS `tags`
from `users` `u`
         join `user_statistics` `us`
              on `u`.`id` = `us`.`user_id`
         join `wallets` `w`
              on `u`.`id` = `w`.`user_id`
         left join `user_info` `ui`
                   on `u`.`id` = `ui`.`user_id`;


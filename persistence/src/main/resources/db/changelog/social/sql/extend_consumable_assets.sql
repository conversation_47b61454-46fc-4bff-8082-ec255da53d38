ALTER TABLE consumables
    ADD COLUMN priority BIGINT DEFAULT 1,
    ADD COLUMN size VARCHAR(255),
    ADD COLUMN effect VARCHAR(255),
    ADD COLUMN left_corner_asset_id BIGINT NULL,
    ADD COLUMN right_badge_asset_id BIGINT NULL,
    ADD COLUMN right_badge_text VARCHAR(255) DEFAULT '',
    ADD COLUMN background_asset_id BIGINT NULL,
    ADD COLUMN default_coin_multiplier INT DEFAULT 100;

ALTER TABLE consumables_custom
    ADD COLUMN priority BIGINT DEFAULT 1,
    ADD COLUMN size VARCHAR(255),
    ADD COLUMN effect VARCHAR(255),
    ADD COLUMN left_corner_asset_id BIGINT NULL,
    ADD COLUMN right_badge_asset_id BIGINT NULL,
    ADD COLUMN right_badge_text VARCHAR(255) DEFAULT '',
    ADD COLUMN background_asset_id BIGINT NULL;

ALTER TABLE consumables_custom_archive
    ADD COLUMN priority BIGINT DEFAULT 1,
    ADD COLUMN size VARCHAR(255),
    ADD COLUMN effect VARCHAR(255),
    ADD COLUMN left_corner_asset_id BIGINT NULL,
    ADD COLUMN right_badge_asset_id BIGINT NULL,
    ADD COLUMN right_badge_text VARCHAR(255) DEFAULT '',
    ADD COLUMN background_asset_id BIGINT NULL;

create procedure REPLACE_STATS_DAILY(IN statsdate date, IN social bit)
    modifies sql data
BEGIN

    DELETE
    from stats_daily
    where rdate = statsdate;


    insert into stats_daily (rdate, bets_count, wins_count, bonus_bets_count, bonus_wins_count, bets_sum, wins_sum,
                             bonus_bets_sum, bonus_wins_sum)
    select statsdate                                as rdate,
           count(CASE WHEN st.bet != 0 THEN 1 END)  as bets_count,
           count(CASE WHEN st.win != 0 THEN 1 END)  as wins_count,
           count(CASE WHEN st.bbet != 0 THEN 1 END) as bonus_bets_count,
           count(CASE WHEN st.bwin != 0 THEN 1 END) as bonus_wins_count,
           COALESCE(sum(st.bet), 0)                 as bets_sum,
           COALESCE(sum(st.win), 0)                 as wins_sum,
           COALESCE(sum(st.bbet), 0)                as bonus_bets_sum,
           COALESCE(sum(st.bwin), 0)                as bonus_wins_sum
    from session s
             join session_transaction st on s.user_id = st.user_id and s.session_id = st.session_id
    where s.created_at >= DATE_SUB(statsdate, INTERVAL 1 DAY)
      and s.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
      and st.created_at >= statsdate
      and st.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)
      and st.cancelled = false
      and st.type in ('BET', 'WIN', 'DIRECTWIN');


    insert into stats_daily (rdate, ftds)
    select *
    from (select statsdate as rdate, count(*) as ftds
          from user_stats_daily
          where rdate = statsdate
            AND depos != 0
            AND depos_total = depos) new
    on duplicate key
        update ftds = new.ftds;


    insert into stats_daily (rdate, active_players)
    select *
    from (select statsdate as rdate, count(DISTINCT user_id) as active_players
          from user_login ul
          where ul.created_at >= statsdate
            and ul.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)) new
    on duplicate key
        update active_players = new.active_players;


    insert into stats_daily (rdate, sign_ups)
    select *
    from (select statsdate as rdate, count(*) as sign_ups
          from users u
          where u.created_at >= statsdate
            and u.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)) new
    on duplicate key
        update sign_ups = new.sign_ups;


    insert into stats_daily (rdate, rewards_count, rewards_sum, bonus_rollover_sum, bonus_rollover_count)
    select *
    from (select statsdate                                                                  as rdate,
                 COUNT(CASE WHEN type = 'RAKEBACK_BONUS' THEN 1 END)                        as rewards_count,
                 COALESCE(SUM(CASE WHEN type = 'RAKEBACK_BONUS' THEN credit ELSE 0 END), 0) as rewards_sum,
                 COUNT(CASE WHEN type = 'BONUS_ROLLOVER' THEN 1 END)                        as bonus_rollover_count,
                 COALESCE(SUM(CASE WHEN type = 'BONUS_ROLLOVER' THEN credit ELSE 0 END), 0) as bonus_rollover_sum
          from user_transaction ut
          where ut.created_at >= statsdate
            and ut.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)) new
    on duplicate key
        update bonus_rollover_sum   = new.bonus_rollover_sum,
               bonus_rollover_count = new.bonus_rollover_count,
               rewards_count        = new.rewards_count,
               rewards_sum          = new.rewards_sum;


    insert into stats_daily (rdate, bonus_claimed_count, bonus_claimed_sum, fs_bets, fs_wins)
    select *
    from (select statsdate                      as rdate,
                 count(*)                       as bonus_claimed_count,
                 COALESCE(sum(bonus_amount), 0) as bonus_claimed_amount,
                 COALESCE(sum(bet), 0)          as fs_bets,
                 COALESCE(sum(win), 0)          as fs_wins
          from user_bonuses ub
          where ub.created_at >= statsdate
            and ub.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)) new
    on duplicate key
        update bonus_claimed_count = new.bonus_claimed_count,
               bonus_claimed_sum   = new.bonus_claimed_amount;


    if
        (social is true) then
        insert into stats_daily (rdate, deposits_count, deposits_user_count, deposits_sum)
        select *
        from (select statsdate                      as rdate,
                     count(*)                       as deposits_count,
                     count(DISTINCT user_id)        as deposits_user_count,
                     COALESCE(sum(applied_cost), 0) as deposits_sum
              from purchases pd
              where pd.created_at >= statsdate
                and pd.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)) new
        on duplicate key
            update deposits_count      = new.deposits_count,
                   deposits_user_count = new.deposits_user_count,
                   deposits_sum        = new.deposits_sum;
    ELSE
        insert into stats_daily (rdate, deposits_count, deposits_user_count, deposits_sum)
        select *
        from (select statsdate                as rdate,
                     count(*)                 as deposits_count,
                     count(DISTINCT user_id)  as deposits_user_count,
                     COALESCE(sum(amount), 0) as deposits_sum
              from payment_deposit pd
              where pd.created_at >= statsdate
                and pd.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)) new
        on duplicate key
            update deposits_count      = new.deposits_count,
                   deposits_user_count = new.deposits_user_count,
                   deposits_sum        = new.deposits_sum;

        insert into stats_daily (rdate, payouts_count, payouts_user_count, payouts_sum)
        select *
        from (select statsdate                as rdate,
                     count(*)                 as payouts_count,
                     count(DISTINCT user_id)  as payouts_user_count,
                     COALESCE(sum(amount), 0) as payouts_sum
              from payment_payout pp
              where pp.created_at >= statsdate
                and pp.created_at < DATE_ADD(statsdate, INTERVAL 1 DAY)) new
        on duplicate key
            update payouts_count      = new.payouts_count,
                   payouts_user_count = new.payouts_user_count,
                   payouts_sum        = new.payouts_sum;
    END IF;
END;

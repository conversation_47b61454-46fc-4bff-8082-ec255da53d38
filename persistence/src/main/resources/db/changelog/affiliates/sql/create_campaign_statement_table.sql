CREATE TABLE `aff_campaign_stmnt`
(
    `user_id`      bigint    NOT NULL,
    `num`          int           NOT NULL,
    `rdate`        DATE          NOT NULL,
    `amount`    decimal(14, 4) NOT NULL DEFAULT '0.00',
    `utx_id`  bigint NULL,
    `booked_at`    TIMESTAMP  NULL ,
    PRIMARY KEY (`user_id`, `num`,`rdate`),
    CONSTRAINT `aff_cmp_stmnt_fk1` FOREIGN KEY (`user_id`, `num`) REFERENCES `aff_campaigns` (`user_id`, `num`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

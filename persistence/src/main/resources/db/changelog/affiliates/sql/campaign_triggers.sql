create trigger aff_campaign_on_insert
    after insert
    on aff_campaigns
    for each row
    CALL ADD_CAMPAIGN_HISTORY(NEW.user_id, NEW.num, CURDATE(),
                              NEW.admin_fee,NEW.rs_deposits,NEW.rs_ggr,NEW.rs_wagers,NEW.cpa,NEW.cpa_baseline,
                              null,null,null,null,null,null);

create trigger aff_campaign_on_update
    after update
    on aff_campaigns
    for each row
    CALL ADD_CAMPAIGN_HISTORY(NEW.user_id, NEW.num, CURDATE(),
                              NEW.admin_fee,NEW.rs_deposits,NEW.rs_ggr,NEW.rs_wagers,NEW.cpa,NEW.cpa_baseline,
                              OLD.admin_fee,OLD.rs_deposits,OLD.rs_ggr,OLD.rs_wagers,OLD.cpa,OLD.cpa_baseline);



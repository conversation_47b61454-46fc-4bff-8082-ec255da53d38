CREATE TABLE `aff_campaigns_history`
(
    `user_id`      bigint(20)    NOT NULL,
    `num`          int           NOT NULL,
    `rdate`        DATE          NOT NULL,
    `admin_fee`    decimal(5, 3) NOT NULL DEFAULT '0.00',
    `rs_deposits`  decimal(5, 3) NOT NULL DEFAULT '0.00',
    `rs_wagers`    decimal(5, 3) NOT NULL DEFAULT '0.00',
    `rs_ggr`       decimal(5, 3) NOT NULL DEFAULT '0.00',
    `cpa`          decimal(5)    NOT NULL DEFAULT '0.00',
    `cpa_baseline` decimal(6, 2) NOT NULL DEFAULT '0.00',
    PRIMARY KEY (`user_id`, `num`,`rdate`),
    KEY `ach_rdate` (`rdate`),
    CONSTRAINT `aff_campaigns_hist_fk_2` FOREIGN KEY (`user_id`, `num`) REFERENCES `aff_campaigns` (`user_id`, `num`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

package com.ously.gamble.dynatrace;

import com.dynatrace.oneagent.sdk.api.LoggingCallback;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DynatraceLoggingCallback implements LoggingCallback {
    final Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    public void error(String message) {
        log.error("[OneAgent SDK ERROR]:{} ", message);
    }

    @Override
    public void warn(String message) {
        log.warn("[OneAgent SDK WARNING]: {}", message);
    }


}

package com.ously.gamble.gateways;

import com.ously.gamble.api.postbacks.PostbackAdapter;
import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PostbackGatewayConfiguration {


    @Bean
    DirectExchange postbackExchange() {
        return ExchangeBuilder.directExchange("postback").durable(true).build();
    }

    @Bean
    Binding sendoutBinding() {
        return BindingBuilder.bind(sendoutQueue()).to(postbackExchange()).withQueueName();
    }


    @Bean
    Queue sendoutQueue() {
        return QueueBuilder.durable(PostbackAdapter.POSTBACK_QUEUE_SENDOUT)
                .withArgument("x-dead-letter-exchange", "")
                .withArgument("x-dead-letter-routing-key", PostbackAdapter.POSTBACK_QUEUE_SENDOUT + "DLQ")
                .build();
    }

    @Bean
    Queue sendoutQueueDLQ() {
        return QueueBuilder.durable(PostbackAdapter.POSTBACK_QUEUE_SENDOUT + "DLQ").build();
    }
}

package com.ously.gamble.shutdown;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ThreadPoolShutdownHand<PERSON> implements ApplicationListener<ContextClosedEvent> {
    private final List<ThreadPoolTaskExecutor> threadPools;
    private final Logger log = LoggerFactory.getLogger(ThreadPoolShutdownHandler.class);

    public ThreadPoolShutdownHandler(List<ThreadPoolTaskExecutor> threadPools) {
        this.threadPools = threadPools;
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        for (ThreadPoolTaskExecutor threadPool : threadPools) {
            log.info("Shutting down thread pool '{}'", threadPool.getThreadNamePrefix());
            threadPool.shutdown();
        }
    }
}
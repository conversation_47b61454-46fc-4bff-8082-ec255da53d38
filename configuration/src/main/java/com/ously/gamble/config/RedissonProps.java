package com.ously.gamble.config;

import org.redisson.config.ReadMode;
import org.redisson.config.SubscriptionMode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties("ously.redisson")
public class RedissonProps {

    String[] host = new String[]{"localhost"};

    String password;

    Boolean ssl = false;

    String type = "SINGLE";

    Integer port = 6379;

    ReadMode mode = ReadMode.MASTER;

    SubscriptionMode smode=SubscriptionMode.MASTER;

    Integer nodecount = 1;

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String[] getHost() {
        return host;
    }

    public void setHost(String... host) {
        this.host = host;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Boolean getSsl() {
        return ssl;
    }

    public void setSsl(Boolean ssl) {
        this.ssl = ssl;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public ReadMode getMode() {
        return mode;
    }

    public void setMode(ReadMode mode) {
        this.mode = mode;
    }

    public Integer getNodecount() {
        return nodecount;
    }

    public void setNodecount(Integer nodecount) {
        this.nodecount = nodecount;
    }

    public SubscriptionMode getSmode() {
        return smode;
    }

    public void setSmode(SubscriptionMode smode) {
        this.smode = smode;
    }
}

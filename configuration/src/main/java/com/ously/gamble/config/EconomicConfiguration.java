package com.ously.gamble.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "economic")
public class EconomicConfiguration {

    long baseBugdet = 1000L;
    long initialScale = 75;
    long registrationScale = 35;
    long doubleUpSaldoLimit = 5L;
    long loyaltyCoinScale = 500L;
    long piggyConversionScale = 20;

    public long getPiggyConversionScale() {
        return piggyConversionScale;
    }

    public void setPiggyConversionScale(long piggyConversionScale) {
        this.piggyConversionScale = piggyConversionScale;
    }

    public long getDoubleUpSaldoLimit() {
        return doubleUpSaldoLimit;
    }

    public long getLoyaltyCoinScale() {
        return loyaltyCoinScale;
    }

    public void setLoyaltyCoinScale(long loyaltyCoinScale) {
        this.loyaltyCoinScale = loyaltyCoinScale;
    }

    public void setDoubleUpSaldoLimit(long doubleUpSaldoLimit) {
        this.doubleUpSaldoLimit = doubleUpSaldoLimit;
    }

    public long getBaseBugdet() {
        return baseBugdet;
    }

    public void setBaseBugdet(long baseBugdet) {
        this.baseBugdet = baseBugdet;
    }

    public long getInitialScale() {
        return initialScale;
    }

    public void setInitialScale(long initialScale) {
        this.initialScale = initialScale;
    }

    public long getRegistrationScale() {
        return registrationScale;
    }

    public void setRegistrationScale(long registrationScale) {
        this.registrationScale = registrationScale;
    }

    public long getInitialCoinAmount() {
        return (baseBugdet * initialScale) / 100;
    }


    public long getScaledLoyaltyCoins(long tableLoyaltyCoins) {
        return (tableLoyaltyCoins * loyaltyCoinScale) / 100;
    }

    public long getConvertedPiggybankValue(long piggyStatus) {
        return ((piggyStatus * piggyConversionScale) / 100) * 30;
    }


}

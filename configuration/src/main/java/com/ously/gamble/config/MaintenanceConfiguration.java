package com.ously.gamble.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "maintenance")
public class MaintenanceConfiguration {

    int maxTransactionAgeInHours = 48;


    public int getMaxTransactionAgeInHours() {
        return maxTransactionAgeInHours;
    }


    public void setMaxTransactionAgeInHours(int maxTransactionAgeInHours) {
        this.maxTransactionAgeInHours = maxTransactionAgeInHours;
    }
}

package com.ously.gamble.config;

import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.core5.util.TimeValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;

public class RestIdleConnectionChecker implements Runnable {
    private static final Logger LOGGER = LoggerFactory.getLogger(RestIdleConnectionChecker.class);

    private static final int CLOSE_IDLE_CONNECTION_WAIT_TIME_SECS = 60;

    private final PoolingHttpClientConnectionManager connectionManager;

    public RestIdleConnectionChecker(final PoolingHttpClientConnectionManager connectionManager) {
        this.connectionManager = connectionManager;
    }

    @Override
    @Scheduled(fixedDelay = 10000)
    public void run() {
        LOGGER.debug("IdleConnectionChecker!!!");
        try {
            if (connectionManager != null) {
                LOGGER.trace("run IdleConnectionMonitor - Closing expired and idle connections...");
                connectionManager.closeExpired();
                connectionManager.closeIdle(TimeValue.ofSeconds(CLOSE_IDLE_CONNECTION_WAIT_TIME_SECS));
            } else {
                LOGGER.trace("run IdleConnectionMonitor - Http Client Connection manager is not initialised");
            }
        } catch (Exception e) {
            LOGGER.error("run IdleConnectionMonitor - Exception occurred. msg={}", e.getMessage(), e);
        }
    }
}

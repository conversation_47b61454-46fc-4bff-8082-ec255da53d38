#
# Feature and base properties
#
features.baseUrl=https://sadestg.ouslygroup.com
features.autoActive=false
features.stage=test
features.testing=false
features.popupsEnabled=false
features.jurisdiction=DE
features.appBaseUrl=https://test.spinarena.de
features.mailfrom=<EMAIL>
#
# Redis
#
ously.redisson.port=6379
ously.redisson.ssl=false
# SLAVE,MASTER,MASTER_SLAVE (for replicated)
ously.redisson.mode=MASTER_SLAVE
ously.redisson.type=SINGLE
#
# Metrics stuff
#
management.metrics.data.repository.autotime.enabled=false
#management.metrics.web.client.request.autotime.enabled=false
#management.metrics.web.server.request.autotime.enabled=false
management.auditevents.enabled=false
#management.trace.http.enabled=false
#
# Liquibase
#
spring.liquibase.contexts=${features.stage}
spring.liquibase.enabled=true
spring.liquibase.user=cpp
#
# RabbitMQ
#
spring.rabbitmq.username=xx
spring.rabbitmq.password=xx
spring.rabbitmq.addresses=amqp://rbmq
spring.rabbitmq.virtual-host=sade-stg
#
# Gamemanager
#
seriously.gamemanager.url=http://service-gamemanager.seriously-shared:9001/api/v1
seriously.gamemanager.key=gmgrTestPkLdfji3!2e9BE22

spring.config.import=classpath:module-config/dataproxy.properties,\
  classpath:module-config/autoconfiguration-exclusions.properties,\
  classpath:module-config/spring.properties
#
# Base features
#
features.jurisdiction=DE
features.platform=SPINARENA_DE
features.sync.cron=0 32 * * * *
#
# Base features
#
seriously.rtp.minspins=10
#
# server settings
#
server.compression.enabled=true
server.http2.enabled=false
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=2048
server.jetty.max-http-form-post-size=10485760B
server.error.include-stacktrace=never

#
# Task scheduling pool configuration
#
spring.task.scheduling.pool.size=10
spring.task.scheduling.shutdown.await-termination=true
spring.task.scheduling.shutdown.await-termination-period=15s
#
# Conn pooling
#
spring.datasource.hikari.connectionTimeout=10000
spring.datasource.hikari.idleTimeout=900000
spring.datasource.hikari.maxLifetime=3600000
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.leak-detection-threshold=60000
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.auto-commit=false
#
# JPA settings
#
spring.jpa.properties.hibernate.connection.provider_disables_autocommit=true
spring.jpa.properties.jakarta.persistence.validation.mode=none
spring.jpa.properties.hibernate.cache-use_second_level_cache=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.properties.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
spring.jpa.properties.hibernate.cache.use_query_cache=false
spring.jpa.generate-ddl=false
spring.jpa.hibernate.ddl-auto=none
spring.jpa.open-in-view=false
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.type.preferred_instant_jdbc_type=TIMESTAMP
#
# Hibernate stuff
#
hibernate.types.print.banner=false
logging.level.org.springframework.ws.server=WARN
logging.level.org.hibernate=WARN
logging.level.org.hibernate.INFO=INFO
logging.level.org.hibernate.SQL=INFO
logging.level.org.hibernate.stat=INFO
logging.level.org.hibernate.cache=INFO
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.level.org.hibernate.orm.incubating=ERROR
logging.level.net.javacrumbs.shedlock=INFO
logging.level.org.springframework.amqp.rabbit.listener=WARN
#
# Spring SQL
#
spring.sql.init.mode=never
#
# Spring data
#
spring.data.jpa.repositories.bootstrap-mode=default
spring.data.redis.repositories.enabled=false
#
# default pattern parser
#
spring.mvc.pathmatch.matching-strategy=path_pattern_parser
#
# jackson configuration
#
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.time-zone=UTC
spring.jackson.deserialization.fail-on-unknown-properties=false
#
# Datasource proxy settings
#
decorator.datasource.enabled=${settings.datasourceproxy.enabled}
decorator.datasource.flexy-pool.metrics.reporter.jmx.enabled=false
decorator.datasource.p6spy.enable-logging=false
decorator.datasource.sleuth.enabled=false
decorator.datasource.datasource-proxy.logging=slf4j
decorator.datasource.datasource-proxy.query.enable-logging=${settings.datasourceproxy.enabled}
decorator.datasource.datasource-proxy.query.log-level=INFO
decorator.datasource.datasource-proxy.query.logger-name=dsproxy.query
decorator.datasource.datasource-proxy.slow-query.enable-logging=${settings.datasourceproxy.enabled}
decorator.datasource.datasource-proxy.slow-query.log-level=warn
decorator.datasource.datasource-proxy.slow-query.logger-name=dsproxy.slowquery
decorator.datasource.datasource-proxy.slow-query.threshold=300
decorator.datasource.datasource-proxy.multiline=true
decorator.datasource.datasource-proxy.json-format=false
decorator.datasource.datasource-proxy.count-query=false
decorator.datasource.p6spy.tracing.include-parameter-values=${settings.datasourceproxy.enabled}
#
# Liquibase defaults
#
logging.level.liquibase=INFO
spring.liquibase.contexts=unittest

#
# Actuator/Management
#
management.endpoint.beans.cache.time-to-live=10s
management.endpoints.enabled-by-default=false
management.endpoints.web.base-path=/actuator
management.endpoints.web.exposure.include=health,prometheus,info
management.endpoint.health.show-details=ALWAYS
management.endpoint.health.enabled=true
management.endpoint.prometheus.enabled=true
management.endpoint.info.enabled=true
management.endpoint.metrics.enabled=true
management.health.mail.enabled=false
management.endpoint.health.probes.enabled=true
#management.metrics.web.server.request.autotime.enabled=false
#management.metrics.web.client.request.autotime.enabled=false
management.endpoints.jmx.exposure.exclude=*
management.httpexchanges.recording.enabled=false
#
# Multipart upload settings
#
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=11MB
spring.servlet.multipart.enabled=true
spring.servlet.multipart.resolve-lazily=false
spring.servlet.multipart.location=${java.io.tmpdir}
#
# AWS config
#
cloud.aws.region.static=eu-central-1
cloud.aws.region.auto=false
cloud.aws.stack.auto=false
aws.s3.accessKey=********************
aws.s3.secretKey=v/4EIJfNoXvfMIMGSRe1upEh9xlECPS5+8OQnFbD
aws.s3.bucketname=spinarena-de-assets
aws.sns.accessKey=********************
aws.sns.secretKey=pQuFWsz0BevFqOxnVYsxuThcXZw+PnDA42Myh1BP
aws.translate.accessKey=********************
aws.translate.secretKey=XpJLX4AV+a9k2NIk5eOMwmXYbMBclqEK+FykXZzM
#
# Rabbit def. settings
#
spring.rabbitmq.listener.direct.prefetch=10
spring.rabbitmq.listener.direct.consumers-per-queue=1
spring.rabbitmq.listener.direct.retry.enabled=true
spring.rabbitmq.listener.direct.retry.initial-interval=1000ms
spring.rabbitmq.listener.direct.retry.max-attempts=3
spring.rabbitmq.listener.direct.retry.max-interval=2000ms
spring.rabbitmq.listener.direct.retry.multiplier=2
spring.rabbitmq.listener.direct.default-requeue-rejected=false
#
# Customer.io
#
customerio.enabled=${settings.customerio.enabled}
customerio.baseUrl=https://track-eu.customer.io/api/v1

#
# MESSAGES
#
features.messages=true
features.socialfeatures=false
features.freespins=true

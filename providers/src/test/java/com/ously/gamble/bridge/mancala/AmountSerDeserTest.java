package com.ously.gamble.bridge.mancala;

import com.ously.gamble.bridge.mancala.payload.MCBaseRequest;
import com.ously.gamble.bridge.mancala.payload.MCCreditRequest;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class AmountSerDeserTest {


    @Test
    public void testBDToStringForHash() {

        var bd = new BigDecimal("1.9" );
        assertEquals("1.9", bd.toPlainString());
        bd = new BigDecimal("0.90" );
        assertEquals("0.90", bd.toPlainString());
        bd = new BigDecimal("7.0" );
        assertEquals("7.0", bd.toPlainString());

    }

    @Test
    public void testAmountFormatting() {
        var req = new MCCreditRequest();
        var bd = new BigDecimal("1.90000" );
        assertEquals("1.90000", bd.toPlainString());
        assertEquals("1.9", MCBaseRequest.formatAmount(bd));
        assertEquals("1", MCBaseRequest.formatAmount(new BigDecimal("1.00000" )));
        assertEquals("10", MCBaseRequest.formatAmount(new BigDecimal("10.00" )));
        assertEquals("0.0045", MCBaseRequest.formatAmount(new BigDecimal("0.00450000" )));
    }


}

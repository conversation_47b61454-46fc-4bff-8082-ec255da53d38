package com.ously.gamble.bridge.onlyplay;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.bridge.onlyplay.payload.OPInfo;
import com.ously.gamble.bridge.onlyplay.payload.OPSignUtil;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class SignBaseTests {

    @Test
    void testSignBase() throws JsonProcessingException {

        ObjectMapper om = new ObjectMapper();

        String signBase = OPSignUtil.getSignBase(Map.of("a", true, "b", "bval", "z", 1022L), null);
        assertEquals("a1bbvalz1022", signBase);

        signBase = OPSignUtil.getSignBase(Map.of("b", "bval", "a", true, "z", 1022L), null);
        assertEquals("a1bbvalz1022", signBase);


        Map<String, Object> sm = new HashMap<>(Map.of("b", "bval", "a", true, "z", 1022L));
        sm.put("d", null);
        signBase = OPSignUtil.getSignBase(sm, null);
        assertEquals("a1bbvalz1022", signBase);

        sm = new HashMap<>(Map.of("b", "bval", "a", true, "z", 1022L));
        sm.put("d", null);
        sm.put("aa", new OPInfo());
        signBase = OPSignUtil.getSignBase(sm, om);
        assertEquals("a1aa{}bbvalz1022", signBase);

        sm = new HashMap<>(Map.of("b", "bval", "a", true, "z", 1022L));
        sm.put("d", null);
        var oi = new OPInfo();
        oi.setIp("122");
        sm.put("aa", oi);
        signBase = OPSignUtil.getSignBase(sm, om);
        assertEquals("a1aa{\"ip\":\"122\"}bbvalz1022", signBase);

    }

}

{"properties": [{"name": "features.baseUrl", "type": "java.lang.String", "description": "main url handed out to clients to resolve stuff like game-urls."}, {"name": "features.autoActive", "type": "java.lang.String", "description": "new users are automatically active."}, {"name": "features.stage", "type": "java.lang.String", "description": "stage name of deployed backend."}, {"name": "ously.redisson.host", "type": "java.lang.String", "description": "defines one or more redis backend urls."}, {"name": "ously.redisson.password", "type": "java.lang.String", "description": "the auth token for redis/cluster."}, {"name": "ously.redisson.port", "type": "java.lang.String", "description": "port of cluster nodes (when not defined in hostname)."}, {"name": "ously.redisson.type", "type": "java.lang.String", "description": "SINGLE,REPLICATED,CLUSTERED."}, {"name": "ously.redisson.mode", "type": "java.lang.String", "description": "READ MODE for replicated type: MASTER,SLAVE,MASTER_SLAVE."}, {"name": "bridge.expiryInHours", "type": "java.lang.String", "description": "session expiry in hours."}, {"name": "bridge.localhostname", "type": "java.lang.String", "description": "the hostname (public, dns) for the backend."}, {"name": "maintenance.maxTransactionAgeInHours", "type": "java.lang.String", "description": "After this time (in hours) the transactions are moved into cold storage."}, {"name": "maintenance.schema", "type": "java.lang.String", "description": "the schema name for maintenance/offloader."}]}
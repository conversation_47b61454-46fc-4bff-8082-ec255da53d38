package com.ously.gamble.bridge.topgrade;

import com.ously.gamble.bridge.topgrade.payload.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


// see: https://www.baeldung.com/spring-url-encoded-form-data
// Addition, do not user @RequestBody with URLENCODED, it wont work

@RestController
@RequestMapping(TopgradeConfiguration.BRIDGE_TOPGRADE)
@ConditionalOnBean(TopgradeConfiguration.class)
public class TopgradeController {
    final Logger log = LoggerFactory.getLogger(TopgradeController.class);

    @Autowired
    TopgradeService epSrv;

    @PostMapping(path = "/callback", consumes = {MediaType.APPLICATION_FORM_URLENCODED_VALUE})
    public ResponseEntity<TGResponse> callback(TGRequest req) {
        return switch (req.getName()) {
            case "init" -> init(new TGInitRequest(req));
            case "bet" -> bet(new TGBetRequest(req));
            case "win" -> win(new TGWinRequest(req));
            case "refund" -> refund(new TGRefundRequest(req));
            default -> throw new IllegalArgumentException("The callback-method '" + req.getName() + "' is not implemented yet");
        };
    }


    public ResponseEntity<TGResponse> init(TGInitRequest req) {
        log.debug("TG-init request: {}", req);
        var o = epSrv.init(req);
        log.debug("TG-init response: {}", o);

        return ResponseEntity.ok(o);
    }


    public ResponseEntity<TGResponse> bet(TGBetRequest req) {
        log.debug("Evoplay-bet request: {}", req);
        var o = epSrv.bet(req);
        log.debug("tg-bet response: {}", o);
        return ResponseEntity.ok(o);
    }


    public ResponseEntity<TGResponse> win(TGWinRequest req) {
        log.debug("tg-win request: {}", req);
        var o = epSrv.win(req);
        log.debug("tg-win response: {}", o);
        return ResponseEntity.ok(o);
    }


    public ResponseEntity<TGResponse> refund(TGRefundRequest req) {
        log.debug("tg-refund request: {}", req);
        var o = epSrv.refund(req);
        log.debug("tg-refund response: {}", o);

        return ResponseEntity.ok(o);
    }


}

package com.ously.gamble.bridge.kagaming.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class KABalanceResponse extends KAResponse {
    Long balance;
    Long balanceSequence;

    public KABalanceResponse() {
        super();
    }

    public KABalanceResponse(int code, String status) {
        super(code, status);
    }

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    public Long getBalanceSequence() {
        return balanceSequence;
    }

    public void setBalanceSequence(Long balanceSequence) {
        this.balanceSequence = balanceSequence;
    }

    @Override
    public String toString() {
        return "KABalanceResponse{" +
                "status='" + status + '\'' +
                ", statusCode=" + statusCode +
                ", userMessage='" + userMessage + '\'' +
                ", balance=" + balance +
                ", balanceSequence=" + balanceSequence +
                '}';
    }
}

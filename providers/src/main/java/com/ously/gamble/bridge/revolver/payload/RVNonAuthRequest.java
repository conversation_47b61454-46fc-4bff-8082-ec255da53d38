package com.ously.gamble.bridge.revolver.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class RVNonAuthRequest extends RVRequest {

    String playerId;
    String currency;
    String gameId;
    RVSessionState sessionState;

    @Override
    @JsonIgnore
    public String getSignSource() {
        return getOrEmpty(this.currency) + getOrEmpty(this.date) + getOrEmpty(this.gameId) + getOrEmpty(this.playerId) + getOrEmpty(this.timeout);
    }

    public String getPlayerId() {
        return playerId;
    }

    public void setPlayerId(String playerId) {
        this.playerId = playerId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public RVSessionState getSessionState() {
        return sessionState;
    }

    public void setSessionState(RVSessionState sessionState) {
        this.sessionState = sessionState;
    }
}

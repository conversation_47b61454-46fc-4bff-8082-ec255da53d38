package com.ously.gamble.bridge.onlyplay.payload;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class OPInfoRequest extends OPRequest {
    public OPInfoRequest() {
        super();
    }

    @Override
    String getSignSource() {
        return "";
    }

    @Override
    public String toString() {
        return "OPInfoRequest{" +
                "userId='" + userId + '\'' +
                ", session_id=" + sessionId +
                ", token='" + token + '\'' +
                ", sign='" + sign + '\'' +
                '}';
    }
}

package com.ously.gamble.bridge.triplecherry;

import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.common.URIBuilder;
import com.ously.gamble.bridge.triplecherry.payload.*;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import jakarta.persistence.OptimisticLockException;
import org.hibernate.StaleStateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
@ConditionalOnBean(TripleCherryConfiguration.class)
public class TripleCherryServiceImpl extends BridgeBaseV2 implements TripleCherryService {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    TripleCherryConfiguration config;

    private static String getPlayerId(Long id, int betLevel) {
        return "TC" + betLevel + "-" + id;
    }

    private static Long parsePlayerId(String pId) {
        if (pId.startsWith("TC-")) {
            return Long.parseLong(pId.substring(3));
        } else if (pId.startsWith("TC")) {
            return Long.parseLong(pId.substring(4));
        }
        return 0L;
    }


    @Override
    @Transactional
    public TCTokenResponse token(String xPlayerToken, String grantType, String playerId) {
        //
        log.debug("TCToken: {},{},{}", xPlayerToken, grantType, playerId);
        // Auth Token
        var token = getToken(xPlayerToken);
        var aLong = parsePlayerId(playerId);
        if (token.getUserId() == aLong) {
            var resp = new TCTokenResponse();
            resp.setAccessToken(token.getSessionToken());
            resp.setExpires(getExpiryInSeconds() - 60);
            resp.setTokenType("Bearer");
            log.debug("TCTokenResponse: {}", resp);
            return resp;
        }
        log.debug("TCToken, token not found");
        return TCTokenResponse.error("invalid_credentials", "Invalid credentials");
    }

    @Override
    @Transactional
    public TCPlayerInfoResponse playerInfo(String xPlayerToken, String gameId, String playerId) {
        log.debug("TCPlayerInfo: {},{},{}", xPlayerToken, gameId, playerId);

        var token = getToken(xPlayerToken);
        if (token != null) {
            var resp = new TCPlayerInfoResponse();

            resp.setCurrency(getCurrencyForBetlevel(token.getBetLevel()));
            var w = getWallet(token.getWalletId());
            resp.setBalance(w.getBalance());
            log.debug("TCTokenResponse: {}", resp);
            return resp;
        }
        log.debug("TCToken, token not found");
        return TCPlayerInfoResponse.error("invalid_credentials", "Invalid credentials");
    }

    private String getCurrencyForBetlevel(int betLevel) {
        return switch (betLevel) {
            case 0 -> config.getCurrency() + "1";
            case 1 -> config.getCurrency() + "2";
            case 2 -> config.getCurrency() + "3";
            default -> config.getCurrency();
        };
    }

    @Override
    @Transactional
    public TCBalanceResponse balance(String xPlayerToken, String gameId, String playerId) {
        log.debug("TCBalance: {},{},{}", xPlayerToken, gameId, playerId);

        var token = getToken(xPlayerToken);
        if (token != null) {
            var resp = new TCBalanceResponse();
            var w = getWallet(token.getWalletId());
            resp.setBalance(w.getBalance());
            log.debug("TCBalanceResponse: {}", resp);
            return resp;
        }
        log.debug("TCToken, token not found");
        return TCBalanceResponse.error("invalid_credentials", "Invalid credentials");
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public TCResponse bet(String xPlayerToken, TCTransactionRequest req) {
        log.debug("TCBet: {}", req);

        var token = getToken(xPlayerToken);
        if (token != null) {
            var txReq = new TxRequest(token);
            txReq.setRoundMode(RoundMode.AUTO);
            txReq.setBet(req.getAmount());
            txReq.setWin(BigDecimal.ZERO);
            txReq.setRoundRef(req.getCycleId());
            txReq.setExternalOrigId(req.getTransactionId());
            txReq.setType(TransactionType.BET);

            try {
                addTxFromProvider(txReq);
            } catch (OuslyTransactionException e) {
                if (e instanceof OuslyOutOfMoneyException) {
                    log.debug("TCBetOutOfFunds: {}", req);
                    return TCResponse.error("insufficient_balance", "Player does not have enough balance for bet");
                }
                log.debug("TCBetError", e);
                return TCResponse.error("invalid_transaction", "Transaction error");
            }
            return new TCResponse();

        }
        log.debug("TCToken, token not found");
        return TCResponse.error("invalid_credentials", "Invalid credentials");
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public TCResponse win(String xPlayerToken, TCTransactionRequest req) {
        log.debug("TCWin: {}", req);

        var token = getToken(xPlayerToken);
        if (token != null) {
            var txReq = new TxRequest(token);
            txReq.setRoundMode(RoundMode.AUTO);
            txReq.setWin(req.getAmount());
            txReq.setBet(BigDecimal.ZERO);
            txReq.setRoundRef(req.getCycleId());
            txReq.setExternalOrigId(req.getTransactionId());
            txReq.setType(TransactionType.WIN);

            try {
                addTxFromProvider(txReq);
            } catch (OuslyTransactionException e) {
                log.debug("TCCancelResponse: {}", req, e);
                return TCResponse.error("invalid_transaction", "Transaction error");
            }
            return new TCResponse();
        }
        log.debug("TCToken, token not found");
        return TCResponse.error("invalid_credentials", "Invalid credentials");
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public TCResponse cancel(String xPlayerToken, TCTransactionRequest req) {
        log.debug("TCCancel: {}", req);
        var token = getToken(xPlayerToken);
        if (token != null) {

            var byUserIdAndVendorAndOrigId = findBySessionIdAndOrigId(token.getUserId(), token.getSessionId(), req.getInvalidTransactionId());
            if (byUserIdAndVendorAndOrigId == null) {
                return TCResponse.error("invalid_transaction", "Transaction does not exists");
            }
            var w = getWallet(token.getWalletId());
            performRollback(byUserIdAndVendorAndOrigId, w);
            return new TCResponse();
        }
        log.debug("TCToken, token not found");
        return TCResponse.error("token_expired", "Access token has expired");
    }


    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(config.getVendorName());
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform,
                                                 GameSettings settings) throws Exception {

        log.info("CreateGame (TripleCherry) for user {}, game {} platform={}", user.getDisplayName(), game.getGameId(), platform);

        String gameUrl = null;
        var token = UUID.randomUUID().toString();
        if (platform != GamePlatform.TEST) {
            var url = config.getLaunchUrl();
            url = url.replace("#gameId#", game.getGameId());
            var bld = new URIBuilder(url);
            bld.addParameter("tokenInQuery", "true");
            bld.addParameter("playerid", getPlayerId(user.getId(), settings.getBetLevel()));
            bld.addParameter("sessiontoken", token);
            bld.addParameter("partner", config.getPartnerId());
            bld.addParameter("lang", settings.getLanguage().toLowerCase());
            bld.addParameter("guest", "false");
            gameUrl = bld.build().toString();
        }

        var gameInstance = new GameInstance();

        gameInstance.setGameUrl(gameUrl);
        gameInstance.setToken(token);
        gameInstance.setAuthToken(token);
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }
}

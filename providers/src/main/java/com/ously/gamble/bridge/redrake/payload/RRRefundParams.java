package com.ously.gamble.bridge.redrake.payload;

import java.math.BigDecimal;

public class RRRefundParams {

    Long roundid;
    Long transactionid;
    Long refundedtransactionid;
    BigDecimal amount;

    public Long getRoundid() {
        return roundid;
    }

    public void setRoundid(Long roundid) {
        this.roundid = roundid;
    }

    public Long getTransactionid() {
        return transactionid;
    }

    public void setTransactionid(Long transactionid) {
        this.transactionid = transactionid;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getRefundedtransactionid() {
        return refundedtransactionid;
    }

    public void setRefundedtransactionid(Long refundedtransactionid) {
        this.refundedtransactionid = refundedtransactionid;
    }

    @Override
    public String toString() {
        return "RRRefundParams{" +
                "roundid=" + roundid +
                ", transactionid=" + transactionid +
                ", refundedtransactionid=" + refundedtransactionid +
                ", amount=" + amount +
                '}';
    }
}

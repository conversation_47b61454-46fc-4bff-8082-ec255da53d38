package com.ously.gamble.bridge.onlyplay;

import com.ously.gamble.bridge.onlyplay.payload.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(OnlyPlayConfiguration.BRIDGE_ONLYPLAY)
@ConditionalOnBean(OnlyPlayConfiguration.class)
public class OnlyPlayController {

    final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    OnlyPlayService og;

    @PostMapping("/info")
    @ResponseBody
    public ResponseEntity<OPInfoResponse> info(@RequestBody OPInfoRequest req) {
        log.debug("OP-INFO:{}", req);
        try {
            return new ResponseEntity<>(og.info(req), HttpStatus.OK);
        } catch (OPError ogError) {
            log.error("ONLYPLAY INFO ERROR -> {}", ogError.toString());
            return new ResponseEntity<>(new OPInfoResponse(ogError), HttpStatus.OK);
        }
    }

    @PostMapping("/bet")
    @ResponseBody
    public ResponseEntity<OPBetResponse> bet(@RequestBody OPBetRequest req) {
        try {
            return new ResponseEntity<>(og.bet(req), HttpStatus.OK);
        } catch (OPError ogError) {
            log.error("ONLYPLAY BET ERROR -> {}", ogError.toString());
            return new ResponseEntity<>(new OPBetResponse(ogError), HttpStatus.OK);
        }
    }

    @PostMapping("/win")
    @ResponseBody
    public ResponseEntity<OPWinResponse> win(@RequestBody OPWinRequest req) {
        try {
            return new ResponseEntity<>(og.win(req), HttpStatus.OK);
        } catch (OPError ogError) {
            log.error("ONLYPLAY WIN ERROR -> {}", ogError.toString());
            return new ResponseEntity<>(new OPWinResponse(ogError), HttpStatus.OK);
        }
    }


    @PostMapping("/cancel")
    @ResponseBody
    public ResponseEntity<OPCancelResponse> cancel(@RequestBody OPCancelRequest req) {
        try {
            return new ResponseEntity<>(og.cancel(req), HttpStatus.OK);
        } catch (OPError ogError) {
            log.error("ONLYPLAY CANCEL ERROR -> {}", ogError.toString());
            return new ResponseEntity<>(new OPCancelResponse(ogError), HttpStatus.OK);
        }
    }

}

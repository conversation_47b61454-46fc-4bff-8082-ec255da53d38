package com.ously.gamble.bridge.eurasian;

import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.eurasian.payload.EAResponse;

import java.math.BigDecimal;

public interface EurasianService extends BridgeHandler {

    EAResponse balance(String remoteId, String sessionId, String remoteData);

    EAResponse debit(String remoteId, String sessionId, BigDecimal amount, String actionType, String transactionId, String roundId, String remoteData, Long timestamp);

    EAResponse credit(String remoteId, String sessionId, BigDecimal amount, String actionType, String transactionId, String roundId, String remoteData, Long timestamp);

    EAResponse rollback(String remoteId, BigDecimal amount, String actionType, String transactionId, String roundId, String remoteData);


}

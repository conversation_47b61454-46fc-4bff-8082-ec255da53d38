package com.ously.gamble.bridge.hacksaw;

import com.ously.gamble.api.bridge.BridgeConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "hacksaw")
@ConditionalOnProperty(
        value = "hacksaw.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class HacksawConfiguration implements BridgeConfiguration {
    public static final String VENDOR_NAME = "HACKSAW GAMING";
    public static final String BRIDGE_HaCKSAW = "/bridge/hacksaw";

    private String partnerId = "spinarena_stg";
    private String secret = "Xzw]/6ULV.?WSx";
    private String currency = "XTOK";
    private String currency2 = "XTK2";
    private String currency3 = "XTK3";


    private String environment = "stg";
    private String apiUrl = "https://api-stg.hacksawgaming.com/api/v1/meta/";

    private String apiUser = "spinarena_api";
    private String apiPassword = "zKg62]B/Ex![";


    public String getCurrency2() {
        return currency2;
    }

    public void setCurrency2(String currency2) {
        this.currency2 = currency2;
    }

    public String getCurrency3() {
        return currency3;
    }

    public void setCurrency3(String currency3) {
        this.currency3 = currency3;
    }

    public String getApiUser() {
        return apiUser;
    }

    public void setApiUser(String apiUser) {
        this.apiUser = apiUser;
    }

    public String getApiPassword() {
        return apiPassword;
    }

    public void setApiPassword(String apiPassword) {
        this.apiPassword = apiPassword;
    }

    public String getApiUrl() {
        return this.apiUrl;
    }

    public void setApiUrl(final String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @Override
    public String getVendorName() {
        return VENDOR_NAME;
    }

    @Override
    public String getBridgeName() {
        return BRIDGE_HaCKSAW;
    }

    @Override
    public String getAuth_user() {
        return null;
    }

    @Override
    public String getAuth_password() {
        return null;
    }

    @Override
    public List<String> getWhitelistIPs() {
        return Collections.emptyList();
    }
}

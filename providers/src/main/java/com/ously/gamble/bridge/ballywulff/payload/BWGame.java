package com.ously.gamble.bridge.ballywulff.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import java.util.Arrays;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class BWGame {

    public static class Settings {
        int[] bets;

        public Settings() {
        }

        public Settings(int... bets) {
            this.bets = bets;
        }

        public int[] getBets() {
            return bets;
        }

        public void setBets(int... bets) {
            this.bets = bets;
        }

        @Override
        public String toString() {
            return "Settings{" +
                    "bets=" + Arrays.toString(bets) +
                    '}';
        }
    }

    Settings settings;

    public Settings getSettings() {
        return settings;
    }

    public void setSettings(Settings settings) {
        this.settings = settings;
    }

    @Override
    public String toString() {
        return "BWGame{" +
                "settings=" + settings +
                '}';
    }
}

package com.ously.gamble.bridge.pragmatic.payload;

import java.io.Serial;
import java.io.Serializable;

public class PPResultResponse extends PPBetResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    public PPResultResponse(long error, String description) {
        super(error, description);
    }

    public PPResultResponse() {
        super();
    }
}

package com.ously.gamble.bridge.spade.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class SPAuthRequest extends SPRequest {

    String acctId;
    String token;
    String language;

    public String getAcctId() {
        return acctId;
    }

    public void setAcctId(String acctId) {
        this.acctId = acctId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    @Override
    public String toString() {
        return "SPAuthRequest{" +
                "acctId='" + acctId + '\'' +
                ", token='" + token + '\'' +
                ", language='" + language + '\'' +
                ", serialNo='" + serialNo + '\'' +
                ", merchantCode='" + merchantCode + '\'' +
                '}';
    }
}

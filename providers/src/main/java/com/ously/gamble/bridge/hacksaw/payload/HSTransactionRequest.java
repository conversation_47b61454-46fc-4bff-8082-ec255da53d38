package com.ously.gamble.bridge.hacksaw.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class HSTransactionRequest extends HSRequest {
    Long amount;
    String currency;
    Long roundId;
    Long gameSessionId;
    Long transactionId;
    String type;
    Long jackpotAmount;

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Long getRoundId() {
        return roundId;
    }

    public void setRoundId(Long roundId) {
        this.roundId = roundId;
    }

    public Long getGameSessionId() {
        return gameSessionId;
    }

    public void setGameSessionId(Long gameSessionId) {
        this.gameSessionId = gameSessionId;
    }

    public Long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getJackpotAmount() {
        return jackpotAmount;
    }

    public void setJackpotAmount(Long jackpotAmount) {
        this.jackpotAmount = jackpotAmount;
    }

    @Override
    public String toString() {
        return "HSTransactionRequest{" +
                "amount=" + amount +
                ", currency='" + currency + '\'' +
                ", roundId=" + roundId +
                ", gameSessionId=" + gameSessionId +
                ", transactionId=" + transactionId +
                ", type='" + type + '\'' +
                ", jackpotAmount=" + jackpotAmount +
                ", action='" + action + '\'' +
                ", secret='" + secret + '\'' +
                ", externalSessionId='" + externalSessionId + '\'' +
                ", externalPlayerId='" + externalPlayerId + '\'' +
                ", gameId=" + gameId +
                "} " + super.toString();
    }
}

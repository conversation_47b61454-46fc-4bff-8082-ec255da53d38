package com.ously.gamble.bridge.apparat;


import com.ously.gamble.api.bridge.BridgeConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "apparat")
@ConditionalOnProperty(
        value = "apparat.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class ApparatConfiguration implements BridgeConfiguration {
    public static final String VENDOR_NAME = "APPARAT";
    public static final String BRIDGE_APPARAT = "/bridge/apparat";

    private String casinoId = "spinarena-staging";
    private String launchUrl = "https://router.apparatgaming.net/game";
    private String apiPassword = "3Qq82U6sckeg2pcTg3b8";
    private String currency = "AC";

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCasinoId() {
        return casinoId;
    }

    public void setCasinoId(String casinoId) {
        this.casinoId = casinoId;
    }

    public String getLaunchUrl() {
        return launchUrl;
    }

    public void setLaunchUrl(String launchUrl) {
        this.launchUrl = launchUrl;
    }

    public String getApiPassword() {
        return apiPassword;
    }

    public void setApiPassword(String apiPassword) {
        this.apiPassword = apiPassword;
    }

    @Override
    public String getVendorName() {
        return VENDOR_NAME;
    }

    @Override
    public String getBridgeName() {
        return BRIDGE_APPARAT;
    }

    @Override
    public String getAuth_user() {
        return null;
    }

    @Override
    public String getAuth_password() {
        return null;
    }

    @Override
    public List<String> getWhitelistIPs() {
        return Collections.emptyList();
    }

}

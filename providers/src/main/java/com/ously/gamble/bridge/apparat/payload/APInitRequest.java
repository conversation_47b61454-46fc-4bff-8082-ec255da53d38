package com.ously.gamble.bridge.apparat.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class APInitRequest extends APRequest {


    @Override
    public String toString() {
        return "APInitRequest{" +
                "token='" + token + '\'' +
                ", auth='" + auth + '\'' +
                ", gameId='" + gameId + '\'' +
                '}';
    }
}

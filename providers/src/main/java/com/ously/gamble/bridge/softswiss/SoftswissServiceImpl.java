package com.ously.gamble.bridge.softswiss;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.bridge.SessionCacheEntry;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxResponse;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.softswiss.payload.*;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import jakarta.persistence.OptimisticLockException;
import jakarta.transaction.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.StaleObjectStateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.*;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;

@Service
@ConditionalOnBean(value = SoftswissConfiguration.class)
public class SoftswissServiceImpl extends BridgeBaseV2 implements SoftswissService {

    private final Logger log = LoggerFactory.getLogger(SoftswissServiceImpl.class);

    @Autowired
    ObjectMapper om;

    @Autowired
    SoftswissConfiguration config;

    @Autowired
    RestTemplate apiTemplate;

    @Override
    public List<String> getVendorNames() {
        return Arrays.asList(config.getVendorName(), "SOFTSWISS");
    }


    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform,
                                                 GameSettings settings) throws Exception {

        log.info("CreateGame (via softswiss for user {}, game {} platform={}, bl={}", user.getDisplayName(),
                game.getGameId(), platform, settings.getBetLevel());

        var token = UUID.randomUUID().toString();
        if (isAggregated()) {
            token = getAggregationKey() + "#" + token;
        }

        String gameUrl = null;
        if (platform != GamePlatform.TEST) {

            // Create SSPlayer
            // Faking dates/gender/name
            SSPlayer player = getSsPlayer(user, settings);

            // Create SSLaunchRequest
            SSLaunchRequest launchRequest = new SSLaunchRequest();
            launchRequest.setPlayer(player);
            launchRequest.setGame(game.getGameId());
            launchRequest.setClientType(platform == GamePlatform.MOBILE ? "mobile" : "desktop");
            launchRequest.setIp(settings.getIp());
            launchRequest.setLocale(Objects.requireNonNull(settings.getLanguage(), "EN").toLowerCase());
            launchRequest.setSessionPayload(token);
            launchRequest.setCasinoId(config.getCasinoId());
            launchRequest.setUrls(Map.of("deposit_url", "https://test.spinarena.net/",
                    "return_url", "https://test.spinarena.net/"));

            // Create payload json and create X-REQUEST-SIGN

            byte[] payload = om.writeValueAsBytes(launchRequest);
            String signKey = SoftswissController.getHash(payload);

            // get LaunchString

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-REQUEST-SIGN", signKey);
            HttpEntity<SSLaunchRequest> entity = new HttpEntity<>(launchRequest, headers);
            //
            log.debug("Trying to get LaunchUrl: {}", entity);

            ResponseEntity<SSLaunchResponse> response =
                    apiTemplate.exchange(config.getUrl() + "v2/casino_a8r.Launcher/Real",
                            HttpMethod.POST,
                            entity,
                            SSLaunchResponse.class);


            log.debug("Got {}", response);
            if (response.getStatusCode().is2xxSuccessful() && response.hasBody()) {
                gameUrl = response.getBody().getLaunchUrl();
            } else {
                log.warn("Getting URL for softswiss game-agg {} resulted in error {}, {}", game.getGameId(), response.getBody(), response);
            }
        }

        var gameInstance = new GameInstance();
        gameInstance.setGameUrl(gameUrl);
        gameInstance.setToken(token);
        gameInstance.setAuthToken(token);
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }

    private SSPlayer getSsPlayer(CasinoUser user, GameSettings settings) {
        SSPlayer player = new SSPlayer();
        player.setCurrency(config.getCurrency());
        player.setId("SSWS#" + "-" + user.getId() + ":" + settings.getBetLevel());
        player.setNickname(user.getDisplayName());
        player.setCountry("DE");
        player.setDateOfBirth("1999-12-31T12:00:00Z");
        player.setFirstname(Objects.requireNonNullElse(user.getName(), user.getDisplayName()));
        player.setLastname(Objects.requireNonNullElse(user.getSurname(), user.getDisplayName()));
        player.setGender("m");
        player.setRegisteredAt("2023-01-01T12:00:00Z");
        return player;
    }


    private long parsePlayerId(String pId) {
        return Long.parseLong(pId.substring(6).split(":")[0]);
    }

    private SessionCacheEntry getTokenForPlayerIdAndGameId(String pId, String gameId) {
        if (StringUtils.isAllBlank(pId) || StringUtils.isAllBlank(gameId)) {
            return null;
        }
        long uid = parsePlayerId(pId);
        Optional<Long> latestSessionIdByUserIdAndGameKey = super.findLatestSessionIdByUserIdAndGameKey(uid, gameId);
        return latestSessionIdByUserIdAndGameKey.map(aLong -> loadTokenBySessionId(aLong).orElse(null)).orElse(null);
    }


    @Override
    @Retryable(retryFor = {ObjectOptimisticLockingFailureException.class,
            StaleObjectStateException.class
            , OptimisticLockException.class,
            SQLException.class},
            backoff = @Backoff(delay = 150))
    public SSBalanceResponse getBalance(SSBalanceRequest req) {
        SessionCacheEntry token = getToken(req.sessionPayload());

        if (token == null) {
            // parse uid from "SSWS#-549:0"
            var w = getWallet(parsePlayerId(req.playerId()));
            return new SSBalanceResponse(w.getBalance());
        }

        return new SSBalanceResponse(getWallet(token.getUserId()).getBalance());
    }

    @Override
    @Transactional
    @Retryable(retryFor = {ObjectOptimisticLockingFailureException.class,
            StaleObjectStateException.class
            , OptimisticLockException.class,
            SQLException.class},
            backoff = @Backoff(delay = 150))
    public SSBetWinResponse betWin(SSBetWinRequest req) throws OuslyTransactionException {

        var token = getToken(req.sessionPayload());
        token = Objects.requireNonNullElseGet(token, () -> getTokenForPlayerIdAndGameId(req.playerId(), req.gameId()));
        if (token == null) {
            throw new IllegalArgumentException("token invalid");
        }

        List<SSTransactionResponse> txResponses = new ArrayList<>(req.getTransactions().size());
        BigDecimal cBalance = BigDecimal.ZERO;
        int i = 1;
        int txSize = req.getTransactions().size();


        // Multiple Bet Workarounds
        BigDecimal totalBet = BigDecimal.ZERO;
        BigDecimal uBalance = getBalanceForUser(token.getUserId());
        for (SSTransaction tx : req.getTransactions()) {
            if (tx.type() == SSTransactionType.bet) {
                totalBet = totalBet.add(new BigDecimal(tx.amount()));
            }
        }
        if (totalBet.compareTo(uBalance) > 0) {
            throw new OuslyOutOfMoneyException(HttpStatus.BAD_REQUEST, "bets are higher than balance", uBalance);
        }


        for (SSTransaction tx : req.getTransactions()) {
            var txr = new TxRequest(token);

            if (i == 1 && i == txSize) {
                txr.setRoundMode(TxRequest.RoundMode.OPENCLOSE);
            } else if (i == 1 && i < txSize) {
                txr.setRoundMode(TxRequest.RoundMode.OPEN);
            } else if (i < txSize) {
                txr.setRoundMode(TxRequest.RoundMode.NONE);
            } else if (i > 1 && i == txSize) {
                txr.setRoundMode(TxRequest.RoundMode.CLOSE);
            }

            var txType = (tx.type() == SSTransactionType.bet) ? TransactionType.BET : TransactionType.WIN;
            txr.setType(txType);
            txr.setExternalOrigId(tx.id());
            txr.setRoundRef(req.roundId());
            if (txType == TransactionType.BET) {
                txr.setBet(new BigDecimal(tx.amount()));
            } else {
                txr.setWin(new BigDecimal(tx.amount()));
            }

            TxResponse txResponse;
            txResponse = addTxFromProvider(txr);
            SSTransactionResponse respTx = new SSTransactionResponse(tx.id(), tx.id(), Instant.now().toString(), "0.0");
            txResponses.add(respTx);
            cBalance = txResponse.getNewBalance();
            i++;
        }

        // ok, betWin handling done
        SSBetWinResponse resp = new SSBetWinResponse();
        resp.setRoundIdCasino(req.roundId());
        resp.setTransactions(txResponses);

        resp.setBalance(cBalance.setScale(6, RoundingMode.HALF_DOWN).toPlainString());
        return resp;


    }

    @Override
    public SSFinishResponse finish(SSFinishRequest req) {
        var token = getToken(req.sessionPayload());
        token = Objects.requireNonNullElseGet(token, () -> getTokenForPlayerIdAndGameId(req.playerId(), req.gameId()));
        if (token == null) {
            throw new IllegalArgumentException("token invalid");
        }

        return new SSFinishResponse(getWallet(token.getUserId()).getBalance());
    }

    @Override
    @Transactional
    @Retryable(retryFor = {ObjectOptimisticLockingFailureException.class,
            StaleObjectStateException.class
            , OptimisticLockException.class,
            SQLException.class},
            backoff = @Backoff(delay = 150))
    public SSRollbackResponse rollback(SSRollbackRequest req) throws OuslyTransactionException {

        var token = getToken(req.sessionPayload());
        token = Objects.requireNonNullElseGet(token, () -> getTokenForPlayerIdAndGameId(req.playerId(), req.gameId()));
        if (token == null) {
            throw new IllegalArgumentException("token invalid");
        }


        List<SSTransactionResponse> resultTxList = new ArrayList<>();

        for (var rbTx : req.getTransactions()) {

            TxResponse txResponse = null;
            // 1. find transaction to rollback
            var byVendorAndOrigId = findBySessionIdAndOrigId(token.getUserId(), token.getSessionId(), rbTx.originalId());
            if (byVendorAndOrigId == null) {
                // add blocking tx

                var txr = new TxRequest(token);
                txr.setType(TransactionType.ROLLBACK);
                txr.setExternalOrigId(rbTx.originalId());
                txr.setExternal_tx_ref(txr.getType().name() + '#' + rbTx.id());
                txr.setRoundRef(req.roundId());
                txr.setBet(BigDecimal.ZERO);
                txr.setWin(BigDecimal.ZERO);
                txr.setVendorName(config.getVendorName());

                try {
                    txResponse = addTxFromProvider(txr);
                    SSTransactionResponse ssTxResp = new SSTransactionResponse(rbTx.id(), rbTx.originalId(), Instant.now().toString(), null);
                    resultTxList.add(ssTxResp);
                } catch (OuslyTransactionException e) {
                    throw new OuslyTransactionException("rollback failed");
                }
            } else {
                var wallet = getWallet(token.getUserId());

                // rollback old tx
                var type = byVendorAndOrigId.getType();
                if (type == TransactionType.ROLLBACK) {
                    // refund already placed. Ignore
                    SSTransactionResponse ssTxResp = new SSTransactionResponse(rbTx.id(), rbTx.originalId(), Instant.now().toString(), null);
                    resultTxList.add(ssTxResp);
                } else {
                    if (wallet != null) {
                        performRollback(byVendorAndOrigId, wallet);
                        SSTransactionResponse ssTxResp = new SSTransactionResponse(rbTx.id(), rbTx.originalId(), Instant.now().toString(), null);
                        resultTxList.add(ssTxResp);

                    }
                }
            }
        }

        //
        SSRollbackResponse resp = new SSRollbackResponse();
        resp.setRoundIdCasino(req.roundId());
        resp.setTransactions(resultTxList);
        resp.setBalance(getBalanceForUser(token.getUserId()).setScale(6, RoundingMode.HALF_DOWN).toPlainString());
        return resp;
    }

    @Override
    public String getBalanceString(String playerId) {
        return getBalanceForUser(parsePlayerId(playerId)).setScale(6, RoundingMode.HALF_DOWN).toPlainString();
    }
}

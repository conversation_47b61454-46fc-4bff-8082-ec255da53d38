package com.ously.gamble.bridge.oryxgaming;

import com.ously.gamble.api.bridge.BridgeConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "oryxgaming")
@ConditionalOnProperty(
        value = "oryxgaming.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class OryxGamingConfiguration implements BridgeConfiguration {

    public static final String VENDOR_NAME = "ORYXGAMING";
    public static final String BRIDGE_ORYXGAMING = "/bridge/oryxgaming";

    String auth_user = "spinarena_stage";
    String auth_password = "spinarena_test";

    String lang = "ENG";
    String walletCode = "SPINARENA";
    String moneymode = "REAL";
    String launcherurl = "https://play-prodcopy.oryxgaming.com/agg_plus_public/launch/wallets/";
    String activeCurrency = "EUR";


    @Override
    public String getVendorName() {
        return VENDOR_NAME;
    }

    public String getLang() {
        return lang;
    }

    public String getWalletCode() {
        return walletCode;
    }

    public String getMoneymode() {
        return moneymode;
    }

    public String getLauncherurl() {
        return launcherurl;
    }

    public String getActiveCurrency() {
        return activeCurrency;
    }


    @Override
    public String getBridgeName() {
        return BRIDGE_ORYXGAMING;
    }

    @Override public String getAuth_user() {
        return auth_user;
    }

    @Override public String getAuth_password() {
        return auth_password;
    }

    @Override
    public List<String> getWhitelistIPs() {
        return Arrays.asList("********", "***********");
    }

    public void setAuth_user(String auth_user) {
        this.auth_user = auth_user;
    }

    public void setAuth_password(String auth_password) {
        this.auth_password = auth_password;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public void setWalletCode(String partner) {
        this.walletCode = partner;
    }

    public void setMoneymode(String moneymode) {
        this.moneymode = moneymode;
    }

    public void setLauncherurl(String launcherurl) {
        this.launcherurl = launcherurl;
    }

    public void setActiveCurrency(String activeCurrency) {
        this.activeCurrency = activeCurrency;
    }

}

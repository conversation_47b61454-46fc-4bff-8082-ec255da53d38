package com.ously.gamble.bridge.playngo.payload;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serial;

@XmlRootElement(name = "cancelReserve")
@XmlAccessorType(XmlAccessType.FIELD)
public class PNGCancelReserveReq extends PNGSessionRequest {
    @Serial
    private static final long serialVersionUID = 1L;

    String transactionId;

    @XmlElement(name = "real")
    String amount;

    String gameSessionId;

    String roundId;

    String channel;

    String actualValue;


    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getGameSessionId() {
        return gameSessionId;
    }

    public void setGameSessionId(String gameSessionId) {
        this.gameSessionId = gameSessionId;
    }

    public String getRoundId() {
        return roundId;
    }

    public void setRoundId(String roundId) {
        this.roundId = roundId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getActualValue() {
        return actualValue;
    }

    public void setActualValue(String actualValue) {
        this.actualValue = actualValue;
    }


    @Override
    public String toString() {
        return "PNGCancelReserveReq{" +
                "transactionId='" + transactionId + '\'' +
                ", amount='" + amount + '\'' +
                ", gameSessionId='" + gameSessionId + '\'' +
                ", roundId='" + roundId + '\'' +
                ", channel='" + channel + '\'' +
                ", actualValue='" + actualValue + '\'' +
                ", accessToken='" + accessToken + '\'' +
                ", gameId='" + gameId + '\'' +
                ", productId='" + productId + '\'' +
                ", clientIP='" + clientIP + '\'' +
                ", contextId='" + contextId + '\'' +
                ", externalId='" + externalId + '\'' +
                ", currency='" + currency + '\'' +
                ", externalGameSessionId='" + externalGameSessionId + '\'' +
                "} ";
    }
}

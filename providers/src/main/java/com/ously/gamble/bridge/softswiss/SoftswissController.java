package com.ously.gamble.bridge.softswiss;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.OuslyJurisdictionViolationException;
import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.bridge.softswiss.payload.*;
import jakarta.annotation.PostConstruct;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.crypto.Mac;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;

@SuppressWarnings({"rawtypes"})
@RestController
@RequestMapping(SoftswissConfiguration.BRIDGE_SOFTSWISS)
@ConditionalOnBean(SoftswissConfiguration.class)
public class SoftswissController {
    private static final String X_REQUEST_SIGN = "X-Request-Sign";
    private final Logger log = LoggerFactory.getLogger(SoftswissController.class);

    @Autowired
    SoftswissConfiguration config;

    @Autowired
    SoftswissService srv;

    @Autowired
    ObjectMapper om;

    private static Mac hmacSHA256;

    @PostConstruct
    public void createKeys() {
        hmacSHA256 = HmacUtils.getInitializedMac(HmacAlgorithms.HMAC_SHA_256, config.getSecret().getBytes(StandardCharsets.UTF_8));
    }

    public static String getHash(byte[] payload) {
        return Hex.encodeHexString(hmacSHA256.doFinal(payload), true);
    }

    @PostMapping("/v2/a8r_casino.Player/Balance")
    @ResponseBody
    public ResponseEntity balance(@RequestBody String body, @RequestHeader(name = X_REQUEST_SIGN, required = false) String hash) {
        log.debug("SS-BALANCE: {}", body);
        if (checkHash(body, hash)) {
            log.warn("SS-Invalid hash - balance");
                return ResponseEntity.status(400).body(new SSErrorResponse("invalid_argument", "missing sign header", "403", "Invalid signature"));
            }

        try {
            var resp = srv.getBalance(om.readValue(body, SSBalanceRequest.class));
            log.debug("SS-BALANCE-RESP:{}", resp);
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.warn("SS-Auth error: {}", body, e);
            return ResponseEntity.status(500).body(new SSErrorResponse("500", "UNKNOWN ERROR", "500", e.getMessage()));
        }
    }


    @PostMapping("/v2/a8r_casino.Round/BetWin")
    @ResponseBody
    public ResponseEntity betWin(@RequestBody String body, @RequestHeader(name = X_REQUEST_SIGN, required = false) String hash) {
        log.debug("SS-BETWIN: {}", body);
        if (checkHash(body, hash)) {
            log.warn("SS-Invalid hash - balance");
            return ResponseEntity.status(400).body(new SSErrorResponse("invalid_argument", "missing sign header", "403", "Invalid signature"));
        }

        String playerId = "";
        try {
            var req = om.readValue(body, SSBetWinRequest.class);
            playerId = req.playerId();
            var resp = srv.betWin(req);
            log.debug("SS-BETWIN-RESP:{}", resp);
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.warn("SS-BETWIN error: {}", body, e);
            if (e instanceof OuslyOutOfMoneyException oomEx) {
                return ResponseEntity.status(400).body(new SSErrorResponse("invalid_argument", "bet to high", "100", "insufficient funds", oomEx.getBalance().setScale(6, RoundingMode.FLOOR).toPlainString()));
            } else if
            (e instanceof OuslyJurisdictionViolationException) {
                return ResponseEntity.status(400).body(new SSErrorResponse("invalid_argument", "bet to high", "100", "insufficient funds", srv.getBalanceString(playerId)));
            }
            return ResponseEntity.status(500).body(new SSErrorResponse("500", "UNKNOWN ERROR", "500", e.getMessage()));
        }
    }

    @PostMapping("/v2/a8r_casino.Round/Rollback")
    @ResponseBody
    public ResponseEntity rollback(@RequestBody String body, @RequestHeader(name = X_REQUEST_SIGN, required = false) String hash) {
        log.debug("SS-ROLLBACK: {}", body);
        if (checkHash(body, hash)) {
            log.warn("SS-Invalid hash - balance");
            return ResponseEntity.status(400).body(new SSErrorResponse("invalid_argument", "missing sign header", "403", "Invalid signature"));
        }

        try {
            var resp = srv.rollback(om.readValue(body, SSRollbackRequest.class));
            log.debug("SS-ROLLBACK-RESP:{}", resp);
            return ResponseEntity.ok(resp);
        } catch (Exception e) {

            if (e instanceof OuslyOutOfMoneyException || e instanceof OuslyJurisdictionViolationException) {
                return ResponseEntity.status(400).body(new SSErrorResponse("400", "INSUFFICIENT FUNDS", "100", "Insufficient funds"));
            }
            log.warn("SS-ROLLBACK error: {}", body, e);
            return ResponseEntity.status(500).body(new SSErrorResponse("500", "UNKNOWN ERROR", "500", e.getMessage()));

        }
    }

    @PostMapping("/v2/a8r_casino.Round/Finish")
    @ResponseBody
    public ResponseEntity finish(@RequestBody String body, @RequestHeader(name = X_REQUEST_SIGN, required = false) String hash) {
        log.debug("SS-FINISH: {}", body);
        if (checkHash(body, hash)) {
            log.warn("SS-Invalid hash - balance");
            return ResponseEntity.status(400).body(new SSErrorResponse("invalid_argument", "missing sign header", "403", "Invalid signature"));
        }
        try {
            var resp = srv.finish(om.readValue(body, SSFinishRequest.class));
            log.debug("SM-FINISH-RESP:{}", resp);
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            log.warn("SM-FINISH error: {}", body, e);
            return ResponseEntity.status(500).body(new SSErrorResponse("500", "UNKNOWN ERROR", "500", e.getMessage()));
        }
    }


    private boolean checkHash(String body, String hash) {
        if (hash == null) {
            return true;
        }
        byte[] bytes = hmacSHA256.doFinal(body.getBytes(StandardCharsets.UTF_8));
        return !hash.equalsIgnoreCase(Hex.encodeHexString(bytes, true));
    }

}

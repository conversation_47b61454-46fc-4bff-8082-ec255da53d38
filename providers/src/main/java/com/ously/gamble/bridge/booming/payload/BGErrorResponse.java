package com.ously.gamble.bridge.booming.payload;

import java.util.ArrayList;
import java.util.List;

public class BGErrorResponse extends BGBaseResponse {
    String message;
    List<BGButton> buttons = new ArrayList<>(0);

    public BGErrorResponse() {
    }

    public BGErrorResponse(String error, String message) {
        this.error = error;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<BGButton> getButtons() {
        return buttons;
    }

    public void setButtons(List<BGButton> buttons) {
        this.buttons = buttons;
    }

    @Override
    public String toString() {
        return "BGErrorResponse{" +
               "message='" + message + '\'' +
               ", buttons=" + buttons +
               ", error='" + error + '\'' +
               "}";
    }
}

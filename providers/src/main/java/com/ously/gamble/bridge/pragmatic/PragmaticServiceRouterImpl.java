package com.ously.gamble.bridge.pragmatic;

import com.ously.gamble.api.bridge.RouterTransportService;
import com.ously.gamble.bridge.RouterBaseV2;
import com.ously.gamble.bridge.pragmatic.payload.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Indexed;


@Indexed
public class PragmaticServiceRouterImpl extends RouterBaseV2 implements PragmaticService {
    final Logger log = LoggerFactory.getLogger(this.getClass());

    public PragmaticServiceRouterImpl(RouterTransportService rtService) {
        super(rtService);
    }

    @Override
    public PPAuthenticateResponse authenticate(PPAuthenticateRequest req) {
        var target = getTarget(req.getToken(), req.getUserId());
        var response = callRemote(target, "PragmaticService::authenticate", req);
        return (PPAuthenticateResponse) response;
    }



    @Override
    public PPGetBalanceResponse balance(PPGetBalanceRequest req) {
        var target = getTarget(req.getToken(), req.getUserId());
        var response = callRemote(target, "PragmaticService::balance", req);
        return (PPGetBalanceResponse) response;

    }

    @Override
    public PPBetResponse bet(PPBetRequest req) {
        var target = getTarget(req.getToken(), req.getUserId());
        var response = callRemote(target, "PragmaticService::bet", req);
        return (PPBetResponse) response;
    }

    @Override
    public PPResultResponse result(PPResultRequest req) {
        var target = getTarget(req.getToken(), req.getUserId());
        var response = callRemote(target, "PragmaticService::result", req);
        return (PPResultResponse) response;
    }

    @Override
    public PPRefundResponse refund(PPRefundRequest req) {
        var target = getTarget(req.getToken(), req.getUserId());
        var response = callRemote(target, "PragmaticService::refund", req);
        return (PPRefundResponse) response;
    }

}

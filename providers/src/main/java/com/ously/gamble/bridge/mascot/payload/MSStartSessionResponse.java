package com.ously.gamble.bridge.mascot.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class MSStartSessionResponse {
    @JsonProperty("SessionId")
    String sessionId;
    @JsonProperty("SeriesId")
    String seriesId;
    @JsonProperty("SessionUrl")
    String sessionUrl;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(String seriesId) {
        this.seriesId = seriesId;
    }

    public String getSessionUrl() {
        return sessionUrl;
    }

    public void setSessionUrl(String sessionUrl) {
        this.sessionUrl = sessionUrl;
    }

    @Override
    public String toString() {
        return "MSStartSessionResponse{" +
                "sessionId='" + sessionId + '\'' +
                ", seriesId='" + seriesId + '\'' +
                ", sessionUrl='" + sessionUrl + '\'' +
                '}';
    }
}

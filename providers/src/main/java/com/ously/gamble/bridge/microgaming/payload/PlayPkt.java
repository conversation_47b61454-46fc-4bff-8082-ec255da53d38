package com.ously.gamble.bridge.microgaming.payload;

import jakarta.xml.bind.annotation.*;

@XmlRootElement(name = "pkt")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "pkt")
public class PlayPkt {

    @XmlElement(required = true, name = "methodresponse")
    PlayResponse resp;

    public PlayResponse getResp() {
        return resp;
    }

    public void setResp(PlayResponse resp) {
        this.resp = resp;
    }
}

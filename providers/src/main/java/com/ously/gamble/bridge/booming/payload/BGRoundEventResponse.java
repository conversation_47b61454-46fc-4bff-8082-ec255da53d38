package com.ously.gamble.bridge.booming.payload;

public class BGRoundEventResponse extends BGBaseResponse {

    String balance;

    public BGRoundEventResponse(String bal) {
        this.balance = bal;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    @Override
    public String toString() {
        return "BGRoundEventResponse{" +
               "balance='" + balance + '\'' +
               ", error='" + error + '\'' +
               "}";
    }
}

package com.ously.gamble.bridge.everymatrix;

import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.bridge.SessionCacheEntry;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.api.session.TxResponse;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.common.URIBuilder;
import com.ously.gamble.bridge.everymatrix.payload.*;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.Wallet;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.model.session.Session;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static com.ously.gamble.api.session.TxRequest.RoundMode.NONE;

@Service
@ConditionalOnBean(EverymatrixConfiguration.class)
public class EverymatrixServiceImpl extends BridgeBaseV2 implements EverymatrixService {

    final Logger log = LoggerFactory.getLogger(EverymatrixServiceImpl.class);

    @Autowired
    EverymatrixConfiguration eConfig;

    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(eConfig.getVendorName());
    }

    @Override
    public GameInstance createNewGameInstanceRaw(final CasinoUser user, final CasinoGame game,
                                                 final GamePlatform platform,
                                                 final GameSettings settings) throws Exception {

        log.info("CreateGame (Everymatrix) for user {}, game {} platform={}", user.getDisplayName(), game.getGameId(), platform);

        String gameUrl = null;
        var token = UUID.randomUUID().toString().replace("-", "");
        if (platform != GamePlatform.TEST) {
            var bld = new URIBuilder(eConfig.getLaunchurl() + "Loader/Start/" + eConfig.getOperatorId() + '/' + game.getGameId());
            bld.addParameter("language", "en");
            bld.addParameter("funMode", "False");
            bld.addParameter("_sid", token);
            gameUrl = bld.build().toString();
        }
        var gameInstance = new GameInstance();
        gameInstance.setGameUrl(gameUrl);
        gameInstance.setToken(token);
        gameInstance.setAuthToken(token);
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }

    @Override
    @Transactional
    public EMGetAccountResponse getAccount(final EMGetAccountRequest req) {
        var uid = -1L;
        var country = "DE";
        if (req.getValidateSession()) {
            var token = getToken(req.getSessionId());
            if (token == null) {
                return new EMGetAccountResponse(req, CODE_SESSION_LIMIT_EXCEEDED, "Sessionid invalid or expired");
            }
            uid = token.getUserId();
            country = token.getCountry();
        } else {
            uid = getUserIdFromExternalUserId(req.getExternalUserId());
        }

        if (uid == -1L) {
            return new EMGetAccountResponse(req, CODE_USER_NOT_FOUND, "User not found");
        }

        var casinoUser = getCasinoUser(uid);
        if (casinoUser == null) {
            return new EMGetAccountResponse(req, CODE_USER_NOT_FOUND, "User not found");
        }
        if (casinoUser.isBlocked()) {
            return new EMGetAccountResponse(req, CODE_USER_BLOCKED, "User is blocked");
        }
        // Account Response
        var resp = new EMGetAccountResponse(req, 0, "Success");
        resp.setExternalUserId(getExternalUserId(casinoUser.getId()));
        resp.setBirthdate("2001-01-01");
        resp.setCountry(getIso3CountryCode(country, casinoUser.getLangCode()));
        resp.setCurrency(eConfig.getCurrency());
        resp.setSessionId(req.getSessionId());
        resp.setUserName(casinoUser.getDisplayName());
        resp.setAlias(casinoUser.getDisplayName());
        return resp;
    }

    private static String getExternalUserId(long uid) {
        return "EMUID-" + uid;
    }

    private static long getUserIdFromExternalUserId(String eUid) {
        try {
            return Long.parseLong(eUid.substring(6));
        } catch (Exception e) {
            return -1L;
        }
    }


    private static String getIso3CountryCode(final String cntrIso2Code, final String pLang) {
        var cntr = Objects.requireNonNullElse(cntrIso2Code, "DE").toUpperCase(Locale.ROOT);
        var lang = Objects.requireNonNullElse(pLang, "de").toLowerCase(Locale.ROOT);
        var lc = new Locale(lang, cntr);
        return lc.getISO3Country();
    }


    @Override
    @Transactional
    public EMGetBalanceResponse getBalance(final EMGetBalanceRequest req) {
        var uid = -1L;
        if (req.getValidateSession()) {
            var token = getToken(req.getSessionId());
            if (token == null) {
                return new EMGetBalanceResponse(req, CODE_SESSION_LIMIT_EXCEEDED, "Sessionid invalid or expired");
            }
            uid = token.getUserId();
//            var country = token.getCountry();
        } else {
            uid = getUserIdFromExternalUserId(req.getExternalUserId());
        }

        if (uid == -1L) {
            return new EMGetBalanceResponse(req, CODE_USER_NOT_FOUND, "User not found");
        }

        var w = getWallet(uid);
        var resp = new EMGetBalanceResponse(req, 0, "Success");
        resp.setCurrency(eConfig.getCurrency());
        resp.setBalance(w.getBalance());
        resp.setBonusMoney(BigDecimal.ZERO);
        resp.setRealMoney(w.getBalance());
        resp.setSessionId(req.getSessionId());
        return resp;
    }

    @Override
    @Transactional
    public EMTransactionResponse debit(final EMTransactionRequest req) {
        var token = findToken(req);
        if (token == null) {
            return new EMTransactionResponse(req, CODE_SESSION_LIMIT_EXCEEDED, "Sessionid invalid or expired");
        }

        var w = getWallet(token.getUserId());
        if (req.getReversal()) {
            return doReversal(req, token, w);
        }

        if (w.getBalance().subtract(req.getAmount()).signum() < 0) {
            return new EMTransactionResponse(req, CODE_INSUFFICIENT_FUNDS, "Not enough funds to execute debit");
        }

        var txReq = new TxRequest(token);
        txReq.setBet(req.getAmount());
        txReq.setType(TransactionType.BET);
        txReq.setExternalOrigId(req.getTransactionId());
        txReq.setRoundRef(req.getRoundId());
        txReq.setWin(BigDecimal.ZERO);
        txReq.setRoundMode(RoundMode.AUTO);

        TxResponse txResp;
        try {
            txResp = addTxFromProvider(txReq);
        } catch (Exception e) {
            return new EMTransactionResponse(req, CODE_UNKNOWN_ERROR, StringUtils.abbreviate(e.getMessage(), 250));
        }

        // Debit response
        var emResp = new EMTransactionResponse(req, 0, "Success");
        emResp.setBalance(txResp.getNewBalance());
        emResp.setCurrency(eConfig.getCurrency());
        emResp.setAccountTransactionId(Long.toString(txResp.getTxId()));
        emResp.setBonusMoneyAffected(BigDecimal.ZERO);
        emResp.setRealMoneyAffected(txReq.getBet());

        return emResp;
    }

    private EMTransactionResponse doReversal(final EMTransactionRequest req,
                                             final SessionCacheEntry token, final Wallet w) {

        var txToReverse = findBySessionIdAndOrigId(token.getUserId(), token.getSessionId(), req.getReversalTransactionId());
        if (txToReverse == null) {
            var emResp = new EMTransactionResponse(req, 0, "Success");
            emResp.setBalance(w.getBalance());
            emResp.setCurrency(eConfig.getCurrency());
            emResp.setAccountTransactionId(req.getTransactionId());
            emResp.setBonusMoneyAffected(BigDecimal.ZERO);
            emResp.setRealMoneyAffected(BigDecimal.ZERO);
            return emResp;
        }

        // Got tx to reverse, now create new tx

        var txType = TransactionType.BET;
        if (req.getTransactionType() == EMTransactionType.Result) {
            txType = TransactionType.WIN;
        }

        if (txType == TransactionType.BET) {
            if (w.getBalance().subtract(req.getAmount()).signum() < 0) {
                return new EMTransactionResponse(req, CODE_INSUFFICIENT_FUNDS, "Not enough funds to execute debit");
            }
        }

        var txReq = new TxRequest(token);
        txReq.setRoundMode(NONE);
        txReq.setRoundRef(req.getRoundId());
        txReq.setType(txType);
        txReq.setWin(BigDecimal.ZERO);
        txReq.setBet(BigDecimal.ZERO);
        txReq.setExternalOrigId(req.getTransactionId());
        if (txType == TransactionType.WIN) {
            txReq.setWin(req.getAmount());
        } else {
            txReq.setBet(req.getAmount());
        }
        TxResponse txResp;
        try {
            txResp = addTxFromProvider(txReq);
        } catch (OuslyOutOfMoneyException e) {
            return new EMTransactionResponse(req, CODE_INSUFFICIENT_FUNDS, "Not enough funds to execute debit");
        } catch (Exception e) {
            return new EMTransactionResponse(req, CODE_UNKNOWN_ERROR, StringUtils.abbreviate(e.getMessage(), 250));
        }
        var emResp = new EMTransactionResponse(req, 0, "Success");
        emResp.setBalance(txResp.getNewBalance());
        emResp.setCurrency(eConfig.getCurrency());
        emResp.setAccountTransactionId(Long.toString(txResp.getTxId()));
        emResp.setBonusMoneyAffected(BigDecimal.ZERO);
        emResp.setRealMoneyAffected(txReq.getBet().add(txReq.getWin()));
        return emResp;
    }

    private SessionCacheEntry findToken(final EMTransactionRequest req) {
        if (req.getValidateSession()) {
            var token = getToken(req.getSessionId());
            if (token == null) {
                return findExpiredToken(req.getSessionId());
            }
            return token;
        }
        var ssId = findLatestSessionIdByUserIdAndGameKeyAndRoundReference(getUserIdFromExternalUserId(req.getExternalUserId()), req.getAdditionalData().getGameSlug(), req.getRoundId());
        if (ssId.isEmpty()) {
            ssId = findLatestSessionIdByUserIdAndGameKey(getUserIdFromExternalUserId(req.getExternalUserId()), req.getAdditionalData().getGameSlug());
            if (ssId.isEmpty()) {
                return null;
            }
        }
        return createTokenFromSession(findSessionByUserIdAndSessionId(getUserIdFromExternalUserId(req.getExternalUserId()), ssId.get()));
    }

    private SessionCacheEntry createTokenFromSession(final Optional<Session> optSession) {
        if (optSession.isEmpty()) {
            return null;
        }
        var ss = optSession.get();
        return new SessionCacheEntry(ss, getCasinoUser(ss.getUserId()), null);
    }

    @Override
    @Transactional
    public EMTransactionResponse credit(final EMTransactionRequest req) {

        var token = findToken(req);
        if (token == null) {
            return new EMTransactionResponse(req, CODE_SESSION_LIMIT_EXCEEDED, "Sessionid invalid or expired");
        }

        var w = getWallet(token.getUserId());
        if (req.getReversal()) {
            return doReversal(req, token, w);
        }

        if (req.getTransactionType() == EMTransactionType.Rollback) {
            // find old tx
            var oldTx = findBySessionIdAndOrigId(token.getUserId(), token.getSessionId(), req.getRollbackTransactionId());
            if (oldTx == null) {
                var emResp = new EMTransactionResponse(req, 0, "Success");
                emResp.setBalance(w.getBalance());
                emResp.setCurrency(eConfig.getCurrency());
                emResp.setAccountTransactionId(req.getTransactionId());
                emResp.setBonusMoneyAffected(BigDecimal.ZERO);
                emResp.setRealMoneyAffected(BigDecimal.ZERO);
                return emResp;
            }
        }

        var txReq = new TxRequest(token);
        txReq.setBet(BigDecimal.ZERO);
        txReq.setType(TransactionType.WIN);
        txReq.setExternalOrigId(req.getTransactionId());
        txReq.setRoundRef(req.getRoundId());
        txReq.setWin(req.getAmount());
        txReq.setRoundMode(RoundMode.AUTO);

        TxResponse txResp;
        try {
            txResp = addTxFromProvider(txReq);
        } catch (Exception e) {
            return new EMTransactionResponse(req, CODE_UNKNOWN_ERROR, StringUtils.abbreviate(e.getMessage(), 250));
        }

        // Credit response
        var emResp = new EMTransactionResponse(req, 0, "Success");
        emResp.setBalance(txResp.getNewBalance());
        emResp.setCurrency(eConfig.getCurrency());
        emResp.setAccountTransactionId(Long.toString(txResp.getTxId()));
        emResp.setBonusMoneyAffected(BigDecimal.ZERO);
        emResp.setRealMoneyAffected(txReq.getBet());

        return emResp;

    }

}

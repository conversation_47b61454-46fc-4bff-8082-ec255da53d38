package com.ously.gamble.bridge.booongo.payload;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

public class BooTransaction {

    @JsonProperty("transaction_uid")
    String transactionUid;

    String bet;

    String win;

    @JsonProperty("round_started")
    Boolean roundStarted;

    @JsonProperty("round_finished")
    Boolean roundFinished;

    @JsonProperty("round_id")
    Long roundId;

    BooBonus bonus;

    BooPlayer player;

    String tag;


    public String getBet() {
        return bet;
    }

    public void setBet(String bet) {
        this.bet = bet;
    }

    public String getWin() {
        return win;
    }

    public void setWin(String win) {
        this.win = win;
    }

    public Boolean getRoundStarted() {
        return roundStarted;
    }

    public void setRoundStarted(Boolean roundStarted) {
        this.roundStarted = roundStarted;
    }

    public Boolean getRoundFinished() {
        return roundFinished;
    }

    public void setRoundFinished(Boolean roundFinished) {
        this.roundFinished = roundFinished;
    }

    public Long getRoundId() {
        return roundId;
    }

    public void setRoundId(Long roundId) {
        this.roundId = roundId;
    }

    public BooBonus getBonus() {
        return bonus;
    }

    public void setBonus(BooBonus bonus) {
        this.bonus = bonus;
    }

    public BooPlayer getPlayer() {
        return player;
    }

    public void setPlayer(BooPlayer player) {
        this.player = player;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getTransactionUid() {
        return transactionUid;
    }

    public void setTransactionUid(String transactionUid) {
        this.transactionUid = transactionUid;
    }

    @Override
    public String toString() {
        return "BooTransaction{" +
                "transactionUid='" + transactionUid + '\'' +
                ", bet='" + bet + '\'' +
                ", win='" + win + '\'' +
                ", roundStarted=" + roundStarted +
                ", roundFinished=" + roundFinished +
                ", roundId=" + roundId +
                ", bonus=" + bonus +
                ", player=" + player +
                ", tag='" + tag + '\'' +
                '}';
    }

    @JsonIgnore
    public BigDecimal getBetDecimal() {
        if (StringUtils.isBlank(bet)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(bet);
    }

    @JsonIgnore
    public BigDecimal getWinDecimal() {
        if (StringUtils.isBlank(win)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(win);
    }
}

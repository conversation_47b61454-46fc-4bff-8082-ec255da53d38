package com.ously.gamble.bridge.reevo;

import com.ously.gamble.api.bridge.BridgeConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "reevo")
@ConditionalOnProperty(
        value = "reevo.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class ReevoConfiguration implements BridgeConfiguration {

    public static final String VENDOR_NAME = "REEVO";
    public static final String BRIDGE_REEVO = "/bridge/reevo";


    @Override
    public String getVendorName() {
        return VENDOR_NAME;
    }

    @Override
    public String getBridgeName() {
        return BRIDGE_REEVO;
    }

    @Override
    public String getAuth_user() {
        return null;
    }

    @Override
    public String getAuth_password() {
        return null;
    }

    @Override
    public List<String> getWhitelistIPs() {
        return new ArrayList<>();
    }
}

package com.ously.gamble.bridge.ballywulff.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class BWGameSessionResponse {
    BWUser user;
    BWGame game;

    public BWUser getUser() {
        return user;
    }

    public void setUser(BWUser user) {
        this.user = user;
    }

    public BWGame getGame() {
        return game;
    }

    public void setGame(BWGame game) {
        this.game = game;
    }

    @Override
    public String toString() {
        return "BWGameSessionResponse{" +
                "user=" + user +
                ", game=" + game +
                '}';
    }
}

package com.ously.gamble.bridge.netent;

import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.netent.payload.NEBalanceResponse;
import com.ously.gamble.bridge.netent.payload.NEGameRoundRequest;
import com.ously.gamble.bridge.netent.payload.NEGameRoundResponse;

public interface NetEntService extends BridgeHandler {

    NEBalanceResponse getBalance(String playerId);

    NEGameRoundResponse doGameRound(NEGameRoundRequest req);
}

package com.ously.gamble.bridge.hoelle.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import java.util.List;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class HOLTxResponse extends HOLResponse {

    List<HOLBalance> balances;
    Boolean useBonusFirst;
    List<HOLTransactionResult> hashesProcessed;

    public HOLTxResponse() {
        super();
    }

    public HOLTxResponse(int eCode) {
        super(eCode);
    }

    public List<HOLBalance> getBalances() {
        return balances;
    }

    public void setBalances(List<HOLBalance> balances) {
        this.balances = balances;
    }

    public Boolean getUseBonusFirst() {
        return useBonusFirst;
    }

    public void setUseBonusFirst(Boolean useBonusFirst) {
        this.useBonusFirst = useBonusFirst;
    }

    public List<HOLTransactionResult> getHashesProcessed() {
        return hashesProcessed;
    }

    public void setHashesProcessed(List<HOLTransactionResult> hashesProcessed) {
        this.hashesProcessed = hashesProcessed;
    }


    @Override
    public String toString() {
        return "HOLTxResponse{" +
                "errorCode=" + errorCode +
                ", balances=" + balances +
                ", useBonusFirst=" + useBonusFirst +
                ", hashesProcessed=" + hashesProcessed +
                '}';
    }
}

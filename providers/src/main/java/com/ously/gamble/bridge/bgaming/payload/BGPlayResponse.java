package com.ously.gamble.bridge.bgaming.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class BGPlayResponse {

    public static BGPlayResponse error(int code, String msg, int httpCode) {
        var resp = new BGPlayResponse(code);
        resp.message = msg;
        resp.httpCode = httpCode;
        return resp;
    }

    private void setHttpCode(int httpCode) {
        this.httpCode = httpCode;
    }


    Integer code;
    String message;
    @JsonIgnore
    int httpCode;

    Long balance;
    @JsonProperty("game_id" )
    String gameId;
    List<BGTransaction> transactions;


    public BGPlayResponse() {
    }

    public BGPlayResponse(int code) {
        this.code = code;
    }

    public BGPlayResponse(Long balance, String gameId) {
        this.balance = balance;
        this.gameId = gameId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    @JsonIgnore
    public Integer getHttpCode() {
        return httpCode;
    }

    public List<BGTransaction> getTransactions() {
        return transactions;
    }

    public void setTransactions(List<BGTransaction> transactions) {
        this.transactions = transactions;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
        this.gameId = null;
        this.balance = null;
        this.transactions = null;
    }

    @Override
    public String toString() {
        return "BGPlayResponse{" +
               "code=" + code +
               ", balance=" + balance +
               ", gameId='" + gameId + '\'' +
               ", transactions=" + transactions +
               '}';
    }
}

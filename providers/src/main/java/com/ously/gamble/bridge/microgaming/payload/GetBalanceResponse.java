package com.ously.gamble.bridge.microgaming.payload;

import com.ously.gamble.bridge.microgaming.MicrogamingUtils;
import jakarta.xml.bind.annotation.*;

import java.math.BigInteger;

@XmlRootElement(name = "methodresponse")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "methodresponse")
public class GetBalanceResponse {


    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "result")
    public static class Result {
        @XmlAttribute(required = true)
        String seq;
        @XmlAttribute(required = true)
        String token;
        @XmlAttribute(required = true)
        BigInteger balance;
        @XmlAttribute(required = true)
        BigInteger bonusbalance = BigInteger.ZERO;

        public String getSeq() {
            return seq;
        }

        public void setSeq(String seq) {
            this.seq = seq;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }


        public BigInteger getBalance() {
            return balance;
        }

        public void setBalance(BigInteger balance) {
            this.balance = balance;
        }

        public BigInteger getBonusbalance() {
            return bonusbalance;
        }

        public void setBonusbalance(BigInteger bonusbalance) {
            this.bonusbalance = bonusbalance;
        }

    }

    @XmlAttribute(required = true)
    String name = "getbalance";

    @XmlAttribute(required = true)
    String timestamp = MicrogamingUtils.createTimestamp();

    @XmlElement(required = true)
    Result result = new Result();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public Result getResult() {
        return result;
    }

    public void setResult(Result result) {
        this.result = result;
    }
}

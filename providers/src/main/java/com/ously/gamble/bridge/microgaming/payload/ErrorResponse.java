package com.ously.gamble.bridge.microgaming.payload;

import com.ously.gamble.bridge.microgaming.MicrogamingUtils;
import jakarta.xml.bind.annotation.*;

@XmlRootElement(name = "methodresponse")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "methodresponse")
public class ErrorResponse {

    public ErrorResponse() {
    }

    public ErrorResponse(String name, String seq, int code, String descr) {
        this.name = name;
        this.timestamp = MicrogamingUtils.createTimestamp();
        this.result.setSeq(seq);
        this.result.setErrorcode(code);
        this.result.setErrordescription(descr);
    }

    @XmlAttribute(required = true)
    String name = "login";
    @XmlAttribute(required = true)
    String timestamp = MicrogamingUtils.createTimestamp();

    @XmlElement(required = true)
    ErrorResult result = new ErrorResult();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public ErrorResult getResult() {
        return result;
    }

    public void setResult(ErrorResult result) {
        this.result = result;
    }
}

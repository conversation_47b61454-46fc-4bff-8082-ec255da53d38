package com.ously.gamble.bridge.spinomenal;


import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.bridge.SessionCacheEntry;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.common.URIBuilder;
import com.ously.gamble.bridge.spinomenal.payload.*;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.Wallet;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.OptimisticLockException;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.StaleStateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
@ConditionalOnBean(SpinomenalConfiguration.class)
public class SpinomenalServiceImpl extends BridgeBaseV2 implements SpinomenalService {

    private final Logger log = LoggerFactory.getLogger(SpinomenalServiceImpl.class);

    @Autowired
    SpinomenalConfiguration config;

    final String[] betSizesDef = new String[]{"0.01", "0.02", "0.05", "0.1", "0.25", "0.5", "0.75", "1.0", "2.0", "5.0", "10.0"};
    final String[] betSizesVip = new String[]{"0.2", "0.35", "0.5", "1", "2", "5", "10",
            "20", "40"};
    final String[] betSizesHigh = new String[]{"4", "8", "15", "20", "40", "50", "100",
            "200", "400"};
    final String[] betSizesExtreme = new String[]{"20", "40", "60", "100", "200", "250", "500",
            "1000", "2000"};
    BigDecimal[] betSizesD;
    BigDecimal[] betSizesV;
    BigDecimal[] betSizesH;
    BigDecimal[] betSizesX;

    @PostConstruct
    public void convertBetsizeDefinitions() {
        betSizesD = convertBetSize(betSizesDef);
        betSizesV = convertBetSize(betSizesVip);
        betSizesH = convertBetSize(betSizesHigh);
        betSizesX = convertBetSize(betSizesExtreme);

    }

    private static BigDecimal[] convertBetSize(String... bets) {
        var betSizes = new BigDecimal[bets.length];
        var mc = new MathContext(2, RoundingMode.HALF_UP);
        for (var i = 0; i < betSizes.length; i++) {
            betSizes[i] = new BigDecimal(bets[i], mc);
        }
        return betSizes;
    }

    @SuppressWarnings(
            "ArrayCreationWithoutNewKeyword") final Map<Integer, String> spinoErrors = Stream.of(new Object[][]{
            {6001, "Invalid Parameters"},
            {6002, "Invalid Signature"},
            {6003, "Token was not found"},
            {6004, "Player account locked"},
            {6005, "Player account disabled"},
            {6006, "Responsible gaming limit"},
            {6007, "Unkown player Id"},
            {6008, "Internal error"},
            {6010, "Unknown transaction id"},
            {6011, "Insufficient funds"},
            {6012, "Player not logged in"},
            {6013, "Token expired"},
            {6014, "Wager limit"},
            {6015, "Problem processing the free spins promo"},
            {6016, "Bet not according to bonus terms and conditions"},
            {5029, "Round doesnt exist"},
            {5030, "Round is closed"},
    }).collect(Collectors.toMap(data -> (Integer) data[0], data -> (String) data[1]));


    private static TransactionType getTypeForSpinoType(String spType) {
        if ("BET".equals(spType)) {
            return TransactionType.BET;
        }
        if ("WIN".equals(spType)) {
            return TransactionType.WIN;
        }
        return TransactionType.DIRECTWIN;
    }


    private static String getCurrencyCodeFromWallet(Wallet w) {
        return "SPN";
    }


    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(config.getVendorName());
    }

    /**
     * Sanity check - incomplete
     * TODO: finish sanity check for all needed attributes
     */
    private boolean checkBetRequest(ProcessBetRequest req) {
        if (StringUtils.isEmpty(req.getTransactionType())
            ||
            StringUtils.isEmpty(req.getGameToken())) {
            log.warn("ProcBet fails check: for exId {} in session {} type {}", req.getExternalId(), req.getGameToken(), req.getTransactionType());

            return true;
        }
        return false;
    }

    private static boolean checkAuthenticateRequest(AuthenticationRequest req) {
        return !StringUtils.isEmpty(req.getGameToken())
               &&
               !StringUtils.isEmpty(req.getSig());
    }

    private PlayerBalanceResponse createPlayerBalanceError(int errorCode) {
        var pbresp = new PlayerBalanceResponse();
        pbresp.setErrorCode(errorCode);
        pbresp.setErrorMessage(spinoErrors.get(errorCode));
        pbresp.setBalance(BigDecimal.ZERO);
        pbresp.setTimeStamp(SpinomenalUtils.convertLocalDateTimeToTimestamp(LocalDateTime.now()));
        log.warn("playerBalance Error {}", pbresp);
        return pbresp;
    }

    private ProcessBetResponse createProcessBetError(int errorCode) {
        var pbresp = new ProcessBetResponse();
        pbresp.setErrorCode(errorCode);
        pbresp.setErrorMessage(spinoErrors.get(errorCode));
        pbresp.setBalanceAfter(null);
        pbresp.setTimeStamp(SpinomenalUtils.convertLocalDateTimeToTimestamp(LocalDateTime.now()));
        log.warn("ProcBet fails {}", pbresp);
        return pbresp;
    }

    private ProcessBetResponse createSolveBetError(int errorCode) {
        var pbresp = new ProcessBetResponse();
        pbresp.setErrorCode(errorCode);
        pbresp.setErrorMessage(spinoErrors.get(errorCode));
        pbresp.setBalanceAfter(null);
        pbresp.setTimeStamp(SpinomenalUtils.convertLocalDateTimeToTimestamp(LocalDateTime.now()));
        log.warn("solveBet fails {}", pbresp);
        return pbresp;
    }

    private AuthenticationResponse createAuthenticateError(int errorCode) {
        var pbresp = new AuthenticationResponse();
        pbresp.setErrorCode(errorCode);
        pbresp.setErrorMessage(spinoErrors.get(errorCode));
        pbresp.setTimeStamp(SpinomenalUtils.convertLocalDateTimeToTimestamp(LocalDateTime.now()));
        log.warn("auth Error {}", pbresp);
        return pbresp;
    }


    private boolean checkSignature(String sig, String sigPart) {
        return sig.equals(config.createSignature(sigPart));
    }


    /**
     * Insert or update the gameInstance row (referred to as token sometimes)
     * Often a unique tuple of user,game
     */
    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform,
                                                 GameSettings settings) throws Exception {

        log.info("CreateGame  for user {}, game {} platform={}", user.getDisplayName(), game.getGameId(), platform);

        var gameUrl = "TEST";
        var token = UUID.randomUUID().toString();
        if (platform != GamePlatform.TEST) {
            var bld = new URIBuilder(config.getBaseUrl());
            bld.addParameter("GameToken", token);
            bld.addParameter("PartnerId", config.getPartnerId());
            bld.addParameter("GameCode", game.getGameId());
            bld.addParameter("FunMode", "false");
            bld.addParameter("LangCode", "en_US");
            bld.addParameter("PlatformId", ((platform == GamePlatform.WEB || platform == GamePlatform.DESKTOP) ? "1" : "2"));
            gameUrl = bld.build().toString();
        }

        var
                gameInstance = new GameInstance();

        gameInstance.setGameUrl(gameUrl);
        gameInstance.setToken(token);
        gameInstance.setAuthToken(token);
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }

    @Override
    @Transactional
    public PlayerBalanceResponse getPlayerBalance(PlayerBalanceRequest pbr) {
        log.debug("playerBalance Request {}", pbr);

        var sigok = checkSignature(pbr.getSig(), pbr.getSigPart());
        try {
            if (sigok) {
                // 1. find token in table/cache
                var cToken = getToken(pbr.getGameToken());
                if (cToken == null) {
                    return createPlayerBalanceError(6003);
                }
                if (cToken.getExpiry().isBefore(Instant.now())) {
                    return createPlayerBalanceError(6013);
                }

                // 2. getPlayer (wallet) from table/cache
                var externalWallet = getWallet(cToken.getWalletId());

                var bd = externalWallet.getBalance();

                // 3.a return balance
                var pbA = new PlayerBalanceResponse();
                pbA.setTimeStamp(SpinomenalUtils.convertLocalDateTimeToTimestamp(LocalDateTime.now()));
                pbA.setBalance(bd);
                pbA.setErrorCode(0);
                log.debug("playerBalance Response {}", pbA);
                return pbA;
            }
            return createPlayerBalanceError(6002);
        } catch (Exception e) {
            log.error("Error getting balance", e);
        }
        return createPlayerBalanceError(6008);
    }


    @Override
    @Transactional
    public AuthenticationResponse authenticate(AuthenticationRequest req) {
        log.debug("Auth incoming {}", req);

        var sigok = checkSignature(req.getSig(), req.getSigPart());
        try {
            if (sigok) {
                if (!checkAuthenticateRequest(req)) {
                    return createAuthenticateError(6001);
                }
                // load game
                var cToken = getToken(req.getGameToken());
                if (cToken == null) {
                    return createAuthenticateError(6003);
                }
                if (cToken.getExpiry().isBefore(Instant.now())) {
                    return createAuthenticateError(6013);
                }

                var resp = new AuthenticationResponse();
                resp.setCountryCode("de_DE");
                resp.setCurrencyCode(getCurrencyCodeFromWallet(null));
                resp.setExternalId("UID:" + cToken.getUserId());
                resp.setName(StringUtils.abbreviate(cToken.getUsername(), 32));
                resp.setTypeId(0);
                if (isCustomBetsizeEnabled()) {
                    switch (cToken.getBetLevel()) {
                        case 1 -> {
                            resp.setBets(betSizesV);
                            resp.setDefaultBet(betSizesV[0]);
                        }
                        case 2 -> {
                            resp.setBets(betSizesH);
                            resp.setDefaultBet(betSizesH[0]);
                        }
                        case 3 -> {
                            resp.setBets(betSizesX);
                            resp.setDefaultBet(betSizesX[0]);
                        }

                        default -> {
                            resp.setBets(betSizesD);
                            resp.setDefaultBet(betSizesD[0]);
                        }
                    }
                } else {
                    resp.setBets(betSizesD);
                    resp.setDefaultBet(betSizesD[0]);
                }
                log.debug("authenticate Response {}", resp);
                return resp;
            }
            return createAuthenticateError(6002);
        } catch (Exception e) {
            log.error("Error authenticating", e);
        }
        return createAuthenticateError(6008);
    }


    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public ProcessBetResponse processBet(ProcessBetRequest req) {
        log.debug("processBet Request {}", req);
        var sigok = checkSignature(req.getSig(), req.getSigPart());
        try {
            if (sigok) {
                // check types and send parameters (sanity check)
                if (checkBetRequest(req)) {
                    return createProcessBetError(6001);
                }

                var cToken = getToken(req.getGameToken());
                if (cToken == null) {
                    if (req.getWinAmount().signum() == 1) {
                        var sessionByToken = findSessionByToken(req.getGameToken());
                        if (sessionByToken.isEmpty()) {
                            return createProcessBetError(6003);
                        }
                        cToken = new SessionCacheEntry();
                        cToken.setUserId(sessionByToken.get().getUserId());
                        cToken.setGameId(sessionByToken.get().getGameId());
                        cToken.setSessionId(sessionByToken.get().getSessionId());
                        cToken.setGp(sessionByToken.get().getPlatform());
                    } else {
                        return createProcessBetError(6003);
                    }
                } else {
                    // check expiry of token
                    if (cToken.getExpiry().isBefore(Instant.now())) {
                        return createProcessBetError(6013);
                    }
                }

                // create Transaction
                var txReq = new TxRequest(cToken);
                txReq.setRoundMode(RoundMode.OPENCLOSE);
                txReq.setBet(req.getBetAmount());
                txReq.setWin(req.getWinAmount());
                txReq.setRoundRef(Long.toString(req.getTicketId()));
                var tType = getTypeForSpinoType(req.getTransactionType());
                txReq.setType(tType);
                txReq.setExternalOrigId("" + req.getTicketId() + ':' + tType.name());
                var txResp = addTxFromProvider(txReq);
                // return result (incl. BalanceAfter + Timestamp)
                var pbResp = new ProcessBetResponse();
                pbResp.setBalanceAfter(txResp.getNewBalance());
                pbResp.setTimeStamp(SpinomenalUtils.convertLocalDateTimeToTimestamp(LocalDateTime.now()));
                pbResp.setExtTransactionId(config.getPartnerId() + ':' + txResp.getTxId());
                log.debug("processBet Response {}", pbResp);
                return pbResp;
            }
            return createProcessBetError(6002);
        } catch (Exception e) {
            log.error("ProcBet fails: {}", req, e);
        }
        return createProcessBetError(6008);
    }

}

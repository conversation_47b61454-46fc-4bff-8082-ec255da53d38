package com.ously.gamble.bridge.common;

import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

public class URIBuilder {


    private final UriComponentsBuilder uriBuilder;

    public URIBuilder(String path) {
        this.uriBuilder = UriComponentsBuilder.fromHttpUrl(path);
    }

    public URIBuilder addParameter(String pName, Object pValue) {
        this.uriBuilder.queryParam(pName, pValue);
        return this;
    }

    public URI build() {
        return uriBuilder.build().toUri();
    }

}

package com.ously.gamble.bridge.mascot.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class JRPCMSSetPlayerRequest extends MSJsonRpcRequest {
    MSSetPlayerRequest params;

    public JRPCMSSetPlayerRequest(Long id, MSSetPlayerRequest bgReq) {
        super("Player.Set", id);
        this.params = bgReq;
    }

    public MSSetPlayerRequest getParams() {
        return params;
    }

    public void setParams(MSSetPlayerRequest params) {
        this.params = params;
    }
}

package com.ously.gamble.bridge.playngo.payload;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

import java.io.Serial;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
public class PNGRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @XmlElement(name = "accessToken", required = true)
    String accessToken;

    @XmlElement(name = "gameId", required = true)
    String gameId;

    @XmlElement(name = "productId", required = true)
    String productId;

    @XmlElement(name = "clientIP", required = true)
    String clientIP;

    @XmlElement(name = "contextId", required = true)
    String contextId;

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getClientIP() {
        return clientIP;
    }

    public void setClientIP(String clientIP) {
        this.clientIP = clientIP;
    }

    public String getContextId() {
        return contextId;
    }

    public void setContextId(String contextId) {
        this.contextId = contextId;
    }
}

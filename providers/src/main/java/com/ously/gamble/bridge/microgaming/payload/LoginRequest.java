package com.ously.gamble.bridge.microgaming.payload;


import jakarta.xml.bind.annotation.*;

@XmlRootElement(name = "methodcall")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "methodcall", propOrder = {"auth", "call"})
public class LoginRequest {

    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "call")
    public static class LoginCall {
        @XmlAttribute(name = "seq", required = true)
        private String seq;
        @XmlAttribute(name = "token", required = true)
        private String token;

        public String getSeq() {
            return seq;
        }

        public void setSeq(String seq) {
            this.seq = seq;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        @Override
        public String toString() {
            return "LoginCall{" +
                    "seq='" + seq + '\'' +
                    ", token='" + token + '\'' +
                    '}';
        }
    }


    @XmlAttribute(name = "name")
    private String name;
    @XmlAttribute(name = "timestamp")
    private String timestamp;
    @XmlAttribute(name = "system")
    private String system;
    @XmlElement(required = true)
    private Auth auth;
    @XmlElement(required = true)
    private LoginCall call;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public Auth getAuth() {
        return auth;
    }

    public void setAuth(Auth auth) {
        this.auth = auth;
    }

    public LoginCall getCall() {
        return call;
    }

    public void setCall(LoginCall call) {
        this.call = call;
    }

    @Override
    public String toString() {
        return "LoginRequest{" +
                "name='" + name + '\'' +
                ", timestamp='" + timestamp + '\'' +
                ", system='" + system + '\'' +
                ", auth=" + auth +
                ", call=" + call +
                '}';
    }
}

package com.ously.gamble.bridge.nolimit;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.bridge.nolimit.payload.*;

public final class NoLimitUtils {

    private NoLimitUtils() {
    }

    static final ObjectMapper om = new ObjectMapper();

    static JsonRpcRequest parseRequest(String req) throws JsonProcessingException {

        var jsonNode = om.readTree(req);
        var jsonrpc = jsonNode.get("jsonrpc");
        var method = jsonNode.get("method");
        var id = jsonNode.get("id");
        var params = jsonNode.get("params");
        var jsonRpcRequest = new JsonRpcRequest();
        jsonRpcRequest.setId(id.textValue());
        jsonRpcRequest.setMethod(method.textValue());
        jsonRpcRequest.setJsonrpc(jsonrpc.textValue());
        jsonRpcRequest.setJsonParams(params.toString());
        return jsonRpcRequest;
    }

    public static NLValidateTokenRequest parseValidateRequest(String jsonParams) throws JsonProcessingException {
        return om.readValue(jsonParams, NLValidateTokenRequest.class);
    }

    public static NLWithdrawRequest parseWithdrawRequest(String jsonParams) throws JsonProcessingException {
        return om.readValue(jsonParams, NLWithdrawRequest.class);
    }

    public static NLDepositRequest parseDepositRequest(String jsonParams) throws JsonProcessingException {
        return om.readValue(jsonParams, NLDepositRequest.class);
    }

    public static String toJson(JsonRpcResponse jsonRpcResponse) throws JsonProcessingException {
        return om.writeValueAsString(jsonRpcResponse);
    }


    public static NLRollbackRequest parseRollbackRequest(String jsonParams) throws JsonProcessingException {
        return om.readValue(jsonParams, NLRollbackRequest.class);
    }

    public static NLBalanceRequest parseBalanceRequest(String jsonParams) throws JsonProcessingException {
        return om.readValue(jsonParams, NLBalanceRequest.class);
    }
}

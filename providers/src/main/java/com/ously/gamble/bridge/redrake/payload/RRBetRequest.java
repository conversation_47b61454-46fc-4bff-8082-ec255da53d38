package com.ously.gamble.bridge.redrake.payload;

public class RRBetRequest extends RRCall<RRBetParams> {

    RRBetParams params;

    @Override
    public RRBetParams getParams() {
        return params;
    }

    @Override
    public void setParams(RRBetParams params) {
        this.params = params;
    }

    @Override
    public String toString() {
        return "RRBetRequest{" +
                "params=" + params +
                "} " + super.toString();
    }
}

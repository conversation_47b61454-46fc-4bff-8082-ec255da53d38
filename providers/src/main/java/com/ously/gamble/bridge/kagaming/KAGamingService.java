package com.ously.gamble.bridge.kagaming;

import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.kagaming.payload.*;

public interface KAGamingService extends BridgeHandler {
    KAStartResponse start(KAStartRequest req);

    KAPlayResponse play(KAPlayRequest req);

    KACreditResponse credit(KACreditRequest req);

    KARevokeResponse revoke(KARevokeRequest req);

    KABalanceResponse balance(KABalanceRequest req);

    KAEndResponse end(KAEndRequest req);
}

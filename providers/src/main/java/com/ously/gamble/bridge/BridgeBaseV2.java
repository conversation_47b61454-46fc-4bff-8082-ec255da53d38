package com.ously.gamble.bridge;

import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.SessionCacheEntry;
import com.ously.gamble.api.games.ActiveGameManagementService;
import com.ously.gamble.api.session.NewTransactionService;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxResponse;
import com.ously.gamble.persistence.model.SessionState;
import com.ously.gamble.persistence.model.Wallet;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.session.Session;
import com.ously.gamble.persistence.model.session.SessionRound;
import com.ously.gamble.persistence.model.session.SessionTransaction;
import com.ously.gamble.persistence.repository.session.SessionRoundRepository;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

public class BridgeBaseV2 extends AbstractBridgeBase {

    @Autowired
    ActiveGameManagementService giRepo;

    @Autowired
    private NewTransactionService txService;

    @Autowired
    SessionRoundRepository srndRepo;



    public SessionTransaction findByUserIdAndOrigId(Long uid, String origId) {
        return sessTransRepo.findFirstByUserIdAndExternalOrigTxId(uid, origId).orElse(null);
    }

    public GameInstance saveGameInstance(GameInstance gi, boolean flush) {
        return giRepo.save(gi, flush);
    }

    public GameInstance findByTokenAndActive(String token, boolean active) {
        return giRepo.findByTokenAndActive(token, active);
    }

    public Optional<SessionRound> findRoundForUserIdAndGameIdAndRoundRef(long userId, long gameId,
                                                                         String roundId) {
        return srndRepo.findFirstByUserIdAndRoundReferenceAndGameIdOrderBySessionIdDesc(userId, roundId, gameId);
    }

    protected void closeSession(Long userId, Long sessionId) {
        findSessionByUserIdAndSessionId(userId, sessionId).ifPresent(a -> {
            if (a.getStatus() == SessionState.ACTIVE) {
                a.setStatus(SessionState.INACTIVE);
            }
        });
        findGameInstanceByUserIdAndSessionId(userId, sessionId).ifPresent(a -> {
            if (a.getActive()) {
                a.setActive(false);
            }
        });

    }

    private Optional<GameInstance> findGameInstanceByUserIdAndSessionId(Long userId,
                                                                        Long sessionId) {
        return giRepo.findById(sessionId);
    }


    @Override
    public int getHandlerVersion() {
        return 1;
    }


    public Optional<Session> findSessionByUserIdAndSessionId(long userId, long sessionId) {
        return findSessionByUserIdAndSessionIdAndStatus(userId, sessionId, null);
    }

    public Optional<Session> findSessionByUserIdAndSessionIdAndStatus(long userId, long sessionId,
                                                                      SessionState status) {
        var optSession = sessRepo.findBySessionIdAndUserId(sessionId, userId);
        if (status == null || (optSession.isPresent() && optSession.get().getStatus() == status)) {
            return optSession;
        }
        return Optional.empty();
    }

    public void saveSession(Session sess, boolean flush) {
        if (flush) {
            sessRepo.saveAndFlush(sess);
        } else {
            sessRepo.save(sess);
        }
    }

    public Optional<Session> findSessionByToken(String token) {
        var byToken = sessRepo.findByToken(token);
        if (byToken.size() == 1) {
            return Optional.of(byToken.getFirst());
        }
        return Optional.empty();
    }

    /**
     * Returns the cacheEntry or Optional.empty()
     *
     * @param token the gameToken
     * @return the sessionCacheEntry (or emtpy optional)
     */
    public Optional<SessionCacheEntry> loadTokenBySessionToken(final String token) {
        return sessRepo.loadTkSession(token).map(SessionCacheEntry::new);
    }

    public Optional<SessionCacheEntry> loadTokenBySessionId(final long id) {
        return sessRepo.loadTkSessionById(id).map(SessionCacheEntry::new);
    }

    public GameInstance findGIByAuthToken(String authToken) {
        return giRepo.findByAuthToken(authToken);
    }

    public Optional<Session> findByAuthToken(String authToken) {
        var activeGameInstance = giRepo.findByAuthToken(authToken);
        if (activeGameInstance == null) {
            return Optional.empty();
        }
        return sessRepo.findBySessionIdAndUserId(activeGameInstance.getId(), activeGameInstance.getUserId());
    }

    public Optional<Session> findByUserIdAndAuthToken(long userId, String authToken) {
        var activeGameInstance = giRepo.findByUserIdAndAuthToken(userId, authToken);
        if (activeGameInstance == null) {
            return Optional.empty();
        }
        return sessRepo.findBySessionIdAndUserId(activeGameInstance.getId(), activeGameInstance.getUserId());
    }

    public Optional<Long> findLatestSessionIdByUserIdAndGameKeyAndRoundReference(long uid,
                                                                                 String gameKey,
                                                                                 String roundRef) {
        long gameIdByKey = findGameIdByKey(gameKey);
        if (gameIdByKey == 0L) {
            return Optional.empty();
        }
        var firstByUserIdAndRoundReference = srndRepo.findFirstByUserIdAndRoundReferenceAndGameIdOrderBySessionIdDesc(uid, roundRef, gameIdByKey);
        return firstByUserIdAndRoundReference.map(SessionRound::getSessionId);
    }

    public Optional<Long> findLatestSessionIdByUserIdAndGameKey(long uid, String gameKey) {
        long gameIdByKey = findGameIdByKey(gameKey);
        if (gameIdByKey == 0L) {
            return Optional.empty();
        }
        return sessRepo.findLatestByUserIdAndGameId(uid, gameIdByKey);
    }


    public Optional<Session> findByTokenAndUserId(String token, long userId) {
        // TODO: in session a token can be used multiple times, so lookup GameInstance
        var byToken = giRepo.findByToken(token);
        if (byToken == null || byToken.getUserId() != userId) {
            return Optional.empty();
        }
        return sessRepo.findBySessionIdAndUserId(byToken.getId(), byToken.getUserId());
    }


    public TxResponse addTxFromProvider(TxRequest req) throws OuslyTransactionException {
        return txService.addTxFromProvider(req);
    }

    public SessionTransaction findBySessionIdAndOrigId(long userId, long sessionId,
                                                       String externalOrigTxId) {
        return txService.findByUserIdAndSessionIdAndOrigId(userId, sessionId, externalOrigTxId);
    }

    public TxResponse performRollback(SessionTransaction tr, Wallet wallet) {
        return txService.performRollback(tr, wallet);
    }

    public List<SessionTransaction> findRoundTransactionsForUserIdAndSessionIdAndRoundRef(
            long userId, long sessionId, String roundId) {
        return txService.findRoundTransactionsForUserIdAndSessionId(userId, sessionId, roundId);
    }

    public SessionRound findRoundForSessionAndRoundRef(long userId, long sessionId,
                                                       String roundId) {
        return txService.findRoundForSessionIdAndRoundReference(userId, sessionId, roundId);
    }

    public void closeRound(long userId, long sessionId, String roundId) {
        srndRepo.closeSessionRoundIfOpen(userId, sessionId, roundId);
    }

    protected List<Session> findSessionByTokenAndRoundReference(String token, long userId,
                                                                String gameRoundRef) {
        return sessRepo.findByTokenAndUserIdAndRoundReference(token, userId, gameRoundRef);
    }

    protected List<Session> findSessionByTokenAndRoundReferenceAndProviderName(long userId,
                                                                               String gameRoundRef,
                                                                               String pName) {
        return sessRepo.findByUserIdAndRoundReferenceAndProviderName(userId, gameRoundRef, pName);
    }


    protected List<Session> findSessionByUserIdAndRoundReferenceAndProviderNameAndAgeBarrier(long userId,
                                                                                             String gameRoundRef,
                                                                                             String pName, int daysMaxAge) {
        return sessRepo.findByUserIdAndRoundReferenceAndProviderNameAgeInDays(userId, gameRoundRef, pName, daysMaxAge);
    }


    protected SessionTransaction findTransactionByUserIdAndOrigIdForPName(long uid,
                                                                          String transactionId,
                                                                          String vendorName) {
        return txService.findTransactionForUIDAndOrigIdAndBridgeName(uid, transactionId, vendorName);
    }

}

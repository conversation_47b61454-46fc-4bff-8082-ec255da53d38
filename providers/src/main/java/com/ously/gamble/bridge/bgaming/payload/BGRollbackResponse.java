package com.ously.gamble.bridge.bgaming.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class BGRollbackResponse extends BGPlayResponse {

    public static BGRollbackResponse error(int code, String message) {
        var resp = new BGRollbackResponse();
        resp.setCode(code);
        resp.setMessage(message);
        return resp;
    }


}

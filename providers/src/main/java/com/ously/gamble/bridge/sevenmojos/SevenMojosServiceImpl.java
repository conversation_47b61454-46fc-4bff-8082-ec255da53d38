package com.ously.gamble.bridge.sevenmojos;

import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.bridge.SessionCacheEntry;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.api.session.TxResponse;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.common.URIBuilder;
import com.ously.gamble.bridge.sevenmojos.payload.*;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import jakarta.persistence.OptimisticLockException;
import org.hibernate.StaleStateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.UUID;


@Service
@ConditionalOnBean(value = SevenMojosConfiguration.class)
public class SevenMojosServiceImpl extends BridgeBaseV2 implements SevenMojosService {

    private final Logger log = LoggerFactory.getLogger(SevenMojosServiceImpl.class);

    @Autowired
    SevenMojosConfiguration config;

    @Override
    @Transactional
    public SMAuthenticateResponse authenticate(SMAuthenticateRequest req) throws Exception {
        var token = getToken(req.getToken());
        if (token == null) {
            throw new Exception("token not found");
        }

        var resp = new SMAuthenticateResponse();
        resp.setCurrency(config.getCurrency());
        resp.setUserId(createUserId(token.getUserId()));
        resp.setUsername("");
        resp.setSessionId(token.getSessionToken());
        resp.setBalance(getWallet(token.getWalletId()).getBalance());
        return resp;
    }

    @Override
    @Transactional
    public SMGetBalanceResponse getBalance(SMGetBalanceRequest req) throws Exception {
        var token = getToken(req.getSessionId());
        if (token == null) {
            throw new Exception("token not found");
        }
        var resp = new SMGetBalanceResponse();
        resp.setBalance(getWallet(token.getWalletId()).getBalance());
        return resp;
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public SMTxResponse debit(SMTxRequest req) throws Exception {
        var token = getToken(req.getSessionId());
        if (token == null) {
            throw new Exception("token not found");
        }

        var txr = new TxRequest(token);
        txr.setRoundMode(RoundMode.AUTO);
        txr.setType(TransactionType.BET);
        txr.setExternalOrigId(req.getTransactionId());
        txr.setRoundRef(req.getRoundId());
        txr.setBet(new BigDecimal(req.getAmount()));
        txr.setWin(BigDecimal.ZERO);
        TxResponse txResponse;
        try {
            txResponse = addTxFromProvider(txr);
        } catch (OuslyTransactionException e) {
            if (e instanceof OuslyOutOfMoneyException) {
                log.debug("OutOfMoney");
            } else {
                log.warn("Error while trying to add credit Tx: {}", req, e);
            }
            throw e;
        }

        var resp = new SMTxResponse();
        resp.setBalance(txResponse.getNewBalance());
        resp.setTransactionId("" + txResponse.getTxId());
        return resp;
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public SMTxResponse credit(SMTxRequest req) throws Exception {
        var token = getToken(req.getSessionId());
        if (token == null) {
            token = loadTokenBySessionToken(req.getSessionId()).orElse(null);
            if (token == null) {
                // just send ok
                var resp = new SMTxResponse();
                resp.setBalance(BigDecimal.ZERO);
                resp.setTransactionId(UUID.randomUUID().toString());
                return resp;
            }
        }

        // WIN (with token)
        var txr = new TxRequest(token);
        txr.setRoundMode(RoundMode.CLOSE);
        txr.setType(TransactionType.WIN);
        txr.setExternalOrigId(req.getTransactionId());
        txr.setRoundRef(req.getRoundId());
        txr.setWin(new BigDecimal(req.getAmount()));
        txr.setBet(BigDecimal.ZERO);
        TxResponse txResponse;
        try {
            txResponse = addTxFromProvider(txr);
        } catch (OuslyTransactionException e) {
            log.debug("SM-Error while trying to credit Tx: {}", req, e);
            throw e;
        }

        var resp = new SMTxResponse();
        resp.setBalance(txResponse.getNewBalance());
        resp.setTransactionId("" + txResponse.getTxId());
        return resp;
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public SMTxResponse offlineCredit(SMTxRequest req) throws Exception {
        var uid = getUserIdFromString(req.getUserId());
        if (uid == -1L) {
            throw new Exception("wrong uid format:" + req.getUserId());
        }

        // find session for round
        var potSessions = findSessionByTokenAndRoundReferenceAndProviderName(uid, req.getRoundId(), config.getVendorName());
        if (potSessions.isEmpty()) {
            throw new Exception("no session found for offline credit");
        }
        var token = new SessionCacheEntry(potSessions.getFirst(), null, null);

        // WIN
        var txr = new TxRequest();
        txr.setRoundMode(RoundMode.NONE);
        txr.setType(TransactionType.WIN);
        txr.setExternalOrigId(req.getTransactionId());
        txr.setRoundRef(req.getRoundId());
        txr.setWin(new BigDecimal(req.getAmount()));
        txr.setBet(BigDecimal.ZERO);
        txr.setSessionId(token.getSessionId());
        txr.setUserId(uid);
        txr.setGameId(token.getGameId());
        txr.setGp(token.getGp());
        txr.setJurisdiction(token.getJurisdiction());
        TxResponse txResponse;
        try {
            txResponse = addTxFromProvider(txr);
        } catch (OuslyTransactionException e) {
            log.debug("SM-Error while trying to offline-credit Tx: {}", req, e);
            throw e;
        }

        var resp = new SMTxResponse();
        resp.setBalance(txResponse.getNewBalance());
        resp.setTransactionId(Long.toString(txResponse.getTxId()));
        return resp;
    }

    @Override
    public SMStatusResponse status(SMStatusRequest req) {
        var resp = new SMStatusResponse();
        resp.setTransactionId("OSLY-" + req.getTransactionId());
        return resp;
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public SMCancelResponse cancel(SMCancelRequest req) throws Exception {
        var uid = getUserIdFromString(req.getUserId());
        if (uid == -1L) {
            throw new Exception("wrong uid format:" + req.getUserId());
        }

        var byUserIdAndVendorAndOrigId = findTransactionByUserIdAndOrigIdForPName(uid, req.getTransactionId(), config.getVendorName());
        if (byUserIdAndVendorAndOrigId != null) {
            var w = getWallet(uid);
            var txResponse = performRollback(byUserIdAndVendorAndOrigId, w);
            var resp = new SMCancelResponse();
            resp.setBalance(txResponse.getNewBalance());
            return resp;
        }
        var resp = new SMCancelResponse();
        resp.setBalance(getWallet(uid).getBalance());
        return resp;
    }

    @Override
    public void resolve(String body) {
        log.warn("SM-Resolve: {} ", body);
    }

    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(config.getVendorName());
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform,
                                                 GameSettings settings) throws Exception {


        log.info("CreateGame (7MOJOS) for user {}, game {} platform={}", user.getDisplayName(), game.getGameId(), platform);
        var token = UUID.randomUUID().toString();
        var gameUrl = "";
        if (platform != GamePlatform.TEST) {
            var bld = new URIBuilder(config.getUrl());
            bld.addParameter("operatorToken", config.getOperatorCode());
            bld.addParameter("playerToken", token);
            bld.addParameter("gameToken", game.getGameId());
            bld.addParameter("host", config.getEngineUrl());
            bld.addParameter("gameType", "slots");
            bld.addParameter("lang", getLangFromSettings(user, settings));
            gameUrl = bld.build().toString();
        }
        var gameInstance = new GameInstance();
        gameInstance.setGameUrl(gameUrl);
        gameInstance.setGameHtml("");
        gameInstance.setToken(token);
        gameInstance.setAuthToken(token);
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }

    private static String getLangFromSettings(CasinoUser user, GameSettings settings) {
        if (settings != null && settings.getLanguage() != null) {
            return settings.getLanguage().toLowerCase();
        }
        return "en";
    }

    private static String createUserId(Long id) {
        return "SM-" + id;
    }

    private static Long getUserIdFromString(String uid) {
        try {
            return Long.valueOf(uid.substring(3));
        } catch (Exception e) {
            return -1L;
        }
    }
}

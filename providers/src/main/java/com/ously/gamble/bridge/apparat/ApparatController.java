package com.ously.gamble.bridge.apparat;

import com.ously.gamble.bridge.apparat.payload.*;
import jakarta.annotation.PostConstruct;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.crypto.Mac;
import java.nio.charset.StandardCharsets;

@RestController
@RequestMapping(ApparatConfiguration.BRIDGE_APPARAT)
@ConditionalOnBean(ApparatConfiguration.class)
public class ApparatController {

    final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    ApparatService srv;

    @Autowired
    ApparatConfiguration config;

    Mac hmacSHA256;

    @PostConstruct
    public void setup() {
        hmacSHA256 = HmacUtils.getInitializedMac(HmacAlgorithms.HMAC_SHA_256, config.getApiPassword().getBytes(StandardCharsets.UTF_8));
    }


    @PostMapping(value = "/init", produces = "application/json")
    @ResponseBody
    public ResponseEntity<APResponse> init(@RequestBody APInitRequest req) {
        log.debug("APPARAT-INIT-REQ:{}", req);
        APResponse resp;
        if (checkAuth(req.getAuth(), req.getToken())) {
            resp = new APError("bad_request", "auth does not resolve");
        } else {
            resp = srv.init(req);
        }
        log.debug("APPARAT-INIT-RESP:{}", resp);
        return ResponseEntity.ok(resp);
    }


    @PostMapping(value = "/balance", produces = "application/json")
    @ResponseBody
    public ResponseEntity<APResponse> balance(@RequestBody APBalanceRequest req) {
        log.debug("APPARAT-BAL-REQ:{}", req);
        APResponse resp;
        if (checkAuth(req.getAuth(), req.getToken())) {
            resp = new APError("bad_request", "auth does not resolve");
        } else {
            resp = srv.balance(req);
        }
        log.debug("APPARAT-BAL-RESP:{}", resp);
        return ResponseEntity.ok(resp);
    }

    @PostMapping(value = "/bet", produces = "application/json")
    @ResponseBody
    public ResponseEntity<APResponse> bet(@RequestBody APTransactionRequest req) {
        log.debug("APPARAT-BET-REQ:{}", req);
        APResponse resp;
        if (checkAuth(req.getAuth(), req.getToken())) {
            resp = new APError("bad_request", "auth does not resolve");
        } else {
            resp = srv.bet(req);
        }
        log.debug("APPARAT-BET-RESP:{}", resp);
        return ResponseEntity.ok(resp);
    }

    @PostMapping(value = "/win", produces = "application/json")
    @ResponseBody
    public ResponseEntity<APResponse> win(@RequestBody APTransactionRequest req) {
        log.debug("APPARAT-WIN-REQ:{}", req);
        APResponse resp;
        if (checkAuth(req.getAuth(), req.getToken())) {
            resp = new APError("bad_request", "auth does not resolve");
        } else {
            resp = srv.win(req);
        }
        log.debug("APPARAT-WIN-RESP:{}", resp);
        return ResponseEntity.ok(resp);
    }

    @PostMapping(value = "/betAndWin", produces = "application/json")
    @ResponseBody
    public ResponseEntity<APResponse> betAndWin(@RequestBody APTransactionRequest req) {
        log.debug("APPARAT-BETWIN-REQ:{}", req);
        APResponse resp;
        if (checkAuth(req.getAuth(), req.getToken())) {
            resp = new APError("bad_request", "auth does not resolve");
        } else {
            resp = srv.betAndWin(req);
        }
        log.debug("APPARAT-BETWIN-RESP:{}", resp);
        return ResponseEntity.ok(resp);
    }

    @PostMapping(value = "/rollback", produces = "application/json")
    @ResponseBody
    public ResponseEntity<APResponse> rollback(@RequestBody APTransactionRequest req) {
        log.debug("APPARAT-ROLLBACK-REQ:{}", req);
        APResponse resp;
        if (checkAuth(req.getAuth(), req.getToken())) {
            resp = new APError("bad_request", "auth does not resolve");
        } else {
            resp = srv.rollback(req);
        }
        log.debug("APPARAT-ROLLBACK-RESP:{}", resp);
        return ResponseEntity.ok(resp);
    }


    private synchronized boolean checkAuth(String auth, String token) {
        var bytes = hmacSHA256.doFinal(token.getBytes(StandardCharsets.UTF_8));
        var myAuth = Base64.encodeBase64String(bytes);
        var equals = myAuth.equals(auth);
        if (!equals) {
            log.warn("AuthMismatch: auth='{}', token='{}', calcAuth='{}'", auth, token, myAuth);
        }

        return !equals;
    }
}
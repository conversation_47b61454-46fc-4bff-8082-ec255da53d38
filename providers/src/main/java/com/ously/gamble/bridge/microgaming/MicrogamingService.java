package com.ously.gamble.bridge.microgaming;

import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.microgaming.payload.*;
import org.javatuples.Pair;

import java.util.List;


public interface MicrogamingService extends BridgeHandler {


    /**
     * Verify that the authToken is available and return the session token.
     * (TODO: think about invalidating the authToken so that call is not idempotent anymore - security reasons)
     */
    Pair<LoginResponse, ErrorResponse> login(LoginRequest req);

    /**
     * R/O request to get current wallet amount.
     */
    Pair<GetBalanceResponse, ErrorResponse> getBalance(GetBalanceRequest req);

    /**
     * bet and win calls!!
     */
    Pair<PlayResponse, ErrorResponse> play(PlayRequest req);

    /**
     * Mark a multi-round game where multiple wins could accumulate. On our system we can safely ignore since we can store multiple wins.
     * For systems where bet & win are combined this might be needed.
     *
     * @param req the request
     * @return a response or error response
     */
    Pair<EndgameResponse, ErrorResponse> endgame(EndgameRequest req);

    Pair<RefreshTokenResponse, ErrorResponse> refreshToken(RefreshTokenRequest req);

    List<MGUnlockItem> performRollbacks(List<MGTransaction> data);

    List<MGUnlockItem> performCommits(List<MGTransaction> data);

    List<MGUnlockItem> performEndgameConfirms(List<MGTransaction> data);
}

package com.ously.gamble.bridge.playngo;

import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.bridge.SessionCacheEntry;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.api.session.TxResponse;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.common.URIBuilder;
import com.ously.gamble.bridge.playngo.payload.*;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.AccountStatus;
import com.ously.gamble.persistence.model.SessionState;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import jakarta.persistence.OptimisticLockException;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.StaleStateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@ConditionalOnBean(PlaynGoConfiguration.class)
public class PlaynGoServiceImpl extends BridgeBaseV2 implements PlaynGoService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    PlaynGoConfiguration config;

    private String createURL(GamePlatform gp, String playerId, String authTk, String token,
                             String gameId, GameSettings settings) throws URISyntaxException {
        var channel = getChannelForPlatform(gp);
        log.info("Creating playngo session for gp:{}, gameId:{}, channel:{}", gp, gameId, channel);

        var bld = new URIBuilder(config.getBaseUrl() + "/casino/ContainerLauncher");
        bld.addParameter("pid", config.getProductGroup().toString());
        bld.addParameter("gid", gameId + (("mobile".equals(channel)) ? "mobile" : ""));
        bld.addParameter("lang", getLanguageCode(settings));
        bld.addParameter("channel", channel);
        bld.addParameter("ticket", authTk);
        bld.addParameter("practice", "0");
        bld.addParameter("origin", getOriginForPlatform(gp));
        // Switch betsize contexts
        if (isCustomBetsizeEnabled()) {
            switch (settings.getBetLevel()) {
                case 1 -> bld.addParameter("ctx", "ously_vip");
                case 2 -> bld.addParameter("ctx", "ously_high");
                default -> bld.addParameter("ctx", "ously_default");
            }
        }

        return bld.build().toString();
    }

    private String getOriginForPlatform(GamePlatform gp) {
        return switch (gp) {
            case IOS, MOBILE, ANDROID -> getBaseUrl();
            default -> getAppBaseUrl();
        };
    }

    private static String getChannelForPlatform(GamePlatform gp) {
        return switch (gp) {
            case IOS, MOBILE, ANDROID -> "mobile";
            default -> "desktop";
        };
    }

    private static String getLanguageCode(GameSettings settings) {
        var stLang = settings.getLanguage();
        if (stLang == null) {
            return "en_US";
        }
        return switch (stLang.toUpperCase(Locale.ROOT)) {
            case "DE" -> "de_DE";
            default -> "en_US";
        };
    }


    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(config.getVendorName());
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform,
                                                 GameSettings settings) throws Exception {
        var plId = createExternalUserId(user.getId(), settings.getBetLevel());
        var ssTk = UUID.randomUUID().toString();
        var auTk = UUID.randomUUID().toString();

        if (isAggregated()) {
            ssTk = getAggregationKey() + "#" + ssTk;
            auTk = getAggregationKey() + "#" + auTk;
        }


        var gameInstance = new GameInstance();

        if (platform == GamePlatform.TEST) {
            gameInstance.setGameUrl(createURL(platform, createExternalUserId(user.getId(), settings.getBetLevel()), auTk, ssTk, game.getGameId(), settings));
            gameInstance.setToken(ssTk);
            gameInstance.setAuthToken(auTk);
            gameInstance.setCreationTime(LocalDateTime.now());
            gameInstance.setExpiryTime(getExpiryTime());
        } else {
            //ggUrlRequest.setProfile("jurisidiction-mga");
            try {
                var url = createURL(platform, plId, auTk, ssTk, game.getGameId(), settings);
                gameInstance.setGameUrl(url);
                gameInstance.setToken(ssTk);
                gameInstance.setAuthToken(auTk);
                gameInstance.setCreationTime(LocalDateTime.now());
                gameInstance.setExpiryTime(getExpiryTime());
            } catch (Exception e) {
                log.error("PNG-createURL for '{}' failed:", e.getMessage(), e);
                return null;
            }
        }
        return gameInstance;
    }

    @Override
    @Transactional
    public PNGAuthenticateResp authorize(PNGAuthenticateReq req) {
        var sessionOpt = findByAuthToken(req.getAuthToken());

        if (sessionOpt.isEmpty()) {
            return new PNGAuthenticateResp("4", "wrong username/password");
        }

        if (sessionOpt.get().getStatus() != SessionState.ACTIVE) {
            return new PNGAuthenticateResp("10", "Session expired");
        }
        var session = sessionOpt.get();
        var user = getCasinoUser(session.getUserId());
        if (user.isBlocked()) {
            if (AccountStatus.BLOCKED.name().equals(user.getStatus())) {
                return new PNGAuthenticateResp("5", "User temp. locked");
            }
            if (AccountStatus.BANNED.name().equals(user.getStatus())) {
                return new PNGAuthenticateResp("6", "User is disabled");
            }
        }
        var w = getWallet(session.getUserId());
        var userInfo = getCasinoUser(user.getId());
        var defLang = "DE";
        if (userInfo != null) {
            var uL = userInfo.getLangCode();
            if (StringUtils.isNotBlank(uL)) {
                defLang = uL.toUpperCase(Locale.ROOT);
            }
        }
        // now create response
        var resp = new PNGAuthenticateResp();
        resp.setStatusCode("0");
        resp.setGameMode("1");
        resp.setExternalId(createExternalUserId(session.getUserId(), session.getBetLevel()));
        resp.setCountry(Objects.requireNonNullElse(session.getCountry(), "DE"));
        resp.setLanguage(defLang);
        var regDate = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(user.getCreatedAt().atZone(ZoneOffset.UTC).toLocalDate());
        resp.setRegistration(regDate);
        // DUMMY FOR SOCIAL, its a mandatory field
        resp.setBirthdate("2000-01-01");
        resp.setNickname(user.getDisplayName());
        resp.setExternalGameSessionId(session.getToken());
        resp.setUserCurrency(config.getCurrency());
        resp.setBalance(formatBalance(w.getBalance()));
        return resp;
    }


    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class,
            ObjectOptimisticLockingFailureException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public PNGBalanceResp balance(PNGBalanceReq req) {
        var token = getToken(req.getExternalGameSessionId());
        long uid = -1L;
        if (token == null) {
            try {
                uid = getUserId(req.getExternalId());
            } catch (Exception e) {
            }

            if (uid < 0) {
                return new PNGBalanceResp("10", "Session expired");
            }
        } else {
            uid = token.getUserId();
        }
        var resp = new PNGBalanceResp();
        resp.setStatusCode("0");
        var w = getWallet(uid);
        resp.setBalance(formatBalance(w.getBalance()));
        resp.setGameMode("1");
        resp.setCurrency(config.getCurrency());
        return resp;
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class,
            ObjectOptimisticLockingFailureException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public PNGReserveResp reserve(PNGReserveReq req) {
        var token = getToken(req.getExternalGameSessionId());
        if (token == null) {
            try {
                var user = getCasinoUser(getUserId(req.getExternalId()));

                if (user.isBlocked()) {
                    if (AccountStatus.BLOCKED.name().equals(user.getStatus())) {
                        return new PNGReserveResp("5", "User temp. locked");
                    }
                    if (AccountStatus.BANNED.name().equals(user.getStatus())) {
                        return new PNGReserveResp("6", "User is disabled");
                    }
                }

            } catch (Exception ignored) {
            }
            return new PNGReserveResp("10", "Session expired");
        }


        var user = getCasinoUser(token.getUserId());

        if (user.isBlocked()) {
            if (AccountStatus.BLOCKED.name().equals(user.getStatus())) {
                return new PNGReserveResp("5", "User temp. locked");
            }
            if (AccountStatus.BANNED.name().equals(user.getStatus())) {
                return new PNGReserveResp("6", "User is disabled");
            }
        }


        var resp = new PNGReserveResp();

        // common fields:
        var roundRef = req.getRoundId();

        // now create Bet transaction
        var txr = new TxRequest(token);
        txr.setType(TransactionType.BET);
        txr.setExternalOrigId(req.getTransactionId());
        txr.setRoundRef(roundRef);
        txr.setBet(parseAmount(req.getAmount()));
        txr.setWin(BigDecimal.ZERO);
        txr.setRoundMode(RoundMode.AUTO);
        TxResponse txResponse;
        try {
            txResponse = addTxFromProvider(txr);
        } catch (OuslyTransactionException e) {
            if (e instanceof OuslyOutOfMoneyException) {
                return new PNGReserveResp("7", "not enough funds");
            }
            log.error("PNG->Error on Bet:", e);
            return new PNGReserveResp("2", e.getMessage());
        }
        //
        resp.setExternalTransactionId("" + txResponse.getTxId());
        resp.setBalance(formatBalance(txResponse.getNewBalance()));
        resp.setGameMode("1");
        resp.setCurrency(config.getCurrency());
        return resp;
    }

    private static BigDecimal parseAmount(String amount) {
        if (StringUtils.isEmpty(amount)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(amount);
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class,
            ObjectOptimisticLockingFailureException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public PNGReleaseResp release(PNGReleaseReq req) {
        var token = getToken(req.getExternalGameSessionId());
        if (token == null) {
            long userId = getUserId(req.getExternalId());
            var byToken = findByTokenAllStates(userId, req.getExternalGameSessionId());
            if (byToken.isEmpty()) {
                return new PNGReleaseResp("10", "Session expired");
            }
            var session = byToken.get();
            token = new SessionCacheEntry();
            token.setUserId(session.getUserId());
            token.setCountry(session.getCountry());
            token.setGp(session.getPlatform());
            token.setGameId(session.getGameId());
            token.setHandlerVersion(session.getHandlerVersion());
            token.setJurisdiction(session.getJurisdiction());
            token.setWalletId(session.getUserId());
            token.setSessionId(session.getSessionId());
        }
        var resp = new PNGReleaseResp();

        var roundRef = req.getRoundId();

        // now create Win transaction or just close session
        var amount = parseAmount(req.getAmount());
        if ("1".equals(req.getState()) && amount.signum() == 0) {
            // Close session -> mark inactive
            closeSession(token.getUserId(), token.getSessionId());

            var w = getWallet(token.getUserId());
            resp.setBalance(formatBalance(w.getBalance()));
            resp.setGameMode("1");
            resp.setCurrency(config.getCurrency());
            return resp;
        }

        var txr = new TxRequest(token);
        txr.setType(TransactionType.WIN);
        txr.setExternalOrigId(req.getTransactionId());
        txr.setRoundRef(roundRef);
        txr.setWin(parseAmount(req.getAmount()));
        txr.setBet(BigDecimal.ZERO);
        txr.setRoundMode(RoundMode.AUTO);
        TxResponse txResponse;
        try {
            txResponse = addTxFromProvider(txr);
        } catch (OuslyOutOfMoneyException e) {
            return new PNGReleaseResp("7", "not enough funds");
        } catch (OuslyTransactionException e) {
            log.debug("PNG->Error while trying to add credit Tx: {}", req, e);
            log.error("PNG->Error on Win:", e);
            return new PNGReleaseResp("2", e.getMessage());
        }
        resp.setExternalTransactionId("" + txResponse.getTxId());
        resp.setBalance(formatBalance(txResponse.getNewBalance()));
        resp.setGameMode("1");
        resp.setCurrency(config.getCurrency());
        return resp;
    }

    private static long getUserId(String req) {
        var rawStr = req;
        if (req.contains("#")) {
            rawStr = rawStr.split("#")[1];
        }

        rawStr = rawStr.substring(4);
        if (rawStr.contains("-")) {
            rawStr = rawStr.substring(0, rawStr.indexOf('-'));
        }
        return Long.parseLong(rawStr);
    }

    @Override
    public boolean isAggregated() {
        return config.isAggregated();
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class,
            ObjectOptimisticLockingFailureException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public PNGCancelReserveResp cancelReserve(PNGCancelReserveReq req) {
        var token = getToken(req.getExternalGameSessionId());
        if (token == null) {
            var userId = getUserId(req.getExternalId());
            var byToken = findByTokenAllStates(userId, req.getExternalGameSessionId());
            if (byToken.isEmpty()) {
                return new PNGCancelReserveResp("10", "Session expired");
            }
            var session = byToken.get();
            token = new SessionCacheEntry();
            token.setUserId(session.getUserId());
            token.setCountry(session.getCountry());
            token.setGp(session.getPlatform());
            token.setGameId(session.getGameId());
            token.setHandlerVersion(session.getHandlerVersion());
            token.setJurisdiction(session.getJurisdiction());
            token.setWalletId(session.getUserId());
            token.setSessionId(session.getSessionId());
        }
        var resp = new PNGCancelReserveResp();

        // find old transaction
        var oldTx = findBySessionIdAndOrigId(token.getUserId(), token.getSessionId(), req.getTransactionId());
        if (oldTx == null) {
            resp.setTransactionId(req.getTransactionId());
            return resp;
        }

        if (oldTx.isCancelled()) {
            resp.setExternalTransactionId("" + oldTx.getTxId());
            resp.setTransactionId(req.getTransactionId());
            return resp;
        }

        // Cancel
        var w = getWallet(token.getUserId());
        var txResponse = performRollback(oldTx, w);
        resp.setExternalTransactionId("" + txResponse.getTxId());
        resp.setTransactionId(req.getTransactionId());
        return resp;
    }


    private static String formatBalance(BigDecimal balance) {
        return String.format(Locale.US, "%.2f", balance);
    }

    private String createExternalUserId(long userId, int betlevel) {
        if (isAggregated()) {
            return getAggregationKey() + "#PNG-" + userId + "-" + betlevel;
        }
        return "PNG-" + userId + "-" + betlevel;
    }


}

package com.ously.gamble.bridge.pariplay;

import com.ously.gamble.api.bridge.BridgeConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "pariplay")
@ConditionalOnProperty(
        value = "pariplay.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class PariplayConfiguration implements BridgeConfiguration {

    public static final String VENDOR_NAME = "PARIPLAY";
    public static final String BRIDGE_PARIPLAY = "/bridge/pariplay";

    String lang = "en";

    String username = "spinarena";
    String password = "spinarena123!";

    String launcherurl = "https://hubgames.stage.pariplaygames.com/api/";
    String activeCurrency = "EUR";

    String opnUsername = "spinarena";
    String opnPassword = "spinarena123!";

    String testTokenOwnerEmail = "<EMAIL>";

    @Override
    public String getVendorName() {
        return VENDOR_NAME;
    }

    public String getLang() {
        return lang;
    }


    public String getLauncherurl() {
        return launcherurl;
    }

    public String getActiveCurrency() {
        return activeCurrency;
    }


    public String getTestTokenOwnerEmail() {
        return testTokenOwnerEmail;
    }

    public void setTestTokenOwnerEmail(String testTokenOwnerEmail) {
        this.testTokenOwnerEmail = testTokenOwnerEmail;
    }

    @Override
    public String getBridgeName() {
        return BRIDGE_PARIPLAY;
    }

    @Override public String getAuth_user() {
        return null;
    }

    @Override public String getAuth_password() {
        return null;
    }

    @Override
    public List<String> getWhitelistIPs() {
        return Collections.emptyList();
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public void setLauncherurl(String launcherurl) {
        this.launcherurl = launcherurl;
    }

    public void setActiveCurrency(String activeCurrency) {
        this.activeCurrency = activeCurrency;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getOpnUsername() {
        return opnUsername;
    }

    public void setOpnUsername(String opnUsername) {
        this.opnUsername = opnUsername;
    }

    public String getOpnPassword() {
        return opnPassword;
    }

    public void setOpnPassword(String opnPassword) {
        this.opnPassword = opnPassword;
    }
}

package com.ously.gamble.bridge.pariplay.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class PPResponse {

    @JsonProperty("Error")
    PPError error;

    @JsonProperty("Balance")
    BigDecimal balance;

    @JsonProperty("TransactionId")
    Long transactionId;

    public PPResponse() {
    }

    public PPResponse(BigDecimal balance) {
        this.balance = balance;
    }

    public PPResponse(BigDecimal bal, Long txId) {
        this.balance = bal;
        this.transactionId = txId;
    }

    public PPResponse(int error) {
        this.error = new PPError(error);
    }

    public PPResponse(int error, BigDecimal balance) {
        this.error = new PPError(error, balance);
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public Long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }

    public PPError getError() {
        return error;
    }

    public void setError(PPError error) {
        this.error = error;
    }

    @Override
    public String toString() {
        return "PPResponse{" +
                "error=" + error +
                ", balance=" + balance +
                ", transactionId=" + transactionId +
                '}';
    }
}




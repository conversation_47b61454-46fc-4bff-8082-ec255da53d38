package com.ously.gamble.bridge.spinomenal.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ously.gamble.bridge.spinomenal.SpinomenalUtils;

import java.time.LocalDateTime;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class AuthenticationRequest {
    // the unique gameToken
    @JsonProperty("GameToken")
    private String gameToken;

    // md5([TIMESTAMP][TOKEN][REQUEST_ID][PRIVATE_KEY]).
    @JsonProperty("Sig")
    private String sig;

    // [4 digit year][2 digit month][2 digit day][2 digit hour][2 digit minute][2
    //    digit seconds]
    @JsonProperty("TimeStamp")
    private String reqTime;

    @JsonProperty("GameCode")
    private String gameCode;

    public String getGameToken() {
        return gameToken;
    }

    public void setGameToken(String gameToken) {
        this.gameToken = gameToken;
    }

    public String getSig() {
        return sig;
    }

    public void setSig(String sig) {
        this.sig = sig;
    }

    public String getReqTime() {
        return reqTime;
    }

    public void setReqTime(String reqTime) {
        this.reqTime = reqTime;
    }

    public String getGameCode() {
        return gameCode;
    }

    public void setGameCode(String gameCode) {
        this.gameCode = gameCode;
    }

    @JsonIgnore
    public void setActualTime() {
        reqTime = SpinomenalUtils.convertLocalDateTimeToTimestamp(LocalDateTime.now());
    }

    @JsonIgnore
    public String getSigPart() {
        // md5([TIMESTAMP][EXTERNAL_ID][PRIVATE_KEY]).
        return reqTime + gameToken;
    }

    @Override
    public String toString() {
        return "AuthenticationRequest{" +
                "gameToken='" + gameToken + '\'' +
                ", sig='" + sig + '\'' +
                ", reqTime='" + reqTime + '\'' +
                ", gameCode='" + gameCode + '\'' +
                '}';
    }

}

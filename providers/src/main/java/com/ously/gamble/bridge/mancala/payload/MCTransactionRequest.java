package com.ously.gamble.bridge.mancala.payload;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public abstract class MCTransactionRequest extends MCBaseRequest {

    @JsonProperty("Amount")
    BigDecimal amount;
    @JsonProperty("TransactionGuid")
    String transactionGuid;
    @JsonProperty("RoundGuid")
    String roundGuid;

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getTransactionGuid() {
        return transactionGuid;
    }

    public void setTransactionGuid(String transactionGuid) {
        this.transactionGuid = transactionGuid;
    }

    public String getRoundGuid() {
        return roundGuid;
    }

    public void setRoundGuid(String roundGuid) {
        this.roundGuid = roundGuid;
    }

    @Override
    public String toString() {
        return "MCTransactionRequest{" +
                "amount=" + amount +
                ", transactionGuid=" + transactionGuid +
                ", roundGuid=" + roundGuid +
                "} " + super.toString();
    }
}

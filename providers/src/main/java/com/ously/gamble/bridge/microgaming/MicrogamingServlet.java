package com.ously.gamble.bridge.microgaming;

import com.ously.gamble.bridge.common.BridgeServlet;
import com.ously.gamble.bridge.microgaming.payload.*;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import java.io.IOException;

@ConditionalOnProperty(
        value = "microgaming.enabled",
        havingValue = "true",
        matchIfMissing = true)
@WebServlet(urlPatterns = {MicrogamingConfiguration.BRIDGE_MICROGAMING + "/*"},
        name = "MicrogamingServlet",
        description = "Entrypoint for all Microgaming integration calls", loadOnStartup = 1)
public class MicrogamingServlet extends BridgeServlet {

    private final Logger log = LoggerFactory.getLogger(MicrogamingServlet.class);

    @Autowired
    private MicrogamingService mgService;

    @Override
    protected void doPost(
            HttpServletRequest request,
            HttpServletResponse response) throws IOException {

        // get request
        var xmlRequest = IOUtils.toByteArray(request.getInputStream());

        if (log.isDebugEnabled() && xmlRequest != null) {
            log.debug("MG Bridge - got request '{}...", new String(xmlRequest));
        }

        try {
            var req = MicrogamingUtils.parseRequest(xmlRequest);
            var nm = req.getClass().getSimpleName();
            ErrorResponse error = null;
            switch (nm) {
                case "LoginRequest" -> {
                    var r = login((LoginRequest) req);
                    if (r.getValue0() != null) {
                        response.setStatus(200);
                        response.setContentType("text/xml");
                        response.getOutputStream().print(MicrogamingUtils.marshallLoginResponse(r.getValue0()));
                    } else {
                        error = r.getValue1();
                    }
                }
                case "GetBalanceRequest" -> {
                    var rb = getBalance((GetBalanceRequest) req);
                    if (rb.getValue0() != null) {
                        response.setStatus(200);
                        response.setContentType("text/xml");
                        response.getOutputStream().print(MicrogamingUtils.marshallGetBalanceResponse(rb.getValue0()));
                    } else {
                        error = rb.getValue1();
                    }
                }
                case "PlayRequest" -> {
                    var rp = play((PlayRequest) req);
                    if (rp.getValue0() != null) {
                        response.setStatus(200);
                        response.setContentType("text/xml");
                        response.getOutputStream().print(MicrogamingUtils.marshallPlayResponse(rp.getValue0()));
                    } else {
                        error = rp.getValue1();
                    }
                }
                case "RefreshTokenRequest" -> {
                    var rt = refreshToken((RefreshTokenRequest) req);
                    if (rt.getValue0() != null) {
                        response.setStatus(200);
                        response.setContentType("text/xml");
                        response.getOutputStream().print(MicrogamingUtils.marshallRefreshTokenResponse(rt.getValue0()));
                    } else {
                        error = rt.getValue1();
                    }
                }
                case "EndgameRequest" -> {
                    var eg = endgame((EndgameRequest) req);
                    if (eg.getValue0() != null) {
                        response.setStatus(200);
                        response.setContentType("text/xml");
                        response.getOutputStream().print(MicrogamingUtils.marshallEndgameResponse(eg.getValue0()));
                    } else {
                        error = eg.getValue1();
                    }
                }
                default -> {
                    log.warn("unknown request sent from microgaming");
                    response.setStatus(500);
                    response.getWriter().println("unknown request");
                }
            }

            if (error != null) {

                response.setStatus(200);
                response.setContentType("text/xml");
                response.getOutputStream().print(MicrogamingUtils.marshallErrorResponse(error));


            }


        } catch (Exception e) {
            log.error("Error handling microgaming request", e);
            response.setStatus(500);
            response.getWriter().println("Error handling request.");
        }

    }


    Pair<LoginResponse, ErrorResponse> login(LoginRequest req) {
        return mgService.login(req);
    }

    Pair<GetBalanceResponse, ErrorResponse> getBalance(GetBalanceRequest req) {
        return mgService.getBalance(req);
    }

    Pair<PlayResponse, ErrorResponse> play(PlayRequest req) {
        return mgService.play(req);
    }

    Pair<EndgameResponse, ErrorResponse> endgame(EndgameRequest req) {
        return mgService.endgame(req);
    }

    Pair<RefreshTokenResponse, ErrorResponse> refreshToken(RefreshTokenRequest req) {
        return mgService.refreshToken(req);
    }

}

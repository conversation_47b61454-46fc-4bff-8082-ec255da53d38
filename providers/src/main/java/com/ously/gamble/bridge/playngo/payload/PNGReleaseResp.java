package com.ously.gamble.bridge.playngo.payload;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serial;

@XmlRootElement(name = "release")
@XmlAccessorType(XmlAccessType.FIELD)
public class PNGReleaseResp extends PNGResponse {
    @Serial
    private static final long serialVersionUID = 1L;


    public PNGReleaseResp() {
        super("0", null);
    }

    public PNGReleaseResp(String code, String msg) {
        super(code, msg);
    }

    String externalTransactionId;

    @XmlElement(name = "real")
    String balance;

    String currency;

    String gameMode;

    public String getExternalTransactionId() {
        return externalTransactionId;
    }

    public void setExternalTransactionId(String externalTransactionId) {
        this.externalTransactionId = externalTransactionId;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getGameMode() {
        return gameMode;
    }

    public void setGameMode(String gameMode) {
        this.gameMode = gameMode;
    }


    @Override
    public String toString() {
        return "PNGReleaseResp{" +
                "externalTransactionId='" + externalTransactionId + '\'' +
                ", balance='" + balance + '\'' +
                ", currency='" + currency + '\'' +
                ", gameMode='" + gameMode + '\'' +
                ", statusCode='" + statusCode + '\'' +
                ", statusMessage='" + statusMessage + '\'' +
                "} ";
    }
}

package com.ously.gamble.bridge.booming;

import com.ously.gamble.api.bridge.BridgeConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "booming")
@ConditionalOnProperty(
        value = "booming.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class BoomingGamesConfiguration implements BridgeConfiguration {

    String apiKey = "l/GKQR0o7FNWCTvkAwoHDQ==";
    String apiSecret = "aZfV2A5EPlfMQB8/xvcPdJ9/tGn0F1OEYW9Kce6lVkqNia04B2kZOlYCayHlpxud";
    String apiUrl = "https://api.intgr.booming-games.com";
    String currency = "ARC";

    public static final String VENDOR_NAME = "BOOMING";
    public static final String BRIDGE_BOOMING = "/bridge/booming";

    @Override
    public String getVendorName() {
        return VENDOR_NAME;
    }

    @Override
    public String getBridgeName() {
        return BRIDGE_BOOMING;
    }

    @Override
    public String getAuth_user() {
        return null;
    }

    @Override
    public String getAuth_password() {
        return null;
    }

    @Override
    public List<String> getWhitelistIPs() {
        return Collections.emptyList();
    }

    public String getApiKey() {
        return apiKey;
    }

    public String getApiSecret() {
        return apiSecret;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public void setApiSecret(String apiSecret) {
        this.apiSecret = apiSecret;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrency() {
        return currency;
    }


}

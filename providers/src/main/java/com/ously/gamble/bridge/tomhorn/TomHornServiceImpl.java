package com.ously.gamble.bridge.tomhorn;

import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.bridge.SessionCacheEntry;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.api.session.TxResponse;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.tomhorn.payload.*;
import com.ously.gamble.bridge.tomhorn.payload.GetModuleInfoResponse.Parameter;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import freemarker.template.Configuration;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.OptimisticLockException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.hibernate.StaleStateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@ConditionalOnBean(TomHornConfiguration.class)
public class TomHornServiceImpl extends BridgeBaseV2 implements TomHornService {

    final Logger log = LoggerFactory.getLogger(this.getClass());

    final Map<String, String> languages =
            Map.of("DE", "de-DE", "CZ", "cs-CZ", "EN", "en-GB", "ES", "es-ES", "HU",
                    "hu-HU", "IT", "it-IT", "LT", "lt-LT", "PL", "pl-PL", "PT", "pt-PT", "RU",
                    "ru-RU");

//    "SK", "sk-SK","CN", "zh-CN");


    @Autowired
    TomHornConfiguration config;

    @Autowired
    private Configuration freemarkerConfig;

    @Autowired
    RestTemplate thTemplate;

    Mac sha256Mac;

    @PostConstruct
    void setupTemplate() {
        sha256Mac = HmacUtils.getInitializedMac(HmacAlgorithms.HMAC_SHA_256, config.getSignKey().getBytes(StandardCharsets.UTF_8));
    }

    @Override
    @Transactional
    public ThBalanceResponse getBalance(ThRequest req) throws ThException {
        log.debug("TOMHORN: Incoming getBalance Req: {}", req);
        checkPartnerId(req.getPartnerID());
        checkSignature(req.getSign(), req.getSignSourceForGetBalance());
        long uid;
        if (req.getSessionID() > 0) {
            var token = findToken(req.getSessionID(), false);
            uid = token.getUserId();
        } else {
            uid = parseUIDFromUserName(req.getName());
        }
        var w = getWallet(uid);
        var resp = new ThBalanceResponse();
        resp.setBalance(new ThBalance(w.getBalance(), config.getCurrency()));
        log.debug("TOMHORN: getBalance Response: {}", resp);
        return resp;
    }

    private static Long parseUIDFromUserName(String name) {
        return Long.valueOf(name.substring(4));
    }


    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public ThTransactionResponse deposit(ThRequest req) throws ThException {
        log.debug("TOMHORN: Incoming deposit Req: {}", req);
        checkPartnerId(req.getPartnerID());
        checkSignature(req.getSign(), req.getSignSourceForDeposit());
        var token = findToken(req.getSessionID(), true);
        var uniqueTxId = req.getReference().toString();
        if (req.getAmount() == null || req.getAmount().signum() == -1) {
            throw new ThInvalidParametersException();
        }
        // prepare tx
        var txReq = new TxRequest(token);
        txReq.setRoundMode(RoundMode.AUTO);
        txReq.setRoundRef(req.getGameRoundID().toString());
        txReq.setType(TransactionType.WIN);
        txReq.setWin(req.getAmount());
        txReq.setBet(BigDecimal.ZERO);
        txReq.setExternalOrigId(uniqueTxId);

        TxResponse txResponse;
        try {
            txResponse = addTxFromProvider(txReq);
        } catch (OuslyTransactionException e) {
            throw new ThException();
        }
        var resp = createTransactionResponse(txResponse);
        log.debug("TOMHORN: deposit Response: {}", resp);
        return resp;
    }


    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public ThTransactionResponse withdraw(ThRequest req) throws ThException {
        log.debug("TOMHORN: Incoming withdraw Req: {}", req);
        checkPartnerId(req.getPartnerID());
        checkSignature(req.getSign(), req.getSignSourceForWithdraw());
        var token = findToken(req.getSessionID(), false);

        var uniqueTxId = req.getReference().toString();

        if (req.getAmount() == null || req.getAmount().signum() == -1) {
            throw new ThInvalidParametersException();
        }

        // prepare tx
        var txReq = new TxRequest(token);
        txReq.setRoundMode(RoundMode.AUTO);
        txReq.setRoundRef(req.getGameRoundID().toString());
        txReq.setType(TransactionType.BET);
        txReq.setBet(req.getAmount());
        txReq.setWin(BigDecimal.ZERO);
        txReq.setExternalOrigId(uniqueTxId);

        TxResponse txResponse;
        try {
            txResponse = addTxFromProvider(txReq);
        } catch (OuslyTransactionException e) {
            throw new ThInsufficientFundsException();
        }
        var resp = createTransactionResponse(txResponse);
        log.debug("TOMHORN: withdraw Response: {}", resp);
        return resp;
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public ThResponse rollback(ThRequest req) throws ThException {
        log.debug("TOMHORN: Incoming rollback Req: {}", req);
        checkPartnerId(req.getPartnerID());
        checkSignature(req.getSign(), req.getSignSourceForRollback());
        var token = findToken(req.getSessionID(), true);

        var oldTx = findBySessionIdAndOrigId(token.getUserId(), token.getSessionId(), req.getReference().toString());
        if (oldTx != null) {
            var w = getWallet(token.getUserId());
            var txResponse = performRollback(oldTx, w);
            log.debug("TOMHORN: rolled back tx: {}", txResponse);
        }
        var resp = new ThResponse(0L, "");
        log.debug("TOMHORN: rollback Response: {}", resp);
        return resp;
    }

    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(config.getVendorName());
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform,
                                                 GameSettings settings) throws Exception {

        var orCreateIdentity = getOrCreateIdentity(user.getId());
        var newSession = createNewSession(orCreateIdentity);
        var token = createTokenFromSession(newSession);
        var moduleInfo = getModuleInfo(newSession, game.getGameId());

        // now fill template
        var language = getLanguage(moduleInfo, user, settings);
        var htmlStr = createGameHtml(moduleInfo, token, language);

        var gameInstance = new GameInstance();

        gameInstance.setGameUrl("");
        gameInstance.setGameHtml(htmlStr);
        gameInstance.setToken(token);
        gameInstance.setAuthToken(token);
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }

    private String getLanguage(GetModuleInfoResponse moduleInfo, CasinoUser user,
                               GameSettings settings) {
        var defLang = "en-GB";
        if (settings == null || settings.getLanguage() == null) {
            return defLang;
        }

        var pLang = languages.get(settings.getLanguage().toUpperCase());
        //        String suppLangsForGame = getLanguagesFromModule(moduleInfo);
        if (pLang == null) {
            return defLang;
        }
        return pLang;
    }

    private static String getLanguagesFromModule(GetModuleInfoResponse moduleInfo) {
        for (var p : moduleInfo.getParameters()) {
            if ("LANGUAGES".equalsIgnoreCase(p.getKey())) {
                return p.getValue();
            }
        }
        return "";
    }


    private static String createTokenFromSession(Long newSession) {
        return "THG_TK_" + newSession;
    }


    private String createGameHtml(GetModuleInfoResponse moduleInfo, String token,
                                  String language) throws Exception {
        var parameters = moduleInfo.getParameters();
        var paramMap = parameters.stream().collect(Collectors.toMap(Parameter::getKey, Parameter::getValue));
        paramMap.put("width", "100%");
        paramMap.put("height", "100%");
        paramMap.put("lang", language);
        Map<String, Object> params = new HashMap<>();
        params.put("BaseURL", paramMap.get("param:base"));
        params.put("params", paramMap);
        log.debug("TomHorn: creating template with params:{}", params);
        var t = freemarkerConfig.getTemplate("tom_horn.ftl");
        return FreeMarkerTemplateUtils.processTemplateIntoString(t, params);
    }

    private void checkPartnerId(String id) throws ThInvalidPartnerIdException {
        if (!config.getPartnerId().equals(id)) {
            throw new ThInvalidPartnerIdException();
        }
    }


    private void checkSignature(String sign, String signSource) throws ThInvalidSignException {
        try {
            var s = getHexSignature(signSource);
            if (s.equals(sign)) {
                return;
            }
            log.debug("SignSource:{}", signSource);
        } catch (Exception e) {
            log.error("Error checking sig:{}", signSource, e);
        }

        throw new ThInvalidSignException();
    }

    private SessionCacheEntry findToken(Long sessionId,
                                        boolean loadExpired) throws ThTokenNotFoundException {
        var gbce = getToken("THG_TK_" + sessionId);
        if (gbce == null && loadExpired) {
            var sessionByToken = findSessionByToken("THG_TK_" + sessionId);
            if (sessionByToken.isEmpty()) {
                throw new ThTokenNotFoundException();
            }
            gbce = new SessionCacheEntry();
            gbce.setSessionId(sessionByToken.get().getSessionId());
            gbce.setUserId(sessionByToken.get().getUserId());
            gbce.setGameId(sessionByToken.get().getGameId());
            gbce.setGp(sessionByToken.get().getPlatform());
        }
        return gbce;
    }

    private ThTransactionResponse createTransactionResponse(TxResponse txResponse) {
        var resp = new ThTransactionResponse();
        resp.setTransaction(new ThBalance(txResponse.getNewBalance(), config.getCurrency(), txResponse.getTxId()));
        return resp;
    }


    private String getOrCreateIdentity(Long userId) {
        var ciReq = new CreateIdentyRequest();
        ciReq.setPartnerID(config.getPartnerId());
        var thUserName = createNameFromId(userId);
        ciReq.setName(thUserName);
        ciReq.setCurrency(config.getCurrency());

        ciReq.setSign(getHexSignature(ciReq.getSignSource()));

        var resp = thTemplate.postForEntity(config.getApiIntegrationBaseUrl() + "/CreateIdentity", ciReq, CreateIdentityResponse.class);
        if (resp.getStatusCode().value() != 200) {
            return null;
        }
        return thUserName;
    }

    private Long createNewSession(String thUsername) {
        var ciReq = new CreateSessionRequest();
        ciReq.setPartnerId(config.getPartnerId());
        ciReq.setName(thUsername);

        ciReq.setSign(getHexSignature(ciReq.getSignSource()));

        var resp = thTemplate.postForEntity(config.getApiIntegrationBaseUrl() + "/CreateSession", ciReq, CreateSessionResponse.class);
        if (resp.getStatusCode().value() != 200) {
            return null;
        }
        return resp.getBody().getSession().getId();
    }


    private GetModuleInfoResponse getModuleInfo(Long sessionID, String gKey) {
        var ciReq = new GetModuleInfoRequest();
        ciReq.setPartnerId(config.getPartnerId());
        ciReq.setModule(gKey);
        ciReq.setSessionId(sessionID);

        ciReq.setSign(getHexSignature(ciReq.getSignSource()));

        var resp = thTemplate.postForEntity(config.getApiIntegrationBaseUrl() + "/GetModuleInfo", ciReq, GetModuleInfoResponse.class);
        if (resp.getStatusCode().value() != 200) {
            return null;
        }
        log.debug("TH-ModInfo:{}", resp.getBody());
        return resp.getBody();
    }


    private static String createNameFromId(Long userId) {
        return "TGU-" + userId;
    }

    private synchronized String getHexSignature(String body) {
        return Hex.encodeHexString(sha256Mac.doFinal(body.getBytes(StandardCharsets.UTF_8)), false);
    }


}

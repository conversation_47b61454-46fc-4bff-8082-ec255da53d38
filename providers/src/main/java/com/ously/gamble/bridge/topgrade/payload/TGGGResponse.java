package com.ously.gamble.bridge.topgrade.payload;

import java.util.Map;

public class TGGGResponse {

    String status;
    Map<Long, TGGGGame> data;

    public String getStatus() {
        return this.status;
    }

    public void setStatus(final String status) {
        this.status = status;
    }

    public Map<Long, TGGGGame> getData() {
        return this.data;
    }

    public void setData(final Map<Long, TGGGGame> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "TGGGResponse{" +
                "status='" + status + '\'' +
                ", data=" + data +
                '}';
    }
}

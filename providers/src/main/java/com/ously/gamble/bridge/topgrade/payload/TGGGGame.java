package com.ously.gamble.bridge.topgrade.payload;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TGGGGame {
    String name;
    String gameId;
    @JsonProperty("absolute_name")
    String absoluteName;
    @JsonProperty("game_sub_type")
    String gameSubType;

    Integer mobile;
    Integer desktop;

    public String getName() {
        return this.name;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public String getGameId() {
        return this.gameId;
    }

    public void setGameId(final String gameId) {
        this.gameId = gameId;
    }

    public String getAbsoluteName() {
        return this.absoluteName;
    }

    public void setAbsoluteName(final String absoluteName) {
        this.absoluteName = absoluteName;
    }

    public String getGameSubType() {
        return this.gameSubType;
    }

    public void setGameSubType(final String gameSubType) {
        this.gameSubType = gameSubType;
    }

    public Integer getMobile() {
        return this.mobile;
    }

    public void setMobile(final Integer mobile) {
        this.mobile = mobile;
    }

    public Integer getDesktop() {
        return this.desktop;
    }

    public void setDesktop(final Integer desktop) {
        this.desktop = desktop;
    }

    @Override
    public String toString() {
        return "TGGGGame{" +
                "name='" + name + '\'' +
                ", gameId='" + gameId + '\'' +
                ", absoluteName='" + absoluteName + '\'' +
                ", gameSubType='" + gameSubType + '\'' +
                ", mobile=" + mobile +
                ", desktop=" + desktop +
                '}';
    }
}

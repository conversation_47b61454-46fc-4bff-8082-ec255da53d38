package com.ously.gamble.bridge.playson.payload;

import jakarta.xml.bind.annotation.*;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "getbalance")
public class PSGetBalance {

    public PSGetBalance() {
    }

    @XmlAttribute(name = "id", required = true)
    Long id;

    @XmlAttribute(name = "guid")
    String guid;

    @XmlAttribute(name = "result")
    String result;

    @XmlElement()
    PSBalance balance;

    public PSGetBalance(Long id, String fail) {
        this.id = id;
        this.result = fail;
    }


    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public PSBalance getBalance() {
        return balance;
    }

    public void setBalance(PSBalance balance) {
        this.balance = balance;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    @Override
    public String toString() {
        return "PSGetBalance{" +
                "id=" + id +
                ", guid='" + guid + '\'' +
                ", result='" + result + '\'' +
                ", balance=" + balance +
                '}';
    }
}

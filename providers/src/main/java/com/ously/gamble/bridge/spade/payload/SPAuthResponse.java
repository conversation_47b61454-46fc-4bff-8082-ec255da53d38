package com.ously.gamble.bridge.spade.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class SPAuthResponse extends SPResponse {

    SPAcctInfo acctInfo;

    public SPAuthResponse(SPRequest req) {
        super(req);
    }

    public SPAcctInfo getAcctInfo() {
        return acctInfo;
    }

    public void setAcctInfo(SPAcctInfo acctInfo) {
        this.acctInfo = acctInfo;
    }

    @Override
    public String toString() {
        return "SPAuthResponse{" +
                "acctInfo=" + acctInfo +
                ", serialNo='" + serialNo + '\'' +
                ", code=" + code +
                ", msg='" + msg + '\'' +
                ", merchantCode='" + merchantCode + '\'' +
                '}';
    }
}

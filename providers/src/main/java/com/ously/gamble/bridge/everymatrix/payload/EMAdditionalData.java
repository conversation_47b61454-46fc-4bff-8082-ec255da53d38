package com.ously.gamble.bridge.everymatrix.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EMAdditionalData {

    @JsonProperty("GameName")
    String gameName;
    @JsonProperty("GameCode")
    String gameCode;
    @JsonProperty("GameSlug")
    String gameSlug;
    @JsonProperty("Language")
    String language;
    @JsonProperty("CasinoGameId")
    String casinoGameId;
    @JsonProperty("ReportCategory")
    String reportCategory;

    @JsonProperty("PlayerCurrency")
    String playerCurrency;
    @JsonProperty("PlayerCountry")
    String playerCountry;
    @JsonProperty("PlayerUserName")
    String playerUserName;
    @JsonProperty("GameModel")
    String gameModel;
    @JsonProperty("BonusId")
    String bonusId;
    @JsonProperty("JackpotContribution")
    String jackpotContribution;

    public String getGameName() {
        return gameName;
    }

    public void setGameName(final String gameName) {
        this.gameName = gameName;
    }

    public String getGameCode() {
        return gameCode;
    }

    public void setGameCode(final String gameCode) {
        this.gameCode = gameCode;
    }

    public String getGameSlug() {
        return gameSlug;
    }

    public void setGameSlug(final String gameSlug) {
        this.gameSlug = gameSlug;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(final String language) {
        this.language = language;
    }

    public String getCasinoGameId() {
        return casinoGameId;
    }

    public void setCasinoGameId(final String casinoGameId) {
        this.casinoGameId = casinoGameId;
    }

    public String getReportCategory() {
        return reportCategory;
    }

    public void setReportCategory(final String reportCategory) {
        this.reportCategory = reportCategory;
    }

    public String getPlayerCurrency() {
        return playerCurrency;
    }

    public void setPlayerCurrency(final String playerCurrency) {
        this.playerCurrency = playerCurrency;
    }

    public String getPlayerCountry() {
        return playerCountry;
    }

    public void setPlayerCountry(final String playerCountry) {
        this.playerCountry = playerCountry;
    }

    public String getPlayerUserName() {
        return playerUserName;
    }

    public void setPlayerUserName(final String playerUserName) {
        this.playerUserName = playerUserName;
    }

    public String getGameModel() {
        return gameModel;
    }

    public void setGameModel(final String gameModel) {
        this.gameModel = gameModel;
    }

    public String getBonusId() {
        return bonusId;
    }

    public void setBonusId(final String bonusId) {
        this.bonusId = bonusId;
    }

    public String getJackpotContribution() {
        return jackpotContribution;
    }

    public void setJackpotContribution(final String jackpotContribution) {
        this.jackpotContribution = jackpotContribution;
    }

    @Override
    public String toString() {
        return "EMAdditionalData{" +
                "gameName='" + gameName + '\'' +
                ", gameCode='" + gameCode + '\'' +
                ", gameSlug='" + gameSlug + '\'' +
                ", language='" + language + '\'' +
                ", casinoGameId='" + casinoGameId + '\'' +
                ", reportCategory='" + reportCategory + '\'' +
                ", playerCurrency='" + playerCurrency + '\'' +
                ", playerCountry='" + playerCountry + '\'' +
                ", playerUserName='" + playerUserName + '\'' +
                ", gameModel='" + gameModel + '\'' +
                ", bonusId='" + bonusId + '\'' +
                ", jackpotContribution='" + jackpotContribution + '\'' +
                '}';
    }
}

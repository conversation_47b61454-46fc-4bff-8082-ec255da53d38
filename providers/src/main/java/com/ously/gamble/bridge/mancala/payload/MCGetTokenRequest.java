package com.ously.gamble.bridge.mancala.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class MCGetTokenRequest {

    @JsonProperty("ClientGuid")
    String clientGuid;
    @JsonProperty("GameId")
    Long gameId;
    @JsonProperty("UserId")
    String userId;
    @JsonProperty("Currency")
    String currency;
    @JsonProperty("Lang")
    String lang;
    @JsonProperty("ClientType")
    Integer clientType = 1;
    @JsonProperty("IsVirtual")
    Boolean isVirtual = false;
    @JsonProperty("Hash")
    String hash;
    @JsonProperty("ApiVersion")
    String version = "v2";

    @JsonProperty("DemoMode")
    Boolean demoMode = false;
    @JsonProperty("ExtraData")
    String extraData;


    public String getClientGuid() {
        return clientGuid;
    }

    public void setClientGuid(String clientGuid) {
        this.clientGuid = clientGuid;
    }

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public Boolean getVirtual() {
        return isVirtual;
    }

    public void setVirtual(Boolean virtual) {
        isVirtual = virtual;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public Boolean getDemoMode() {
        return demoMode;
    }

    public void setDemoMode(Boolean demoMode) {
        this.demoMode = demoMode;
    }

    public String getExtraData() {
        return extraData;
    }

    public void setExtraData(String extraData) {
        this.extraData = extraData;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @JsonIgnore
    public String getHashSource() {
        return "GetToken/" + clientGuid + gameId + userId + currency;
    }

    @Override
    public String toString() {
        return "MCGetTokenRequest{" +
                "clientGuid='" + clientGuid + '\'' +
                ", gameId=" + gameId +
                ", userId='" + userId + '\'' +
                ", currency='" + currency + '\'' +
                ", lang='" + lang + '\'' +
                ", clientType=" + clientType +
                ", isVirtual=" + isVirtual +
                ", apiVersion='" + version + '\'' +
                ", hash='" + hash + '\'' +
                ", demoMode=" + demoMode +
                ", extraData='" + extraData + '\'' +
                '}';
    }
}

package com.ously.gamble.bridge.wazdan.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class WDMessage {
    final String text;
    Integer type = 1;
    Integer time = 10;


    public WDMessage(String text) {
        this.text = text;
    }

    public String getText() {
        return text;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getTime() {
        return time;
    }

    public void setTime(Integer time) {
        this.time = time;
    }

    @Override
    public String toString() {
        return "WDMessage{" +
                "text='" + text + '\'' +
                ", type=" + type +
                ", time=" + time +
                '}';
    }
}

package com.ously.gamble.bridge.mascot.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class MSBalanceRequest extends MSRequest {

    String currency;
    String bonusId;
    String seriesId;


    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }


    public String getBonusId() {
        return bonusId;
    }

    public void setBonusId(String bonusId) {
        this.bonusId = bonusId;
    }

    public String getSeriesId() {
        return seriesId;
    }

    public void setSeriesId(String seriesId) {
        this.seriesId = seriesId;
    }

    @Override
    public String toString() {
        return "MSBalanceRequest{" +
                "playerName='" + playerName + '\'' +
                ", currency='" + currency + '\'' +
                ", gameId='" + gameId + '\'' +
                ", sessionAlternativeId='" + sessionAlternativeId + '\'' +
                ", bonusId='" + bonusId + '\'' +
                ", seriesId='" + seriesId + '\'' +
                ", callerId=" + callerId +
                '}';
    }
}

package com.ously.gamble.bridge.revolver.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class RVTransactionRequest extends RVNonAuthRequest {

    Long amount;
    Long channel;
    Long creditAmount;
    Long debitAmount;
    Boolean isRoundFinished;

    String reason;
    String relatedExternalDebitTransactionId;
    String roundId;
    String transactionId;
    String transactionProviderPrefix;


    @Override
    @JsonIgnore
    public String getSignSource() {
        return getOrEmpty(this.amount) +
                getOrEmpty(this.channel) +
                getOrEmpty(this.creditAmount) +
                getOrEmpty(this.currency) +
                getOrEmpty(this.date) +
                getOrEmpty(this.debitAmount) +
                getOrEmpty(this.gameId) +
                getOrEmpty(this.isRoundFinished) +
                getOrEmpty(this.playerId) +
                getOrEmpty(this.reason) +
                getOrEmpty(this.relatedExternalDebitTransactionId) +
                getOrEmpty(this.roundId) +
                getOrEmpty(this.timeout) +
                getOrEmpty(this.transactionId) +
                getOrEmpty(this.transactionProviderPrefix);
    }


    public String getRoundId() {
        return roundId;
    }

    public void setRoundId(String roundId) {
        this.roundId = roundId;
    }

    public Long getChannel() {
        return channel;
    }

    public void setChannel(Long channel) {
        this.channel = channel;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Boolean getRoundFinished() {
        return isRoundFinished;
    }

    public void setRoundFinished(Boolean roundFinished) {
        isRoundFinished = roundFinished;
    }

    public String getTransactionProviderPrefix() {
        return transactionProviderPrefix;
    }

    public void setTransactionProviderPrefix(String transactionsProviderPrefix) {
        this.transactionProviderPrefix = transactionsProviderPrefix;
    }

    public String getRelatedExternalDebitTransactionId() {
        return relatedExternalDebitTransactionId;
    }

    public void setRelatedExternalDebitTransactionId(String relatedExternalDebitTransactionId) {
        this.relatedExternalDebitTransactionId = relatedExternalDebitTransactionId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Long getDebitAmount() {
        return debitAmount;
    }

    public void setDebitAmount(Long debitAmount) {
        this.debitAmount = debitAmount;
    }

    public Long getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(Long creditAmount) {
        this.creditAmount = creditAmount;
    }

    @Override
    public String toString() {
        return "RVTransactionRequest{" +
                "amount=" + amount +
                ", channel=" + channel +
                ", creditAmount=" + creditAmount +
                ", debitAmount=" + debitAmount +
                ", isRoundFinished=" + isRoundFinished +
                ", reason='" + reason + '\'' +
                ", relatedExternalDebitTransactionId='" + relatedExternalDebitTransactionId + '\'' +
                ", roundId='" + roundId + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", transactionsProviderPrefix='" + transactionProviderPrefix + '\'' +
                ", playerId='" + playerId + '\'' +
                ", currency='" + currency + '\'' +
                ", gameId='" + gameId + '\'' +
                ", sessionState=" + sessionState +
                ", date=" + date +
                ", sign='" + sign + '\'' +
                ", timeout=" + timeout +
                "} , signSrc='" + getSignSource() + "', " + super.toString();
    }
}

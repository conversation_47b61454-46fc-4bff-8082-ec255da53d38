package com.ously.gamble.bridge.topgrade.payload;


import java.util.Map;

public class TGWinRequest implements TGDataCarrier {

    private final TGRequest delegate;

    public TGWinRequest(TGRequest dele) {
        this.delegate = dele;
    }

    public String getSignatureSource() {
        return delegate.getSignatureSource() + '*' + getSignatureSourceWin();
    }

    public String getName() {
        return delegate.getName();
    }

    @Override public String getCallbackId() {
        return delegate.getCallback_id();
    }

    public String getToken() {
        return delegate.getToken();
    }

    public String getSignature() {
        return delegate.getSignature();
    }

    private String getSignatureSourceWin() {
        return
                delegate.data.get("round_id") + ":" +
                        delegate.data.get("action_id") + ':' +
                        delegate.data.get("final_action") + ':' +
                        delegate.data.get("amount") + ':' +
                        delegate.data.get("currency") + ':' +
                        delegate.data.get("details");
    }

    @Override public Map<String, Object> getData() {
        return delegate.data;
    }

    @Override
    public String toString() {
        return delegate.toString();
    }
}

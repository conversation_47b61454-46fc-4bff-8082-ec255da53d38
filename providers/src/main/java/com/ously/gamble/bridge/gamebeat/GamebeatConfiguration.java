package com.ously.gamble.bridge.gamebeat;

import com.ously.gamble.api.bridge.BridgeConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "gamebeat")
@ConditionalOnProperty(
        value = "gamebeat.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class GamebeatConfiguration implements BridgeConfiguration {
    public static final String VENDOR_NAME = "GAMEBEAT";
    public static final String BRIDGE_GAMEBEAT = "/bridge/gamebeat";


    String apiUrl = "http://ously.releaseband.com";
    String currency = "ACC";
    String secret = "5CLgxwoRspig08afJ7cLi4uLFzRXI67thpVv8D3Oq1ugola65J3xgmIKCWQEEDEq";
    String casinoId = "ously";

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getCasinoId() {
        return casinoId;
    }

    public void setCasinoId(String casinoId) {
        this.casinoId = casinoId;
    }


    @Override
    public String getVendorName() {
        return VENDOR_NAME;
    }

    @Override
    public String getBridgeName() {
        return BRIDGE_GAMEBEAT;
    }

    @Override
    public String getAuth_user() {
        return null;
    }

    @Override
    public String getAuth_password() {
        return null;
    }

    @Override
    public List<String> getWhitelistIPs() {
        return Collections.emptyList();
    }
}

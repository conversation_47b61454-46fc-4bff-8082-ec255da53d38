package com.ously.gamble.bridge.tomhorn.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class CreateIdentityResponse extends ThResponse {

    public static class Identity {

        @JsonProperty("Name")
        String name;

        @JsonProperty("State")
        Long state;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Long getState() {
            return state;
        }

        public void setState(Long state) {
            this.state = state;
        }


        @Override
        public String toString() {
            return "Identity{" +
                   "name='" + name + '\'' +
                   ", state=" + state +
                   '}';
        }
    }

    @JsonProperty("Identity")
    Identity identity;

    public Identity getIdentity() {
        return identity;
    }

    public void setIdentity(Identity identity) {
        this.identity = identity;
    }

    @Override
    public String toString() {
        return "CreateIdentityResponse{" +
               "identity=" + identity +
               ", code=" + code +
               ", message='" + message + '\'' +
               "}";
    }
}

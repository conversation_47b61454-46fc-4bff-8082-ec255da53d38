package com.ously.gamble.bridge.bgaming;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.bridge.bgaming.payload.BGPlayRequest;
import com.ously.gamble.bridge.bgaming.payload.BGRollbackRequest;
import jakarta.annotation.PostConstruct;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;

import javax.crypto.Mac;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * The bridge callbacks only controller
 */
@RestController
@RequestMapping(BGamingConfiguration.BRIDGE_BGAMING)
@ConditionalOnBean(BGamingConfiguration.class)
public class BGamingController {
    final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    BGamingConfiguration config;

    @Autowired
    ObjectMapper om;

    @Autowired
    BGamingService srv;

    Mac hmacSHA256;

    @PostConstruct
    public void setup() {
        hmacSHA256 = HmacUtils.getInitializedMac(HmacAlgorithms.HMAC_SHA_256, config.getAuthToken().getBytes(StandardCharsets.UTF_8));
    }

    @PostMapping("/play")
    @ResponseBody
    public ResponseEntity<String> play(@RequestBody String request, @RequestHeader Map<String, String> headers) throws Exception {
        log.debug("BGaming Play Request:{}", request);
        ResponseEntity<String> resp;
        if (checkSignature(request, headers)) {
            var bgPlayRequest = om.readValue(request, BGPlayRequest.class);
            var play = srv.play(bgPlayRequest);
            var respBody = om.writeValueAsString(play);
            var status = 200;
            if (play.getCode() != null) {
                status = play.getHttpCode();
            }
            resp = new ResponseEntity<>(respBody, getSignatureHeader(respBody), status);
        } else {
            resp = new ResponseEntity<>("{\"code\":403}", getSignatureHeader("{\"code\":403}"), 403);
        }
        if (log.isDebugEnabled()) {
            log.debug("BGaming Play Response:{}", resp.getBody());
        }
        return resp;
    }

    private MultiValueMap<String, String> getSignatureHeader(String respBody) {
        var hMap = new HttpHeaders();
        hMap.add("X-REQUEST-SIGN", createSignature(respBody));
        return hMap;
    }

    private synchronized String createSignature(String body) {
        return Hex.encodeHexString(hmacSHA256.doFinal(body.getBytes(StandardCharsets.UTF_8)));
    }

    private synchronized boolean checkSignature(String request, Map<String, String> headers) {
        log.debug("CHK-SIG:{},{}", request, headers);
        var sigReceived = headers.get("X-Request-Sign");
        var ownSig = Hex.encodeHexString(hmacSHA256.doFinal(request.getBytes(StandardCharsets.UTF_8)));
        if (!ownSig.equalsIgnoreCase(sigReceived)) {
            log.warn("Sigs do not match-orig:{} - calculated:{}, request:'{}'", sigReceived, ownSig, request);
            return false;
        }
        return true;
    }

    @PostMapping("/rollback")
    @ResponseBody
    public ResponseEntity<String> rollback(@RequestBody String request,
                                           @RequestHeader Map<String, String> headers) throws Exception {

        if (checkSignature(request, headers)) {
            var bgRollbackReq = om.readValue(request, BGRollbackRequest.class);
            var rb = srv.rollback(bgRollbackReq);
            var respBody = om.writeValueAsString(rb);
            var status = 200;
            if (rb.getCode() != null) {
                status = 401;
            }
            return new ResponseEntity<>(respBody, getSignatureHeader(respBody), status);
        }
        return new ResponseEntity<>("{\"code\":403}", getSignatureHeader("{\"code\":403}"), 403);
    }


}

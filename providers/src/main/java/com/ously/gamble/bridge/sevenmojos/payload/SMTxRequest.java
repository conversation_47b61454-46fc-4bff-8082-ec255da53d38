package com.ously.gamble.bridge.sevenmojos.payload;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SMTxRequest {

    @JsonProperty("SessionId")
    String sessionId;
    @JsonProperty("TransactionId")
    String transactionId;
    @JsonProperty("Amount")
    String amount;
    @JsonProperty("GameId")
    String gameId;
    @JsonProperty("RoundId")
    String roundId;
    @JsonProperty("TrnReason")
    String TrnReason;
    @JsonProperty("TrnDescription")
    String TrnDescription;
    @JsonProperty("UserId")
    String userId;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public String getRoundId() {
        return roundId;
    }

    public void setRoundId(String roundId) {
        this.roundId = roundId;
    }

    public String getTrnReason() {
        return TrnReason;
    }

    public void setTrnReason(String trnReason) {
        TrnReason = trnReason;
    }

    public String getTrnDescription() {
        return TrnDescription;
    }

    public void setTrnDescription(String trnDescription) {
        TrnDescription = trnDescription;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "SMTxRequest{" +
                "sessionId='" + sessionId + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", amount='" + amount + '\'' +
                ", gameId='" + gameId + '\'' +
                ", roundId='" + roundId + '\'' +
                ", TrnReason='" + TrnReason + '\'' +
                ", TrnDescription='" + TrnDescription + '\'' +
                ", userId='" + userId + '\'' +
                '}';
    }
}

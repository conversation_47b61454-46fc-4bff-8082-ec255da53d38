package com.ously.gamble.bridge.mascot;

import com.ously.gamble.api.bridge.BridgeConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "mascot")
@ConditionalOnProperty(
        value = "mascot.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class MascotConfiguration implements BridgeConfiguration {

    public static final String VENDOR_NAME = "MASCOT";
    public static final String BRIDGE_MASCOT = "/bridge/mascot";


    Long partnerId = 610L;
    String partnerIdStr = "mascot_Ously_test";
    String apiUrl = "https://api.mascot.games/v1/";
    String activeCurrency = "AC";


    @Override
    public String getVendorName() {
        return VENDOR_NAME;
    }

    @Override
    public String getBridgeName() {
        return BRIDGE_MASCOT;
    }

    @Override public String getAuth_user() {
        return null;
    }

    @Override public String getAuth_password() {
        return null;
    }

    @Override
    public List<String> getWhitelistIPs() {
        return Collections.emptyList();
    }

    public void setActiveCurrency(String activeCurrency) {
        this.activeCurrency = activeCurrency;
    }

    public Long getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Long partnerId) {
        this.partnerId = partnerId;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getActiveCurrency() {
        return activeCurrency;
    }


}

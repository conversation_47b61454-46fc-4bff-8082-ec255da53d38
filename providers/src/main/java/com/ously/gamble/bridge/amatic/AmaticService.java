package com.ously.gamble.bridge.amatic;

import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.amatic.payload.*;

public interface AmaticService extends BridgeHandler {

    AMAuthenticateResponse authenticate(AMAuthenticateRequest req);

    AMGetBalanceResponse getBalance(AMGetBalanceRequest req);

    AMDebitResponse debit(AMDebitRequest req);

    AMCreditResponse credit(AMCreditRequest req);

    AMEndRoundResponse endRound(AMEndRoundRequest req);

    AMCancelBetResponse cancelBet(AMCancelBetRequest req);

    AMCancelWinResponse cancelWin(AMCancelWinRequest req);

}

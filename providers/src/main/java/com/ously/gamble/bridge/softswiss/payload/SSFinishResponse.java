package com.ously.gamble.bridge.softswiss.payload;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;
import java.math.RoundingMode;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class SSFinishResponse extends SSBaseResponse {
    public SSFinishResponse(BigDecimal balance) {
        this.balance = balance.setScale(6, RoundingMode.HALF_DOWN).toPlainString();
    }
}

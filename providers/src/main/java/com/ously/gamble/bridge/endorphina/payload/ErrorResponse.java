package com.ously.gamble.bridge.endorphina.payload;

public class ErrorResponse {
    String code;
    String message;

    public static ErrorResponse of(String code, String message) {
        return new ErrorResponse(code, message);
    }

    public ErrorResponse() {
    }

    public ErrorResponse(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}

package com.ously.gamble.bridge.oryxgaming.payload;

import com.ously.gamble.bridge.oryxgaming.OryxGamingServiceImpl;

import java.math.BigDecimal;

public class OGOutOfMoneyError extends OGError {

    final Long balance;

    public OGOutOfMoneyError(String code, String description, int status, BigDecimal balance) {
        super(code, description, status);
        this.balance = balance.multiply(OryxGamingServiceImpl.AMOUNT_MULTIPLIER).longValue();
    }

    public Long getBalance() {
        return balance;
    }
}

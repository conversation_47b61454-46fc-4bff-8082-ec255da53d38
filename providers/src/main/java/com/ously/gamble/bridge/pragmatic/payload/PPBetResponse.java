package com.ously.gamble.bridge.pragmatic.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class PPBetResponse extends PPResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    public PPBetResponse() {
    }

    public PPBetResponse(long error, String description) {
        this.error = error;
        this.description = description;
    }

    String transactionId;
    String currency;
    BigDecimal cash;
    BigDecimal bonus = BigDecimal.ZERO;
    BigDecimal usedPromo = BigDecimal.ZERO;

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getCash() {
        return cash;
    }

    public void setCash(BigDecimal cash) {
        this.cash = cash;
    }

    public BigDecimal getBonus() {
        return bonus;
    }

    public void setBonus(BigDecimal bonus) {
        this.bonus = bonus;
    }

    public BigDecimal getUsedPromo() {
        return usedPromo;
    }

    public void setUsedPromo(BigDecimal usedPromo) {
        this.usedPromo = usedPromo;
    }

    @Override
    public String toString() {
        return "PPBetResponse{" +
                "transactionId=" + transactionId +
                ", currency='" + currency + '\'' +
                ", cash=" + cash +
                ", bonus=" + bonus +
                ", usedPromo=" + usedPromo +
                "} " + super.toString();
    }
}

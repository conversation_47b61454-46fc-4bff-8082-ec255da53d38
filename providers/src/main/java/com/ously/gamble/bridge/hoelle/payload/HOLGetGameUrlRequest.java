package com.ously.gamble.bridge.hoelle.payload;

import java.util.Map;

public class HOLGetGameUrlRequest {
    String playerId;
    String currency;
    String gameCode;
    String language;
    String mode;
    String profile;
    HOLPlayerDetails playerDetails;
    Map<String, Object> options;

    public HOLGetGameUrlRequest() {
    }

    public HOLGetGameUrlRequest(String playerId, String currency, String gameCode, String language) {
        this.playerId = playerId;
        this.currency = currency;
        this.gameCode = gameCode;
        this.language = language;
        this.mode = "real";
    }

    public String getPlayerId() {
        return playerId;
    }

    public void setPlayerId(String playerId) {
        this.playerId = playerId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getGameCode() {
        return gameCode;
    }

    public void setGameCode(String gameCode) {
        this.gameCode = gameCode;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public HOLPlayerDetails getPlayerDetails() {
        return playerDetails;
    }

    public void setPlayerDetails(HOLPlayerDetails playerDetails) {
        this.playerDetails = playerDetails;
    }

    public Map<String, Object> getOptions() {
        return options;
    }

    public void setOptions(Map<String, Object> options) {
        this.options = options;
    }


    @Override
    public String toString() {
        return "HOLGetGameUrlRequest{" +
                "playerId='" + playerId + '\'' +
                ", currency='" + currency + '\'' +
                ", gameCode='" + gameCode + '\'' +
                ", language='" + language + '\'' +
                ", mode='" + mode + '\'' +
                ", profile='" + profile + '\'' +
                ", playerDetails=" + playerDetails +
                ", options=" + options +
                '}';
    }

    public String getSignaturePayload() {
        return String.join("#", playerId, currency, gameCode, mode);
    }

}

package com.ously.gamble.bridge.kagaming.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.ously.gamble.bridge.kagaming.KAGamingConfiguration;

import java.util.Random;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class KAApiGetGamesRequest {

    String partnerName;
    String accessKey;
    String language;
    Integer randomId;

    public KAApiGetGamesRequest() {
    }

    public KAApiGetGamesRequest(KAGamingConfiguration config) {
        this.partnerName = config.getOperatorCode();
        this.language = "en";
        this.accessKey = config.getAccessKey();
        this.randomId = new Random().nextInt(100000);
    }

    public String getPartnerName() {
        return partnerName;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public String getLanguage() {
        return language;
    }

    public Integer getRandomId() {
        return randomId;
    }
}

package com.ously.gamble.bridge.gamebeat.payload;

import com.fasterxml.jackson.annotation.JsonProperty;

public class GBBalanceRequest {
    @JsonProperty("user_id")
    String userId;
    String game;
    String currency;
    String token;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getGame() {
        return game;
    }

    public void setGame(String game) {
        this.game = game;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @Override
    public String toString() {
        return "GBBalanceRequest{" +
                "userId='" + userId + '\'' +
                ", game='" + game + '\'' +
                ", currency='" + currency + '\'' +
                ", token='" + token + '\'' +
                '}';
    }
}

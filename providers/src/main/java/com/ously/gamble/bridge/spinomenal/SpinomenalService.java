package com.ously.gamble.bridge.spinomenal;

import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.spinomenal.payload.*;
import org.springframework.web.bind.annotation.RequestBody;

public interface SpinomenalService extends BridgeHandler {

    PlayerBalanceResponse getPlayerBalance(@RequestBody PlayerBalanceRequest pbr);

    AuthenticationResponse authenticate(@RequestBody AuthenticationRequest req);

//    ProcessBetResponse solveBet(@RequestBody ProcessBetRequest req);

    ProcessBetResponse processBet(@RequestBody ProcessBetRequest req);
}

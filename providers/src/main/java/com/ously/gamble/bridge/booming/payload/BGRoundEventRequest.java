package com.ously.gamble.bridge.booming.payload;

public class BGRoundEventRequest {

    String session_id;
    String player_id;
    Integer round;
    String type;
    String debit;
    String credit;
    String customer_id;
    BGFreespins freespins;
    String operator_data;

    public String getSession_id() {
        return session_id;
    }

    public void setSession_id(String session_id) {
        this.session_id = session_id;
    }

    public String getPlayer_id() {
        return player_id;
    }

    public void setPlayer_id(String player_id) {
        this.player_id = player_id;
    }

    public Integer getRound() {
        return round;
    }

    public void setRound(Integer round) {
        this.round = round;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDebit() {
        return debit;
    }

    public void setDebit(String debit) {
        this.debit = debit;
    }

    public String getCredit() {
        return credit;
    }

    public void setCredit(String credit) {
        this.credit = credit;
    }

    public String getCustomer_id() {
        return customer_id;
    }

    public void setCustomer_id(String customer_id) {
        this.customer_id = customer_id;
    }

    public BGFreespins getFreespins() {
        return freespins;
    }

    public void setFreespins(BGFreespins freespins) {
        this.freespins = freespins;
    }

    public String getOperator_data() {
        return operator_data;
    }

    public void setOperator_data(String operator_data) {
        this.operator_data = operator_data;
    }

    @Override
    public String toString() {
        return "BGRoundEventRequest{" +
               "session_id='" + session_id + '\'' +
               ", player_id='" + player_id + '\'' +
               ", round=" + round +
               ", type='" + type + '\'' +
               ", bet='" + debit + '\'' +
               ", win='" + credit + '\'' +
               ", customer_id='" + customer_id + '\'' +
               ", freespins=" + freespins +
               ", operator_data='" + operator_data + '\'' +
               '}';
    }
}

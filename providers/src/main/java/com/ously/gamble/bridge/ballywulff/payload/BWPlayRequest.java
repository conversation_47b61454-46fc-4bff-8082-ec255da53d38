package com.ously.gamble.bridge.ballywulff.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import java.math.BigDecimal;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class BWPlayRequest {
    BigDecimal betAmount;
    BigDecimal winAmount;
    BigDecimal virtualAmount;

    public BigDecimal getBetAmount() {
        return betAmount;
    }

    public void setBetAmount(BigDecimal betAmount) {
        this.betAmount = betAmount;
    }

    public BigDecimal getWinAmount() {
        return winAmount;
    }

    public void setWinAmount(BigDecimal winAmount) {
        this.winAmount = winAmount;
    }

    public BigDecimal getVirtualAmount() {
        return virtualAmount;
    }

    public void setVirtualAmount(BigDecimal virtualAmount) {
        this.virtualAmount = virtualAmount;
    }

    @Override
    public String toString() {
        return "BWPlayRequest{" +
                "betAmount=" + betAmount +
                ", winAmount=" + winAmount +
                ", virtualAmount=" + virtualAmount +
                '}';
    }
}

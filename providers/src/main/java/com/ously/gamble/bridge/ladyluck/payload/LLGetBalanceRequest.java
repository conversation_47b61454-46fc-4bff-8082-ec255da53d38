package com.ously.gamble.bridge.ladyluck.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class LLGetBalanceRequest extends LLBaseBean {
    String sessionToken;
    String site;
    Long retry;

    public String getSessionToken() {
        return sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public Long getRetry() {
        return retry;
    }

    public void setRetry(Long retry) {
        this.retry = retry;
    }

    @Override
    public String getSignSource() {
        return getSignPart("retry", retry) + getSignPart("sessionToken", sessionToken) + getSignPart("site", site);
    }

    @Override
    public String toString() {
        return "LLGetBalanceRequest{" +
                "sessionToken='" + sessionToken + '\'' +
                ", site='" + site + '\'' +
                ", retry=" + retry +
                "} ";
    }
}

package com.ously.gamble.bridge.booming.payload;

public class BGSessionRequest {
    String game_id;
    String balance;
    String currency;
    String locale;
    String player_id;

    String player_ip;
    String callback;
    String rollback_callback;
    String variant = "mobile";

    // optional
    Boolean demo = Boolean.FALSE;

    String operator_launch_data;
    String player_origin;
    String jurisdiction;

    String exit;
    String cashier;

    public String getGame_id() {
        return game_id;
    }

    public void setGame_id(String game_id) {
        this.game_id = game_id;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getPlayer_id() {
        return player_id;
    }

    public void setPlayer_id(String player_id) {
        this.player_id = player_id;
    }

    public String getCallback() {
        return callback;
    }

    public void setCallback(String callback) {
        this.callback = callback;
    }

    public String getRollback_callback() {
        return rollback_callback;
    }

    public void setRollback_callback(String rollback_callback) {
        this.rollback_callback = rollback_callback;
    }

    public String getPlayer_ip() {
        return player_ip;
    }

    public void setPlayer_ip(String player_ip) {
        this.player_ip = player_ip;
    }

    public String getVariant() {
        return variant;
    }

    public void setVariant(String variant) {
        this.variant = variant;
    }

    public Boolean getDemo() {
        return demo;
    }

    public void setDemo(Boolean demo) {
        this.demo = demo;
    }

    public String getOperator_launch_data() {
        return operator_launch_data;
    }

    public void setOperator_launch_data(String operator_launch_data) {
        this.operator_launch_data = operator_launch_data;
    }

    public String getPlayer_origin() {
        return player_origin;
    }

    public void setPlayer_origin(String player_origin) {
        this.player_origin = player_origin;
    }

    public String getJurisdiction() {
        return jurisdiction;
    }

    public void setJurisdiction(String jurisdiction) {
        this.jurisdiction = jurisdiction;
    }

    public String getExit() {
        return exit;
    }

    public void setExit(String exit) {
        this.exit = exit;
    }

    public String getCashier() {
        return cashier;
    }

    public void setCashier(String cashier) {
        this.cashier = cashier;
    }

    @Override
    public String toString() {
        return "BGSessionRequest{" +
               "game_id='" + game_id + '\'' +
               ", balance='" + balance + '\'' +
               ", currency='" + currency + '\'' +
               ", locale='" + locale + '\'' +
               ", player_id='" + player_id + '\'' +
               ", callback='" + callback + '\'' +
               ", rollback_callback='" + rollback_callback + '\'' +
               ", variant='" + variant + '\'' +
               ", demo=" + demo +
               ", operator_data='" + operator_launch_data + '\'' +
               ", player_origin='" + player_origin + '\'' +
               ", jurisdiction='" + jurisdiction + '\'' +
               ", exit='" + exit + '\'' +
               ", cashier='" + cashier + '\'' +
               '}';
    }
}

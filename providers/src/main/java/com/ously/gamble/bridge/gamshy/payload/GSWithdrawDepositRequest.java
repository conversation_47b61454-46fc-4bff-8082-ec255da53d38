package com.ously.gamble.bridge.gamshy.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class GSWithdrawDepositRequest {

    GSGame game;
    GSWallet wallet;
    GSCustomState customState;

    public GSGame getGame() {
        return game;
    }

    public void setGame(GSGame game) {
        this.game = game;
    }

    public GSWallet getWallet() {
        return wallet;
    }

    public void setWallet(GSWallet wallet) {
        this.wallet = wallet;
    }

    public GSCustomState getCustomState() {
        return customState;
    }

    public void setCustomState(GSCustomState customState) {
        this.customState = customState;
    }

    @Override
    public String toString() {
        return "GSWithdrawDepositRequest{" +
                "game=" + game +
                ", wallet=" + wallet +
                ", customState=" + customState +
                '}';
    }
}

//package com.ously.gamble.bridge.pariplay;
//
//import com.ously.gamble.api.OuslyOutOfMoneyException;
//import com.ously.gamble.api.OuslyTransactionException;
//import com.ously.gamble.api.bridge.GameSettings;
//import com.ously.gamble.bridge.BridgeBase;
//import com.ously.gamble.bridge.pariplay.payload.*;
//import com.ously.gamble.api.games.CasinoGame;
//import com.ously.gamble.api.session.TxRequest;
//import com.ously.gamble.api.session.TxResponse;
//import com.ously.gamble.persistence.dto.CasinoUser;
//import com.ously.gamble.persistence.model.game.GameInstance;
//import com.ously.gamble.persistence.model.game.GamePlatform;
//import com.ously.gamble.persistence.model.Transaction;
//import com.ously.gamble.persistence.model.TransactionType;
//import org.apache.commons.lang3.RandomUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.web.client.RestTemplate;
//
//import java.math.BigDecimal;
//import java.time.Instant;
//import java.time.LocalDateTime;
//import java.time.temporal.ChronoUnit;
//import java.util.Collections;
//import java.util.List;
//
//public class PariplayServiceImplFull extends BridgeBase implements PariplayService {
//
//	final Logger log = LoggerFactory.getLogger(getClass());
//
//	static final int ERROR_USER_BLOCKED = 12;
//	static final int ERROR_INVALID_GAME = 7;
//	static final int ERROR_INVALID_ROUND = 9;
//	static final int ERROR_ROUND_ALREADY_CLOSED = 17;
//	static final int ERROR_TRANSACTION_ALREADY_CANCELLED = 18;
//	static final int ERROR_INSUFFICIENT_FUNDS = 1;
//	static final int ERROR_UNKNOWN_TRANSACTION_ID = 6;
//	static final int ERROR_INVALID_TOKEN = 8;
//	static final int ERROR_TRANSACTION_ALREADY_SETTLED = 11;
//	static final int ERROR_INVALID_NEGATIVE_AMOUNT = 3;
//	static final int ERROR_INVALID_USER_ID = 10;
//
//	@Autowired
//	RestTemplate ppTemplate;
//
//	@Autowired
//	PariplayConfiguration config;
//
//	@Override
//	@Transactional
//	public PPResponse authenticate(PPAuthenticationRequest req) {
//
//		var userStatus = getUserStatus(req.getPlayerId());
//		if (userStatus == PariplayUserStatus.UNKNOWN) {
//			return new PPResponse(ERROR_INVALID_USER_ID);
//		}
//        if (userStatus == PariplayUserStatus.BLOCKED) {
//            return new PPResponse(ERROR_USER_BLOCKED);
//        }
//
//        var token = getToken(req.getToken());
//		if (token == null) {
//			return new PPResponse(ERROR_INVALID_TOKEN);
//		}
//		if (!createPlayerIdFromUserId(token.getUserId()).equals(req.getPlayerId())) {
//			return new PPResponse(ERROR_INVALID_USER_ID);
//		}
//
//		var w = getWallet(token.getWalletId());
//		return new PPResponse(w.getBalance());
//	}
//
//	@Override
//	@Transactional
//	public PPResponse debit(PPTransactionRequest req) {
//		var userStatus = getUserStatus(req.getPlayerId());
//		if (userStatus == PariplayUserStatus.UNKNOWN) {
//			return new PPResponse(ERROR_INVALID_USER_ID);
//		}
//        if (userStatus == PariplayUserStatus.BLOCKED) {
//            return new PPResponse(ERROR_USER_BLOCKED);
//        }
//        var token = getToken(req.getToken());
//		if (token == null) {
//			return new PPResponse(ERROR_INVALID_TOKEN);
//		}
//		if (!createPlayerIdFromUserId(token.getUserId()).equals(req.getPlayerId())) {
//			return new PPResponse(ERROR_INVALID_USER_ID);
//		}
//
//		if (req.getAmount().signum() < 0) {
//			return new PPResponse(ERROR_INVALID_NEGATIVE_AMOUNT);
//		}
//		if (getGameIdFromGameCode(req.getGameCode()) == -1L) {
//			return new PPResponse(ERROR_INVALID_GAME);
//		}
//
//		// Check for round closed
//		if (allowAllWagersAfterRoundEnd(req)) {
//			var roundTransactionsForVendorAndUser = findRoundTransactionsForVendorAndUser(req.getRoundId(), config.getVendorName(), token.getUserId());
//			for (var t : roundTransactionsForVendorAndUser) {
//				if (t.getExternalTransactionRef().startsWith("CLOSED")) {
//					return new PPResponse(ERROR_ROUND_ALREADY_CLOSED);
//				}
//			}
//		}
//
//		// now create Bet transaction
//		var txr = new TxRequest();
//		txr.setType(TransactionType.BET);
//		txr.setExternalOrigId("" + req.getTransactionId());
//		txr.setExternal_tx_ref((req.getEndGame()) ? "CLOSED" : "OPEN" + ':' + req.getRoundId());
//		txr.setRoundRef(req.getRoundId());
//		txr.setBet(req.getAmount());
//		txr.setWin(BigDecimal.ZERO);
//		txr.setSessionId(token.getSessionId());
//		txr.setUserId(token.getUserId());
//		txr.setGameId(token.getGameId());
//		txr.setVendorName(config.getVendorName());
//		txr.setFailOnCancelledAlready(true);
//		TxResponse txResponse;
//		try {
//			txResponse = addTxFromProvider(txr);
//			if (txResponse.isIdempotent()) {
//				var one = getWallet(token.getWalletId());
//				return new PPResponse(ERROR_TRANSACTION_ALREADY_SETTLED, one.getBalance());
//			}
//		} catch (OuslyTransactionException e) {
//			if (e.getMessage().equals("already cancelled")) {
//				return new PPResponse(ERROR_TRANSACTION_ALREADY_CANCELLED);
//			}
//
//			if (e instanceof OuslyOutOfMoneyException) {
//				var bal = BigDecimal.ZERO;
//				try {
//					var one = getWallet(token.getWalletId());
//					bal = one.getBalance();
//				} catch (Exception e1) {
//					//
//				}
//				return new PPResponse(ERROR_INSUFFICIENT_FUNDS, bal);
//			}
//            log.error("Error on PariP Bet:{}", req, e);
//            return new PPResponse(900);
//        }
//
//		// finalise response
//		return new PPResponse(txResponse.getNewBalance(), txResponse.getTxId());
//	}
//
//
//	@Override @Transactional
//	public PPResponse credit(PPTransactionRequest req) {
//		var userStatus = getUserStatus(req.getPlayerId());
//		if (userStatus == PariplayUserStatus.UNKNOWN) {
//			return new PPResponse(ERROR_INVALID_USER_ID);
//		}
//        if (userStatus == PariplayUserStatus.BLOCKED) {
//            return new PPResponse(ERROR_USER_BLOCKED);
//        }
//        var token = getToken(req.getToken());
//		if (token == null) {
//			return new PPResponse(ERROR_INVALID_TOKEN);
//		}
//
//		if (!createPlayerIdFromUserId(token.getUserId()).equals(req.getPlayerId())) {
//			return new PPResponse(ERROR_INVALID_USER_ID);
//		}
//
//		if (req.getAmount().signum() < 0) {
//			return new PPResponse(ERROR_INVALID_NEGATIVE_AMOUNT);
//		}
//
//		var debitFound = false;
//		var closedRound = false;
//		var allCancelled = false;
//
//		var roundTransactionsForVendorAndUser = findRoundTransactionsForVendorAndUser(req.getRoundId(), config.getVendorName(), token.getUserId());
//		if (roundTransactionsForVendorAndUser.size() > 0) {
//			allCancelled = true;
//			for (var t : roundTransactionsForVendorAndUser) {
//				allCancelled = true;
//				if (t.getType() == TransactionType.BET) {
//					debitFound = !t.isCancelled();
//
//					if (t.getSessionId().longValue() != token.getSessionId().longValue()) {
//						return new PPResponse(ERROR_INVALID_USER_ID);
//					}
//					if (!req.getPlayerId().equals(createPlayerIdFromUserId(t.getUserId()))) {
//						return new PPResponse(ERROR_INVALID_USER_ID);
//					}
//
//					if (t.getGameId().longValue() != getGameIdFromGameCode(req.getGameCode()).longValue()) {
//						return new PPResponse(ERROR_INVALID_GAME);
//					}
//
//				}
//
//				if (t.getExternalTransactionRef().startsWith("CLOSED")) {
//					closedRound = true;
//				}
//				if (!t.isCancelled()) {
//					allCancelled = false;
//				}
//			}
//		}
//		if (!debitFound && !allowCreditWithoutDebit(req)) {
//			return new PPResponse(ERROR_INVALID_ROUND);
//		}
//
//		if (allCancelled) {
//			return new PPResponse(ERROR_INVALID_ROUND);
//		}
//
//		if (closedRound && allowAllWagersAfterRoundEnd(req)) {
//			return new PPResponse(ERROR_ROUND_ALREADY_CLOSED);
//		}
//
//		// now create Bet transaction
//
//		var txr = new TxRequest();
//		txr.setType(TransactionType.BET);
//		txr.setExternalOrigId("" + req.getTransactionId());
//		txr.setExternal_tx_ref((req.getEndGame()) ? "CLOSED" : "OPEN" + ':' + req.getRoundId());
//		txr.setRoundRef(req.getRoundId());
//		txr.setBet(BigDecimal.ZERO);
//		txr.setWin(req.getAmount());
//		txr.setSessionId(token.getSessionId());
//		txr.setUserId(token.getUserId());
//		txr.setGameId(token.getGameId());
//		txr.setVendorName(config.getVendorName());
//		TxResponse txResponse;
//		try {
//			txResponse = addTxFromProvider(txr);
//			if (txResponse.isIdempotent()) {
//				var one = getWallet(token.getWalletId());
//				return new PPResponse(ERROR_TRANSACTION_ALREADY_SETTLED, one.getBalance());
//
//			}
//
//
//		} catch (OuslyTransactionException e) {
//			log.error("Error while trying to add credit Tx: {}", req, e);
//			return new PPResponse(900);
//		}
//
//		// finalise response
//		return new
//
//				PPResponse(txResponse.getNewBalance(), txResponse.
//
//				getTxId());
//
//	}
//
//	private boolean allowAllWagersAfterRoundEnd(PPTransactionRequest req) {
//		return !checkForTransactionConfig(req, 6);
//	}
//
//	private boolean allowCreditWithoutDebit(PPTransactionRequest req) {
//		return checkForTransactionConfig(req, 3);
//	}
//
//	private boolean checkForTransactionConfig(PPTransactionRequest req, int tc) {
//		if (req.getTransactionConfiguration().length > 0) {
//			for (int i : req.getTransactionConfiguration()) {
//				if (i == tc) {
//					return true;
//				}
//			}
//		}
//		return false;
//	}
//
//	private Long getGameIdFromGameCode(String gameCode) {
//		var gameByKeyAndVendor = findGameByKeyAndVendor(gameCode, config.getVendorName());
//		if (gameByKeyAndVendor.isPresent()) {
//			return gameByKeyAndVendor.get().getId();
//		}
//        log.debug("Cannot find game for {}", gameCode);
//        return -1L;
//    }
//
//	@Override
//	@Transactional
//	public PPResponse balance(PPAuthenticationRequest req) {
//		var userStatus = getUserStatus(req.getPlayerId());
//		if (userStatus == PariplayUserStatus.UNKNOWN) {
//			return new PPResponse(ERROR_INVALID_USER_ID);
//		}
//		var token = getToken(req.getToken(), true);
//		if (token == null) {
//			return new PPResponse(ERROR_INVALID_TOKEN);
//		}
//		if (!createPlayerIdFromUserId(token.getUserId()).equals(req.getPlayerId())) {
//			return new PPResponse(ERROR_INVALID_USER_ID);
//		}
//		var w = getWallet(token.getWalletId());
//		return new PPResponse(w.getBalance());
//	}
//
//	@Override
//	@Transactional
//	public PPResponse endGame(PPEndGameRequest req) {
//		var userStatus = getUserStatus(req.getPlayerId());
//		if (userStatus == PariplayUserStatus.UNKNOWN) {
//			return new PPResponse(ERROR_INVALID_USER_ID);
//		}
//		var token = getToken(req.getToken(), true);
//		if (token == null) {
//			return new PPResponse(ERROR_INVALID_TOKEN);
//		}
//		if (!createPlayerIdFromUserId(token.getUserId()).equals(req.getPlayerId())) {
//			return new PPResponse(ERROR_INVALID_USER_ID);
//		}
//		if (!req.getGameCode().equals(token.getGameCode())) {
//			return new PPResponse(ERROR_INVALID_GAME);
//		}
//
//		var w = getWallet(token.getWalletId());
//		return new PPResponse(w.getBalance(), -1L * Math.abs(RandomUtils.nextLong()));
//	}
//
//	@Override
//	@Transactional
//	public PPResponse debitAndCredit(PPTransactionRequest req) {
//		var userStatus = getUserStatus(req.getPlayerId());
//		if (userStatus == PariplayUserStatus.UNKNOWN) {
//			return new PPResponse(ERROR_INVALID_USER_ID);
//		}
//        if (userStatus == PariplayUserStatus.BLOCKED) {
//            return new PPResponse(ERROR_USER_BLOCKED);
//        }
//        var token = getToken(req.getToken());
//		if (token == null) {
//			return new PPResponse(ERROR_INVALID_TOKEN);
//		}
//		if (!createPlayerIdFromUserId(token.getUserId()).equals(req.getPlayerId())) {
//			return new PPResponse(ERROR_INVALID_USER_ID);
//		}
//
//		if (!req.getGameCode().equals(token.getGameCode())) {
//			return new PPResponse(ERROR_INVALID_GAME);
//		}
//
//		if (req.getDebitAmount().signum() < 0 || req.getCreditAmount().signum() < 0) {
//			return new PPResponse(ERROR_INVALID_NEGATIVE_AMOUNT);
//		}
//		var roundTransactionsForVendorAndUser = findRoundTransactionsForVendorAndUser(req.getRoundId(), config.getVendorName(), token.getUserId());
//		var closedRound = false;
//		var allCancelled = false;
//		if (roundTransactionsForVendorAndUser.size() > 0) {
//			for (var t : roundTransactionsForVendorAndUser) {
//				if (t.getExternalTransactionRef().startsWith("CLOSED")) {
//					closedRound = true;
//					break;
//				}
//				if (!t.isCancelled()) {
//				}
//			}
//		}
//		if (closedRound) {
//			return new PPResponse(ERROR_ROUND_ALREADY_CLOSED);
//		}
//
//		// now create DIRECTWIN transaction
//		var txr = new TxRequest();
//		txr.setType(TransactionType.DIRECTWIN);
//		txr.setExternalOrigId("" + req.getTransactionId());
//		txr.setExternal_tx_ref("CLOSED" + ':' + req.getRoundId());
//		txr.setRoundRef(req.getRoundId());
//		txr.setBet(req.getDebitAmount());
//		txr.setWin(req.getCreditAmount());
//		txr.setSessionId(token.getSessionId());
//		txr.setUserId(token.getUserId());
//		txr.setGameId(token.getGameId());
//		txr.setVendorName(config.getVendorName());
//		TxResponse txResponse;
//		try {
//			txResponse = addTxFromProvider(txr);
//			if (txResponse.isIdempotent()) {
//				var one = getWallet(token.getWalletId());
//				return new PPResponse(ERROR_TRANSACTION_ALREADY_SETTLED, one.getBalance());
//
//			}
//		} catch (OuslyTransactionException e) {
//			if (e instanceof OuslyOutOfMoneyException) {
//				var bal = BigDecimal.ZERO;
//				try {
//					var one = getWallet(token.getWalletId());
//					bal = one.getBalance();
//				} catch (Exception e1) {
//					//
//				}
//				return new PPResponse(ERROR_INSUFFICIENT_FUNDS, bal);
//			}
//			log.error("Error while trying to add directwin Tx: {}", req, e);
//			return new PPResponse(900);
//		}
//
//		// finalise response
//		return new PPResponse(txResponse.getNewBalance(), txResponse.getTxId());
//	}
//
//	@Override
//	@Transactional
//	public PPResponse cancel(PPTransactionRequest req) {
//		var userStatus = getUserStatus(req.getPlayerId());
//		if (userStatus == PariplayUserStatus.UNKNOWN) {
//			return new PPResponse(ERROR_INVALID_USER_ID);
//		}
//		var token = getToken(req.getToken());
//		if (token == null) {
//			return new PPResponse(ERROR_INVALID_TOKEN);
//		}
//        if (!createPlayerIdFromUserId(token.getUserId()).equals(req.getPlayerId())) {
//            return new PPResponse(ERROR_INVALID_USER_ID);
//        }
//        if (!req.getGameCode().equals(token.getGameCode())) {
//            return new PPResponse(ERROR_INVALID_GAME);
//        }
//
//        Long userId;
//		if (token == null) {
//			userId = createUserIdFromPlayerId(req.getPlayerId());
//		} else {
//			userId = token.getUserId();
//		}
//
//		// NOW check if we need to cancel a single tx or a complete round
//
//		if (!req.getCancelEntireRound()) {
//			Transaction oldTx;
//			if (userId == -1L) {
//				oldTx = findByVendorAndOrigId(config.getVendorName(), req.getRefTransactionId());
//				if (oldTx != null) {
//					userId = oldTx.getUserId();
//				}
//			} else {
//				oldTx = findByUserIdAndVendorAndOrigId(userId, config.getVendorName(), req.getRefTransactionId());
//			}
//			var w = getWallet(userId);
//			if (oldTx != null) {
//				if (oldTx.isCancelled()) {
//					return new PPResponse(ERROR_TRANSACTION_ALREADY_SETTLED, w.getBalance());
//				}
//				var txResponse = performRollback(oldTx, w);
//				return new PPResponse(w.getBalance(), txResponse.getTxId());
//			}
//            return new PPResponse(ERROR_UNKNOWN_TRANSACTION_ID, w.getBalance());
//        }
//        List<Transaction> roundTransactionsForVendor;
//
//        if (userId == -1L) {
//            roundTransactionsForVendor = findRoundTransactionsForVendor(req.getRoundId(), config.getVendorName());
//            if (!roundTransactionsForVendor.isEmpty()) {
//                userId = roundTransactionsForVendor.get(0).getUserId();
//            }
//        } else {
//            roundTransactionsForVendor = findRoundTransactionsForVendorAndUser(req.getRoundId(), config.getVendorName(), userId);
//        }
//
//        TxResponse txResp = null;
//
//        for (var tx : roundTransactionsForVendor) {
//            var w = getWallet(userId);
//            if (!tx.isCancelled()) {
//                txResp = performRollback(tx, w);
//            } else {
//                return new PPResponse(ERROR_TRANSACTION_ALREADY_SETTLED, w.getBalance());
//            }
//        }
//        if (txResp != null) {
//            return new PPResponse(txResp.getNewBalance(), txResp.getTxId());
//        }
//        var w = getWallet(userId);
//        return new PPResponse(w.getBalance(), req.getTransactionId());
//    }
//
//	private String createPlayerIdFromUserId(Long userId) {
//		return "PP:" + userId;
//	}
//
//	private Long createUserIdFromPlayerId(String playerId) {
//		try {
//			return Long.valueOf(playerId.substring(3));
//		} catch (Exception e) {
//			return -1L;
//		}
//	}
//
//	@Override
//	@Transactional
//	public PPCreateTokenResponse createToken(PPCreateTokenRequest req) {
//
//		var userId = createUserIdFromPlayerId(req.getPlayerId());
//		CasinoUser tkUser = null;
//
//		if (userId != -1) {
//			tkUser = getCasinoUser(userId);
//		}
//
//		// 1.) Fallback
//		if (tkUser == null) {
//			var userByEmail = findUserByEmail(config.getTestTokenOwnerEmail());
//			if (userByEmail.isEmpty()) {
//				log.warn("Pariplay 'createToken' aborted since {} was not found in users", config.getTestTokenOwnerEmail());
//				return new PPCreateTokenResponse();
//			}
//		}
//
//		// ok, now find game
//		var game = findGameByKeyAndVendor(req.getGameCode(), config.getVendorName());
//		if (game.isEmpty()) {
//			log.warn("Pariplay 'createToken' aborted since {} was not found in games", req.getGameCode());
//			return new PPCreateTokenResponse();
//		}
//		var w = getWallet(userId);
//		var token = req.getToken();
//
//		var gi = new GameInstance();
//		gi.setActive(true);
//		gi.setAuthToken(token);
//		gi.setToken(token);
//		gi.setCreationTime(LocalDateTime.now());
//		gi.setUserId(tkUser.getId());
//		gi.setGameId(game.get().getId());
//		gi.setExpiryTime(Instant.now().plus(2, ChronoUnit.HOURS));
//		gi.setGameHtml("");
//		gi.setGameUrl("");
//		gi.setStartlevel(w.getLevel());
//		gi.setStartpercnl(w.getPercnl());
//		saveGameInstance(gi, true);
//		return new PPCreateTokenResponse();
//	}
//
//	@Override
//	public PPCreateTokenResponse closeGameRounds(PPCreateTokenRequest req) {
//		return new PPCreateTokenResponse();
//	}
//
//	@Override
//	public List<String> getVendorNames() {
//		return Collections.singletonList(config.getVendorName());
//	}
//
//	@Override
//	public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game, GamePlatform platform, GameSettings settings) {
//
//		var resp = performLaunchRequest(createPlayerIdFromUserId(user.getId()), game.getGameId(), settings);
//
//		var gameInstance = new GameInstance();
//
//		gameInstance.setGameUrl(resp.getUrl());
//		gameInstance.setToken(resp.getToken());
//		gameInstance.setAuthToken(resp.getToken());
//		gameInstance.setCreationTime(LocalDateTime.now());
//		gameInstance.setExpiryTime(getExpiryTime());
//		return gameInstance;
//
//	}
//
//	PPLaunchResponse performLaunchRequest(String playerId, String gameKey, GameSettings settings) {
//		var req = new PPLaunchRequest();
//		req.setAccount(new PPAccount(config.getUsername(), config.getPassword()));
//		req.setGameCode(gameKey);
//		req.setPlayerId(playerId);
//		req.setCurrencyCode(config.getActiveCurrency());
//		req.setLanguageCode(getLanguageCode(settings));
//		req.setCountryCode(getCountryCode(settings));
//		req.setPlayerRegulation(1);
//		req.setPlayerIP(getIpAdress(settings));
//
//		var uri = config.getLauncherurl() + "LaunchGame";
//		log.debug("LaunchResp before: sending POST TO {},with payload:{}", uri, req);
//		var resp = ppTemplate.postForEntity(uri, req, PPLaunchResponse.class);
//		log.debug("LaunchResp '{}':{}", resp.getStatusCode(), resp.getBody());
//		return resp.getBody();
//	}
//
//	private String getIpAdress(GameSettings settings) {
//		if (settings != null) {
//			var ip = settings.getIp();
//			if (ip != null && ip.length() >= 8) {
//				return ip;
//			}
//		}
//		return "127.0.0.1";
//	}
//
//	private String getCountryCode(GameSettings settings) {
//		if (settings != null && settings.getUserCountry() != null) {
//			return settings.getUserCountry().toUpperCase();
//		}
//		return "DE";
//	}
//
//	private String getLanguageCode(GameSettings settings) {
//		if (settings != null && settings.getLanguage() != null) {
//			return settings.getLanguage().toLowerCase();
//		}
//		return "en";
//	}
//
//	private PariplayUserStatus getUserStatus(String playerId) {
//		var userId = createUserIdFromPlayerId(playerId);
//		if (userId == -1L) {
//			return PariplayUserStatus.UNKNOWN;
//		}
//		var u = getCasinoUser(userId);
//		if (u == null) {
//			return PariplayUserStatus.UNKNOWN;
//		}
//		if (u.isBlocked()) {
//			return PariplayUserStatus.BLOCKED;
//		}
//		return PariplayUserStatus.OK;
//	}
//
//}

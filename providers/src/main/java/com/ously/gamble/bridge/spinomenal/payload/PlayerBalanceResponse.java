package com.ously.gamble.bridge.spinomenal.payload;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ously.gamble.bridge.spinomenal.SpinomenalUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class PlayerBalanceResponse {

//    {"Balance":200,"ErrorCode":0,"ErrorMessage":null,"TimeStamp": "20181101101048"}

    @JsonProperty("Balance")
    private BigDecimal balance = BigDecimal.ZERO;

    @JsonProperty("ErrorCode")
    private Integer errorCode = 0;

    @JsonProperty("ErrorMessage")
    private String errorMessage;

    @JsonProperty("TimeStamp")
    private String timeStamp = SpinomenalUtils.convertLocalDateTimeToTimestamp(LocalDateTime.now());

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    @JsonIgnore
    public static void setTimeStampAsDateTime(LocalDateTime ldt) {
        SpinomenalUtils.convertLocalDateTimeToTimestamp(ldt);
    }

    @Override
    public String toString() {
        return "PlayerBalanceResponse{" +
               "balance=" + balance +
               ", errorCode=" + errorCode +
               ", errorMessage='" + errorMessage + '\'' +
               ", timeStamp='" + timeStamp + '\'' +
               '}';
    }
}

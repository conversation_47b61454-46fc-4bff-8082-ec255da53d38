package com.ously.gamble.bridge.mascot;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.bridge.mascot.payload.*;
import org.javatuples.Pair;

@SuppressWarnings("rawtypes")
public final class MascotUtils {


    private MascotUtils() {
    }
    

    public static MSWithdrawRequest parseWithdrawRequest(ObjectMapper om, String jsonParams) throws JsonProcessingException {
        return om.readValue(jsonParams, JRPCMSWithdrawRequest.class).getParams();
    }

    public static MSWithdrawAndDepositRequest parseWithdrawAndDepositRequest(ObjectMapper om,
                                                                             String jsonParams) throws JsonProcessingException {
        return om.readValue(jsonParams, JRPCMSWithdrawAndDepositRequest.class).getParams();
    }

    public static MSDepositRequest parseDepositRequest(ObjectMapper om, String jsonParams) throws JsonProcessingException {
        return om.readValue(json<PERSON>ara<PERSON>, JRPCMSDepositRequest.class).getParams();
    }

    public static String toJson(ObjectMapper om, MSGenericJsonRpcResponse jsonRpcResponse) throws JsonProcessingException {
        return om.writeValueAsString(jsonRpcResponse);
    }

    public static MSRollbackRequest parseRollbackRequest(ObjectMapper om, String jsonParams) throws JsonProcessingException {
        return om.readValue(jsonParams, JRPCMSRollbackRequest.class).getParams();
    }

    public static MSBalanceRequest parseBalanceRequest(ObjectMapper om, String jsonParams) throws JsonProcessingException {
        return om.readValue(jsonParams, JRPCMSBalanceRequest.class).getParams();
    }

    public static Pair<String, Long> parseMethod(ObjectMapper om, String jsonReq) {
        try {
            var jsonNode = om.readTree(jsonReq);
            var method = jsonNode.get("method");
            var id = jsonNode.get("id");
            return Pair.with(method.textValue(), id.longValue());
        } catch (JsonProcessingException e) {
            return Pair.with("UNKNOWN", 0L);
        }
    }

    public static String toErrorJson(ObjectMapper om, MSJsonRpcResponse msJsonRpcResponse) throws JsonProcessingException {
        return om.writeValueAsString(msJsonRpcResponse);
    }
}

package com.ously.gamble.bridge.softswiss;

import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.BridgeHandler;
import com.ously.gamble.bridge.softswiss.payload.*;

public interface SoftswissService extends BridgeHandler {

    SSBalanceResponse getBalance(SSBalanceRequest req);

    SSBetWinResponse betWin(SSBetWinRequest req) throws OuslyTransactionException;

    SSFinishResponse finish(SSFinishRequest req);

    SSRollbackResponse rollback(SSRollbackRequest req) throws OuslyTransactionException;

    String getBalanceString(String playerId);
}

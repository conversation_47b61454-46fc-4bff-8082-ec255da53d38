package com.ously.gamble.bridge.amatic.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class AMDebitResponse extends AMTransactionResponse {
    public AMDebitResponse(long code, String msg) {
        super(code, msg);
    }

    public AMDebitResponse() {
        super();
    }
}

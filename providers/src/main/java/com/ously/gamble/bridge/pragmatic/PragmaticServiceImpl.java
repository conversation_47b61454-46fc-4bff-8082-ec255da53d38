package com.ously.gamble.bridge.pragmatic;

import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.api.session.TxResponse;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.pragmatic.payload.*;
import com.ously.gamble.bridge.pragmatic.payload.PPAuthenticateResponse.BetLimits;
import com.ously.gamble.conditions.ConditionalOnNotBridgeRouter;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import jakarta.persistence.OptimisticLockException;
import org.hibernate.StaleObjectStateException;
import org.hibernate.StaleStateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.*;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Service
@ConditionalOnBean(PragmaticConfiguration.class)
@ConditionalOnNotBridgeRouter
public class PragmaticServiceImpl extends BridgeBaseV2 implements PragmaticService {
    final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    PragmaticConfiguration config;

    @Autowired
    RestTemplate apiTemplate;


    @Override
    public boolean isAggregated() {
        return config.isAggregated();
    }

    @Override
    @Transactional
    public PPAuthenticateResponse authenticate(PPAuthenticateRequest req) {

        var token = getToken(req.getToken());
        if (token == null) {
            return new PPAuthenticateResponse(4L, "Token not found");
        }
        var w = getWallet(token.getWalletId());
        var resp = new PPAuthenticateResponse();
        resp.setBonus(BigDecimal.ZERO);
        resp.setCash(w.getBalance());
//        if (isAggregated()) {
        resp.setUserId(getAggregationKey() + "#PP:" + token.getUserId());
//        } else {
//            resp.setUserId("PP:" + token.getUserId());
//        }
        resp.setCountry("DE");
        resp.setJurisdiction("99");
        resp.setToken(req.getToken());
        resp.setCurrency(config.getActiveCurrency());
        if (isCustomBetsizeEnabled()) {
            resp.setBetLimits(createBetlimits(token.getBetLevel()));
        }
        return resp;
    }

    private static BetLimits createBetlimits(int betLevel) {
        var bl = new BetLimits();
        switch (betLevel) {
            case 0 -> {
                bl.setDefaultBet(BigDecimal.valueOf(0.01));
                bl.setMinBet(BigDecimal.valueOf(0.01));
                bl.setMaxBet(BigDecimal.valueOf(5));
                bl.setMinTotalBet(BigDecimal.valueOf(0.20));
                bl.setMaxTotalBet(BigDecimal.valueOf(10_000));
                bl.setExtMinTotalBet(BigDecimal.valueOf(10));
                bl.setExtMaxTotalBet(BigDecimal.valueOf(10_000));
            }
            case 1 -> {
                bl.setDefaultBet(BigDecimal.valueOf(0.25));
                bl.setMinBet(BigDecimal.valueOf(0.25));
                bl.setMaxBet(BigDecimal.valueOf(50));
                bl.setMinTotalBet(BigDecimal.valueOf(5));
                bl.setMaxTotalBet(BigDecimal.valueOf(100_000));
                bl.setExtMinTotalBet(BigDecimal.valueOf(100));
                bl.setExtMaxTotalBet(BigDecimal.valueOf(100_000));
            }
            case 2 -> {
                bl.setDefaultBet(BigDecimal.valueOf(50));
                bl.setMinBet(BigDecimal.valueOf(50));
                bl.setMaxBet(BigDecimal.valueOf(500));
                bl.setMinTotalBet(BigDecimal.valueOf(1000));
                bl.setMaxTotalBet(BigDecimal.valueOf(10_000_000));
                bl.setExtMinTotalBet(BigDecimal.valueOf(1000));
                bl.setExtMaxTotalBet(BigDecimal.valueOf(10_000_000));
            }
            case 3 -> {
                bl.setDefaultBet(BigDecimal.valueOf(250));
                bl.setMinBet(BigDecimal.valueOf(250));
                bl.setMaxBet(BigDecimal.valueOf(2500));
                bl.setMinTotalBet(BigDecimal.valueOf(5000));
                bl.setMaxTotalBet(BigDecimal.valueOf(50_000_000));
                bl.setExtMinTotalBet(BigDecimal.valueOf(5000));
                bl.setExtMaxTotalBet(BigDecimal.valueOf(50_000_000));
            }
            default -> {
                return null;
            }
        }
        return bl;
    }

    @Override
    @Transactional
    public PPGetBalanceResponse balance(PPGetBalanceRequest req) {

        var token = getToken(req.getToken());
        if (token == null) {
            return new PPGetBalanceResponse(4L, "Token not found");
        }
        var w = getWallet(token.getWalletId());
        var resp = new PPGetBalanceResponse();
        resp.setBonus(BigDecimal.ZERO);
        resp.setCash(w.getBalance());
        resp.setCurrency(config.getActiveCurrency());
        return resp;
    }

    @Override
    @Transactional
    @Retryable(retryFor = {ObjectOptimisticLockingFailureException.class,
            StaleObjectStateException.class
            , OptimisticLockException.class,
            SQLException.class},
            backoff = @Backoff(delay = 150))
    public PPBetResponse bet(PPBetRequest req) {

        var token = getToken(req.getToken());
        if (token == null) {
            return new PPBetResponse(4L, "Token not found");
        }

        // now create Bet transaction
        var txr = new TxRequest(token);
        txr.setRoundMode(RoundMode.AUTO);
        txr.setType(TransactionType.BET);
        txr.setExternalOrigId(req.getReference());
        txr.setRoundRef(req.getRoundId());
        txr.setBet(req.getAmount());
        txr.setWin(BigDecimal.ZERO);
        TxResponse txResponse;
        try {
            txResponse = addTxFromProvider(txr);
        } catch (OuslyTransactionException e) {
            log.debug("Error while trying to add debit Tx: {}", req, e);
            if (e instanceof OuslyOutOfMoneyException) {
                return new PPBetResponse(1L, "not enough funds");
            }
            log.error("Error on PP Bet:", e);
            return new PPBetResponse(100L, "unknownError");
        }

        // finalise response
        var resp = new PPBetResponse();
        resp.setBonus(BigDecimal.ZERO);
        if (txResponse.isIdempotent()) {
            var one = getWallet(token.getUserId());
            resp.setCash(one.getBalance());
        } else {
            resp.setCash(txResponse.getNewBalance());
        }
        resp.setCurrency(config.getActiveCurrency());
        resp.setTransactionId(Long.toString(txResponse.getTxId()));
        return resp;
    }

    @Override
    @Transactional
    @Retryable(retryFor =
            {ObjectOptimisticLockingFailureException.class,
                    StaleObjectStateException.class
                    , OptimisticLockException.class,
                    SQLException.class},
            backoff = @Backoff(delay = 150))
    public PPResultResponse result(PPResultRequest req) {
        var token = getToken(req.getToken());
        var sendLateHandlingMigration = false;

        if (token == null) {
            token = loadTokenBySessionToken(req.getToken()).orElse(null);
            if (token == null) {
                return new PPResultResponse(4L, "Token not found and player cannot be identified");
            }
            sendLateHandlingMigration = true;
        }

        // now create Win transaction
        var txr = new TxRequest(token);
        txr.setRoundMode(RoundMode.CLOSE);
        txr.setType(TransactionType.WIN);
        txr.setExternalOrigId(req.getReference());
        txr.setRoundRef(req.getRoundId());
        txr.setWin(req.getAmount());
        txr.setBet(BigDecimal.ZERO);
        TxResponse txResponse;
        try {
            txResponse = addTxFromProvider(txr);
        } catch (OuslyTransactionException e) {
            log.debug("Error while trying to add credit Tx: {}", req, e);
            log.error("Error on PP Result:", e);
            return new PPResultResponse(100L, "unknownError");
        }

        // finalise response
        var resp = new PPResultResponse();
        resp.setBonus(BigDecimal.ZERO);
        if (txResponse.isIdempotent()) {
            var one = getWallet(token.getUserId());
            resp.setCash(one.getBalance());
        } else {
            resp.setCash(txResponse.getNewBalance());
        }
        resp.setCurrency(config.getActiveCurrency());
        resp.setTransactionId(Long.toString(txResponse.getTxId()));
        if (sendLateHandlingMigration) {
            sendSessionMaintenanceRequestLateArrival(token.getUserId(), token.getSessionId());
        }
        return resp;
    }

    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 150))
    public PPRefundResponse refund(PPRefundRequest req) {
        var sendLateHandlingMigration = false;
        var token = getToken(req.getToken());
        if (token == null) {
            token = loadTokenBySessionToken(req.getToken()).orElse(null);
            if (token == null) {
                return new PPRefundResponse(4L, "Token not found and player cannot be identified");
            }
            sendLateHandlingMigration = true;
        }

        var uid = req.getReference();
        var byUserIdAndVendorAndOrigId = findBySessionIdAndOrigId(token.getUserId(), token.getSessionId(), uid);
        if (byUserIdAndVendorAndOrigId == null) {
            var resp = new PPRefundResponse(0, "Success");
            resp.setTransactionId(req.getReference());
            return resp;
        }
        if (!byUserIdAndVendorAndOrigId.isCancelled()) {
            var wallet = getWallet(token.getUserId());
            var txResponse = performRollback(byUserIdAndVendorAndOrigId, wallet);
            var resp = new PPRefundResponse(0, "Success");
            resp.setTransactionId(Long.toString(txResponse.getTxId()));
            if (sendLateHandlingMigration) {
                sendSessionMaintenanceRequestLateArrival(token.getUserId(), token.getSessionId());
            }
            return resp;
        }
        var t = findBySessionIdAndOrigId(token.getUserId(), token.getSessionId(), uid + ":ROLLBACK");
        var resp = new PPRefundResponse(0, "Success");
        resp.setTransactionId(t.getId().getTxId().toString());
        return resp;

    }

    @Override
    public List<String> getVendorNames() {
        return Arrays.asList(config.getVendorName(), "PG_REELKINGDOM");
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform,
                                                 GameSettings settings) throws Exception {

        log.info("CreateGame  for user {}, game {} platform={}, bl={}", user.getDisplayName(),
                game.getGameId(), platform, settings.getBetLevel());

        var token = UUID.randomUUID().toString();
        if (isAggregated()) {
            token = getAggregationKey() + "#" + token;
        }

        String gameUrl = null;
        if (platform != GamePlatform.TEST) {
            // create token call to spino
            var keyValue = createLaunchKey(token, game.getGameId(),
                    getLanguageCode(user, settings), "H5", platform.toString(), getAggregationKey() + "#PP:" + user.getId());

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            map.add("externalPlayerId", getAggregationKey() + "#PP:" + user.getId());
            map.add("language", getLanguageCode(user, settings));
            map.add("lobbyURL", getAppBaseUrl());
            map.add("platform", platform.toString());
            map.add("secureLogin", config.getStylename());
            map.add("stylename", config.getStylename());
            map.add("symbol", game.getGameId());
            map.add("technology", "H5");
            map.add("token", token);

            log.debug("Using {} as hash base", keyValue);
            log.debug("Adding secret {}", config.getHashSecret());
            var hashValue = keyValue + config.getHashSecret();
            map.add("hash", DigestUtils.md5DigestAsHex(hashValue.getBytes(StandardCharsets.UTF_8)));

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(map, headers);
            //
            log.debug("Trying to get LaunchUrl: {}", entity);

            ResponseEntity<PPGetUrlResponse> response =
                    apiTemplate.exchange(config.getLauncherurl(),
                            HttpMethod.POST,
                            entity,
                            PPGetUrlResponse.class);


            log.debug("Got {}", response);
            if (response.getStatusCode().is2xxSuccessful() && "0".equals(response.getBody().getError())) {
                gameUrl = response.getBody().getGameURL();
            } else {
                log.warn("Getting URL for pragmatic game {} resulted in error {}, {}", game.getGameId(), response.getBody().getError(), response.getBody().getDescription());
            }
        }

        var gameInstance = new GameInstance();
        gameInstance.setGameUrl(gameUrl);
        gameInstance.setToken(token);
        gameInstance.setAuthToken(token);
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }

    private String createLaunchKey(String token, String gameId, String languageCode,
                                   String technology, String platform, String extPlayerId) {
        // in the form of key1=value1&key2=value2
        var bld = new StringBuilder();
        addKeyParam("externalPlayerId", extPlayerId, bld).append("&");
        addKeyParam("language", languageCode, bld).append("&");
        addKeyParam("lobbyURL", getAppBaseUrl(), bld).append("&");
        addKeyParam("platform", platform, bld).append("&");
        addKeyParam("secureLogin", config.getStylename(), bld).append("&");
        addKeyParam("stylename", config.getStylename(), bld).append("&");
        addKeyParam("symbol", gameId, bld).append("&");
        addKeyParam("technology", technology, bld).append("&");
        addKeyParam("token", token, bld);
        return bld.toString();
    }

    private static StringBuilder addKeyParam(String key, String value, StringBuilder bld) {
        bld.append(key);
        bld.append("=");
        bld.append(value);
        return bld;
    }

    private static String getLanguageCode(CasinoUser user, GameSettings settings) {
        if (settings != null && settings.getLanguage() != null) {
            return settings.getLanguage().toLowerCase();
        }
        return "en";
    }
}

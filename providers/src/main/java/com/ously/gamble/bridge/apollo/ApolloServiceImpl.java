package com.ously.gamble.bridge.apollo;

import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.bridge.SessionCacheEntry;
import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.api.session.TxResponse;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.apollo.payload.*;
import com.ously.gamble.bridge.common.URIBuilder;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
@ConditionalOnBean(ApolloConfiguration.class)
public class ApolloServiceImpl extends BridgeBaseV2 implements ApolloService {

    final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    ApolloConfiguration config;

    @Autowired
    FeatureConfig fConfig;

    @Override
    @Transactional
    public APIdentifyResponse identify(APIdentifyRequest req) {
        log.debug("APL IDENTIFY: {}", req);
        var token = getToken(req.getToken());
        if (token == null) {
            return APIdentifyResponse.error(15, "auth token not found");
        }

        var w = getWallet(token.getWalletId());

        var resp = new APIdentifyResponse();
        resp.setBalance(w.getBalance());
        resp.setCurrency(config.getCurrency());
        resp.setGameMode(req.getGameMode());
        resp.setPlayerId(req.getPlayerId());
        resp.setGameName(req.getGameName());
        resp.setSessionId(token.getSessionToken());
        log.debug("APL IDENTIFY RESP:{}", resp);
        return resp;
    }

    private static String createPlayerId(Long userId) {
        return "APL-" + userId + '-' + System.currentTimeMillis() % 1_000L;
    }

    @Override
    @Transactional
    public APResponse balance(APBalanceRequest req) {
        log.debug("APL BALANCE: {}", req);

        var token = getToken(req.getSessionId());
        if (token == null) {
            token = findExpiredToken(req.getSessionId());

            if (token == null) {
                return APResponse.error(15, "auth token not found");
            }
        }
        var w = getWallet(token.getWalletId());
        var resp = new APResponse();
        resp.setBalance(w.getBalance());
        resp.setCurrency(config.getCurrency());

        log.debug("APL BALANCE RESP: {}", resp);
        return resp;
    }

    @Override
    @Transactional
    public APResponse closeSession(APCloseSessionRequest req) {
        return balance(new APBalanceRequest(req));
    }

    @Override
    @Transactional
    public APResponse notifySession(APNotifySessionRequest req) {
        log.debug("APL NOTIFY REQ: {}", req);
        var token = getToken(req.getSessionId());
        if (token == null) {
            return APResponse.error(15, "auth token not found");
        }
        if (token.getExpiry().isBefore(Instant.now().plus(5L, ChronoUnit.MINUTES))) {
            var gameInstance =
                    findByTokenAndActive(token.getSessionToken(), true);
            if (gameInstance != null) {
                gameInstance.setExpiryTime(Instant.now().plus(30L, ChronoUnit.MINUTES));
                saveGameInstance(gameInstance, true);
                expireToken(token.getSessionToken());
            }
        }
        var resp = new APResponse();
        log.debug("APL NOTIFY RESP: {}", resp);
        return resp;
    }

    @Override
    @Transactional
    public APBetResponse bet(APBetRequest req) {
        log.debug("APL BET REQ: {}", req);

        var token = getToken(req.getSessionId());
        if (token == null) {

            var optSession = findSessionByToken(req.getSessionId());
            if (optSession.isPresent()) {
                token = new SessionCacheEntry();
                token.setSessionToken(req.getSessionId());
                token.setWalletId(optSession.get().getUserId());
                token.setUserId(optSession.get().getUserId());
                token.setGameId(optSession.get().getGameId());
                token.setSessionId(optSession.get().getSessionId());
                token.setGp(optSession.get().getPlatform());
                token.setJurisdiction(optSession.get().getJurisdiction());
            } else {
                return APBetResponse.error(15, "auth token not found");
            }
        }

        //1. no or 1 win, add directwin
        if (req.getWin() == null || req.getWin().isEmpty()) {
            var resp = processBetNoWin(req, token);
            log.debug("APL BET RESP: {}", resp);
            return resp;
        }
        if (req.getWin() != null && !req.getWin().isEmpty()) {
            var betResp = processBetNoWin(req, token);
            if (betResp.getResult() != 0) {
                // Some error occured.
                log.debug("APL BET RESP: {}", betResp);
                return betResp;
            }
            // now process win(s)
            TxResponse txResp = null;
            var resp = new APBetResponse();
            for (var win : req.getWin()) {
                var txr = new TxRequest(token);
                txr.setRoundMode(RoundMode.AUTO);
                txr.setType(TransactionType.WIN);
                txr.setExternalOrigId(Long.toString(win.getAccountingId()));
                txr.setRoundRef(Long.toString(req.getAccountingId()));
                txr.setWin(win.getAmount());
                txr.setBet(BigDecimal.ZERO);
                try {
                    txResp = addTxFromProvider(txr);
                } catch (OuslyTransactionException e) {
                    log.error("Error on Apollo WIN Result:", e);
                    return APBetResponse.error(999, e.getMessage());
                }
                resp.addWinTransactionId(txResp.getTxId());
            }

            resp.setTransactionId(betResp.getTransactionId());
            resp.setBalance(txResp.getNewBalance());
            resp.setCurrency(config.getCurrency());
            return resp;
        }
        return APBetResponse.error(999, "should not happen");
    }

    private APBetResponse processBetNoWin(APBetRequest req, SessionCacheEntry token) {
        // now create Bet transaction
        var txr = new TxRequest(token);
        txr.setRoundMode(RoundMode.AUTO);
        txr.setType(TransactionType.BET);
        txr.setExternalOrigId(Long.toString(req.getAccountingId()));
        txr.setRoundRef(Long.toString(req.getAccountingId()));
        txr.setBet(req.getBet());
        txr.setWin(BigDecimal.ZERO);
        TxResponse txResponse;
        try {
            txResponse = addTxFromProvider(txr);
        } catch (OuslyTransactionException e) {
            if (e instanceof OuslyOutOfMoneyException) {
                return APBetResponse.error(3, "not enough funds");
            }
            log.error("Error on Apollo Result:", e);
            return APBetResponse.error(999, e.getMessage());
        }

        var resp = new APBetResponse();
        resp.setTransactionId(txResponse.getTxId());
        resp.setBalance(txResponse.getNewBalance());
        resp.setCurrency(config.getCurrency());
        return resp;
    }

    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(config.getVendorName());
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform,
                                                 GameSettings settings) throws Exception {

        log.info("CreateGame APOLLO for user {}, game {} platform={}", user.getDisplayName(), game.getGameId(), platform);

        String gameUrl = null;
        var token = UUID.randomUUID().toString();
        if (platform != GamePlatform.TEST) {
            // create token call to spino
            var bld = new URIBuilder(config.getLaunchURL());
            bld.addParameter("gameName", game.getGameId());
            bld.addParameter("authToken", token);
            bld.addParameter("playerId", createPlayerId(user.getId()));
            bld.addParameter("language", getLanguage(settings));
            bld.addParameter("currency", config.getCurrency());
            bld.addParameter("gameMode", "Real");
            if (platform == GamePlatform.WEB) {
                bld.addParameter("backUrl", fConfig.getAppBaseUrl() + '/');
            } else {
                bld.addParameter("backUrl", fConfig.getBaseUrl() + "/api/app/session/end/99");
            }
            gameUrl = bld.build().toString();
        }

        var
                gameInstance = new GameInstance();

        gameInstance.setGameUrl(gameUrl);
        gameInstance.setToken(token);
        gameInstance.setAuthToken(token);
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }

    private static String getLanguage(GameSettings settings) {
        return settings.getLanguage().toLowerCase();
    }
}

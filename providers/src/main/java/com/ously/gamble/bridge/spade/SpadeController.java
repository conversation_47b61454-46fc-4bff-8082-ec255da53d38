package com.ously.gamble.bridge.spade;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.bridge.spade.payload.SPAuthRequest;
import com.ously.gamble.bridge.spade.payload.SPBalanceRequest;
import com.ously.gamble.bridge.spade.payload.SPResponse;
import com.ously.gamble.bridge.spade.payload.SPTransferRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(SpadeConfiguration.BRIDGE_SPADE)
@ConditionalOnBean(SpadeConfiguration.class)
public class SpadeController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    SpadeService srv;

    @Autowired
    ObjectMapper om;

    @PostMapping(value = "/api", produces = "application/json", consumes = "application/json")
    @ResponseBody
    public SPResponse api(@RequestBody String req, @RequestHeader("API") String method) {
        SPResponse resp = null;
        try {
            switch (method) {
                case "authorize" -> resp = auth(req);
                case "getBalance" -> resp = getPlayerBalance(req);
                case "transfer" -> resp = transfer(req);
                default -> log.error("Unknown api method: {} request: {}", method, req);
            }
        } catch (JsonProcessingException jE) {
            log.error("Error deserializing method {} request: {}", method, req);
        }

        //noinspection CatchMayIgnoreException
        try {
            log.debug("Serialized to:{}", om.writeValueAsString(resp));
        } catch (Exception e) {

        }

        log.debug("SPADE CTRL RESP:{}", resp);
        return resp;
    }


    public SPResponse getPlayerBalance(String req) throws JsonProcessingException {
        var bReq = om.readValue(req, SPBalanceRequest.class);
        return srv.balance(bReq);
    }

    public SPResponse auth(String req) throws JsonProcessingException {
        var aReq = om.readValue(req, SPAuthRequest.class);
        return srv.auth(aReq);
    }

    public SPResponse transfer(String req) throws JsonProcessingException {
        var tReq = om.readValue(req, SPTransferRequest.class);
        return srv.transfer(tReq);
    }


}

package com.ously.gamble.bridge.spinomenal;

import com.ously.gamble.api.bridge.BridgeConfiguration;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "spinomenal")
@ConditionalOnProperty(
        value = "spinomenal.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class SpinomenalConfiguration implements BridgeConfiguration {
    public static final String VENDOR_NAME = "SPINOMENAL";

    public static final String BRIDGE_SPINOMENAL = "/bridge/spinomenal";

    private String privateKey = "dsf3454324";

    private String partnerId = "gameplay-dev";

    private Integer tokenExpiryInHours = 24;

    //        private String url = "https://sgs-api.spinomenal-dev.com/GameLauncher/LaunchSocial";
    private String url = "https://api-social.spinomenal.co/GameLauncher/LaunchSocial";


    public String createSignature(String stringWithoutPK) {
        var unhashed = stringWithoutPK + privateKey;
        return DigestUtils.md5Hex(unhashed);
    }

    public String getBaseUrl() {
        return url;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public Integer getTokenExpiryInHours() {
        return tokenExpiryInHours;
    }

    public void setTokenExpiryInHours(Integer tokenExpiryInHours) {
        this.tokenExpiryInHours = tokenExpiryInHours;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String getVendorName() {
        return VENDOR_NAME;
    }

    @Override
    public String getBridgeName() {
        return BRIDGE_SPINOMENAL;
    }

    @Override
    public String getAuth_user() {
        return null;
    }

    @Override
    public String getAuth_password() {
        return null;
    }

    @Override
    public List<String> getWhitelistIPs() {
        return Collections.emptyList();
    }
}

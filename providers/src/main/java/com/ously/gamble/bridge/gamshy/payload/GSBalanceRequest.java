package com.ously.gamble.bridge.gamshy.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class GSBalanceRequest {
    GSGame game;
    GSOperator operator;
    GSLUser user;
    GSCustomState customState;
    GSWallet wallet;

    public GSGame getGame() {
        return game;
    }

    public void setGame(GSGame game) {
        this.game = game;
    }

    public GSOperator getOperator() {
        return operator;
    }

    public void setOperator(GSOperator operator) {
        this.operator = operator;
    }

    public GSLUser getUser() {
        return user;
    }

    public void setUser(GSLUser user) {
        this.user = user;
    }

    public GSCustomState getCustomState() {
        return customState;
    }

    public void setCustomState(GSCustomState customState) {
        this.customState = customState;
    }

    public GSWallet getWallet() {
        return wallet;
    }

    public void setWallet(GSWallet wallet) {
        this.wallet = wallet;
    }

    @Override
    public String toString() {
        return "GSBalanceRequest{" +
                "game=" + game +
                ", operator=" + operator +
                ", user=" + user +
                ", customState=" + customState +
                ", wallet=" + wallet +
                '}';
    }
}

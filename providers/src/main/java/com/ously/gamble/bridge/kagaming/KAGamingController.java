package com.ously.gamble.bridge.kagaming;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.bridge.kagaming.payload.*;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.crypto.Mac;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

@RestController
@RequestMapping(KAGamingConfiguration.BRIDGE_KAGAMING)
@ConditionalOnBean(KAGamingConfiguration.class)
public class KAGamingController {

    final Logger log = LoggerFactory.getLogger(getClass());

    private final ObjectMapper om;

    private final KAGamingService service;

    private final Mac hmacSHA256;

    @Autowired
    public KAGamingController(KAGamingService srv, KAGamingConfiguration conf, ObjectMapper om) {
        this.service = srv;
        this.om = om;
        hmacSHA256 = HmacUtils.getInitializedMac(HmacAlgorithms.HMAC_SHA_256, conf.getSecretKey().getBytes(StandardCharsets.UTF_8));
    }


    @PostMapping(value = "/start", consumes = "application/json", produces = "application/json")
    @ResponseBody
    public ResponseEntity<KAStartResponse> start(@RequestBody String req, @RequestParam("hash") String hash) {

        // Check Hash
        log.debug("KA-start: {}", req);
        KAStartResponse resp;
        if (checkhash(req, hash)) {
            resp = new KAStartResponse(3, "invalid hash");
        } else {
            try {
                resp = service.start(om.readValue(req, KAStartRequest.class));
            } catch (JsonProcessingException e) {
                log.warn("KA:Error converting {} to KAStartRequest", req, e);
                resp = new KAStartResponse(2, e.getMessage());
            }
        }
        log.debug("KA-start-resp: {}", resp);
        return ResponseEntity.ok(resp);
    }

    @PostMapping(value = "/balance", consumes = "application/json", produces = "application/json")
    @ResponseBody
    public ResponseEntity<KABalanceResponse> balance(@RequestBody String req, @RequestParam("hash") String hash) {

        // Check Hash
        log.debug("KA-balance: {}", req);
        KABalanceResponse resp;
        if (checkhash(req, hash)) {
            resp = new KABalanceResponse(3, "invalid hash");
        } else {
            try {
                resp = service.balance(om.readValue(req, KABalanceRequest.class));
            } catch (JsonProcessingException e) {
                log.warn("KA:Error converting {} to KABalanceRequest", req, e);
                resp = new KABalanceResponse(2, e.getMessage());
            }
        }
        log.debug("KA-balance-resp: {}", resp);
        return ResponseEntity.ok(resp);
    }

    @PostMapping(value = "/play", consumes = "application/json", produces = "application/json")
    @ResponseBody
    public ResponseEntity<KAPlayResponse> play(@RequestBody String req, @RequestParam("hash") String hash) {

        // Check Hash
        log.debug("KA-play: {}", req);
        KAPlayResponse resp;
        if (checkhash(req, hash)) {
            resp = new KAPlayResponse(3, "invalid hash");
        } else {
            try {
                resp = service.play(om.readValue(req, KAPlayRequest.class));
            } catch (JsonProcessingException e) {
                log.warn("KA:Error converting {} to KAPlayRequest", req, e);
                resp = new KAPlayResponse(2, e.getMessage());
            }
        }
        log.debug("KA-play-resp: {}", resp);
        return ResponseEntity.ok(resp);
    }

    @PostMapping(value = "/credit", consumes = "application/json", produces = "application/json")
    @ResponseBody
    public ResponseEntity<KACreditResponse> credit(@RequestBody String req, @RequestParam("hash") String hash) {

        // Check Hash
        log.debug("KA-credit: {}", req);
        KACreditResponse resp;
        if (checkhash(req, hash)) {
            resp = new KACreditResponse(3, "invalid hash");
        } else {
            try {
                resp = service.credit(om.readValue(req, KACreditRequest.class));
            } catch (JsonProcessingException e) {
                log.warn("KA:Error converting {} to KACreditRequest", req, e);
                resp = new KACreditResponse(2, e.getMessage());
            }
        }
        log.debug("KA-credit-resp: {}", resp);
        return ResponseEntity.ok(resp);
    }

    @PostMapping(value = "/revoke", consumes = "application/json", produces = "application/json")
    @ResponseBody
    public ResponseEntity<KARevokeResponse> revoke(@RequestBody String req, @RequestParam("hash") String hash) {

        // Check Hash
        log.debug("KA-revoke: {}", req);
        KARevokeResponse resp;
        if (checkhash(req, hash)) {
            resp = new KARevokeResponse(3, "invalid hash");
        } else {
            try {
                resp = service.revoke(om.readValue(req, KARevokeRequest.class));
            } catch (JsonProcessingException e) {
                log.warn("KA:Error converting {} to KARevokeRequest", req, e);
                resp = new KARevokeResponse(2, e.getMessage());
            }
        }
        log.debug("KA-revoke-resp: {}", resp);
        return ResponseEntity.ok(resp);
    }

    @PostMapping(value = "/end", consumes = "application/json", produces = "application/json")
    @ResponseBody
    public ResponseEntity<KAEndResponse> end(@RequestBody String req, @RequestParam("hash") String hash) {

        // Check Hash
        log.debug("KA-end: {}", req);
        KAEndResponse resp;
        if (checkhash(req, hash)) {
            resp = new KAEndResponse(3, "invalid hash");
        } else {
            try {
                resp = service.end(om.readValue(req, KAEndRequest.class));
            } catch (JsonProcessingException e) {
                log.warn("KA:Error converting {} to KAEndRequest", req, e);
                resp = new KAEndResponse(2, e.getMessage());
            }
        }
        log.debug("KA-end-resp: {}", resp);
        return ResponseEntity.ok(resp);
    }

    private synchronized boolean checkhash(String req, String hash) {
        try {
            return !Arrays.equals(hmacSHA256.doFinal(req.getBytes(StandardCharsets.UTF_8)), Hex.decodeHex(hash));
        } catch (DecoderException e) {
            log.warn("Error checking hash:", e);
        }
        return true;
    }


}

package com.ously.gamble.bridge.gamzix.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class GZTransactionRequest {
	String token;
	String type;
	String tid;
	String originalTid;
	String pid;
	String rid;
	Long gid;
	Long bet;
	Long win;
	Long amount;

    public String getToken() {
		return token;
	}

	public void setToken(final String token) {
		this.token = token;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	public String getOriginalTid() {
		return originalTid;
	}

	public void setOriginalTid(String originalTid) {
		this.originalTid = originalTid;
	}

	public String getPid() {
		return pid;
	}

	public void setPid(String pid) {
		this.pid = pid;
	}

	public String getRid() {
		return rid;
	}

	public void setRid(String rid) {
		this.rid = rid;
	}

	public Long getGid() {
		return gid;
	}

	public void setGid(Long gid) {
		this.gid = gid;
	}

	public Long getBet() {
		return bet;
	}

	public void setBet(Long bet) {
		this.bet = bet;
	}

	public Long getWin() {
		return win;
	}

	public void setWin(Long win) {
		this.win = win;
	}

	public Long getAmount() {
		return amount;
	}

	public void setAmount(Long amount) {
		this.amount = amount;
	}


	@Override
	public String toString() {
		return "GZTransactionRequest{" + "token='" + token + '\'' + ", type='" + type + '\'' + ", tid='" + tid + '\'' + ", originalTid='" + originalTid + '\'' + ", pid='" + pid + '\'' + ", rid='" + rid + '\'' + ", gid=" + gid + ", bet=" + bet + ", win=" + win + ", amount=" + amount + '}';
	}
}

package com.ously.gamble.bridge.microgaming;

import com.ously.gamble.api.bridge.BridgeConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "microgaming")
@ConditionalOnProperty(
        value = "microgaming.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class MicrogamingConfiguration implements BridgeConfiguration {

    public static final String VENDOR_NAME = "MICROGAMING";
    public static final String BRIDGE_MICROGAMING = "/bridge/microgaming";

    private String applicationId = "4123";
    private String serverId = "32799";

    private String callerId = "none";
    private String callerPW = "none";

    private String baseUrl = "https://redirector32.valueactive.eu/Casino/Default.aspx";
    private String apiKey = "589f5da6-a07f-4fea-8778-687a9081038f";
    private String getTokenUrl = "https://operatorsecurityuat.valueactive.eu/System/OperatorSecurity/v1/operatortokens";
    private String reconUrl = "https://api32.api.valueactive.eu/ExternalOperators/Reconciliation/v1/api/pendingTransactions/";
    private String casinoName = "OUSLY";
    private String currency = "QFD";
    private boolean reconciliation;

    public String getGetTokenUrl() {
        return getTokenUrl;
    }

    public void setGetTokenUrl(String getTokenUrl) {
        this.getTokenUrl = getTokenUrl;
    }

    public String getReconUrl() {
        return reconUrl;
    }

    public void setReconUrl(String reconUrl) {
        this.reconUrl = reconUrl;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getServerId() {
        return serverId;
    }

    public void setServerId(String serverId) {
        this.serverId = serverId;
    }

    public void setCallerId(String callerId) {
        this.callerId = callerId;
    }

    public void setCallerPW(String callerPW) {
        this.callerPW = callerPW;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public void setCasinoName(String casinoName) {
        this.casinoName = casinoName;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrency() {
        return currency;
    }

    public String getCallerId() {
        return callerId;
    }

    public String getCallerPW() {
        return callerPW;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public String getCasinoName() {
        return casinoName;
    }

    @Override
    public String getVendorName() {
        return VENDOR_NAME;
    }

    @Override
    public String getBridgeName() {
        return BRIDGE_MICROGAMING;
    }

    @Override
    public String getAuth_user() {
        return null;
    }

    @Override
    public String getAuth_password() {
        return null;
    }

    @Override
    public List<String> getWhitelistIPs() {
        return new ArrayList<>();
    }

    public boolean isReconciliation() {
        return reconciliation;
    }

    public void setReconciliation(boolean reconciliation) {
        this.reconciliation = reconciliation;
    }
}

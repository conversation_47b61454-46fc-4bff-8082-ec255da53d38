package com.ously.gamble.bridge.pragmatic.payload;

import org.springframework.util.MultiValueMap;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

public class PPRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    public PPRequest(MultiValueMap<String, String> params) {
        this.hash = getFromParams("hash", params);
        this.userId = getFromParams("userId", params);
        this.token = getFromParams("token", params);
        this.providerId = getFromParams("providerId", params);
    }

    protected String getFromParams(String key, MultiValueMap<String, String> params) {
        var val = params.getFirst(key);
        return Objects.requireNonNullElse(val, "");
    }

    protected String getFromParamsNum(String key, MultiValueMap<String, String> params) {
        var val = params.getFirst(key);
        return Objects.requireNonNullElse(val, "0");
    }

    String hash;
    String userId;
    String token;
    String providerId;

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getProviderId() {
        return providerId;
    }

    public void setProviderId(String providerId) {
        this.providerId = providerId;
    }

    @Override
    public String toString() {
        return "PPRequest{" +
                "hash='" + hash + '\'' +
                ", userId='" + userId + '\'' +
                ", token='" + token + '\'' +
                ", providerId='" + providerId + '\'' +
                '}';
    }
}

package com.ously.gamble.bridge.netent.payload;

import java.math.BigDecimal;

public class NEGameRoundRequest {

    private String playerId;
    private BigDecimal betAmount;
    private BigDecimal winAmount;
    private Long gameRoundId;
    private String gameId;

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public String getPlayerId() {
        return playerId;
    }

    public void setPlayerId(String playerId) {
        this.playerId = playerId;
    }

    public BigDecimal getBetAmount() {
        return betAmount;
    }

    public void setBetAmount(BigDecimal betAmount) {
        this.betAmount = betAmount;
    }

    public BigDecimal getWinAmount() {
        return winAmount;
    }

    public void setWinAmount(BigDecimal winAmount) {
        this.winAmount = winAmount;
    }

    public Long getGameRoundId() {
        return gameRoundId;
    }

    public void setGameRoundId(Long gameRoundId) {
        this.gameRoundId = gameRoundId;
    }

    @Override
    public String toString() {
        return "NEGameRoundRequest{" +
                "playerId='" + playerId + '\'' +
                ", betAmount=" + betAmount +
                ", winAmount=" + winAmount +
                ", gameRoundId=" + gameRoundId +
                ", gameId='" + gameId + '\'' +
                '}';
    }
}

package com.ously.gamble.bridge.ballywulff.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import java.util.ArrayList;
import java.util.List;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class BWResponse<T> {

    Long status = 200L;
    T payload;
    List<String> errors;

    public BWResponse() {
    }

    public BWResponse(T payload) {
        this.payload = payload;
    }

    public BWResponse(T payload, Long status) {
        this.payload = payload;
        this.status = status;
    }

    public BWResponse(String error, Long status) {
        this.errors = new ArrayList<>();
        errors.add(error);
        this.status = status;
    }

    public List<String> getErrors() {
        return errors;
    }

    public void setErrors(List<String> errors) {
        this.errors = errors;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public T getPayload() {
        return payload;
    }

    public void setPayload(T payload) {
        this.payload = payload;
    }

    @Override
    public String toString() {
        return "BWResponse{" +
                "status=" + status +
                ", payload=" + payload +
                ", errors=" + errors +
                '}';
    }
}

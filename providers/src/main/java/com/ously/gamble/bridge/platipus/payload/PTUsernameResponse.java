package com.ously.gamble.bridge.platipus.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class PTUsernameResponse {

    Long userId;
    String username;
    Integer errorCode;

    public PTUsernameResponse() {
    }

    public PTUsernameResponse(int code) {
        this.errorCode = code;
    }

    public PTUsernameResponse(Long id, String uname) {
        this.username = uname;
        this.userId = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    @Override
    public String toString() {
        return "PTUsernameResponse{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", errorCode=" + errorCode +
                '}';
    }
}

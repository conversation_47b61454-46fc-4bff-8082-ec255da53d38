package com.ously.gamble.bridge.bgaming.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class BGCreateSessionRequest {
    @JsonProperty("casino_id")
    String casinoId;

    String game;

    String currency;

    String locale;

    String ip;
    @JsonProperty("client_type")
    String clientType;

    Long balance;

    BGUrls urls;

    BGUser user;

    public String getCasinoId() {
        return casinoId;
    }

    public void setCasinoId(String casinoId) {
        this.casinoId = casinoId;
    }

    public String getGame() {
        return game;
    }

    public void setGame(String game) {
        this.game = game;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    public BGUrls getUrls() {
        return urls;
    }

    public void setUrls(BGUrls urls) {
        this.urls = urls;
    }

    public BGUser getUser() {
        return user;
    }

    public void setUser(BGUser user) {
        this.user = user;
    }

    @Override
    public String toString() {
        return "BGCreateSessionRequest{" +
                "casinoId='" + casinoId + '\'' +
                ", game='" + game + '\'' +
                ", currency='" + currency + '\'' +
                ", locale='" + locale + '\'' +
                ", ip='" + ip + '\'' +
                ", clientType='" + clientType + '\'' +
                ", balance=" + balance +
                ", urls=" + urls +
                ", user=" + user +
                '}';
    }
}

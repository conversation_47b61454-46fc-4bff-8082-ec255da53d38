package com.ously.gamble.bridge.oryxgaming.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class OGGameTransactionRequest extends OGBaseRequest {

    private String playerId;
    private String roundId;
    private String roundAction;
    private String freeRoundId;
    private String freeRoundExternalId;
    private OGTransaction bet;
    private OGTransaction win;

    public String getPlayerId() {
        return playerId;
    }

    public void setPlayerId(String playerId) {
        this.playerId = playerId;
    }

    public String getRoundId() {
        return roundId;
    }

    public void setRoundId(String roundId) {
        this.roundId = roundId;
    }

    public String getRoundAction() {
        return roundAction;
    }

    public void setRoundAction(String roundAction) {
        this.roundAction = roundAction;
    }

    public String getFreeRoundId() {
        return freeRoundId;
    }

    public void setFreeRoundId(String freeRoundId) {
        this.freeRoundId = freeRoundId;
    }

    public String getFreeRoundExternalId() {
        return freeRoundExternalId;
    }

    public void setFreeRoundExternalId(String freeRoundExternalId) {
        this.freeRoundExternalId = freeRoundExternalId;
    }

    public OGTransaction getBet() {
        return bet;
    }

    public void setBet(OGTransaction bet) {
        this.bet = bet;
    }

    public OGTransaction getWin() {
        return win;
    }

    public void setWin(OGTransaction win) {
        this.win = win;
    }

    @Override
    public String toString() {
        return "OGGameTransactionRequest{" +
                super.toString() + ',' +
                "playerId='" + playerId + '\'' +
                ", roundId='" + roundId + '\'' +
                ", roundAction='" + roundAction + '\'' +
                ", freeRoundId='" + freeRoundId + '\'' +
                ", freeRoundExternalId='" + freeRoundExternalId + '\'' +
                ", bet=" + bet +
                ", win=" + win +
                '}';
    }
}

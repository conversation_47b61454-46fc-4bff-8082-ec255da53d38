package com.ously.gamble.bridge.eurasian.payload;

import java.util.List;

public class EAGGResponse {
    Integer status;
    List<EAGGGame> response;

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(final Integer status) {
        this.status = status;
    }

    public List<EAGGGame> getResponse() {
        return this.response;
    }

    public void setResponse(final List<EAGGGame> response) {
        this.response = response;
    }

    @Override
    public String toString() {
        return "EAGGResponse{" +
                "status=" + status +
                ", response=" + response +
                '}';
    }
}

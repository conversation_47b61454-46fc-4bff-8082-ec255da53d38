package com.ously.gamble.bridge.tomhorn;

import com.ously.gamble.api.bridge.BridgeConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "tomhorn")
@ConditionalOnProperty(
        value = "tomhorn.enabled",
        havingValue = "true",
        matchIfMissing = true)
public class TomHornConfiguration implements BridgeConfiguration {

    public static final String VENDOR_NAME = "TOMHORN";
    public static final String BRIDGE_TOMHORN = "/bridge/tomhorn";

    private String partnerId = "F7F58D4E-6757-4E0E-9851-FD536C5F98DA";

    private String signKey = "8QVX4rZnU79ShTVk";

    private String currency = "EUR";

    private String apiIntegrationBaseUrl = "https://staging.tomhorngames.com/services/gms/RestCustomerIntegrationService.svc";

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getSignKey() {
        return signKey;
    }

    public void setSignKey(String signKey) {
        this.signKey = signKey;
    }


    @Override
    public String getVendorName() {
        return VENDOR_NAME;
    }

    @Override
    public String getBridgeName() {
        return BRIDGE_TOMHORN;
    }

    @Override
    public String getAuth_user() {
        return null;
    }

    @Override
    public String getAuth_password() {
        return null;
    }

    @Override
    public List<String> getWhitelistIPs() {
        return Collections.emptyList();
    }

    public String getApiIntegrationBaseUrl() {
        return apiIntegrationBaseUrl;
    }

    public void setApiIntegrationBaseUrl(String apiIntegrationBaseUrl) {
        this.apiIntegrationBaseUrl = apiIntegrationBaseUrl;
    }
}

package com.ously.gamble.bridge.eurasian;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.AvailableGame;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.SessionSearchRequest;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.common.URIBuilder;
import com.ously.gamble.bridge.eurasian.payload.EACreateGameResponse;
import com.ously.gamble.bridge.eurasian.payload.EAGGResponse;
import com.ously.gamble.bridge.eurasian.payload.EAResponse;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@ConditionalOnBean(EurasianConfiguration.class)
public class EurasianServiceImpl extends BridgeBaseV2 implements EurasianService {

    final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    EurasianConfiguration config;

    @Autowired
    ObjectMapper om;

    @Autowired
    RestTemplate eurasianTemplate;

    @Override
    @Transactional
    public EAResponse balance(String remoteId, String sessionId, String remoteData) {

        log.debug("EA-Balance: {},{},{}", remoteId, sessionId, remoteData);
        var token = getToken("EA:" + sessionId);
        if (token == null) {
            return new EAResponse(500, "session invalid", null);
        }
        var one = getWallet(token.getWalletId());
        var result = new EAResponse(one.getBalance());
        log.debug("EA-Balance RES: {}", result);
        return result;
    }

    @Override
    @Transactional
    public EAResponse debit(String remoteId, String sessionId, BigDecimal amount, String actionType,
                            String transactionId, String roundId, String remoteData,
                            Long timestamp) {

        log.debug("EA-debit: {},{},{},{},{}", remoteId, sessionId, remoteData, amount, actionType);
        var token = getToken("EA:" + sessionId);
        if (token == null) {
            return new EAResponse(500, "session invalid", null);
        }
        var txr = new TxRequest(token);
        txr.setRoundMode(RoundMode.AUTO);
        txr.setType(TransactionType.BET);
        txr.setExternalOrigId(transactionId);
        txr.setRoundRef(roundId);
        txr.setBet(amount);
        txr.setWin(BigDecimal.ZERO);
        EAResponse resp;
        try {
            var txResponse = addTxFromProvider(txr);
            resp = new EAResponse(200, null, txResponse.getNewBalance());
        } catch (OuslyTransactionException e) {
            var w = getWallet(token.getWalletId());
            if (e instanceof OuslyOutOfMoneyException) {
                resp = new EAResponse(503, "not enough funds", w.getBalance());
            } else {
                resp = new EAResponse(500, "unknown error:" + e.getLocalizedMessage(), w.getBalance());
            }
        }
        log.debug("EA-Debit: {}", resp);
        return resp;
    }

    @Override
    @Transactional
    public EAResponse credit(String remoteId, String sessionId, BigDecimal amount,
                             String actionType, String transactionId, String roundId,
                             String remoteData, Long timestamp) {
        log.debug("EA-credit: {},{},{},{},{}", remoteId, sessionId, remoteData, amount, actionType);
        var token = getToken("EA:" + sessionId);

        Long userId;
        long gameId;
        long ssId;
        var gp = GamePlatform.UNKNOWN;

        if (token == null) {
            // try to find session. At least we have the sessionId which should simplify things
            userId = createUserIdFromRemoteId(remoteId);
            if (userId == null) {
                return new EAResponse(500, "Invalid user given as remoteId for credit", null);
            }
            var sessionCandidates = findSessionByTokenAndRoundReference("EA:" + sessionId, userId, roundId);
            if (sessionCandidates.isEmpty()) {
                return new EAResponse(500, "session invalid", null);
            }
            var ss = sessionCandidates.getFirst();
            gameId = ss.getGameId();
            ssId = ss.getSessionId();
            gp = ss.getPlatform();
        } else {
            userId = token.getUserId();
            gameId = token.getGameId();
            ssId = token.getSessionId();
            gp = token.getGp();
        }

        // we do not need to store zerowins
        if (amount.signum() == 0) {
            var w = getWallet(userId);
            var resp = new EAResponse(200, null, w.getBalance());
            log.debug("EA-credit - zero win, no tx written");
            return resp;
        }

        var txr = (token == null) ? new TxRequest() : new TxRequest(token);
        txr.setRoundMode(RoundMode.AUTO);
        txr.setGp(gp);
        txr.setType(TransactionType.WIN);
        txr.setExternalOrigId(transactionId);
        txr.setRoundRef(roundId);
        txr.setWin(amount);
        txr.setBet(BigDecimal.ZERO);
        txr.setSessionId(ssId);
        txr.setUserId(userId);
        txr.setGameId(gameId);
        EAResponse resp;
        try {
            var txResponse = addTxFromProvider(txr);
            resp = new EAResponse(200, null, txResponse.getNewBalance());
        } catch (OuslyTransactionException e) {
            var w = getWallet(userId);
            resp = new EAResponse(500, "unknown error:" + e.getLocalizedMessage(), w.getBalance());
        }
        log.debug("EA-Debit: {}", resp);
        return resp;

    }

    @Override
    @Transactional
    public EAResponse rollback(String remoteId, BigDecimal amount, String actionType,
                               String transactionId, String roundId, String remoteData) {
        log.debug("EA-debit: {},{},{},{}", remoteId, remoteData, amount, actionType);

        var userId = createUserIdFromRemoteId(remoteId);
        if (userId == null) {
            return new EAResponse(500, "Invalid user given as remoteId for rollback", null);
        }

        var casinoUser = getCasinoUser(userId);
        if (casinoUser == null) {
            return new EAResponse(500, "Invalid user given as remoteId for rollback", null);
        }

        // now try to find old session
        var ssReq = new SessionSearchRequest();
        ssReq.setUserId(userId);
        ssReq.setRoundRef(roundId);
        ssReq.sethVersion(getHandlerVersion());

        Long sessionId = null;
        Long gameId = null;
        var gp = GamePlatform.UNKNOWN;

        var optSession = findSessionFor(ssReq);
        if (optSession.isPresent()) {
            sessionId = optSession.get().getSessionId();
            gameId = optSession.get().getGameId();
            var optSession2 = findSessionByUserIdAndSessionId(userId, sessionId);
            if (optSession2.isPresent()) {
                gp = optSession2.get().getPlatform();
            }
        }

        // now create debit/win tx as type rollback
        var txr = new TxRequest();
        txr.setRoundMode(RoundMode.AUTO);
        txr.setGp(gp);
        txr.setType(TransactionType.ROLLBACK);
        txr.setExternalOrigId(transactionId);
        txr.setRoundRef(roundId);
        txr.setWin(amount);
        txr.setBet(BigDecimal.ZERO);
        txr.setSessionId(sessionId);
        txr.setUserId(userId);
        txr.setGameId(gameId);
        EAResponse resp;
        try {
            var txResponse = addTxFromProvider(txr);
            resp = new EAResponse(200, null, txResponse.getNewBalance());
        } catch (OuslyTransactionException e) {
            var w = getWallet(userId);
            resp = new EAResponse(500, "unknown error:" + e.getLocalizedMessage(), w.getBalance());
        }
        log.debug("EA-Rollback: {}", resp);
        return resp;
    }

    private static Long createUserIdFromRemoteId(String remoteId) {
        try {
            return Long.valueOf(remoteId.substring(3));
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(config.getVendorName());
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform, GameSettings settings) {
        var uid = createRemoteId(user);

        var lResp = getLaunchParamsFor(getCorrectKeyForStage(game.getGameId(), settings.getStage()), uid, "none");
        if (lResp == null) {
            return null;
        }
        var gameInstance = new GameInstance();

        gameInstance.setGameUrl(lResp.getResponse().getGameUrl());
        gameInstance.setToken("EA:" + lResp.getResponse().getToken());
        gameInstance.setAuthToken("EA:" + lResp.getResponse().getToken());
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }

    private static String getCorrectKeyForStage(String gameId, String stage) {
        var keyComps = gameId.split(":");
        if (keyComps.length == 2) {
            if ("live".equalsIgnoreCase(stage)) {
                return keyComps[0];
            }
            return keyComps[1];
        }
        return gameId;
    }

    private static String createRemoteId(CasinoUser user) {
        return "EA-" + user.getId();
    }

    private EACreateGameResponse getLaunchParamsFor(String gameid, String userid, String remData) {
        try {
            var bld = new URIBuilder(config.getLaunchUrl());
            bld.addParameter("action", "get_game");
            bld.addParameter("usr", config.getApiUser());
            bld.addParameter("passw", config.getApiPW());
            bld.addParameter("game_id", gameid);
            bld.addParameter("remote_id", userid);
            bld.addParameter("remote_data", remData);
            var forEntity = eurasianTemplate.getForEntity(bld.build().toString(), String.class);
            log.debug("EA Launch answer {}", forEntity);
            if (forEntity.getBody().contains("invalid credentials")) {
                log.warn("got Illegal credentials while trying to get gameUrl for Eurasian game");
                return null;
            }
            return om.readValue(forEntity.getBody(), EACreateGameResponse.class);
        } catch (Exception e) {
            log.error("Error getting launch:", e);
        }
        return null;
    }

    @Override
    public boolean canRetrieveGames() {
        return true;
    }

    @Override
    public List<AvailableGame> retrieveGames() {

        try {
            var bld = new URIBuilder(config.getLaunchUrl());
            bld.addParameter("action", "available_games");
            bld.addParameter("usr", config.getApiUser());
            bld.addParameter("passw", config.getApiPW());
            var uri = bld.build();
            log.debug("getting games using: {}", uri);

            var forEntity = eurasianTemplate.getForEntity(bld.build(), EAGGResponse.class);
            log.debug("Got:{}", forEntity.getBody());
            var resp = forEntity.getBody();
            log.debug("EURASIAN: got game list:{}", resp);
            if (resp.getStatus() == 200) {
                List<AvailableGame> lg = new ArrayList<>();
                for (var game : resp.getResponse()) {
                    var gId = game.getId();
                    var rawData = "{}";
                    try {
                        rawData = getObjectMapper().writeValueAsString(game);
                    } catch (Exception ignored) {
                    }
                    var ag = new AvailableGame(config.getVendorName(), gId, game.getName(), rawData);
                    lg.add(ag);
                }
                return lg;
            }
        } catch (Exception e) {
            log.error("Error getting gamelist", e);
        }
        return Collections.emptyList();
    }

}

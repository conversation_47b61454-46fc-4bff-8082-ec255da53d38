package com.ously.gamble.bridge.mascot.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class MSBalanceResponse extends MSResponse {
    Long balance;

    public MSBalanceResponse() {
        super();
    }

    public MSBalanceResponse(Long balance
    ) {
        super();
        this.balance = balance;
    }

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    @Override
    public String toString() {
        return "MSBalanceResponse{" +
                "balance=" + balance +
                '}';
    }
}

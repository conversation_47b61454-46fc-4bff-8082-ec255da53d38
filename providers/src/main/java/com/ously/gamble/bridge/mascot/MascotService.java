package com.ously.gamble.bridge.mascot;

import com.ously.gamble.bridge.mascot.payload.*;
import org.javatuples.Pair;

public interface MascotService {
    Pair<MSBalanceResponse, MSError> balance(MSBalanceRequest req);

    Pair<MSRollbackResponse, MSError> rollback(MSRollbackRequest req);

    Pair<MSDepositResponse, MSError> deposit(MSDepositRequest req);

    Pair<MSWithdrawResponse, MSError> withdraw(MSWithdrawRequest req);

    Pair<MSWithdrawAndDepositResponse, MSError> withdrawAndDeposit(MSWithdrawAndDepositRequest req);
}

package com.ously.gamble.bridge.mascot.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class MSWithdrawAndDepositResponse extends MSDepositResponse {

    Long newBalance;

    public MSWithdrawAndDepositResponse() {
        super();
    }

    public MSWithdrawAndDepositResponse(long bal, String txId) {
        super(bal, txId);
        newBalance = balance;
    }

}

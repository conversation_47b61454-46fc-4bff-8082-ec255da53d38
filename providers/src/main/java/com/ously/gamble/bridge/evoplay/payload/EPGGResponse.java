package com.ously.gamble.bridge.evoplay.payload;

import java.util.Map;

public class EPGGResponse {

    String status;
    Map<Long, EPGGGame> data;

    public String getStatus() {
        return this.status;
    }

    public void setStatus(final String status) {
        this.status = status;
    }

    public Map<Long, EPGGGame> getData() {
        return this.data;
    }

    public void setData(final Map<Long, EPGGGame> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "TGGGResponse{" +
                "status='" + status + '\'' +
                ", data=" + data +
                '}';
    }
}

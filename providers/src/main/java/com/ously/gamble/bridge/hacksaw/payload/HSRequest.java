package com.ously.gamble.bridge.hacksaw.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class HSRequest {
    String action;
    String secret;
    String externalSessionId;
    String externalPlayerId;
    Long gameId;

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getExternalSessionId() {
        return externalSessionId;
    }

    public void setExternalSessionId(String externalSessionId) {
        this.externalSessionId = externalSessionId;
    }

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }

    public String getExternalPlayerId() {
        return externalPlayerId;
    }

    public void setExternalPlayerId(String externalPlayerId) {
        this.externalPlayerId = externalPlayerId;
    }

    @Override
    public String toString() {
        return "HSRequest{" +
                "action='" + action + '\'' +
                ", secret='" + secret + '\'' +
                ", externalSessionId='" + externalSessionId + '\'' +
                ", externalPlayerId='" + externalPlayerId + '\'' +
                ", gameId=" + gameId +
                '}';
    }
}

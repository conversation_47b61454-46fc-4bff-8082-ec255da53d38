package com.ously.gamble.bridge.everymatrix.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EMGetAccountResponse extends EMResponse {

    @JsonProperty("ExternalUserId")
    String externalUserId;
    @JsonProperty("City")
    String city;
    @JsonProperty("Country")
    String country;
    @JsonProperty("Currency")
    String currency;
    @JsonProperty("SessionId")
    String sessionId;
    @JsonProperty("UserName")
    String userName;
    @JsonProperty("FirstName")
    String firstName;
    @JsonProperty("LastName")
    String lastName;
    @JsonProperty("Alias")
    String alias;
    @JsonProperty("Birthdate")
    String birthdate;
    @JsonProperty("RCPeriod")
    Integer rcPeriod = 30;

    public EMGetAccountResponse(EMRequest req, int code, String msg) {
        super(req, code, msg);
    }

    public String getExternalUserId() {
        return externalUserId;
    }

    public void setExternalUserId(final String externalUserId) {
        this.externalUserId = externalUserId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(final String city) {
        this.city = city;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(final String country) {
        this.country = country;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(final String currency) {
        this.currency = currency;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(final String sessionId) {
        this.sessionId = sessionId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(final String userName) {
        this.userName = userName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(final String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(final String lastName) {
        this.lastName = lastName;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(final String alias) {
        this.alias = alias;
    }

    public String getBirthdate() {
        return birthdate;
    }

    public void setBirthdate(final String birthdate) {
        this.birthdate = birthdate;
    }

    public Integer getRcPeriod() {
        return rcPeriod;
    }

    public void setRcPeriod(final Integer rcPeriod) {
        this.rcPeriod = rcPeriod;
    }


    @Override
    public String toString() {
        return "EMGetAccountResponse{" + "externalUserId='" + externalUserId + '\'' + ", city='" + city + '\'' + ", country='" + country + '\'' + ", currency='" + currency + '\'' + ", sessionId='" + sessionId + '\'' + ", userName='" + userName + '\'' + ", firstName='" + firstName + '\'' + ", lastName='" + lastName + '\'' + ", alias='" + alias + '\'' + ", birthdate='" + birthdate + '\'' + ", rcPeriod=" + rcPeriod + ", apiVersion='" + apiVersion + '\'' + ", request='" + request + '\'' + ", returnCode=" + returnCode + ", message='" + message + '\'' + "}";
    }
}

package com.ously.gamble.bridge.apparat;

import com.ously.gamble.api.OuslyOutOfMoneyException;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.api.session.TxResponse;
import com.ously.gamble.bridge.BridgeBaseV2;
import com.ously.gamble.bridge.apparat.payload.*;
import com.ously.gamble.bridge.common.URIBuilder;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
@ConditionalOnBean(ApparatConfiguration.class)
public class ApparatServiceImpl extends BridgeBaseV2 implements ApparatService {
    final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final BigDecimal MULT100 = BigDecimal.valueOf(100L);

    @Autowired
    ApparatConfiguration config;


    @Override
    public List<String> getVendorNames() {
        return Collections.singletonList(config.getVendorName());
    }

    @Override
    public GameInstance createNewGameInstanceRaw(CasinoUser user, CasinoGame game,
                                                 GamePlatform platform,
                                                 GameSettings settings) throws Exception {
        log.info("CreateGame (APPARAT) for user {}, game {} platform={}", user.getDisplayName(), game.getGameId(), platform);

        String gameUrl = null;
        var token = UUID.randomUUID().toString();
        if (platform != GamePlatform.TEST) {
            var bld = new URIBuilder(config.getLaunchUrl());
            bld.addParameter("token", token);
            bld.addParameter("casinoId", config.getCasinoId());
            bld.addParameter("gameId", game.getGameId());
            bld.addParameter("playerId", createPlayerId(user.getId()));
            bld.addParameter("locale", settings.getLanguage());
            gameUrl = bld.build().toString();
        }

        var
                gameInstance = new GameInstance();

        gameInstance.setGameUrl(gameUrl);
        gameInstance.setToken(token);
        gameInstance.setAuthToken(token);
        gameInstance.setBetLevel(settings.getBetLevel());
        gameInstance.setCreationTime(LocalDateTime.now());
        gameInstance.setExpiryTime(getExpiryTime());
        return gameInstance;
    }

    private static String createPlayerId(Long id) {
        return "APP-" + id;
    }

    @Override
    @Transactional
    public APResponse init(APInitRequest req) {

        var token = getToken(req.getToken());
        if (token == null) {
            return new APError("session_invalid", "session not found or expired");
        }

        var resp = new APInitResponse();
        var ui = getCasinoUser(token.getUserId());
        if (ui == null) {
            resp.setLocale("en");
        } else {
            var lang = ui.getLangCode();
            resp.setLocale((lang == null) ? "en" : lang);
        }
        resp.setPlayerId(createPlayerId(token.getUserId()));
        resp.setCurrency(config.getCurrency());
        if (token.getBetLevel() == 1) {
            resp.setBets(500L, 1000L, 2000L, 5000L, 10000L, 25000L, 50000L, 75000L, 100000L);
        } else if (token.getBetLevel() == 2) {
            resp.setBets(10000L, 20000L, 40000L, 50000L, 75000L, 100000L, 250000L, 500000L, 750000L, 1000000L);
        } else {
            resp.setBets(10L, 20L, 40L, 50L, 75L, 100L, 200L, 500L, 1000L, 2000L, 5000L, 10000L);
        }
        var w = getWallet(token.getUserId());
        resp.setBalance(w.getBalance().multiply(MULT100).longValue());
        return resp;
    }

    @Override
    @Transactional
    public APResponse balance(APBalanceRequest req) {
        var token = getToken(req.getToken());
        if (token == null) {
            return new APError("session_invalid", "session not found or expired");
        }
        var w = getWallet(token.getUserId());
        return new APResponse(w.getBalance().multiply(MULT100).longValue());
    }

    @Override
    @Transactional
    public APResponse bet(APTransactionRequest req) {
        var token = getToken(req.getToken());
        if (token == null) {
            return new APError("session_invalid", "session not found or expired");
        }

        var txReq = new TxRequest(token);
        txReq.setRoundMode(RoundMode.AUTO);
        txReq.setType(TransactionType.BET);
        txReq.setBet(BigDecimal.valueOf(req.getBet()).divide(MULT100, 2, RoundingMode.DOWN));
        txReq.setWin(BigDecimal.ZERO);
        txReq.setRoundRef(req.getGameRoundId());
        txReq.setExternalOrigId(req.getRequestId());
        TxResponse txResp;
        try {
            txResp = addTxFromProvider(txReq);
        } catch (OuslyOutOfMoneyException oom) {
            return new APError("balance_insufficient", "not enough funds");
        } catch (Exception e) {
            return new APError("internal", e.getMessage());
        }
        if (txResp.isIdempotent()) {
            return new APError("transaction_already_processed", "idempotency");
        }
        return new APResponse(txResp.getNewBalance().multiply(MULT100).longValue(), Long.toString(txResp.getTxId()));
    }

    @Override
    @Transactional
    public APResponse win(APTransactionRequest req) {
        var token = getToken(req.getToken());

        long userId;
        long sessionId;
        long gameId;
        GamePlatform gp;

        if (token == null) {
            var ss = findSessionByToken(req.getToken());
            if (ss.isPresent()) {
                userId = ss.get().getUserId();
                sessionId = ss.get().getSessionId();
                gameId = ss.get().getGameId();
                gp = ss.get().getPlatform();
            } else {
                return new APError("session_invalid", "session not found or expired");
            }
        } else {
            userId = token.getUserId();
            sessionId = token.getSessionId();
            gameId = token.getGameId();
            gp = token.getGp();
        }

        var txReq = (token == null) ? new TxRequest() : new TxRequest(token);
        txReq.setRoundMode(RoundMode.AUTO);
        txReq.setType(TransactionType.WIN);
        txReq.setWin(BigDecimal.valueOf(req.getWin()).divide(MULT100, 2, RoundingMode.DOWN));
        txReq.setBet(BigDecimal.ZERO);
        txReq.setGameId(gameId);
        txReq.setUserId(userId);
        txReq.setRoundRef(req.getGameRoundId());
        txReq.setGp(gp);
        txReq.setExternalOrigId(req.getRequestId());
        txReq.setSessionId(sessionId);
        TxResponse txResp;
        try {
            txResp = addTxFromProvider(txReq);
        } catch (Exception e) {
            return new APError("internal", e.getMessage());
        }
        if (txResp.isIdempotent()) {
            return new APError("transaction_already_processed", "idempotency");
        }
        return new APResponse(txResp.getNewBalance().multiply(MULT100).longValue(), Long.toString(txResp.getTxId()));
    }

    @Override
    @Transactional
    public APResponse betAndWin(APTransactionRequest req) {

        var token = getToken(req.getToken());
        if (token == null) {
            return new APError("session_invalid", "session not found or expired");
        }

        var txReq = new TxRequest(token);
        txReq.setRoundMode(RoundMode.AUTO);
        txReq.setType(TransactionType.DIRECTWIN);
        txReq.setBet(BigDecimal.valueOf(req.getBet()).divide(MULT100, 2, RoundingMode.DOWN));
        txReq.setWin(BigDecimal.valueOf(req.getWin()).divide(MULT100, 2, RoundingMode.DOWN));
        txReq.setRoundRef(req.getGameRoundId());
        txReq.setExternalOrigId(req.getRequestId());
        TxResponse txResp;
        try {
            txResp = addTxFromProvider(txReq);
        } catch (OuslyOutOfMoneyException oom) {
            return new APError("balance_insufficient", "not enough funds");
        } catch (Exception e) {
            return new APError("internal", e.getMessage());
        }
        if (txResp.isIdempotent()) {
            return new APError("transaction_already_processed", "idempotency");
        }
        return new APResponse(txResp.getNewBalance().multiply(MULT100).longValue(), Long.toString(txResp.getTxId()));
    }

    @Override
    @Transactional
    public APResponse rollback(APTransactionRequest req) {
        var token = getToken(req.getToken());

        long userId;
        long sessionId;

        if (token == null) {
            var ss = findSessionByToken(req.getToken());
            if (ss.isPresent()) {
                userId = ss.get().getUserId();
                sessionId = ss.get().getSessionId();
            } else {
                return new APError("session_invalid", "session not found or expired");
            }
        } else {
            userId = token.getUserId();
            sessionId = token.getSessionId();
        }

        var oldTx = findBySessionIdAndOrigId(userId, sessionId, req.getRollbackRequestId());
        if (oldTx == null) {
            return new APError("transaction_not_found", "");
        }
        if (oldTx.isCancelled()) {
            return new APError("transaction_already_processed", "idempotency");
        }

        // now cancel
        var w = getWallet(userId);
        var txResp = performRollback(oldTx, w);
        return new APResponse(txResp.getNewBalance().multiply(MULT100).longValue(), Long.toString(txResp.getTxId()));
    }
}

package com.ously.gamble.bridge.kagaming.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class KAStartResponse extends KAResponse {

    String playerId;
    String sessionId;
    long balance;
    String currency;
    Integer sessionRTP;

    public KAStartResponse() {
        super();
    }

    public KAStartResponse(int code, String status) {
        super(code, status);
    }

    public String getPlayerId() {
        return playerId;
    }

    public void setPlayerId(String playerId) {
        this.playerId = playerId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public long getBalance() {
        return balance;
    }

    public void setBalance(long balance) {
        this.balance = balance;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Integer getSessionRTP() {
        return sessionRTP;
    }

    public void setSessionRTP(Integer sessionRTP) {
        this.sessionRTP = sessionRTP;
    }

    @Override
    public String toString() {
        return "KAStartResponse{" +
                "status='" + status + '\'' +
                ", statusCode=" + statusCode +
                ", userMessage='" + userMessage + '\'' +
                ", playerId='" + playerId + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", balance=" + balance +
                ", currency='" + currency + '\'' +
                ", sessionRTP=" + sessionRTP +
                '}';
    }
}

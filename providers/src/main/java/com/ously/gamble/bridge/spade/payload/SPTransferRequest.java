package com.ously.gamble.bridge.spade.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import java.math.BigDecimal;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class SPTransferRequest extends SPRequest {

    String transferId;
    String acctId;
    String currency;
    BigDecimal amount;
    Integer type;
    String channel;
    String gameCode;
    String ticketId;
    String referenceId;

    public String getTransferId() {
        return transferId;
    }

    public void setTransferId(String transferId) {
        this.transferId = transferId;
    }

    public String getAcctId() {
        return acctId;
    }

    public void setAcctId(String acctId) {
        this.acctId = acctId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getGameCode() {
        return gameCode;
    }

    public void setGameCode(String gameCode) {
        this.gameCode = gameCode;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    @Override
    public String toString() {
        return "SPTransferRequest{" +
                "serialNo='" + serialNo + '\'' +
                ", merchantCode='" + merchantCode + '\'' +
                ", transferId='" + transferId + '\'' +
                ", acctId='" + acctId + '\'' +
                ", currency='" + currency + '\'' +
                ", amount=" + amount +
                ", type=" + type +
                ", channel='" + channel + '\'' +
                ", gameCode='" + gameCode + '\'' +
                ", ticketId='" + ticketId + '\'' +
                ", referenceId='" + referenceId + '\'' +
                '}';
    }
}

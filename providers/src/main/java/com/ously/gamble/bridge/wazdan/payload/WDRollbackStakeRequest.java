package com.ously.gamble.bridge.wazdan.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class WDRollbackStakeRequest extends WDTransactionSource {
    Double amount;
    Double bonusAmount;


    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getBonusAmount() {
        return bonusAmount;
    }

    public void setBonusAmount(Double bonusAmount) {
        this.bonusAmount = bonusAmount;
    }

    @Override
    public String toString() {
        return "WDRollbackStakeRequest{" +
                "user=" + user +
                ", amount=" + amount +
                ", bonusAmount=" + bonusAmount +
                ", gameId=" + gameId +
                ", roundId='" + roundId + '\'' +
                ", originalTransactionId='" + originalTransactionId + '\'' +
                ", transactionId='" + transactionId + '\'' +
                '}';
    }
}

package com.ously.gamble.bridge.microgaming.payload;

import jakarta.xml.bind.annotation.*;

@XmlRootElement(name = "pkt")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "pkt")
public class LoginPkt {

    @XmlElement(required = true, name = "methodresponse")
    LoginResponse resp;

    public LoginResponse getResp() {
        return resp;
    }

    public void setResp(LoginResponse resp) {
        this.resp = resp;
    }
}

# Games DB/Structure changes

done since mid jan.2022.

## Motivation

We want to have a central location where games are synced-to and synced-from. Lets call it "golden master".

All platform installations should sync from the master. Creation/Updates of Vendor,Game,GameSetup,CustomCategories
should be forwarded to master.

So when regular syncs are done every 5 minutes, a new vendor or updated vendors should be available on all installations
quite quickly. All new Entities are "inactive" per default. GameSetups get only synced for the active jurisdiction.

## DB model

We could either use a specific mysql instance as master (small!!) and create a shared rest-api for updating,reading or
creating the master-entities (vendor,game,game_setup,game_info,game_info_content,....)
or we could manage this via s3 (wouldnt recommend it due to transactional problems).

So when any platform creates a new vendor it is created on the master (PK name). And it should automatically appear on
the other platforms (albeit inactive)!
(important-> the id on the local platform is not autoincr.!!)
Updates of entities:

- prepare the update locally
- send the updated entity (PUT .../api/vendors/{id})
    - on the master the update is performed (except for active flag)
    - save locally
- syncs should now migrate the update (except for active flag)

each entity has certain update/sync restrictions to avoid setting master attributes not appl. for other setups. We could
also think about memorizing the setup the update/create/... is coming from
(e.g. active/inactive flags per setup "sanet","sade","sacrpt" and stage) so we could automatically create the correct
flags for the installations. But this could get messy since we would need to extract those flags per entity key and
setup/stage combination.

### workflow examples

Syncing needs to be done in order and each entity needs to check if dep. entities are available:
game needs to check existence of vendor and game_info.

1. vendor
2. game_info
3. game_info_content
4. game_tags
5. game_category
6. game
7. game_setup

For new game

1. create game
2. create game_setup

Linking a game:

1. send info_id + game_id
2. send game_id + null info_id (unlink)

Updating game image:

1. send game_image + game_id

...

#### sync vendors

Each platform regularly calls GET .../api/vendors and checks if any changes need to be updated locally.

#### Vendor:

- id (auto)
- name
- slotcatalog_name
- url
- jurisdictions (SOC,MGA,CU,CRPT,..)
- blocked_countries
- _**active**_

#### Game

- id (which comp. is creating id?)
- name
- vendor_id (Vendor.id)
- info_id (GameInfo.id)
- _**sort_order**_
- _**ios**_
- _**android**_

PK (id,vendor_id)
IDX (name,vendor_id) UNIQUE

#### GameSetup

- game_id (Game.id)
- key_default
- key_mobile
- rtp
- baseFee
- bridge_name
- jurisdiction
- _**active**_

PK (game_id,jurisdiction,bridge_name)

#### GameInfo

- id
- rtp
- description (en/de)
- paylines
- release_date
- sc_vendor_name (Vendor.slotcatalog_name)
- slotrank
- ...
- JSON sc-data

PK(id)

#### GameInfoContent

- id
- info_id (GameInfo.id)
- fetched
- url

PK(id)

#### GameCategories (retrieved from slotcatalog)

- id
- parentId
- type
- hidden

PK(id)

#### GameImages

- id (auto)
- game_id
- type (_1X1,_2X1,_1X2,_1X1_ANIM,...)
- BLOB (image stream)
- s3 synced
- imgix synced
- .... (created,updated,...)

PK(id)
IDX (game_id,type)

## Queries/Joins

### get all

select * from game g join vendor v on g.vendor_id = v.id join game_setup gs on g.id = gs.game_id left join game_info gi
on g.info_id = gi.id

### get all active (cash variant, no dist. between ios/android)

select * from game g join vendor v on g.vendor_id = v.id join game_setup gs on g.id = gs.game_id left join game_info gi
on g.info_id = gi.id where v.active = true and gs.active=true and gs.jurisdiction='MGA'

### get all active (social variant, ios)

select * from game g join vendor v on g.vendor_id = v.id join game_setup gs on g.id = gs.game_id left join game_info gi
on g.info_id = gi.id where v.active = true and gs.active=true and gs.jurisdiction='SOC' and g.ios = true

## Implementation

### create local db (mysql 8)

Use simple inserts (as created from dg) to copy:
vendors,games,game_info,game_info_content,....

### create initial tables

create new tables for games, game_setup, ....

### fill new tables with data from old tables

create selects for filling new tables with data from old tables

prepare some queries for getting game_list, casinoGame,....

### slotcatalog sync container

create new container which handles the slotcatalog sync from slotcatalog to our tables.
game_info,game_info_content,categories,... create api so our backend can retrieve the data. Its r/o so that should be
easy.

### vendor functionality

add vendor api and add vendor sync (every n. minutes).

### add new tables to schema and work on sync/update/... with shared api

### secure api (apikey), remove slotcatalog from our platform

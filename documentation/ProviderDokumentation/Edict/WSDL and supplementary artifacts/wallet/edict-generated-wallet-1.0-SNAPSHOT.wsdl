<?xml version='1.0' encoding='UTF-8'?><!-- Published by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is Metro/2.2.0-1 (tags/2.2.0u1-7139; 2012-06-02T10:55:19+0000) JAXWS-RI/2.2.6-2 JAXWS/2.2 svn-revision#unknown. --><!-- Generated by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is Metro/2.2.0-1 (tags/2.2.0u1-7139; 2012-06-02T10:55:19+0000) JAXWS-RI/2.2.6-2 JAXWS/2.2 svn-revision#unknown. -->
<definitions
        xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata"
        xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://wallet.integration.eoc.edict.de"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.xmlsoap.org/wsdl/"
        targetNamespace="http://wallet.integration.eoc.edict.de" name="WalletServer">
    <types>
        <xsd:schema>
            <xsd:import namespace="http://wallet.integration.eoc.edict.de"
                        schemaLocation="edict-generated-wallet-1.0-SNAPSHOT.xsd"/>
        </xsd:schema>
    </types>
    <message name="rollbackTransaction">
        <part name="parameters" element="tns:rollbackTransaction"/>
    </message>
    <message name="rollbackTransactionResponse">
        <part name="parameters" element="tns:rollbackTransactionResponse"/>
    </message>
    <message name="RollbackTransactionFault">
        <part name="fault" element="tns:RollbackTransactionFault"/>
    </message>
    <message name="withdraw">
        <part name="parameters" element="tns:withdraw"/>
    </message>
    <message name="withdrawResponse">
        <part name="parameters" element="tns:withdrawResponse"/>
    </message>
    <message name="WithdrawFault">
        <part name="fault" element="tns:WithdrawFault"/>
    </message>
    <message name="deposit">
        <part name="parameters" element="tns:deposit"/>
    </message>
    <message name="depositResponse">
        <part name="parameters" element="tns:depositResponse"/>
    </message>
    <message name="DepositFault">
        <part name="fault" element="tns:DepositFault"/>
    </message>
    <message name="getPlayerCurrency">
        <part name="parameters" element="tns:getPlayerCurrency"/>
    </message>
    <message name="getPlayerCurrencyResponse">
        <part name="parameters" element="tns:getPlayerCurrencyResponse"/>
    </message>
    <message name="GetPlayerCurrencyFault">
        <part name="fault" element="tns:GetPlayerCurrencyFault"/>
    </message>
    <message name="withdrawAndDeposit">
        <part name="parameters" element="tns:withdrawAndDeposit"/>
    </message>
    <message name="withdrawAndDepositResponse">
        <part name="parameters" element="tns:withdrawAndDepositResponse"/>
    </message>
    <message name="WithdrawAndDepositFault">
        <part name="fault" element="tns:WithdrawAndDepositFault"/>
    </message>
    <message name="getBalance">
        <part name="parameters" element="tns:getBalance"/>
    </message>
    <message name="getBalanceResponse">
        <part name="parameters" element="tns:getBalanceResponse"/>
    </message>
    <message name="GetBalanceFault">
        <part name="fault" element="tns:GetBalanceFault"/>
    </message>
    <portType name="WalletSoapResource">
        <operation name="rollbackTransaction">
            <input wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/rollbackTransactionRequest"
                   message="tns:rollbackTransaction"/>
            <output wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/rollbackTransactionResponse"
                    message="tns:rollbackTransactionResponse"/>
            <fault message="tns:RollbackTransactionFault" name="RollbackTransactionFault"
                   wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/rollbackTransaction/Fault/RollbackTransactionFault"/>
        </operation>
        <operation name="withdraw">
            <input wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/withdrawRequest"
                   message="tns:withdraw"/>
            <output wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/withdrawResponse"
                    message="tns:withdrawResponse"/>
            <fault message="tns:WithdrawFault" name="WithdrawFault"
                   wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/withdraw/Fault/WithdrawFault"/>
        </operation>
        <operation name="deposit">
            <input wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/depositRequest"
                   message="tns:deposit"/>
            <output wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/depositResponse"
                    message="tns:depositResponse"/>
            <fault message="tns:DepositFault" name="DepositFault"
                   wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/deposit/Fault/DepositFault"/>
        </operation>
        <operation name="getPlayerCurrency">
            <input wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/getPlayerCurrencyRequest"
                   message="tns:getPlayerCurrency"/>
            <output wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/getPlayerCurrencyResponse"
                    message="tns:getPlayerCurrencyResponse"/>
            <fault message="tns:GetPlayerCurrencyFault" name="GetPlayerCurrencyFault"
                   wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/getPlayerCurrency/Fault/GetPlayerCurrencyFault"/>
        </operation>
        <operation name="withdrawAndDeposit">
            <input wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/withdrawAndDepositRequest"
                   message="tns:withdrawAndDeposit"/>
            <output wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/withdrawAndDepositResponse"
                    message="tns:withdrawAndDepositResponse"/>
            <fault message="tns:WithdrawAndDepositFault" name="WithdrawAndDepositFault"
                   wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/withdrawAndDeposit/Fault/WithdrawAndDepositFault"/>
        </operation>
        <operation name="getBalance">
            <input wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/getBalanceRequest"
                   message="tns:getBalance"/>
            <output wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/getBalanceResponse"
                    message="tns:getBalanceResponse"/>
            <fault message="tns:GetBalanceFault" name="GetBalanceFault"
                   wsam:Action="http://wallet.integration.eoc.edict.de/WalletSoapResource/getBalance/Fault/GetBalanceFault"/>
        </operation>
    </portType>
    <binding name="WalletSoapResourcePortBinding" type="tns:WalletSoapResource">
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="document"/>
        <operation name="rollbackTransaction">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="RollbackTransactionFault">
                <soap:fault name="RollbackTransactionFault" use="literal"/>
            </fault>
        </operation>
        <operation name="withdraw">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="WithdrawFault">
                <soap:fault name="WithdrawFault" use="literal"/>
            </fault>
        </operation>
        <operation name="deposit">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="DepositFault">
                <soap:fault name="DepositFault" use="literal"/>
            </fault>
        </operation>
        <operation name="getPlayerCurrency">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="GetPlayerCurrencyFault">
                <soap:fault name="GetPlayerCurrencyFault" use="literal"/>
            </fault>
        </operation>
        <operation name="withdrawAndDeposit">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="WithdrawAndDepositFault">
                <soap:fault name="WithdrawAndDepositFault" use="literal"/>
            </fault>
        </operation>
        <operation name="getBalance">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="GetBalanceFault">
                <soap:fault name="GetBalanceFault" use="literal"/>
            </fault>
        </operation>
    </binding>
    <service name="WalletServer">
        <port name="WalletSoapResourcePort" binding="tns:WalletSoapResourcePortBinding">
            <soap:address location="http://localhost:8080/integration/gaming/edict/WalletServer"/>
        </port>
    </service>
</definitions>
<?xml version="1.0" encoding="UTF-8"?>
<!-- Published by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is Metro/2.2.0-1 (tags/2.2.0u1-7139; 2012-06-02T10:55:19+0000) JAXWS-RI/2.2.6-2 JAXWS/2.2 svn-revision#unknown. -->
<!-- Generated by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is Metro/2.2.0-1 (tags/2.2.0u1-7139; 2012-06-02T10:55:19+0000) JAXWS-RI/2.2.6-2 JAXWS/2.2 svn-revision#unknown. -->
<definitions
        xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata"
        xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
        xmlns:tns="http://authorization.wallet.integration.eoc.edict.de"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.xmlsoap.org/wsdl/"
        targetNamespace="http://authorization.wallet.integration.eoc.edict.de" name="AuthorizationServer">
    <types>
        <xsd:schema>
            <xsd:import namespace="http://authorization.wallet.integration.eoc.edict.de"
                        schemaLocation="edict-generated-authorize-1.0-SNAPSHOT.xsd"/>
        </xsd:schema>
    </types>
    <message name="authorizeAnonymous">
        <part name="parameters" element="tns:authorizeAnonymous"/>
    </message>
    <message name="authorizeAnonymousResponse">
        <part name="parameters" element="tns:authorizeAnonymousResponse"/>
    </message>
    <message name="AuthorizationFault">
        <part name="fault" element="tns:AuthorizationFault"/>
    </message>
    <message name="authorizePlayer">
        <part name="parameters" element="tns:authorizePlayer"/>
    </message>
    <message name="authorizePlayerResponse">
        <part name="parameters" element="tns:authorizePlayerResponse"/>
    </message>
    <portType name="AuthorizationSoapResource">
        <operation name="authorizeAnonymous">
            <input wsam:Action="http://authorization.wallet.integration.eoc.edict.de/AuthorizationSoapResource/authorizeAnonymousRequest"
                   message="tns:authorizeAnonymous"/>
            <output wsam:Action="http://authorization.wallet.integration.eoc.edict.de/AuthorizationSoapResource/authorizeAnonymousResponse"
                    message="tns:authorizeAnonymousResponse"/>
            <fault message="tns:AuthorizationFault" name="AuthorizationFault"
                   wsam:Action="http://authorization.wallet.integration.eoc.edict.de/AuthorizationSoapResource/authorizeAnonymous/Fault/AuthorizationFault"/>
        </operation>
        <operation name="authorizePlayer">
            <input wsam:Action="http://authorization.wallet.integration.eoc.edict.de/AuthorizationSoapResource/authorizePlayerRequest"
                   message="tns:authorizePlayer"/>
            <output wsam:Action="http://authorization.wallet.integration.eoc.edict.de/AuthorizationSoapResource/authorizePlayerResponse"
                    message="tns:authorizePlayerResponse"/>
            <fault message="tns:AuthorizationFault" name="AuthorizationFault"
                   wsam:Action="http://authorization.wallet.integration.eoc.edict.de/AuthorizationSoapResource/authorizePlayer/Fault/AuthorizationFault"/>
        </operation>
    </portType>
    <binding name="AuthorizationSoapResourcePortBinding" type="tns:AuthorizationSoapResource">
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="document"/>
        <operation name="authorizeAnonymous">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="AuthorizationFault">
                <soap:fault name="AuthorizationFault" use="literal"/>
            </fault>
        </operation>
        <operation name="authorizePlayer">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="AuthorizationFault">
                <soap:fault name="AuthorizationFault" use="literal"/>
            </fault>
        </operation>
    </binding>
    <service name="AuthorizationServer">
        <port name="AuthorizationSoapResourcePort" binding="tns:AuthorizationSoapResourcePortBinding">
            <soap:address location="http://localhost:8080/integration/gaming/edict/AuthorizationServer"/>
        </port>
    </service>
</definitions>
<!-- Published by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is Metro/2.2.0-1 (tags/2.2.0u1-7139; 2012-06-02T10:55:19+0000) JAXWS-RI/2.2.6-2 JAXWS/2.2 svn-revision#unknown. -->
<xs:schema xmlns:tns="http://authorization.wallet.integration.eoc.edict.de" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           version="1.0" targetNamespace="http://authorization.wallet.integration.eoc.edict.de">
    <xs:element name="AuthorizationFault" type="tns:AuthorizationFault"/>
    <xs:element name="AuthorizationRequest" type="tns:authorizationRequest"/>
    <xs:element name="authorizationResponse" type="tns:authorizationResponse"/>
    <xs:element name="authorizeAnonymous" type="tns:authorizeAnonymous"/>
    <xs:element name="authorizeAnonymousResponse" type="tns:authorizeAnonymousResponse"/>
    <xs:element name="authorizePlayer" type="tns:authorizePlayer"/>
    <xs:element name="authorizePlayerResponse" type="tns:authorizePlayerResponse"/>
    <xs:complexType name="authorizeAnonymous">
        <xs:sequence>
            <xs:element name="authorizationRequest" type="tns:authorizationRequest"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="authorizationRequest">
        <xs:sequence>
            <xs:element name="callerId" type="xs:string"/>
            <xs:element name="callerPassword" type="xs:string"/>
            <xs:element name="playerName" type="xs:string"/>
            <xs:element name="sessionToken" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="authorizeAnonymousResponse">
        <xs:sequence>
            <xs:element name="return" type="tns:authorizationResponse" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="authorizationResponse">
        <xs:sequence>
            <xs:element name="sessionId" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AuthorizationFault">
        <xs:sequence>
            <xs:element name="errorCode" type="xs:int"/>
            <xs:element name="message" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="authorizePlayer">
        <xs:sequence>
            <xs:element name="authorizationRequest" type="tns:authorizationRequest"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="authorizePlayerResponse">
        <xs:sequence>
            <xs:element name="return" type="tns:authorizationResponse" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
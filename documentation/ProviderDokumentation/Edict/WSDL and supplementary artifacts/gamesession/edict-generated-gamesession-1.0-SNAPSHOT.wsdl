<?xml version="1.0" encoding="UTF-8"?>
<!-- Published by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is Metro/2.2.0-1 (tags/2.2.0u1-7139; 2012-06-02T10:55:19+0000) JAXWS-RI/2.2.6-2 JAXWS/2.2 svn-revision#unknown. -->
<!-- Generated by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is Metro/2.2.0-1 (tags/2.2.0u1-7139; 2012-06-02T10:55:19+0000) JAXWS-RI/2.2.6-2 JAXWS/2.2 svn-revision#unknown. -->
<definitions
        xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata"
        xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
        xmlns:tns="http://gamesession.wallet.integration.eoc.edict.de"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.xmlsoap.org/wsdl/"
        targetNamespace="http://gamesession.wallet.integration.eoc.edict.de" name="GameSessionServer">
    <types>
        <xsd:schema>
            <xsd:import namespace="http://gamesession.wallet.integration.eoc.edict.de"
                        schemaLocation="edict-generated-gamesession-1.0-SNAPSHOT.xsd"/>
        </xsd:schema>
    </types>
    <message name="markGameSessionClosed">
        <part name="parameters" element="tns:markGameSessionClosed"/>
    </message>
    <message name="markGameSessionClosedResponse">
        <part name="parameters" element="tns:markGameSessionClosedResponse"/>
    </message>
    <message name="MarkGameSessionClosedFault">
        <part name="fault" element="tns:MarkGameSessionClosedFault"/>
    </message>
    <portType name="GameSessionSoapResource">
        <operation name="markGameSessionClosed">
            <input wsam:Action="http://gamesession.wallet.integration.eoc.edict.de/GameSessionSoapResource/markGameSessionClosedRequest"
                   message="tns:markGameSessionClosed"/>
            <output wsam:Action="http://gamesession.wallet.integration.eoc.edict.de/GameSessionSoapResource/markGameSessionClosedResponse"
                    message="tns:markGameSessionClosedResponse"/>
            <fault message="tns:MarkGameSessionClosedFault" name="MarkGameSessionClosedFault"
                   wsam:Action="http://gamesession.wallet.integration.eoc.edict.de/GameSessionSoapResource/markGameSessionClosed/Fault/MarkGameSessionClosedFault"/>

        </operation>
    </portType>
    <binding name="GameSessionSoapResourcePortBinding" type="tns:GameSessionSoapResource">
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="document"/>
        <operation name="markGameSessionClosed">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="MarkGameSessionClosedFault">
                <soap:fault name="MarkGameSessionClosedFault" use="literal"/>
            </fault>
        </operation>
    </binding>
    <service name="GameSessionServer">
        <port name="GameSessionSoapResourcePort" binding="tns:GameSessionSoapResourcePortBinding">
            <soap:address location="http://localhost:8080/integration/gaming/edict/GameSessionServer"/>
        </port>
    </service>
</definitions>
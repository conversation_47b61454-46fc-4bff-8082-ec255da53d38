/*
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.
 *
 * Copyright © edict egaming GmbH, Hamburg (Germany) [2014]
 */
package de.edict.eoc.integration.gaming.wallet.request;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
public class PlayerCurrencyRequest implements SoapRequest {

    @XmlElement(name = CALLER_ID, required = true)
    private String callerId;
    @XmlElement(name = CALLER_PW, required = true)
    private String callerPassword;
    @XmlElement(name = PLAYER_NAME, required = true)
    private String playerName;
    @XmlElement(name = SESSION_ID, required = false)
    private String sessionId;

    public PlayerCurrencyRequest() {
    }

    public String getCallerId() {
        return callerId;
    }

    public void setCallerId(String callerId) {
        this.callerId = callerId;
    }

    public String getCallerPassword() {
        return callerPassword;
    }

    public void setCallerPassword(String callerPassword) {
        this.callerPassword = callerPassword;
    }

    public String getPlayerName() {
        return playerName;
    }

    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
}

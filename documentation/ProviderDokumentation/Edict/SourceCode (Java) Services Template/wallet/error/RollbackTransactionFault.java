package de.edict.eoc.integration.gaming.wallet.error;

import de.edict.eoc.implementation.wallet.dto.exceptions.RollbackTransactionFaultDTO;

public class RollbackTransactionFault extends AbstractFault {

    public RollbackTransactionFault(int errorCode, String message) {
        super(errorCode, message);
    }

    public RollbackTransactionFault(RollbackTransactionFaultDTO rollbackTransactionFaultDTO) {
        super(rollbackTransactionFaultDTO.getErrorCode(), rollbackTransactionFaultDTO.getMessage());
    }
}

package de.edict.eoc.integration.gaming.wallet.error;

import de.edict.eoc.implementation.wallet.dto.exceptions.WithdrawAndDepositFaultDTO;

public class WithdrawAndDepositFault extends AbstractBalancedFault {

    public WithdrawAndDepositFault(int errorCode, String message) {
        super(errorCode, message);
    }

    public WithdrawAndDepositFault(WithdrawAndDepositFaultDTO withdrawAndDepositFaultDTO) {
        super(withdrawAndDepositFaultDTO.getErrorCode(), withdrawAndDepositFaultDTO.getMessage());
        setBalance(convert(withdrawAndDepositFaultDTO.getBalance()));
    }
}

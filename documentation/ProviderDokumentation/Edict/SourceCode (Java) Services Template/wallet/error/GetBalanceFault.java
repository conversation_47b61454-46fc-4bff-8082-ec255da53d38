package de.edict.eoc.integration.gaming.wallet.error;

import de.edict.eoc.implementation.wallet.dto.exceptions.GetBalanceFaultDTO;

public class GetBalanceFault extends AbstractFault {

    public GetBalanceFault(int errorCode, String message) {
        super(errorCode, message);
    }

    public GetBalanceFault(GetBalanceFaultDTO getBalanceFaultDTO) {
        super(getBalanceFaultDTO.getErrorCode(), getBalanceFaultDTO.getMessage());
    }
}
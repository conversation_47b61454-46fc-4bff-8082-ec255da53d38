package de.edict.eoc.integration.gaming.wallet.response;

import de.edict.eoc.implementation.wallet.dto.response.BalanceResponseDTO;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
public class BalanceResponse implements SoapResponse {

    public static final long serialVersionUID = 1L;

    @XmlElement
    private double balance;

    public BalanceResponse() {
    }

    public BalanceResponse(BalanceResponseDTO balanceResponseDTO) {
        this.setBalance(balanceResponseDTO.getBalance());
    }

    public BalanceResponse(double balance) {
        this.balance = balance;
    }

    public double getBalance() {
        return balance;
    }

    public void setBalance(double balance) {
        this.balance = balance;
    }


}

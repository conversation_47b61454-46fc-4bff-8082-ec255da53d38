/*
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.
 *
 * Copyright © edict egaming GmbH, Hamburg (Germany) [2014]
 */
package de.edict.eoc.integration.gaming.wallet.tournaments;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "Tournament")
@XmlAccessorType(XmlAccessType.FIELD)
public class Tournament {

    public static final long serialVersionUID = 1L;

    private long tournamentId;
    private long tournamentOccurrenceId;

    protected Tournament() {
    }

    public Tournament(long tournamentId, long tournamentOccurrenceId) {
        this.tournamentId = tournamentId;
        this.tournamentOccurrenceId = tournamentOccurrenceId;
    }

    public long getTournamentId() {
        return tournamentId;
    }

    public long getTournamentOccurrenceId() {
        return tournamentOccurrenceId;
    }
}

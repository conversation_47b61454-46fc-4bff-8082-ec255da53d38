package de.edict.eoc.integration.gaming.authorization.response;

import de.edict.eoc.implementation.authorize.dto.AuthorizationResponseDTO;
import de.edict.eoc.integration.gaming.wallet.response.SoapResponse;

import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

@XmlRootElement
public class AuthorizationResponse implements SoapResponse {

    public static final long serialVersionUID = 1L;

    @XmlElement(required = true)
    private String sessionId;

    public AuthorizationResponse() {
    }

    public AuthorizationResponse(String sessionId) {
        this.sessionId = sessionId;
    }

    public AuthorizationResponse(AuthorizationResponseDTO nextResponse) {
        this(nextResponse.getSessionId());
    }

    public String getSessionId() {
        return sessionId;
    }

}

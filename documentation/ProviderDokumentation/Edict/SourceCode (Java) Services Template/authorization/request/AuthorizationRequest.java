/*
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.
 *
 * Copyright © edict egaming GmbH, Hamburg (Germany) [2014]
 */
package de.edict.eoc.integration.gaming.authorization.request;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "AuthorizationRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class AuthorizationRequest {

    public static final long serialVersionUID = 1L;

    @XmlElement(required = true)
    private String callerId;
    @XmlElement(required = true)
    private String callerPassword;
    @XmlElement(required = true)
    private String playerName;
    @XmlElement(required = true)
    private String sessionToken;

    public AuthorizationRequest() {
    }

    public AuthorizationRequest(String callerId, String callerPassword, String playerName, String sessionToken) {
        this.callerId = callerId;
        this.callerPassword = callerPassword;
        this.playerName = playerName;
        this.sessionToken = sessionToken;
    }

    public String getCallerId() {
        return callerId;
    }

    public void setCallerId(String callerId) {
        this.callerId = callerId;
    }

    public String getCallerPassword() {
        return callerPassword;
    }

    public void setCallerPassword(String callerPassword) {
        this.callerPassword = callerPassword;
    }

    public String getPlayerName() {
        return playerName;
    }

    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }

    public String getSessionToken() {
        return sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }
}

/*
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.
 *
 * Copyright © edict egaming GmbH, Hamburg (Germany) [2014]
 */
package de.edict.eoc.integration.gaming.authorization.error;

import de.edict.eoc.implementation.authorize.dto.AuthorizationFaultDTO;
import de.edict.eoc.integration.gaming.wallet.error.AbstractFault;

public class AuthorizationFault extends AbstractFault {

    public AuthorizationFault(int errorCode, String message) {
        super(errorCode, message);
    }

    public AuthorizationFault(AuthorizationFaultDTO authorizationFaultDTO) {
        this(authorizationFaultDTO.getErrorCode(), authorizationFaultDTO.getMessage());
    }
}

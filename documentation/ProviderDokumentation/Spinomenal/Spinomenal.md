## General behaviour and formats

### What we need:

(from Spinomenal:)

* partnerId
* privateKey

what we use to identify a session:

* GameToken (which is unique per user and spinomenal) is generated upon the "Generate Token" call and is unique per
  user+gameId. A user can only have on active Spinomenal token at a time. So we should take that into account when
  handling switching of games.

### Attention -> idempotency

All calls from provider must be processed idempotent (it should not make a difference on the users balance if the same
message - by ticketid - is sent multiple times - a local cache and a ticket-id cache/counter should do the trick. We can
at least implemente idempotency for the last xx minutes. Since the timestamp is also sent - that should suffice!)

### Dateformat

[4 digit year][2 digit month][2 digit day][2 digit hour][2 digit minute][2 digit seconds]

**************

can be easily parsed. might be a nuisance in the bridges payload/json converters but ok.

### amount, balance

all amounts, balances, bets, wins, ... are handled in integer cents.

### Retry

Spinomenals retry mechanism needs to be taken care of with utter importance since sending out bad responses might cause
a player locked on all games from spinomenal!!

### Signature checks

Signatures must always be checked, we assume that sig-caching is useless since in most usecases the ticket-id is
increasing and so sigs are constantly changing.

### ticket-ID

Basic key of the transaction - is used in processBet AND in SolveBet which can be called (anytime) to check the status
of any ticketId.

## Endpoint Spinomenal

These methods are initiated from our side (we call spinomenal)

### LaunchDemoGame

Returns a url to start a specific demo game

### Generate Token

Returns a token and url. Token represents a session of a ously user and a spinomenal game. This token is used for every
call of:

* player balance
* processBet

## Endpoint OuslyBridge

These methods are initiated from spinomenal (spinomenal calls us)

### PlayerBalance

returns the players current balance or an error code when player is locked, unknown,....

### processBet

bet (start of game), win (end of game), and cancellations of bets and instantWins So we expect to receive 2 per game.

### SolveBet

Spinomenal might send those requests anytime to verify the reception (and processing) of any prior ticketId.

## DB Structure

This are the tables we need for the spinomenal bridge. I would rather make those global tables (at least transactions)
but that might be difficult.

We need to decide if we have a set of tables per provider - might make a lot of sense since we do not know all apis
upfront. For reporting, revshare calculation, etc... we can create views and unions to use all providers for revshare
calculation. If those views prove slow we can materialize them (preaggregated or not) daily,weekly, monthly.

### ActiveTokens (Spinomenal)

* externalId
* providerId
* gameCode
* url of game
* gameToken
* levelId
* requestedTimestamp

The active tokens are cached, if not in cache the token gets loaded from DB. Its write-once-read-multiple (WORM) so we
only need to remove it from cache when a new token comes in!

### Transaction (Spinomenal)

* gameToken
* ticketid
* providerCode (unique gameId on provider site) makes ticketId unique
* gameCode - gameId (i do not get the difference to providerCode)
* RequestId - (used for sig)
* roundId
* IsRoundFinish booleand
* externalId (external wallet id)
* bet amount
* win amount
* type (win,bet,winbet,....)
* timestamp
* done boolean (true if no errors)
* refTicketId

### Leveling (Global)

* id
* provider
* gameId
* level
* bet amount total
* win amount total
* betcount
* wincount

The current levelling is cached (via jpa?!) - the problem might be opt. locking - when loadbalancer decides to switch
hosts. But in that case the new host would load the entity. Problem might arise when loadbalancer switches back to old
host which can have a stale version - then optLock would fail and we would need to wait for the retry -> which would
also fail !! Or we register a remove from cache upon any transaction error. But this can also happen on wallet. We
should just try it...


 
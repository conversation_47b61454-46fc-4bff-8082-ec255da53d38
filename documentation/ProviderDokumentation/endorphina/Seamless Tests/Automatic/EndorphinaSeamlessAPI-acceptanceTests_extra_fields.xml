<?xml version="1.0" encoding="UTF-8"?>
<con:soapui-project activeEnvironment="Default" name="EndorphinaSeamlessAPI_acceptanceTests_full" resourceRoot=""
                    soapui-version="5.2.1" abortOnError="false" runType="SEQUENTIAL"
                    id="10f5072f-ec07-4146-931b-04fe1a2ca448" xmlns:con="http://eviware.com/soapui/config">
    <con:settings/>
    <con:interface xsi:type="con:RestService" wadlVersion="http://wadl.dev.java.net/2009/02" name="" type="rest"
                   id="7e564a63-4dd6-4abd-9b1f-1fded7e9ec0c" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <con:settings/>
        <con:definitionCache type="TEXT" rootPart=""/>
        <con:endpoints/>
        <con:resource name="session" path="/session" id="f8610e90-fb6e-4dc7-bcc3-0245136c9f43">
            <con:settings/>
            <con:parameters/>
            <con:method name="session" method="GET" id="aeb72935-d836-4f2c-89c9-d7ed1b3e07d5">
                <con:settings>
                    <con:setting id="RecordResponseRepresentations">false</con:setting>
                </con:settings>
                <con:parameters>
                    <con:parameter>
                        <con:name>token</con:name>
                        <con:value/>
                        <con:style>QUERY</con:style>
                        <con:default/>
                    </con:parameter>
                    <con:parameter>
                        <con:name>sign</con:name>
                        <con:style>QUERY</con:style>
                    </con:parameter>
                </con:parameters>
                <con:request name="Request 1" mediaType="application/json" id="86362999-0c19-4d28-ae83-eb6f6c20c2ab">
                    <con:settings>
                        <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/>
                        </con:setting>
                    </con:settings>
                    <con:endpoint/>
                    <con:request/>
                    <con:credentials>
                        <con:authType>No Authorization</con:authType>
                    </con:credentials>
                    <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                    <con:jmsPropertyConfig/>
                    <con:parameters/>
                    <con:parameterOrder>
                        <con:entry>token</con:entry>
                        <con:entry>sign</con:entry>
                    </con:parameterOrder>
                </con:request>
            </con:method>
        </con:resource>
        <con:resource name="bet" path="/bet" id="87c4f898-e553-45fa-9ea8-ab3788ae503c">
            <con:settings/>
            <con:parameters>
                <con:parameter>
                    <con:name>amount</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>date</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>id</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>gameId</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>token</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>sign</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>game</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>player</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>currency</con:name>
                    <con:style>QUERY</con:style>
                </con:parameter>
            </con:parameters>
            <con:method name="bet" method="POST" id="208d37c3-3c1d-418e-ab03-c979fca7d43c">
                <con:settings>
                    <con:setting id="RecordResponseRepresentations">false</con:setting>
                </con:settings>
                <con:parameters/>
                <con:representation type="REQUEST">
                    <con:mediaType>application/x-www-form-urlencoded</con:mediaType>
                    <con:params/>
                </con:representation>
                <con:request name="Request 1" mediaType="application/x-www-form-urlencoded"
                             id="366c4e54-79b1-402c-94f5-70d047dd171a" postQueryString="true">
                    <con:settings>
                        <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/>
                        </con:setting>
                        <con:setting id="WsdlSettings@pretty-print-response-xml">true</con:setting>
                    </con:settings>
                    <con:endpoint/>
                    <con:request/>
                    <con:credentials>
                        <con:authType>No Authorization</con:authType>
                    </con:credentials>
                    <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                    <con:jmsPropertyConfig/>
                    <con:parameters/>
                    <con:parameterOrder>
                        <con:entry>amount</con:entry>
                        <con:entry>date</con:entry>
                        <con:entry>id</con:entry>
                        <con:entry>gameId</con:entry>
                        <con:entry>token</con:entry>
                        <con:entry>sign</con:entry>
                        <con:entry>game</con:entry>
                        <con:entry>player</con:entry>
                        <con:entry>currency</con:entry>
                    </con:parameterOrder>
                </con:request>
            </con:method>
        </con:resource>
        <con:resource name="refund" path="/refund" id="2fac492f-49db-4951-982b-cbc113b7028f">
            <con:settings/>
            <con:parameters>
                <con:parameter>
                    <con:name>amount</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>date</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>id</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>gameId</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>token</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>sign</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>game</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>player</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>currency</con:name>
                    <con:style>QUERY</con:style>
                </con:parameter>
            </con:parameters>
            <con:method name="refund" method="POST" id="5d081c21-72c0-4be0-a21f-2c3038c44dae">
                <con:settings>
                    <con:setting id="RecordResponseRepresentations">false</con:setting>
                </con:settings>
                <con:parameters/>
                <con:request name="Request 1" mediaType="application/x-www-form-urlencoded"
                             id="1d14dbe9-37f4-4335-8d07-66b07ffceedc" postQueryString="true">
                    <con:settings>
                        <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/>
                        </con:setting>
                    </con:settings>
                    <con:endpoint/>
                    <con:request/>
                    <con:credentials>
                        <con:authType>No Authorization</con:authType>
                    </con:credentials>
                    <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                    <con:jmsPropertyConfig/>
                    <con:parameters/>
                    <con:parameterOrder>
                        <con:entry>amount</con:entry>
                        <con:entry>date</con:entry>
                        <con:entry>id</con:entry>
                        <con:entry>gameId</con:entry>
                        <con:entry>token</con:entry>
                        <con:entry>sign</con:entry>
                        <con:entry>game</con:entry>
                        <con:entry>player</con:entry>
                        <con:entry>currency</con:entry>
                    </con:parameterOrder>
                </con:request>
            </con:method>
        </con:resource>
        <con:resource name="win" path="/win" id="ff3f4d5b-32fd-429a-9c94-a9acbe8cf204">
            <con:settings/>
            <con:parameters>
                <con:parameter>
                    <con:name>amount</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>date</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>id</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>gameId</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter required="false">
                    <con:name>progressive</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter required="false">
                    <con:name>progressiveDesc</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>token</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>sign</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>currency</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>player</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>game</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>betSessionId</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>betTransactionId</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
            </con:parameters>
            <con:method name="win" method="POST" id="112fc0cd-cc2c-4eed-9321-bc834d8a72c0">
                <con:settings>
                    <con:setting id="RecordResponseRepresentations">false</con:setting>
                </con:settings>
                <con:parameters/>
                <con:representation type="REQUEST">
                    <con:mediaType>application/x-www-form-urlencoded</con:mediaType>
                    <con:params/>
                </con:representation>
                <con:request name="Request 1" mediaType="application/x-www-form-urlencoded"
                             id="d3c0fd89-2de3-4851-a66a-5a2fa598340c" postQueryString="true">
                    <con:settings>
                        <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/>
                        </con:setting>
                    </con:settings>
                    <con:endpoint/>
                    <con:request/>
                    <con:credentials>
                        <con:authType>No Authorization</con:authType>
                    </con:credentials>
                    <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                    <con:jmsPropertyConfig/>
                    <con:parameters/>
                    <con:parameterOrder>
                        <con:entry>amount</con:entry>
                        <con:entry>date</con:entry>
                        <con:entry>id</con:entry>
                        <con:entry>gameId</con:entry>
                        <con:entry>progressive</con:entry>
                        <con:entry>progressiveDesc</con:entry>
                        <con:entry>token</con:entry>
                        <con:entry>sign</con:entry>
                        <con:entry>currency</con:entry>
                        <con:entry>player</con:entry>
                        <con:entry>game</con:entry>
                        <con:entry>betSessionId</con:entry>
                        <con:entry>betTransactionId</con:entry>
                    </con:parameterOrder>
                </con:request>
            </con:method>
        </con:resource>
        <con:resource name="balance" path="/balance" id="5e961daa-837b-497a-8f10-18560366a7e2">
            <con:settings/>
            <con:parameters>
                <con:parameter>
                    <con:name>token</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>sign</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>game</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>player</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>currency</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
            </con:parameters>
            <con:method name="balance" method="GET" id="48627f63-e19a-4feb-b53d-d330fc32df5a">
                <con:settings>
                    <con:setting id="RecordResponseRepresentations">false</con:setting>
                </con:settings>
                <con:parameters/>
                <con:request name="Request 1" mediaType="application/json" id="88a29592-804f-4f3b-8259-b1f439d1c753">
                    <con:settings>
                        <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/>
                        </con:setting>
                    </con:settings>
                    <con:endpoint/>
                    <con:request/>
                    <con:credentials>
                        <con:authType>No Authorization</con:authType>
                    </con:credentials>
                    <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                    <con:jmsPropertyConfig/>
                    <con:parameters/>
                    <con:parameterOrder>
                        <con:entry>token</con:entry>
                        <con:entry>sign</con:entry>
                        <con:entry>game</con:entry>
                        <con:entry>player</con:entry>
                        <con:entry>currency</con:entry>
                    </con:parameterOrder>
                </con:request>
            </con:method>
        </con:resource>
        <con:resource name="endSession" path="/endSession" id="5e961daa-837b-497a-8f10-18560366a7e2">
            <con:settings/>
            <con:parameters>
                <con:parameter>
                    <con:name>bet</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>bonusBet</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>bonusGames</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>bonusWin</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>currency</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                    <con:description xsi:nil="true"/>
                </con:parameter>
                <con:parameter>
                    <con:name>game</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>games</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>jackpotWin</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>jackpots</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>player</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>token</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>win</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>sign</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
            </con:parameters>
            <con:method name="endSession" method="POST" id="48627f63-e19a-4feb-b53d-d330fc32df5a">
                <con:settings>
                    <con:setting id="RecordResponseRepresentations">false</con:setting>
                </con:settings>
                <con:parameters/>
                <con:request name="Request 1" mediaType="application/json" id="88a29592-804f-4f3b-8259-b1f439d1c753">
                    <con:settings>
                        <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/>
                        </con:setting>
                    </con:settings>
                    <con:endpoint/>
                    <con:request/>
                    <con:credentials>
                        <con:authType>No Authorization</con:authType>
                    </con:credentials>
                    <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                    <con:jmsPropertyConfig/>
                    <con:parameters/>
                    <con:parameterOrder>
                        <con:entry>bet</con:entry>
                        <con:entry>bonusBet</con:entry>
                        <con:entry>bonusGames</con:entry>
                        <con:entry>bonusWin</con:entry>
                        <con:entry>currency</con:entry>
                        <con:entry>game</con:entry>
                        <con:entry>games</con:entry>
                        <con:entry>jackpotWin</con:entry>
                        <con:entry>jackpots</con:entry>
                        <con:entry>player</con:entry>
                        <con:entry>token</con:entry>
                        <con:entry>win</con:entry>
                        <con:entry>sign</con:entry>
                    </con:parameterOrder>
                </con:request>
            </con:method>
        </con:resource>
        <con:resource name="check" path="/check" id="5e961daa-837b-497a-8f10-18560366a7e2">
            <con:settings/>
            <con:parameters>
                <con:parameter>
                    <con:name>param</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
                <con:parameter>
                    <con:name>sign</con:name>
                    <con:value/>
                    <con:style>QUERY</con:style>
                    <con:default/>
                </con:parameter>
            </con:parameters>
            <con:method name="check" method="GET" id="48627f63-e19a-4feb-b53d-d330fc32df5a">
                <con:settings>
                    <con:setting id="RecordResponseRepresentations">false</con:setting>
                </con:settings>
                <con:parameters/>
                <con:request name="Request 1" mediaType="application/json" id="88a29592-804f-4f3b-8259-b1f439d1c753"
                             postQueryString="false">
                    <con:settings>
                        <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/>
                        </con:setting>
                    </con:settings>
                    <con:endpoint/>
                    <con:request/>
                    <con:credentials>
                        <con:authType>No Authorization</con:authType>
                    </con:credentials>
                    <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                    <con:jmsPropertyConfig/>
                    <con:parameters/>
                    <con:parameterOrder>
                        <con:entry>param</con:entry>
                        <con:entry>sign</con:entry>
                    </con:parameterOrder>
                </con:request>
            </con:method>
        </con:resource>
    </con:interface>
    <con:testSuite name="Templates" id="315aae5d-7bbb-4932-8492-305774319b41" disabled="true">
        <con:settings/>
        <con:runType>SEQUENTIAL</con:runType>
        <con:testCase failOnError="false" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="session"
                      searchProperties="true" id="0d26fbde-2c14-4eec-b1bc-d92a27b3cc3d" timeout="0" wsrmEnabled="false"
                      wsrmVersion="1.0" wsrmAckTo="" amfAuthorisation="false" amfEndpoint="" amfLogin="" amfPassword="">
            <con:settings/>
            <con:testStep type="restrequest" name="REST Test Request" id="5c7a49cd-4323-4145-b8b6-e425ca66290a">
                <con:settings/>
                <con:config service="" methodName="session" resourcePath="/session" xsi:type="con:RestRequestStep"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:restRequest name="REST Test Request" mediaType="application/json"
                                     id="40e1438b-cbef-4046-b5b5-871904027f77">
                        <con:settings>
                            <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;entry
                                key="Accept" value="application/json" xmlns="http://eviware.com/soapui/config"/>
                            </con:setting>
                        </con:settings>
                        <con:encoding>UTF-8</con:encoding>
                        <con:endpoint>${#Project#seamlessUrl}</con:endpoint>
                        <con:request/>
                        <con:originalUri>http://localhost/endorphina/session</con:originalUri>
                        <con:assertion type="Valid HTTP Status Codes" name="Valid HTTP Status Codes"
                                       id="0848d335-314f-4c90-8173-9426dccee719">
                            <con:configuration>
                                <codes>200</codes>
                            </con:configuration>
                        </con:assertion>
                        <con:assertion type="GroovyScriptAssertion" name="Script Assertion"
                                       id="413c9f44-888f-4eb8-8b64-164ca1d9ee10">
                            <con:configuration>
                                <scriptText><![CDATA[import groovy.json.JsonSlurper

assert messageExchange.responseHeaders["Content-Type"][0].contains("application/json");

def response = messageExchange.response.responseContent
def slurper = new JsonSlurper()
def json = slurper.parseText response

assert json.player instanceof String;
assert json.game instanceof String;
assert json.currency instanceof String;

context.testCase.setPropertyValue("_player", json.player);
context.testCase.setPropertyValue("_currency", json.currency);
context.testCase.setPropertyValue("_game", json.game);
context.testCase.setPropertyValue("_token", context.expand('${#TestCase#token}'));


assert json.size() == 3;
assert (json.player.length() >= 1) && (json.player.length() <= 32);
assert (json.game.length() > 3) && (json.game.length() <= 255);
assert json.currency.length() == 3;]]></scriptText>
                            </con:configuration>
                        </con:assertion>
                        <con:credentials>
                            <con:authType>No Authorization</con:authType>
                        </con:credentials>
                        <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                        <con:jmsPropertyConfig/>
                        <con:parameters>
                            <con:entry key="sign" value="${sign}"/>
                            <con:entry key="token" value="${#TestCase#token}"/>
                        </con:parameters>
                    </con:restRequest>
                </con:config>
            </con:testStep>
            <con:setupScript>import org.apache.commons.codec.digest.DigestUtils;
                def token = context.expand('${#TestCase#token}');
                def secretKey = context.expand('${#Project#secretKey}');

                context.sign = DigestUtils.shaHex("$token$secretKey");
            </con:setupScript>
            <con:properties>
                <con:property>
                    <con:name>token</con:name>
                    <con:value>9bdbbf5e2e604cf3b9155ad20d8e2aa6</con:value>
                </con:property>
                <con:property>
                    <con:name>_player</con:name>
                    <con:value>297519c7cfac45c0beb770ad87492791</con:value>
                </con:property>
                <con:property>
                    <con:name>_currency</con:name>
                    <con:value>AED</con:value>
                </con:property>
                <con:property>
                    <con:name>_game</con:name>
                    <con:value>endorphina_TheKing2@ORGANIC</con:value>
                </con:property>
                <con:property>
                    <con:name>_token</con:name>
                    <con:value>9bdbbf5e2e604cf3b9155ad20d8e2aa6</con:value>
                </con:property>
            </con:properties>
        </con:testCase>
        <con:testCase failOnError="false" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="bet"
                      searchProperties="true" id="3b5358cb-dde1-465b-9117-cceacb7bce2d" timeout="0" wsrmEnabled="false"
                      wsrmVersion="1.0" wsrmAckTo="" amfAuthorisation="false" amfEndpoint="" amfLogin="" amfPassword="">
            <con:settings/>
            <con:testStep type="restrequest" name="REST Test Request" id="9e9620ca-e084-4846-95b2-257466fe7cbe">
                <con:settings/>
                <con:config service="" methodName="bet" resourcePath="/bet" xsi:type="con:RestRequestStep"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:restRequest name="REST Test Request" mediaType="application/x-www-form-urlencoded"
                                     postQueryString="true" id="18cc664f-0549-4fe3-94d4-9ebf265b22b0">
                        <con:settings>
                            <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;entry
                                key="Accept" value="application/json" xmlns="http://eviware.com/soapui/config"/>
                            </con:setting>
                            <con:setting id="RecordRequestRepresentations">true</con:setting>
                        </con:settings>
                        <con:encoding>UTF-8</con:encoding>
                        <con:endpoint>${#Project#seamlessUrl}</con:endpoint>
                        <con:request/>
                        <con:originalUri>http://localhost/bet</con:originalUri>
                        <con:assertion type="Valid HTTP Status Codes" name="Valid HTTP Status Codes"
                                       id="7aa0c976-c8bc-4d21-a60c-60165998f507">
                            <con:configuration>
                                <codes>200</codes>
                            </con:configuration>
                        </con:assertion>
                        <con:assertion type="GroovyScriptAssertion" name="Script Assertion"
                                       id="652babb8-145f-4be2-a8a6-1574cbe55a0b">
                            <con:configuration>
                                <scriptText>import groovy.json.JsonSlurper

                                    assert
                                    messageExchange.responseHeaders["Content-Type"][0].contains("application/json");

                                    def response = messageExchange.response.responseContent

                                    assert response != null;

                                    def slurper = new JsonSlurper()
                                    def json = slurper.parseText response

                                    assert json.size() == 2;
                                    assert json.balance instanceof Number;

                                    def balance = new BigDecimal(json.balance) / 1000;
                                    context.testCase.setPropertyValue("_balance", "$balance");

                                    def etalonBalance = context.expand('${#TestCase#etalonBalance}')
                                    if (etalonBalance != '')
                                    assert new BigDecimal(etalonBalance) == balance
                                    else
                                    assert balance >= 0;

                                    def etalonTransactionId = context.expand('${#TestCase#etalonTransactionId}')
                                    assert json.transactionId instanceof String;
                                    if (etalonTransactionId != '')
                                    assert json.transactionId == etalonTransactionId
                                    else
                                    assert (json.transactionId.length() > 1) &amp;&amp; (json.transactionId.length()
                                    &lt;= 32);

                                    context.testCase.setPropertyValue('_transactionId', '' + "$json.transactionId");
                                    context.testCase.setPropertyValue('_balance', "$balance");
                                    context.testCase.setPropertyValue("_id", context.expand('${#TestCase#id}'));
                                </scriptText>
                            </con:configuration>
                        </con:assertion>
                        <con:credentials>
                            <con:authType>No Authorization</con:authType>
                        </con:credentials>
                        <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                        <con:jmsPropertyConfig/>
                        <con:parameters>
                            <con:entry key="gameId" value="${#TestCase#gameId}"/>
                            <con:entry key="date" value="${#TestCase#date}"/>
                            <con:entry key="amount" value="${amount}"/>
                            <con:entry key="game" value="${#TestCase#game}"/>
                            <con:entry key="sign" value="${sign}"/>
                            <con:entry key="currency" value="${#TestCase#currency}"/>
                            <con:entry key="id" value="${#TestCase#id}"/>
                            <con:entry key="token" value="${#TestCase#token}"/>
                            <con:entry key="player" value="${#TestCase#player}"/>
                        </con:parameters>
                        <con:parameterOrder>
                            <con:entry>amount</con:entry>
                            <con:entry>date</con:entry>
                            <con:entry>id</con:entry>
                            <con:entry>gameId</con:entry>
                            <con:entry>token</con:entry>
                            <con:entry>sign</con:entry>
                            <con:entry>game</con:entry>
                            <con:entry>player</con:entry>
                            <con:entry>currency</con:entry>
                        </con:parameterOrder>
                    </con:restRequest>
                </con:config>
            </con:testStep>
            <con:setupScript>import org.apache.commons.codec.digest.DigestUtils;

                def token = context.expand('${#TestCase#token}');
                def game = context.expand('${#TestCase#game}');
                def player = context.expand('${#TestCase#player}');
                def currency = context.expand('${#TestCase#currency}');

                def secretKey = context.expand('${#Project#secretKey}');
                def amount = (new BigDecimal(context.expand('${#TestCase#amount}')) * 1000).setScale(0);
                def date = context.expand('${#TestCase#date}');
                def id = context.expand('${#TestCase#id}');
                def gameId = context.expand('${#TestCase#gameId}');

                context.amount = amount;
                context.sign = DigestUtils.shaHex("$amount$currency$date$game$gameId$id$player$token$secretKey");
            </con:setupScript>
            <con:properties>
                <con:property>
                    <con:name>amount</con:name>
                    <con:value>0.03</con:value>
                </con:property>
                <con:property>
                    <con:name>date</con:name>
                    <con:value>1568132515969</con:value>
                </con:property>
                <con:property>
                    <con:name>id</con:name>
                    <con:value>1562155613524</con:value>
                </con:property>
                <con:property>
                    <con:name>gameId</con:name>
                    <con:value>7</con:value>
                </con:property>
                <con:property>
                    <con:name>token</con:name>
                    <con:value>9bdbbf5e2e604cf3b9155ad20d8e2aa6</con:value>
                </con:property>
                <con:property>
                    <con:name>etalonBalance</con:name>
                    <con:value>77.77</con:value>
                </con:property>
                <con:property>
                    <con:name>etalonTransactionId</con:name>
                    <con:value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">1562155613529</con:value>
                </con:property>
                <con:property>
                    <con:name>_balance</con:name>
                    <con:value>409.03</con:value>
                </con:property>
                <con:property>
                    <con:name>_id</con:name>
                    <con:value>1562176353281</con:value>
                </con:property>
                <con:property>
                    <con:name>_transactionId</con:name>
                    <con:value>1562176353302</con:value>
                </con:property>
                <con:property>
                    <con:name>game</con:name>
                    <con:value>endorphina_TheKing2@ORGANIC</con:value>
                </con:property>
                <con:property>
                    <con:name>player</con:name>
                    <con:value>297519c7cfac45c0beb770ad87492791</con:value>
                </con:property>
                <con:property>
                    <con:name>currency</con:name>
                    <con:value>AED</con:value>
                </con:property>
            </con:properties>
        </con:testCase>
        <con:testCase failOnError="false" failTestCaseOnErrors="true" keepSession="false" maxResults="0"
                      name="bet error" searchProperties="true" id="444821f2-0fb5-4e57-9b1e-3556b93f0161" timeout="0"
                      wsrmEnabled="false" wsrmVersion="1.0" wsrmAckTo="" amfAuthorisation="false" amfEndpoint=""
                      amfLogin="" amfPassword="">
            <con:settings/>
            <con:testStep type="restrequest" name="REST Test Request" id="04b9d2f0-a85c-437c-81b0-57e124f1a6f0">
                <con:settings/>
                <con:config service="" methodName="bet" resourcePath="/bet" xsi:type="con:RestRequestStep"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:restRequest name="REST Test Request" mediaType="application/x-www-form-urlencoded"
                                     postQueryString="true" id="cab95cac-0c78-43b7-8d3e-584e236820e0">
                        <con:settings>
                            <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;entry
                                key="Accept" value="application/json" xmlns="http://eviware.com/soapui/config"/>
                            </con:setting>
                        </con:settings>
                        <con:encoding>UTF-8</con:encoding>
                        <con:endpoint>${#Project#seamlessUrl}</con:endpoint>
                        <con:request/>
                        <con:originalUri>http://localhost/bet</con:originalUri>
                        <con:assertion type="Valid HTTP Status Codes" name="Valid HTTP Status Codes"
                                       id="5c45e03c-a7d8-4686-b7ce-e2f292e8a264">
                            <con:configuration>
                                <codes>${#TestCase#httpCode}</codes>
                            </con:configuration>
                        </con:assertion>
                        <con:assertion type="GroovyScriptAssertion" name="Script Assertion"
                                       id="652babb8-145f-4be2-a8a6-1574cbe55a0b">
                            <con:configuration>
                                <scriptText>import groovy.json.JsonSlurper

                                    assert
                                    messageExchange.responseHeaders["Content-Type"][0].contains("application/json");

                                    def response = messageExchange.response.responseContent

                                    assert response != null

                                    def slurper = new JsonSlurper()
                                    def json = slurper.parseText response

                                    assert json.size() == 2;

                                    def errorCode = context.expand('${#TestCase#errorCode}')
                                    assert json.code instanceof String;
                                    assert json.code == errorCode;

                                    def errorMessage = context.expand('${#TestCase#errorMessage}')
                                    assert json.message instanceof String;

                                    if (errorMessage != "")
                                    assert json.message == errorMessage
                                    else
                                    assert json.message.length() > 0;

                                    context.testCase.setPropertyValue("_id", context.expand('${#TestCase#id}'));
                                </scriptText>
                            </con:configuration>
                        </con:assertion>
                        <con:credentials>
                            <con:authType>No Authorization</con:authType>
                        </con:credentials>
                        <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                        <con:jmsPropertyConfig/>
                        <con:parameters>
                            <con:entry key="gameId" value="${#TestCase#gameId}"/>
                            <con:entry key="date" value="${#TestCase#date}"/>
                            <con:entry key="amount" value="${amount}"/>
                            <con:entry key="game" value="${#TestCase#game}"/>
                            <con:entry key="sign" value="${sign}"/>
                            <con:entry key="currency" value="${#TestCase#currency}"/>
                            <con:entry key="id" value="${#TestCase#id}"/>
                            <con:entry key="token" value="${#TestCase#token}"/>
                            <con:entry key="player" value="${#TestCase#player}"/>
                        </con:parameters>
                        <con:parameterOrder>
                            <con:entry>amount</con:entry>
                            <con:entry>date</con:entry>
                            <con:entry>id</con:entry>
                            <con:entry>gameId</con:entry>
                            <con:entry>token</con:entry>
                            <con:entry>sign</con:entry>
                            <con:entry>game</con:entry>
                            <con:entry>player</con:entry>
                            <con:entry>currency</con:entry>
                        </con:parameterOrder>
                    </con:restRequest>
                </con:config>
            </con:testStep>
            <con:setupScript>import org.apache.commons.codec.digest.DigestUtils;

                def token = context.expand('${#TestCase#token}');
                def game = context.expand('${#TestCase#game}');
                def player = context.expand('${#TestCase#player}');
                def currency = context.expand('${#TestCase#currency}');
                def secretKey = context.expand('${#Project#secretKey}');
                def amount = (new BigDecimal(context.expand('${#TestCase#amount}')) * 1000).setScale(0);
                def date = context.expand('${#TestCase#date}');
                def id = context.expand('${#TestCase#id}');
                def gameId = context.expand('${#TestCase#gameId}');

                context.amount = amount;
                context.sign = DigestUtils.shaHex("$amount$currency$date$game$gameId$id$player$token$secretKey");
            </con:setupScript>
            <con:properties>
                <con:property>
                    <con:name>amount</con:name>
                    <con:value>5003143.58</con:value>
                </con:property>
                <con:property>
                    <con:name>date</con:name>
                    <con:value>1568132515898</con:value>
                </con:property>
                <con:property>
                    <con:name>id</con:name>
                    <con:value>1568132515904</con:value>
                </con:property>
                <con:property>
                    <con:name>gameId</con:name>
                    <con:value>5</con:value>
                </con:property>
                <con:property>
                    <con:name>token</con:name>
                    <con:value>9bdbbf5e2e604cf3b9155ad20d8e2aa6</con:value>
                </con:property>
                <con:property>
                    <con:name>httpCode</con:name>
                    <con:value>402</con:value>
                </con:property>
                <con:property>
                    <con:name>errorCode</con:name>
                    <con:value>INSUFFICIENT_FUNDS</con:value>
                </con:property>
                <con:property>
                    <con:name>errorMessage</con:name>
                    <con:value/>
                </con:property>
                <con:property>
                    <con:name>_id</con:name>
                    <con:value>1562155613423</con:value>
                </con:property>
                <con:property>
                    <con:name>game</con:name>
                    <con:value>endorphina_TheKing2@ORGANIC</con:value>
                </con:property>
                <con:property>
                    <con:name>player</con:name>
                    <con:value>297519c7cfac45c0beb770ad87492791</con:value>
                </con:property>
                <con:property>
                    <con:name>currency</con:name>
                    <con:value>AED</con:value>
                </con:property>
            </con:properties>
        </con:testCase>
        <con:testCase failOnError="false" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="win"
                      searchProperties="true" id="3f1d1fe9-e0dd-4981-9713-ea03b0959627" timeout="0" wsrmEnabled="false"
                      wsrmVersion="1.0" wsrmAckTo="" amfAuthorisation="false" amfEndpoint="" amfLogin="" amfPassword="">
            <con:settings/>
            <con:testStep type="restrequest" name="REST Test Request" id="442cf991-395c-40b4-849a-8e59da056acc">
                <con:settings/>
                <con:config service="" methodName="win" resourcePath="/win" xsi:type="con:RestRequestStep"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:restRequest name="REST Test Request" mediaType="application/x-www-form-urlencoded"
                                     postQueryString="true" id="abe7d440-c284-4557-ab4b-96f802a5f47c">
                        <con:settings>
                            <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;entry
                                key="Accept" value="application/json" xmlns="http://eviware.com/soapui/config"/>
                            </con:setting>
                            <con:setting id="RecordRequestRepresentations">true</con:setting>
                        </con:settings>
                        <con:encoding>UTF-8</con:encoding>
                        <con:endpoint>${#Project#seamlessUrl}</con:endpoint>
                        <con:request/>
                        <con:originalUri>http://localhost/win</con:originalUri>
                        <con:assertion type="Valid HTTP Status Codes" name="Valid HTTP Status Codes"
                                       id="91176c93-9adf-4a86-b820-a3476a19a712">
                            <con:configuration>
                                <codes>200</codes>
                            </con:configuration>
                        </con:assertion>
                        <con:assertion type="GroovyScriptAssertion" name="Script Assertion"
                                       id="d0ef5725-51ff-4709-b137-00aac0ad85df">
                            <con:configuration>
                                <scriptText>import groovy.json.JsonSlurper

                                    assert
                                    messageExchange.responseHeaders["Content-Type"][0].contains("application/json");

                                    def response = messageExchange.response.responseContent

                                    assert response != null

                                    def slurper = new JsonSlurper()
                                    def json = slurper.parseText response

                                    assert json.size() == 2;

                                    assert json.balance instanceof Number;
                                    def balance = new BigDecimal(json.balance) / 1000;

                                    def etalonBalance = context.expand('${#TestCase#etalonBalance}')
                                    if (etalonBalance != '')
                                    assert new BigDecimal(etalonBalance) == balance
                                    else
                                    assert balance >= 0;

                                    def etalonTransactionId = context.expand('${#TestCase#etalonTransactionId}')
                                    assert json.transactionId instanceof String;
                                    if (etalonTransactionId != '')
                                    assert json.transactionId == etalonTransactionId
                                    else
                                    assert (json.transactionId.length() >= 0) &amp;&amp; (json.transactionId.length()
                                    &lt;= 32);

                                    context.testCase.setPropertyValue('_transactionId', '' + "$json.transactionId");
                                    context.testCase.setPropertyValue('_balance', "$balance");

                                    context.testCase.setPropertyValue("_id", context.expand('${#TestCase#id}'));
                                </scriptText>
                            </con:configuration>
                        </con:assertion>
                        <con:credentials>
                            <con:authType>No Authorization</con:authType>
                        </con:credentials>
                        <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                        <con:jmsPropertyConfig/>
                        <con:parameters>
                            <con:entry key="gameId" value="${#TestCase#gameId}"/>
                            <con:entry key="date" value="${#TestCase#date}"/>
                            <con:entry key="amount" value="${amount}"/>
                            <con:entry key="game" value="${#TestCase#game}"/>
                            <con:entry key="betTransactionId" value="${#TestCase#betTransactionId}"/>
                            <con:entry key="sign" value="${sign}"/>
                            <con:entry key="currency" value="${#TestCase#currency}"/>
                            <con:entry key="id" value="${#TestCase#id}"/>
                            <con:entry key="token" value="${#TestCase#token}"/>
                            <con:entry key="player" value="${#TestCase#player}"/>
                            <con:entry key="betSessionId" value="${#TestCase#betSessionId}"/>
                        </con:parameters>
                        <con:parameterOrder>
                            <con:entry>amount</con:entry>
                            <con:entry>date</con:entry>
                            <con:entry>id</con:entry>
                            <con:entry>gameId</con:entry>
                            <con:entry>progressive</con:entry>
                            <con:entry>progressiveDesc</con:entry>
                            <con:entry>token</con:entry>
                            <con:entry>sign</con:entry>
                            <con:entry/>
                            <con:entry>currency</con:entry>
                            <con:entry>player</con:entry>
                            <con:entry>game</con:entry>
                            <con:entry>betSessionId</con:entry>
                        </con:parameterOrder>
                    </con:restRequest>
                </con:config>
            </con:testStep>
            <con:setupScript>import org.apache.commons.codec.digest.DigestUtils;

                def token = context.expand('${#TestCase#token}');
                def game = context.expand('${#TestCase#game}');
                def player = context.expand('${#TestCase#player}');
                def currency = context.expand('${#TestCase#currency}');
                def betSessionId = context.expand('${#TestCase#betSessionId}');
                def betTransactionId = context.expand('${#TestCase#betTransactionId}');
                def secretKey = context.expand('${#Project#secretKey}');

                def amount = (new BigDecimal(context.expand('${#TestCase#amount}')) * 1000).setScale(0);
                def date = context.expand('${#TestCase#date}');
                def id = context.expand('${#TestCase#id}');
                def gameId = context.expand('${#TestCase#gameId}');

                context.amount = amount;

                context.sign =
                DigestUtils.shaHex("$amount$betSessionId$betTransactionId$currency$date$game$gameId$id$player$token$secretKey");
            </con:setupScript>
            <con:properties>
                <con:property>
                    <con:name>amount</con:name>
                    <con:value>77.77</con:value>
                </con:property>
                <con:property>
                    <con:name>date</con:name>
                    <con:value>1568132515937</con:value>
                </con:property>
                <con:property>
                    <con:name>id</con:name>
                    <con:value>1568132515942</con:value>
                </con:property>
                <con:property>
                    <con:name>gameId</con:name>
                    <con:value>6</con:value>
                </con:property>
                <con:property>
                    <con:name>token</con:name>
                    <con:value>9bdbbf5e2e604cf3b9155ad20d8e2aa6</con:value>
                </con:property>
                <con:property>
                    <con:name>game</con:name>
                    <con:value>endorphina_TheKing2@ORGANIC</con:value>
                </con:property>
                <con:property>
                    <con:name>player</con:name>
                    <con:value>297519c7cfac45c0beb770ad87492791</con:value>
                </con:property>
                <con:property>
                    <con:name>currency</con:name>
                    <con:value>AED</con:value>
                </con:property>
                <con:property>
                    <con:name>betSessionId</con:name>
                    <con:value>9bdbbf5e2e604cf3b9155ad20d8e2aa6</con:value>
                </con:property>
                <con:property>
                    <con:name>betTransactionId</con:name>
                    <con:value>1562176353302</con:value>
                </con:property>
                <con:property>
                    <con:name>etalonBalance</con:name>
                    <con:value>77.77</con:value>
                </con:property>
                <con:property>
                    <con:name>etalonTransactionId</con:name>
                    <con:value/>
                </con:property>
                <con:property>
                    <con:name>_id</con:name>
                    <con:value>1562235120270</con:value>
                </con:property>
                <con:property>
                    <con:name>_balance</con:name>
                    <con:value>1379.48</con:value>
                </con:property>
                <con:property>
                    <con:name>_transactionId</con:name>
                    <con:value>1562235120345</con:value>
                </con:property>
            </con:properties>
        </con:testCase>
        <con:testCase failOnError="false" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="winJP"
                      searchProperties="true" id="4ca4e695-c3f5-421b-87b8-d75bcb759a53" timeout="0" wsrmEnabled="false"
                      wsrmVersion="1.0" wsrmAckTo="" amfAuthorisation="false" amfEndpoint="" amfLogin="" amfPassword="">
            <con:settings/>
            <con:testStep type="restrequest" name="REST Test Request" id="c702cecd-ae89-4393-aa05-994c3550e1fe">
                <con:settings/>
                <con:config service="" methodName="win" resourcePath="/win" xsi:type="con:RestRequestStep"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:restRequest name="REST Test Request" mediaType="application/x-www-form-urlencoded"
                                     postQueryString="true" id="a7273101-829a-462c-8d16-615b86daeb9a">
                        <con:settings>
                            <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;entry
                                key="Accept" value="application/json" xmlns="http://eviware.com/soapui/config"/>
                            </con:setting>
                        </con:settings>
                        <con:encoding>UTF-8</con:encoding>
                        <con:endpoint>${#Project#seamlessUrl}</con:endpoint>
                        <con:request/>
                        <con:originalUri>http://localhost/win</con:originalUri>
                        <con:assertion type="Valid HTTP Status Codes" name="Valid HTTP Status Codes"
                                       id="fcd32b66-3e5e-42b8-ae3c-9258590796c8">
                            <con:configuration>
                                <codes>200</codes>
                            </con:configuration>
                        </con:assertion>
                        <con:assertion type="GroovyScriptAssertion" name="Script Assertion"
                                       id="10933915-68ee-4691-9e43-66d06fca8b47">
                            <con:configuration>
                                <scriptText>import groovy.json.JsonSlurper

                                    assert
                                    messageExchange.responseHeaders["Content-Type"][0].contains("application/json");

                                    def response = messageExchange.response.responseContent

                                    assert response != null

                                    def slurper = new JsonSlurper()
                                    def json = slurper.parseText response

                                    assert json.size() == 2;

                                    assert json.balance instanceof Number;
                                    def balance = new BigDecimal(json.balance) / 1000;
                                    context.testCase.setPropertyValue("_balance", "$balance");

                                    def etalonBalance = context.expand('${#TestCase#etalonBalance}')
                                    if (etalonBalance != '')
                                    assert new BigDecimal(etalonBalance) == balance
                                    else
                                    assert balance >= 0;

                                    def etalonTransactionId = context.expand('${#TestCase#etalonTransactionId}')
                                    assert json.transactionId instanceof String;
                                    if (etalonTransactionId != '')
                                    assert json.transactionId == etalonTransactionId
                                    else
                                    assert (json.transactionId.length() > 1) &amp;&amp; (json.transactionId.length()
                                    &lt;= 32)

                                    context.testCase.setPropertyValue('_transactionId', '' + "$json.transactionId");
                                    context.testCase.setPropertyValue('_balance', "$balance");

                                    context.testCase.setPropertyValue("_id", context.expand('${#TestCase#id}'));
                                </scriptText>
                            </con:configuration>
                        </con:assertion>
                        <con:credentials>
                            <con:authType>No Authorization</con:authType>
                        </con:credentials>
                        <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                        <con:jmsPropertyConfig/>
                        <con:parameters>
                            <con:entry key="gameId" value="${#TestCase#gameId}"/>
                            <con:entry key="date" value="${#TestCase#date}"/>
                            <con:entry key="amount" value="${amount}"/>
                            <con:entry key="game" value="${#TestCase#game}"/>
                            <con:entry key="progressiveDesc" value="${#TestCase#progressiveDesc}"/>
                            <con:entry key="sign" value="${sign}"/>
                            <con:entry key="token" value="${#TestCase#token}"/>
                            <con:entry key="betSessionId" value="${#TestCase#betSessionId}"/>
                            <con:entry key="betTransactionId" value="${#TestCase#betTransactionId}"/>
                            <con:entry key="progressive" value="${progressive}"/>
                            <con:entry key="currency" value="${#TestCase#currency}"/>
                            <con:entry key="id" value="${#TestCase#id}"/>
                            <con:entry key="player" value="${#TestCase#player}"/>
                        </con:parameters>
                        <con:parameterOrder>
                            <con:entry>amount</con:entry>
                            <con:entry>date</con:entry>
                            <con:entry>id</con:entry>
                            <con:entry>gameId</con:entry>
                            <con:entry>progressive</con:entry>
                            <con:entry>progressiveDesc</con:entry>
                            <con:entry>token</con:entry>
                            <con:entry>sign</con:entry>
                            <con:entry>currency</con:entry>
                            <con:entry>player</con:entry>
                            <con:entry>game</con:entry>
                            <con:entry>betSessionId</con:entry>
                            <con:entry>betTransactionId</con:entry>
                        </con:parameterOrder>
                    </con:restRequest>
                </con:config>
            </con:testStep>
            <con:setupScript>import org.apache.commons.codec.digest.DigestUtils;

                def token = context.expand('${#TestCase#token}');
                def game = context.expand('${#TestCase#game}');
                def player = context.expand('${#TestCase#player}');
                def currency = context.expand('${#TestCase#currency}');
                def betSessionId = context.expand('${#TestCase#betSessionId}');
                def betTransactionId = context.expand('${#TestCase#betTransactionId}');

                def secretKey = context.expand('${#Project#secretKey}');

                def amount = (new BigDecimal(context.expand('${#TestCase#amount}')) * 1000).setScale(0);
                def date = context.expand('${#TestCase#date}');
                def id = context.expand('${#TestCase#id}');
                def gameId = context.expand('${#TestCase#gameId}');
                def progressiveDesc = context.expand('${#TestCase#progressiveDesc}');
                def progressive = "true";

                context.amount = amount;
                context.progressive = progressive;

                context.sign =
                DigestUtils.shaHex("$amount$betSessionId$betTransactionId$currency$date$game$gameId$id$player$progressive$progressiveDesc$token$secretKey");
            </con:setupScript>
            <con:properties>
                <con:property>
                    <con:name>amount</con:name>
                    <con:value>888.88</con:value>
                </con:property>
                <con:property>
                    <con:name>date</con:name>
                    <con:value>1568132515852</con:value>
                </con:property>
                <con:property>
                    <con:name>id</con:name>
                    <con:value>1568132515857</con:value>
                </con:property>
                <con:property>
                    <con:name>gameId</con:name>
                    <con:value>4</con:value>
                </con:property>
                <con:property>
                    <con:name>token</con:name>
                    <con:value>9bdbbf5e2e604cf3b9155ad20d8e2aa6</con:value>
                </con:property>
                <con:property>
                    <con:name>progressiveDesc</con:name>
                    <con:value>Jackpot1</con:value>
                </con:property>
                <con:property>
                    <con:name>game</con:name>
                    <con:value>endorphina_TheKing2@ORGANIC</con:value>
                </con:property>
                <con:property>
                    <con:name>player</con:name>
                    <con:value>297519c7cfac45c0beb770ad87492791</con:value>
                </con:property>
                <con:property>
                    <con:name>currency</con:name>
                    <con:value>AED</con:value>
                </con:property>
                <con:property>
                    <con:name>betSessionId</con:name>
                    <con:value>9bdbbf5e2e604cf3b9155ad20d8e2aa6</con:value>
                </con:property>
                <con:property>
                    <con:name>betTransactionId</con:name>
                    <con:value>1562176353302</con:value>
                </con:property>
                <con:property>
                    <con:name>etalonBalance</con:name>
                    <con:value>1297.91</con:value>
                </con:property>
                <con:property>
                    <con:name>etalonTransactionId</con:name>
                    <con:value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
                </con:property>
                <con:property>
                    <con:name>_balance</con:name>
                    <con:value>5002220.13</con:value>
                </con:property>
                <con:property>
                    <con:name>_id</con:name>
                    <con:value>1562155613361</con:value>
                </con:property>
                <con:property>
                    <con:name>_transactionId</con:name>
                    <con:value>1562155613366</con:value>
                </con:property>
            </con:properties>
        </con:testCase>
        <con:testCase failOnError="false" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="balance"
                      searchProperties="true" id="807f3a77-c2b0-4736-bfcd-51e2a1f365e1" timeout="0" wsrmEnabled="false"
                      wsrmVersion="1.0" wsrmAckTo="" amfAuthorisation="false" amfEndpoint="" amfLogin="" amfPassword="">
            <con:settings/>
            <con:testStep type="restrequest" name="REST Test Request" id="1be07a0c-c925-45ce-9442-4b95efc9610f">
                <con:settings/>
                <con:config service="" methodName="balance" resourcePath="/balance" xsi:type="con:RestRequestStep"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:restRequest name="REST Test Request" mediaType="application/json"
                                     id="b355832b-23a5-4af6-b980-09c22a26fbc2">
                        <con:settings>
                            <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;entry
                                key="Accept" value="application/json" xmlns="http://eviware.com/soapui/config"/>
                            </con:setting>
                        </con:settings>
                        <con:encoding>UTF-8</con:encoding>
                        <con:endpoint>${#Project#seamlessUrl}</con:endpoint>
                        <con:request/>
                        <con:originalUri>http://localhost/balance</con:originalUri>
                        <con:assertion type="Valid HTTP Status Codes" name="Valid HTTP Status Codes"
                                       id="2cb6ede5-dc43-4e81-baba-30f2e372b5ac">
                            <con:configuration>
                                <codes>200</codes>
                            </con:configuration>
                        </con:assertion>
                        <con:assertion type="GroovyScriptAssertion" name="Script Assertion"
                                       id="fc945239-08b9-439c-a341-aad0bdddddeb">
                            <con:configuration>
                                <scriptText>import groovy.json.JsonSlurper

                                    assert
                                    messageExchange.responseHeaders["Content-Type"][0].contains("application/json");

                                    def response = messageExchange.response.responseContent

                                    assert response != null

                                    def slurper = new JsonSlurper()
                                    def json = slurper.parseText response

                                    assert json.size() == 1;

                                    assert json.balance instanceof Number;
                                    def balance = new BigDecimal(json.balance) / 1000;
                                    context.testCase.setPropertyValue("_balance", "$balance");

                                    def etalonBalance = context.expand('${#TestCase#etalonBalance}')

                                    if (etalonBalance != '')
                                    assert new BigDecimal(etalonBalance) == balance
                                    else
                                    assert balance >= 0;


                                </scriptText>
                            </con:configuration>
                        </con:assertion>
                        <con:credentials>
                            <con:authType>No Authorization</con:authType>
                        </con:credentials>
                        <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                        <con:jmsPropertyConfig/>
                        <con:parameters>
                            <con:entry key="game" value="${#TestCase#game}"/>
                            <con:entry key="sign" value="${sign}"/>
                            <con:entry key="currency" value="${#TestCase#currency}"/>
                            <con:entry key="token" value="${#TestCase#token}"/>
                            <con:entry key="player" value="${#TestCase#player}"/>
                        </con:parameters>
                        <con:parameterOrder>
                            <con:entry>token</con:entry>
                            <con:entry>sign</con:entry>
                            <con:entry>game</con:entry>
                            <con:entry>player</con:entry>
                            <con:entry/>
                        </con:parameterOrder>
                    </con:restRequest>
                </con:config>
            </con:testStep>
            <con:setupScript>import org.apache.commons.codec.digest.DigestUtils;
                def token = context.expand('${#TestCase#token}');
                def game = context.expand('${#TestCase#game}');
                def player = context.expand('${#TestCase#player}');
                def currency = context.expand('${#TestCase#currency}');
                def secretKey = context.expand('${#Project#secretKey}');

                context.sign = DigestUtils.shaHex("$currency$game$player$token$secretKey");
            </con:setupScript>
            <con:properties>
                <con:property>
                    <con:name>token</con:name>
                    <con:value>9bdbbf5e2e604cf3b9155ad20d8e2aa6</con:value>
                </con:property>
                <con:property>
                    <con:name>etalonBalance</con:name>
                    <con:value>0</con:value>
                </con:property>
                <con:property>
                    <con:name>_balance</con:name>
                    <con:value>0</con:value>
                </con:property>
                <con:property>
                    <con:name>game</con:name>
                    <con:value>endorphina_TheKing2@ORGANIC</con:value>
                </con:property>
                <con:property>
                    <con:name>player</con:name>
                    <con:value>297519c7cfac45c0beb770ad87492791</con:value>
                </con:property>
                <con:property>
                    <con:name>currency</con:name>
                    <con:value>AED</con:value>
                </con:property>
            </con:properties>
        </con:testCase>
        <con:testCase failOnError="false" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="refund"
                      searchProperties="true" id="3e0604b7-dddb-4547-a554-fc59b8e6aeeb" timeout="0" wsrmEnabled="false"
                      wsrmVersion="1.0" wsrmAckTo="" amfAuthorisation="false" amfEndpoint="" amfLogin="" amfPassword="">
            <con:settings/>
            <con:testStep type="restrequest" name="REST Test Request" id="0dae4985-8ef2-4094-8e6a-a4088822a790">
                <con:settings/>
                <con:config service="" methodName="refund" resourcePath="/refund" xsi:type="con:RestRequestStep"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:restRequest name="REST Test Request" mediaType="application/x-www-form-urlencoded"
                                     postQueryString="true" id="e225622b-57db-49dc-a255-838cb94d177e">
                        <con:settings>
                            <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;entry
                                key="Accept" value="application/json" xmlns="http://eviware.com/soapui/config"/>
                            </con:setting>
                        </con:settings>
                        <con:encoding>UTF-8</con:encoding>
                        <con:endpoint>${#Project#seamlessUrl}</con:endpoint>
                        <con:request/>
                        <con:originalUri>http://localhost/refund</con:originalUri>
                        <con:assertion type="Valid HTTP Status Codes" name="Valid HTTP Status Codes"
                                       id="44509930-c62b-43f0-9e14-967b79645210">
                            <con:configuration>
                                <codes>200</codes>
                            </con:configuration>
                        </con:assertion>
                        <con:assertion type="GroovyScriptAssertion" name="Script Assertion"
                                       id="d1362242-4ac7-45ad-ad93-3d79ffc586c1">
                            <con:configuration>
                                <scriptText>import groovy.json.JsonSlurper

                                    assert
                                    messageExchange.responseHeaders["Content-Type"][0].contains("application/json");

                                    def response = messageExchange.response.responseContent

                                    assert response != null

                                    def slurper = new JsonSlurper()
                                    def json = slurper.parseText response

                                    assert json.size() == 2;

                                    assert json.balance instanceof Number;
                                    def balance = new BigDecimal(json.balance) / 1000;
                                    context.testCase.setPropertyValue("_balance", "$balance");

                                    def etalonBalance = context.expand('${#TestCase#etalonBalance}')
                                    if (etalonBalance != '')
                                    assert new BigDecimal(etalonBalance) == balance
                                    else
                                    balance >= 0;

                                    def etalonTransactionId = context.expand('${#TestCase#etalonTransactionId}')
                                    assert json.transactionId instanceof String;
                                    if (etalonTransactionId != '')
                                    assert json.transactionId == etalonTransactionId
                                    else
                                    assert (json.transactionId.length() > 1) &amp;&amp; (json.transactionId.length()
                                    &lt;= 32);

                                    context.testCase.setPropertyValue('_transactionId', '' + "$json.transactionId");
                                    context.testCase.setPropertyValue('_balance', "$balance");

                                    context.testCase.setPropertyValue("_id", context.expand('${#TestCase#id}'));

                                </scriptText>
                            </con:configuration>
                        </con:assertion>
                        <con:credentials>
                            <con:authType>No Authorization</con:authType>
                        </con:credentials>
                        <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                        <con:jmsPropertyConfig/>
                        <con:parameters>
                            <con:entry key="gameId" value="${#TestCase#gameId}"/>
                            <con:entry key="date" value="${#TestCase#date}"/>
                            <con:entry key="amount" value="${amount}"/>
                            <con:entry key="game" value="${#TestCase#game}"/>
                            <con:entry key="sign" value="${sign}"/>
                            <con:entry key="currency" value="${#TestCase#currency}"/>
                            <con:entry key="id" value="${#TestCase#id}"/>
                            <con:entry key="token" value="${#TestCase#token}"/>
                            <con:entry key="player" value="${#TestCase#player}"/>
                        </con:parameters>
                        <con:parameterOrder>
                            <con:entry>amount</con:entry>
                            <con:entry>date</con:entry>
                            <con:entry>id</con:entry>
                            <con:entry>gameId</con:entry>
                            <con:entry>token</con:entry>
                            <con:entry>sign</con:entry>
                            <con:entry/>
                            <con:entry>game</con:entry>
                            <con:entry>player</con:entry>
                        </con:parameterOrder>
                    </con:restRequest>
                </con:config>
            </con:testStep>
            <con:setupScript>import org.apache.commons.codec.digest.DigestUtils;

                def token = context.expand('${#TestCase#token}');
                def game = context.expand('${#TestCase#game}');
                def player = context.expand('${#TestCase#player}');
                def currency = context.expand('${#TestCase#currency}');

                def secretKey = context.expand('${#Project#secretKey}');
                def amount = (new BigDecimal(context.expand('${#TestCase#amount}')) * 1000).setScale(0);
                def date = context.expand('${#TestCase#date}');
                def id = context.expand('${#TestCase#id}');
                def gameId = context.expand('${#TestCase#gameId}');

                context.amount = amount;
                context.sign = DigestUtils.shaHex("$amount$currency$date$game$gameId$id$player$token$secretKey");
            </con:setupScript>
            <con:properties>
                <con:property>
                    <con:name>amount</con:name>
                    <con:value>12.11</con:value>
                </con:property>
                <con:property>
                    <con:name>date</con:name>
                    <con:value>1568132515965</con:value>
                </con:property>
                <con:property>
                    <con:name>id</con:name>
                    <con:value>1562155613524</con:value>
                </con:property>
                <con:property>
                    <con:name>gameId</con:name>
                    <con:value>7</con:value>
                </con:property>
                <con:property>
                    <con:name>token</con:name>
                    <con:value>9bdbbf5e2e604cf3b9155ad20d8e2aa6</con:value>
                </con:property>
                <con:property>
                    <con:name>etalonBalance</con:name>
                    <con:value>77.77</con:value>
                </con:property>
                <con:property>
                    <con:name>etalonTransactionId</con:name>
                    <con:value>1562155613529</con:value>
                </con:property>
                <con:property>
                    <con:name>_balance</con:name>
                    <con:value>77.77</con:value>
                </con:property>
                <con:property>
                    <con:name>_transactionId</con:name>
                    <con:value>1562155613529</con:value>
                </con:property>
                <con:property>
                    <con:name>_id</con:name>
                    <con:value>1562155613524</con:value>
                </con:property>
                <con:property>
                    <con:name>game</con:name>
                    <con:value>endorphina_TheKing2@ORGANIC</con:value>
                </con:property>
                <con:property>
                    <con:name>player</con:name>
                    <con:value>297519c7cfac45c0beb770ad87492791</con:value>
                </con:property>
                <con:property>
                    <con:name>currency</con:name>
                    <con:value>AED</con:value>
                </con:property>
            </con:properties>
        </con:testCase>
        <con:testCase failOnError="false" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="check"
                      searchProperties="true" id="680064fc-858b-44d8-a84d-8683fd0accc9" timeout="0" wsrmEnabled="false"
                      wsrmVersion="1.0" wsrmAckTo="" amfAuthorisation="false" amfEndpoint="" amfLogin="" amfPassword="">
            <con:settings/>
            <con:testStep type="restrequest" name="REST Test Request" id="6597abe1-f1f4-4ee1-8217-0fa50bdcf2b7">
                <con:settings/>
                <con:config service="" methodName="check" resourcePath="/check" xsi:type="con:RestRequestStep"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:restRequest name="REST Test Request" mediaType="application/json"
                                     id="40e1438b-cbef-4046-b5b5-871904027f77">
                        <con:settings>
                            <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;entry
                                key="Accept" value="application/json" xmlns="http://eviware.com/soapui/config"/>
                            </con:setting>
                        </con:settings>
                        <con:encoding>UTF-8</con:encoding>
                        <con:endpoint>${#Project#seamlessUrl}</con:endpoint>
                        <con:request/>
                        <con:originalUri>http://localhost/endorphina/session</con:originalUri>
                        <con:assertion type="Valid HTTP Status Codes" name="Valid HTTP Status Codes"
                                       id="0848d335-314f-4c90-8173-9426dccee719">
                            <con:configuration>
                                <codes>200</codes>
                            </con:configuration>
                        </con:assertion>
                        <con:assertion type="GroovyScriptAssertion" name="Script Assertion"
                                       id="413c9f44-888f-4eb8-8b64-164ca1d9ee10">
                            <con:configuration>
                                <scriptText>import groovy.json.JsonSlurper
                                    import org.apache.commons.codec.digest.DigestUtils;

                                    assert
                                    messageExchange.responseHeaders["Content-Type"][0].contains("application/json");

                                    def response = messageExchange.response.responseContent
                                    def slurper = new JsonSlurper()
                                    def json = slurper.parseText response


                                    def etalonNodeId = context.expand('${#TestCase#nodeId}')
                                    assert json.nodeId instanceof Integer;
                                    assert json.nodeId == new BigDecimal(etalonNodeId);

                                    def etalonParam = context.expand('${#TestCase#param}')
                                    assert json.param instanceof String;
                                    assert json.param == etalonParam;

                                    def salt = context.expand('${#TestCase#salt}')
                                    assert json.sign instanceof String;

                                    log.info ("$etalonNodeId$etalonParam$salt");
                                    def etalonSign = DigestUtils.shaHex ("$etalonNodeId$etalonParam$salt");
                                    log.info ("$etalonSign");
                                    assert json.sign ==new String(etalonSign);

                                </scriptText>
                            </con:configuration>
                        </con:assertion>
                        <con:credentials>
                            <con:authType>No Authorization</con:authType>
                        </con:credentials>
                        <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                        <con:jmsPropertyConfig/>
                        <con:parameters>
                            <con:entry key="param" value="${#TestCase#param}"/>
                            <con:entry key="sign" value="${sign}"/>
                            <con:entry key="token" value="${#TestCase#token}"/>
                        </con:parameters>
                    </con:restRequest>
                </con:config>
            </con:testStep>
            <con:setupScript>import org.apache.commons.codec.digest.DigestUtils;
                def param = context.expand('${#TestCase#param}');
                def secretKey = context.expand('${#Project#secretKey}');

                context.sign = DigestUtils.shaHex("$param$secretKey");
                log.info("SIGN = $context.sign");
                context.testCase.setPropertyValue("sign", "" + context.sign);
            </con:setupScript>
            <con:properties>
                <con:property>
                    <con:name>param</con:name>
                    <con:value>1568132515411</con:value>
                </con:property>
                <con:property>
                    <con:name>nodeId</con:name>
                    <con:value>777</con:value>
                </con:property>
                <con:property>
                    <con:name>sign</con:name>
                    <con:value>959a0c623d9229d99cbf0a4408e40520e1bae4d4</con:value>
                </con:property>
                <con:property>
                    <con:name>salt</con:name>
                    <con:value>E09A0EF00E5D4B23B169E8548067B8E3</con:value>
                </con:property>
            </con:properties>
        </con:testCase>
        <con:testCase failOnError="false" failTestCaseOnErrors="true" keepSession="false" maxResults="0"
                      name="endSession" searchProperties="true" id="b7335dfd-db5f-4fc2-a20d-1fd2f5ea2755" timeout="0"
                      wsrmEnabled="false" wsrmVersion="1.0" wsrmAckTo="" amfAuthorisation="false" amfEndpoint=""
                      amfLogin="" amfPassword="">
            <con:settings/>
            <con:testStep type="restrequest" name="REST Test Request" id="f702d03d-3ce2-4243-854f-1fa1a6dfdc64">
                <con:settings/>
                <con:config service="" methodName="endSession" resourcePath="/endSession" xsi:type="con:RestRequestStep"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:restRequest name="REST Test Request" mediaType="application/x-www-form-urlencoded"
                                     postQueryString="true" id="e225622b-57db-49dc-a255-838cb94d177e">
                        <con:settings>
                            <con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;entry
                                key="Accept" value="application/json" xmlns="http://eviware.com/soapui/config"/>
                            </con:setting>
                        </con:settings>
                        <con:encoding>UTF-8</con:encoding>
                        <con:endpoint>${#Project#seamlessUrl}</con:endpoint>
                        <con:request/>
                        <con:originalUri>http://localhost/refund</con:originalUri>
                        <con:assertion type="Valid HTTP Status Codes" name="Valid HTTP Status Codes"
                                       id="44509930-c62b-43f0-9e14-967b79645210">
                            <con:configuration>
                                <codes>200</codes>
                            </con:configuration>
                        </con:assertion>
                        <con:credentials>
                            <con:authType>No Authorization</con:authType>
                        </con:credentials>
                        <con:jmsConfig JMSDeliveryMode="PERSISTENT"/>
                        <con:jmsPropertyConfig/>
                        <con:parameters>
                            <con:entry key="gameId" value="${#TestCase#gameId}"/>
                            <con:entry key="date" value="${#TestCase#date}"/>
                            <con:entry key="amount" value="${amount}"/>
                            <con:entry key="game" value="${#TestCase#game}"/>
                            <con:entry key="sign" value="${sign}"/>
                            <con:entry key="token" value="${#TestCase#token}"/>
                            <con:entry key="bet" value="${#TestCase#bet}"/>
                            <con:entry key="jackpotWin" value="${#TestCase#jackpotWin}"/>
                            <con:entry key="bonusBet" value="${#TestCase#bonusBet}"/>
                            <con:entry key="bonusGames" value="${#TestCase#bonusGames}"/>
                            <con:entry key="games" value="${#TestCase#games}"/>
                            <con:entry key="currency" value="${#TestCase#currency}"/>
                            <con:entry key="id" value="${#TestCase#id}"/>
                            <con:entry key="jackpots" value="${#TestCase#jackpots}"/>
                            <con:entry key="win" value="${#TestCase#win}"/>
                            <con:entry key="player" value="${#TestCase#player}"/>
                            <con:entry key="bonusWin" value="${#TestCase#bonusWin}"/>
                        </con:parameters>
                        <con:parameterOrder>
                            <con:entry>amount</con:entry>
                            <con:entry>date</con:entry>
                            <con:entry>id</con:entry>
                            <con:entry>gameId</con:entry>
                            <con:entry>token</con:entry>
                            <con:entry>sign</con:entry>
                            <con:entry/>
                            <con:entry>game</con:entry>
                            <con:entry>player</con:entry>
                        </con:parameterOrder>
                    </con:restRequest>
                </con:config>
            </con:testStep>
            <con:setupScript>import org.apache.commons.codec.digest.DigestUtils;

                def token = context.expand('${#TestCase#token}');
                def game = context.expand('${#TestCase#game}');
                def player = context.expand('${#TestCase#player}');
                def currency = context.expand('${#TestCase#currency}');

                def bet = context.expand('${#TestCase#bet}');
                def bonusBet = context.expand('${#TestCase#bonusBet}');
                def bonusGames = context.expand('${#TestCase#bonusGames}');
                def bonusWin = context.expand('${#TestCase#bonusWin');
def games = context.expand('${#TestCase#games}');
                def jackpotWin = context.expand('${#TestCase#jackpotWin}');
                def jackpots = context.expand('${#TestCase#jackpots}');
                def win = context.expand('${#TestCase#win}');

                def secretKey = context.expand('${#Project#secretKey}');


                context.sign =
                DigestUtils.shaHex("$bet$bonusBet$bonusGames$bonusWin$currency$game$games$jackpotWin$jackpots$player$token$win$secretKey");
            </con:setupScript>
            <con:properties>
                <con:property>
                    <con:name>bet</con:name>
                    <con:value>1000</con:value>
                </con:property>
                <con:property>
                    <con:name>bonusBet</con:name>
                    <con:value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">0</con:value>
                </con:property>
                <con:property>
                    <con:name>bonusGames</con:name>
                    <con:value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">0</con:value>
                </con:property>
                <con:property>
                    <con:name>bonusWin</con:name>
                    <con:value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">0</con:value>
                </con:property>
                <con:property>
                    <con:name>currency</con:name>
                    <con:value>AED</con:value>
                </con:property>
                <con:property>
                    <con:name>game</con:name>
                    <con:value>endorphina_TheKing2@ORGANIC</con:value>
                </con:property>
                <con:property>
                    <con:name>games</con:name>
                    <con:value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">10</con:value>
                </con:property>
                <con:property>
                    <con:name>jackpotWin</con:name>
                    <con:value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">444</con:value>
                </con:property>
                <con:property>
                    <con:name>jackpots</con:name>
                    <con:value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">2</con:value>
                </con:property>
                <con:property>
                    <con:name>player</con:name>
                    <con:value>297519c7cfac45c0beb770ad87492791</con:value>
                </con:property>
                <con:property>
                    <con:name>token</con:name>
                    <con:value>9bdbbf5e2e604cf3b9155ad20d8e2aa6</con:value>
                </con:property>
                <con:property>
                    <con:name>win</con:name>
                    <con:value xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">777</con:value>
                </con:property>
            </con:properties>
        </con:testCase>
        <con:properties/>
    </con:testSuite>
    <con:testSuite name="SeamlessApiTests" id="bdba2c05-611c-41ed-966a-04150370a886">
        <con:settings/>
        <con:runType>SEQUENTIAL</con:runType>
        <con:testCase failOnError="true" failTestCaseOnErrors="true" keepSession="false" maxResults="0" name="1. Launch"
                      searchProperties="true" id="ad95c43e-32d2-4b88-a329-1abd266c8913" timeout="0" wsrmEnabled="false"
                      wsrmVersion="1.0" wsrmAckTo="" amfAuthorisation="false" amfEndpoint="" amfLogin="" amfPassword="">
            <con:description>Validate launch url, signature calculation.</con:description>
            <con:settings/>
            <con:testStep type="groovy" name="validate" id="7b69c42b-e7b3-4844-8968-cb09a4470cc6">
                <con:settings/>
                <con:config>
                    <script>import org.apache.commons.codec.digest.DigestUtils;

                        def exit = context.inocationRequest.getParameter("exit");
                        if (exit == null) exit= "";

                        def nodeId = context.inocationRequest.getParameter("nodeId");

                        def profile = context.inocationRequest.getParameter("profile");
                        if (profile == null) profile = "";

                        def token = context.inocationRequest.getParameter("token");
                        def sign = context.inocationRequest.getParameter("sign");

                        context.testCase.testSuite.setPropertyValue("invocationToken", token);

                        log.info "/sessions/seamless/res/v1?exit=$exit&amp;nodeId=$nodeId&amp;profile=$profile&amp;token=$token&amp;sign=$sign";

                        def salt = context.expand('${#Project#secretKey}');
                        def etalonNodeId = context.expand('${#Project#nodeId}');

                        def str2sign = "$exit$nodeId$profile$token$salt"

                        def etalonSign = DigestUtils.shaHex(str2sign);

                        log.info "str2sign: $str2sign $etalonSign"

                        assert etalonSign == sign

                        assert etalonNodeId == nodeId;

                    </script>
                </con:config>
            </con:testStep>
            <con:properties/>
        </con:testCase>
        <con:testCase failOnError="false" failTestCaseOnErrors="true" keepSession="false" maxResults="0"
                      name="2. Base TestCase" searchProperties="true" id="666891a8-06d2-48a5-add2-ccc700fec049"
                      timeout="0" wsrmEnabled="false" wsrmVersion="1.0" wsrmAckTo="" amfAuthorisation="false"
                      amfEndpoint="" amfLogin="" amfPassword="">
            <con:description>Acceptance test sequence</con:description>
            <con:settings/>
            <con:testStep type="calltestcase" name="/check" id="491ce460-c331-4cdb-853f-79e50540c167">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>680064fc-858b-44d8-a84d-8683fd0accc9</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>param</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>nodeId</con:name>
                            <con:value>${#Project#nodeId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>sign</con:name>
                            <con:value>959a0c623d9229d99cbf0a4408e40520e1bae4d4</con:value>
                        </con:property>
                        <con:property>
                            <con:name>salt</con:name>
                            <con:value>${#Project#secretKey}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>sign</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/session" id="ad6007eb-2be2-4260-9892-4b2a1fd5d319">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>0d26fbde-2c14-4eec-b1bc-d92a27b3cc3d</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${#TestSuite#invocationToken}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_player</con:name>
                            <con:value>297519c7cfac45c0beb770ad87492791</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_currency</con:name>
                            <con:value>AED</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_game</con:name>
                            <con:value>endorphina_TheKing2@ORGANIC</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_token</con:name>
                            <con:value>9bdbbf5e2e604cf3b9155ad20d8e2aa6</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_player</con:entry>
                        <con:entry>_currency</con:entry>
                        <con:entry>_game</con:entry>
                        <con:entry>_token</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/balance0" id="1311a837-0e69-479f-9734-1fca31e0b6d5">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>807f3a77-c2b0-4736-bfcd-51e2a1f365e1</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value/>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>5000000</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_balance</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/win0 (take win)" id="b1a1a477-f27c-458a-8a30-eb82aee4cc8c">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" ignoreEmptyProperties="true"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3f1d1fe9-e0dd-4981-9713-ea03b0959627</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/balance0#_balance}+${amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>320.15</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${=System.currentTimeMillis()+1}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betSessionId</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betTransactionId</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>0</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value/>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562235120270</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562235120345</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>1379.48</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_id</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/balance1.0" id="f23a21ce-3063-4356-bb36-12e6256b5ac5">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>807f3a77-c2b0-4736-bfcd-51e2a1f365e1</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/balance0#_balance}+${/win0 (take win)#amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>5000320.15</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_balance</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/bet1" id="afec92df-f2bd-476a-87a4-23cdf6dbba40">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3b5358cb-dde1-465b-9117-cceacb7bce2d</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/balance1.0#_balance}-${amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>12.34</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${=System.currentTimeMillis()+1}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value xsi:nil="true"/>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>1</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>409.03</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562176353281</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562176353302</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_balance</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/balance1.1" id="172cb8d7-2425-4006-9a9e-b03eee28490e">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>807f3a77-c2b0-4736-bfcd-51e2a1f365e1</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/balance1.0#_balance}-${/bet1#amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>5000307.81</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_balance</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/win1" id="cdaefa6a-6958-40bf-b9e8-e3c2f2dcd339">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" ignoreEmptyProperties="false"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3f1d1fe9-e0dd-4981-9713-ea03b0959627</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/balance1.1#_balance}+${amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>10</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${=System.currentTimeMillis()+1}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betSessionId</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betTransactionId</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>1</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value/>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562235120270</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562235120345</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>1379.48</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/balance1.2" id="ac0bbe57-e5bf-46ca-acf5-72f40bcab461">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>807f3a77-c2b0-4736-bfcd-51e2a1f365e1</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/balance1.1#_balance}+${/win1#amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>5000317.81</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_balance</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/bet2" id="28e9359a-4698-49f4-8b60-82a8795531da">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3b5358cb-dde1-465b-9117-cceacb7bce2d</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/balance1.2#_balance}-${amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>100</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${=System.currentTimeMillis()+1}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value xsi:nil="true"/>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>2</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>409.03</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562176353281</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562176353302</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/balance2.1" id="c83311d8-145d-415c-b6f5-d2cdbe8bf7e2">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>807f3a77-c2b0-4736-bfcd-51e2a1f365e1</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/balance1.2#_balance}-${/bet2#amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>5000217.81</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_balance</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/bet2 duplicate" id="965355e8-7322-48c8-8263-91992fcd6589">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3b5358cb-dde1-465b-9117-cceacb7bce2d</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${/bet2#_balance}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>${/bet2#amount}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${/bet2#date}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${/bet2#_id}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value>${/bet2#_transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>${/bet2#gameId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>409.03</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562176353281</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562176353302</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/balance2.2" id="d5572a66-c46a-4e07-b695-f7e2e03d60b6">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>807f3a77-c2b0-4736-bfcd-51e2a1f365e1</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${/balance2.1#_balance}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>5000117.81</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_balance</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/refund bet2" id="5c054847-c793-4d59-a751-2e8782dc8cc6">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3e0604b7-dddb-4547-a554-fc59b8e6aeeb</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/balance2.2#_balance}+${/bet2#amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>5000217.81</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>${/bet2#amount}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${/bet2#date}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${/bet2#_id}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>${/bet2#gameId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562155613529</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value>${/bet2#transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562155613524</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_transactionId</con:entry>
                        <con:entry>_id</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/balance2.3" id="e700f2f6-e6f5-419e-8c05-02bd4a4efd13">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>807f3a77-c2b0-4736-bfcd-51e2a1f365e1</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${/refund bet2#_balance}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>5000217.81</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_balance</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/bet3" id="acfc0e9e-8faf-4280-a04a-246a938f275c">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3b5358cb-dde1-465b-9117-cceacb7bce2d</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/balance2.3#_balance}-${amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>5.00</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${=System.currentTimeMillis()+1}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value xsi:nil="true"/>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>3</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>409.03</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562176353281</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562176353302</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/win3" id="f773ab93-6422-43d9-b0db-b4e4f178b2d1">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3f1d1fe9-e0dd-4981-9713-ea03b0959627</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/bet3#_balance}+${amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>23.45</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${=System.currentTimeMillis()+1}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betSessionId</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betTransactionId</con:name>
                            <con:value>${/bet3#_transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value/>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>3</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562235120270</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>1379.48</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562235120345</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/win3 duplicate" id="ff126a2f-b726-4055-ad5a-8424c3eb41ce">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3f1d1fe9-e0dd-4981-9713-ea03b0959627</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${/win3#_balance}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>${/win3#amount}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${/win3#date}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${/win3#_id}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betSessionId</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betTransactionId</con:name>
                            <con:value>${/bet3#_transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>${/win3#gameId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value>${/win3#_transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562235120270</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562235120345</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>1379.48</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/win3 (jackpot)" id="74b59c0e-3f87-4581-97f4-843893f0071d">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>4ca4e695-c3f5-421b-87b8-d75bcb759a53</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/win3#_balance}+${amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>999.99</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${=System.currentTimeMillis()+1}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value/>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betSessionId</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betTransactionId</con:name>
                            <con:value>${/bet3#_transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>3</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>5002220.13</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562155613366</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562155613361</con:value>
                        </con:property>
                        <con:property>
                            <con:name>progressiveDesc</con:name>
                            <con:value>Jackpot1</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/win3 (jackpot) duplicate"
                          id="cc334f10-8366-4d27-a94f-e39f64744b15">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>4ca4e695-c3f5-421b-87b8-d75bcb759a53</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${/win3 (jackpot)#_balance}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>${/win3 (jackpot)#amount}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${/win3 (jackpot)#date}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${/win3 (jackpot)#_id}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value>${/win3 (jackpot)#_transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betSessionId</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betTransactionId</con:name>
                            <con:value>${/bet3#_transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>${/win3 (jackpot)#gameId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>5002220.13</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562155613366</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562155613361</con:value>
                        </con:property>
                        <con:property>
                            <con:name>progressiveDesc</con:name>
                            <con:value>${/win3 (jackpot)#progressiveDesc}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/balance3.1" id="f7e3b468-a749-4cf7-b026-ed4f3e8376c1">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>807f3a77-c2b0-4736-bfcd-51e2a1f365e1</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${/win3 (jackpot)#_balance}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>5002259.69</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_balance</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/bet4" id="e2de1d08-b0b2-4f93-9065-c31b5f106818">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3b5358cb-dde1-465b-9117-cceacb7bce2d</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/balance3.1#_balance}-${amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>5.00</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${=System.currentTimeMillis()+1}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value xsi:nil="true"/>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>4</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>409.03</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562176353281</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562176353302</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/win4 (jackpot)" id="a10e6c3a-9794-4d1f-9474-4644db2c5c29">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>4ca4e695-c3f5-421b-87b8-d75bcb759a53</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/bet4#_balance}+${amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>888.88</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${=System.currentTimeMillis()+1}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value/>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betSessionId</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betTransactionId</con:name>
                            <con:value>${/bet4#_transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>4</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>5002220.13</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562155613366</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562155613361</con:value>
                        </con:property>
                        <con:property>
                            <con:name>progressiveDesc</con:name>
                            <con:value>Jackpot1</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/win4 (zero)" id="8c4714e9-942f-4544-bf03-8444126d8e09">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3f1d1fe9-e0dd-4981-9713-ea03b0959627</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${/win4 (jackpot0)#_balance}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>0</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value/>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betSessionId</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betTransactionId</con:name>
                            <con:value>${/bet4#_transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value/>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>4</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562235120270</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>1379.48</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562235120345</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/win4 (zero) duplicate" id="b928f928-5d07-4e9b-bdeb-d33c8b9b0644">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3f1d1fe9-e0dd-4981-9713-ea03b0959627</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/win4 (jackpot)#_balance}+${amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>0</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value/>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betSessionId</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betTransactionId</con:name>
                            <con:value>${/bet4#_transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value/>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>4</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562235120270</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>1379.48</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562235120345</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/balance4.1" id="690889d7-94fc-426e-95a8-08271552ef9e">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>807f3a77-c2b0-4736-bfcd-51e2a1f365e1</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${/win4 (zero)#_balance}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>5003143.57</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_balance</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/bet5 (insufficient funds)"
                          id="e4e3a1c7-567d-4c04-95eb-eb9be55650d7">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>444821f2-0fb5-4e57-9b1e-3556b93f0161</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>${=${/balance4.1#_balance}+0.01}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${=System.currentTimeMillis()+1}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>5</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562155613423</con:value>
                        </con:property>
                        <con:property>
                            <con:name>httpCode</con:name>
                            <con:value>402</con:value>
                        </con:property>
                        <con:property>
                            <con:name>errorCode</con:name>
                            <con:value>INSUFFICIENT_FUNDS</con:value>
                        </con:property>
                        <con:property>
                            <con:name>errorMessage</con:name>
                            <con:value/>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_id</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/bet6 (all)" id="2a45d616-820f-4308-b298-983cc07ae750">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3b5358cb-dde1-465b-9117-cceacb7bce2d</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>${/balance4.1#_balance}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${=System.currentTimeMillis()+1}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>6</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562176353281</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>0</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>409.03</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562176353302</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/balance6.1" id="a1394c98-7bfc-4672-8de6-001ae1530783">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>807f3a77-c2b0-4736-bfcd-51e2a1f365e1</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>0</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>0</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_balance</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/win6" id="bf39bf09-afed-4d9b-8e05-cd3ec0776f6e">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" ignoreEmptyProperties="false"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3f1d1fe9-e0dd-4981-9713-ea03b0959627</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${=${/balance6.1#_balance}+${amount}}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>77.77</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${=System.currentTimeMillis()+1}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betSessionId</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>betTransactionId</con:name>
                            <con:value>${/bet6 (all)#_transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>6</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value/>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562235120270</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562235120345</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>1379.48</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/refund bet7" id="e3b75435-2029-4e4a-be09-d738b8012660">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3e0604b7-dddb-4547-a554-fc59b8e6aeeb</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${/win6#_balance}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>77.77</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>12.11</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${=System.currentTimeMillis()}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${=System.currentTimeMillis()+1}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>7</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562155613529</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value/>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562155613524</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_transactionId</con:entry>
                        <con:entry>_id</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/refund bet7 duplicate" id="e2ad7ad0-c805-4270-a728-356f11d3a9a3">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3e0604b7-dddb-4547-a554-fc59b8e6aeeb</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${/refund bet7#_balance}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>77.77</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>${/refund bet7#amount}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${/refund bet7#date}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${/refund bet7#_id}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>7</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562155613529</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value>${/refund bet7#_transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562155613524</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_transactionId</con:entry>
                        <con:entry>_id</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/bet7 (after refund)" id="0b0a3584-0382-4986-85dc-5422cfb91763">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>3b5358cb-dde1-465b-9117-cceacb7bce2d</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonBalance</con:name>
                            <con:value>${/refund bet7#_balance}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>amount</con:name>
                            <con:value>0.03</con:value>
                        </con:property>
                        <con:property>
                            <con:name>date</con:name>
                            <con:value>${/refund bet7#date}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>id</con:name>
                            <con:value>${/refund bet7#_id}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>etalonTransactionId</con:name>
                            <con:value>${/refund bet7#_transactionId}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>gameId</con:name>
                            <con:value>7</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_balance</con:name>
                            <con:value>409.03</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_id</con:name>
                            <con:value>1562176353281</con:value>
                        </con:property>
                        <con:property>
                            <con:name>_transactionId</con:name>
                            <con:value>1562176353302</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties>
                        <con:entry>_alreadyProcessed</con:entry>
                        <con:entry>_balance</con:entry>
                        <con:entry>_id</con:entry>
                        <con:entry>_transactionId</con:entry>
                    </con:returnProperties>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:testStep type="calltestcase" name="/endSession" id="a8c1f9db-51b4-4e7a-a172-39eeed2e81d8">
                <con:settings/>
                <con:config xsi:type="con:RunTestCaseStep" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                    <con:targetTestCase>b7335dfd-db5f-4fc2-a20d-1fd2f5ea2755</con:targetTestCase>
                    <con:properties>
                        <con:property>
                            <con:name>token</con:name>
                            <con:value>${_token}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>game</con:name>
                            <con:value>${_game}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>player</con:name>
                            <con:value>${_player}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>currency</con:name>
                            <con:value>${_currency}</con:value>
                        </con:property>
                        <con:property>
                            <con:name>bet</con:name>
                            <con:value>1000</con:value>
                        </con:property>
                        <con:property>
                            <con:name>bonusBet</con:name>
                            <con:value>0</con:value>
                        </con:property>
                        <con:property>
                            <con:name>bonusGames</con:name>
                            <con:value>0</con:value>
                        </con:property>
                        <con:property>
                            <con:name>bonusWin</con:name>
                            <con:value>0</con:value>
                        </con:property>
                        <con:property>
                            <con:name>games</con:name>
                            <con:value>10</con:value>
                        </con:property>
                        <con:property>
                            <con:name>jackpotWin</con:name>
                            <con:value>444</con:value>
                        </con:property>
                        <con:property>
                            <con:name>jackpots</con:name>
                            <con:value>2</con:value>
                        </con:property>
                        <con:property>
                            <con:name>win</con:name>
                            <con:value>777</con:value>
                        </con:property>
                    </con:properties>
                    <con:returnProperties/>
                    <con:runMode>SINGLETON_AND_WAIT</con:runMode>
                </con:config>
            </con:testStep>
            <con:properties/>
        </con:testCase>
        <con:properties>
            <con:property>
                <con:name>invocationToken</con:name>
                <con:value>9bdbbf5e2e604cf3b9155ad20d8e2aa6</con:value>
            </con:property>
        </con:properties>
    </con:testSuite>
    <con:restMockService port="8080" path="/" host="localhost" name="LaunchMock"
                         id="2819674e-db69-47bb-8fed-273c6f38cd2f">
        <con:settings/>
        <con:properties/>
        <con:restMockAction name="/launch" method="GET" resourcePath="/api/sessions/seamless/rest/v1"
                            id="788759b3-214e-421e-911d-b17167184fcb">
            <con:settings/>
            <con:defaultResponse>Response 1</con:defaultResponse>
            <con:dispatchStyle>SCRIPT</con:dispatchStyle>
            <con:dispatchPath><![CDATA[import com.eviware.soapui.impl.wsdl.teststeps.WsdlMessageExchangeTestStepResult;

def params = new com.eviware.soapui.support.types.StringToObjectMap();
params.put("inocationRequest", mockRequest.getRequest());

def runner

try {
	runner = mockOperation.mockService.project.testSuites["SeamlessApiTests"].run(params, false)
} catch (all) {
	log.info "test failed"
};

//=======================================================================
// have some fun :-)

def reportData = "<tr><th>TestStep</th><th>Result</th><th>Details</th></tr>";

for(tcr in runner.getResults().sort{it.testCase.label}) {
	reportData <<= "<tr>"
	reportData <<= "<td colspan='2'><h3>$tcr.testCase.label</h3></td><td colspan='2'><h3>$tcr.testCase.description</h3></td>"
	for(tsr in tcr.getResults()) {
		def tsStatus = tsr.status.toString() == "OK";
		def color = tsStatus ? "green" : "red";
		reportData <<= "<tr style='color: $color'>"
		reportData <<= "<td>$tsr.testStep.label</td><td>$tsr.status</td>"
		reportData <<= "<td><table style='table-layout:fixed;width:100%'>"

		if (tsr instanceof WsdlMessageExchangeTestStepResult) {
			for (mer in tsr.messageExchanges) {
				def req = "none"
				def resp = "none"
				try {
					req = new String(mer.rawRequestData);
					resp = new String(mer.rawResponseData);
				} catch (all) {}

				reportData <<= "<tr><td width='50%'>$req</td><td width='50%'>$resp</td></tr>";
			}
		}

		if (!tsStatus) {
			for (ast in  tsr.messages) {
				reportData <<= "<tr><td colspan='2'>$ast</td></tr>"
				log.info "assert: $ast"
			}
		}

		reportData <<= "</table></td>"
		reportData <<= "</tr>"
	}
	reportData <<= "</tr>"
}

context.reportData = reportData;
]]></con:dispatchPath>
            <con:response name="Response 1" httpResponseStatus="200" mediaType="text/xml"
                          id="febe1113-1976-46d5-a299-2821d85172a7">
                <con:settings/>
                <con:responseContent><![CDATA[<!DOCTYPE html>
<html>
<head>
	<title>Endorphina SeamlessAPI</title>
	<meta content="text/html;charset=UTF-8" http-equiv="Content-Type"/>

	<style type="text/css">
 		td {
			white-space: -o-pre-wrap;
			word-wrap: break-word;
			white-space: pre-wrap;
			white-space: -moz-pre-wrap;
			white-space: -pre-wrap;
		}

	</style>

</head>
<body>
	<h1>Endorphina SeamlessAPI Acceptance Test Report</h1>
	<h1>Details</h1>
	<table border="1" width="100%">
		${reportData}
	</table>
</body>
</html>]]></con:responseContent>
                <con:header>
                    <con:name>Content-Type</con:name>
                    <con:value>text/html</con:value>
                </con:header>
            </con:response>
        </con:restMockAction>
    </con:restMockService>
    <con:properties>
        <con:property>
            <con:name>seamlessUrl</con:name>
            <con:value>http://localhost:8888</con:value>
        </con:property>
        <con:property>
            <con:name>secretKey</con:name>
            <con:value>E09A0EF00E5D4B23B169E8548067B8E3</con:value>
        </con:property>
        <con:property>
            <con:name>nodeId</con:name>
            <con:value>777</con:value>
        </con:property>
    </con:properties>
    <con:wssContainer/>
    <con:oAuth2ProfileContainer/>
    <con:sensitiveInformation/>
</con:soapui-project>
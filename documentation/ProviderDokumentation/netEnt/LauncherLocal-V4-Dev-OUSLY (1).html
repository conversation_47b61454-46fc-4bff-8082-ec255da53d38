<html>
  <head>
    <title>My game page</title>
    <script type="text/javascript" src="https://netent-static.casinomodule.com/gameinclusion/library/gameinclusion.js"></script>
    <script type="text/javascript">
      var startGame = function () {
        var config = {
          gameId: "hotline_not_mobile",
          staticServerURL: "https://netent-static.casinomodule.com",
          // gameServerURL will be replaced based on operator's integration
          gameServerURL: "https://socialcasino-dev.casinomodule.com/gs-api/v3/ously",
          //sessionId will be provided by Operator based on integration
          sessionId: "eyJhbGciOiJIUzUxMiJ9.eyJhdWQiOiJuZXRlbnQiLCJzdWIiOiJOVC01NDktb3VzbHkiLCJiZXRMZXZlbHMiOiIxIiwiZ2lkIjoiaG90bGluZV9ub3RfbW9iaWxlIiwibHZsIjoxLCJjZmciOnsiZGVmYXVsdEJldCI6MC4wMSwiYmV0cyI6WzAuMDEsMC4wMiwzLDUsMTAsMjAsMzAsNTAsMTAwLDIwMCwzMDAsNTAwLDYwMF19LCJwaXAiOiJuZXRlbnQiLCJjdXJyZW5jeSI6IlNQTiIsImV4cCI6MTY1MjgxODMyNH0.5t0wFyoUP835xCSpn7bmYGLj4Urug6FwAUbvbN7nJZirRNVZZxQItGfX7ZQdomRQZSB1ABkl3ChYhc9xeMYrLQ",
          targetElement: "gameArea",
		  disableDeviceDetection: true,
		  bettingModes: ['coins'],
		  launchType: "iframe",
		  //applicationType: "browser"
        };
        // Game launch successful.
        var success = function (netEntExtend) {
            netEntExtend.addEventListener("gameRoundEnded", function(){
            console.log("gameRoundEnded is found");
          });
        };
        // Error handling here.
        var error = function (e) {
        };
        netent.launch(config, success, error);
      };
      window.onload = startGame;
    </script>
  </head>
  <body>
    <div id="gameArea"></div>
  </body>
</html>

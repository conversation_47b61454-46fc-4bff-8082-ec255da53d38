HELP.md
.gradle
/build/
!gradle/wrapper/gradle-wrapper.jar

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
/out/
/**/build/
/**/out/

### workaround for submodule generated code via apt
/persistence/src/main/generated/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

### docker sql data ###
docker/data/**

gradle.properties

# Elastic Beanstalk Files
.elasticbeanstalk/*
!.elasticbeanstalk/*.cfg.yml
!.elasticbeanstalk/*.global.yml
.DS_Store
/admin/node_modules/
/admin/package.json
/admin/package-lock.json
/admin/tsconfig.json
/admin/types.d.ts
/admin/vite.config.ts
/admin/vite.generated.ts
/admin/src/main/frontend/generated/
/admin/src/main/frontend/index.html

package com.ously.gamble.controller;

import com.adyen.model.checkout.PaymentMethodsResponse;
import com.adyen.model.checkout.PaymentsResponse;
import com.fasterxml.jackson.annotation.JsonView;
import com.ously.gamble.api.PurchaseService;
import com.ously.gamble.api.achievements.AchievementService;
import com.ously.gamble.api.achievements.ConfirmAchievementRequest;
import com.ously.gamble.api.auth.*;
import com.ously.gamble.api.bonuscode.ActivateCodeResponse;
import com.ously.gamble.api.bonuscode.BonusCodeApplyRequest;
import com.ously.gamble.api.bonuscode.BonuscodeAffiliateService;
import com.ously.gamble.api.bridge.BridgeTools;
import com.ously.gamble.api.cpopups.CPopupFeedback;
import com.ously.gamble.api.cpopups.CPopupService;
import com.ously.gamble.api.crm.CRMPushEvent;
import com.ously.gamble.api.crm.ExternalEvents;
import com.ously.gamble.api.features.UserToken;
import com.ously.gamble.api.features.*;
import com.ously.gamble.api.games.*;
import com.ously.gamble.api.leaderboard.LeaderboardService;
import com.ously.gamble.api.leaderboard.Leaderboards;
import com.ously.gamble.api.leaderboards.GameLeaderboards;
import com.ously.gamble.api.leaderboards.GameLeaderboardsService;
import com.ously.gamble.api.marketingemails.MarketingEmailService;
import com.ously.gamble.api.missions.MissionService;
import com.ously.gamble.api.missions.UserMissions;
import com.ously.gamble.api.notification.ContactRequest;
import com.ously.gamble.api.notification.NotificationService;
import com.ously.gamble.api.popups.CasinoPopup;
import com.ously.gamble.api.popups.PopupFeedback;
import com.ously.gamble.api.popups.PopupService;
import com.ously.gamble.api.rankings.RankResults;
import com.ously.gamble.api.rankings.RankingResult;
import com.ously.gamble.api.rankings.RankingService;
import com.ously.gamble.api.security.CurrentUser;
import com.ously.gamble.api.security.UserPrincipal;
import com.ously.gamble.api.session.InitialiseGameRequest;
import com.ously.gamble.api.session.InitialiseGameResponse;
import com.ously.gamble.api.session.SessionService;
import com.ously.gamble.api.session.SessionStatistics;
import com.ously.gamble.api.slotfinder.RecommendationKey;
import com.ously.gamble.api.slotfinder.SlotRecommender;
import com.ously.gamble.api.user.*;
import com.ously.gamble.api.vendor.VendorService;
import com.ously.gamble.baseclasses.BaseController;
import com.ously.gamble.payload.PagedResponse;
import com.ously.gamble.payload.purchase.*;
import com.ously.gamble.payment.adyen.api.AdyenWebPaymentService;
import com.ously.gamble.persistence.dto.*;
import com.ously.gamble.persistence.dto.JsonViews.JVUser;
import com.ously.gamble.persistence.model.PaymentStatus;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.util.AppConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.security.RolesAllowed;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;

import static org.springframework.data.domain.Sort.Direction.DESC;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * The APP (web, ios and android) controller. All (normal) traffic from the applications end here.
 */
@RestController
@RequestMapping("/api/app")
@Tag(name = "app-controller", description = "The app controller")
public class AppController extends BaseController {

    final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final String HEADER_STRING = "Authorization";

    @Autowired
    BonuscodeAffiliateService affiliateSrv;

    @Autowired
    HelloService helloService;

    @Autowired
    AuthenticationService authenticationService;

    @Autowired
    AdyenWebPaymentService aps;

    @Autowired
    MissionService missionSrv;

    @Autowired
    CategoryService catSrv;

    @Autowired
    VendorService vSrv;

    @Autowired
    UserMessageService usmSrv;

    @Autowired(required = false)
    UserAttributeService attSrv;

    @Autowired(required = false)
    CPopupService cPuService;

    @Autowired(required = false)
    MarketingEmailService marketingEmailService;


    private final HttpHeaders ceGzipHeaders = new HttpHeaders();

    @PostConstruct
    public void prepareHeaders() {
        ceGzipHeaders.set("Content-Encoding", "gzip");
    }

    @Operation(description = "run html game", hidden = true)
    @GetMapping(path = "/game/launch", produces = "text/html")
    public ResponseEntity<String> runHtmlGameAESA(@RequestParam(value = "sid") String id) {
        return runHtmlGameAESANew(id);
    }

    @Operation(description = "run html game", hidden = true)
    @GetMapping(path = "/session/launch", produces = "text/html")
    public ResponseEntity<String> runHtmlGameAESANew(@RequestParam(value = "sid") String id) {
        var html = sessionService.getHtmlForSessionAES(id, null);
        if (html == null) {
            log.warn("Error resolving crypt sid {}", id);
            return ResponseEntity.badRequest().build();
        }
        return ResponseEntity.ok(html);
    }

    /**
     * Mission stuff
     */
    @Operation(description = "get Missions",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/missions", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<UserMissions> getUserMissions(@CurrentUser UserPrincipal currentUser,
                                                        @Parameter(hidden = true) @RequestHeader(
                                                                value = "X-System-Name",
                                                                defaultValue = "WEB") String ua) {
        var gp = findPlatformByUserAgent("getMissions", ua);
        return ResponseEntity.ok(missionSrv.getMissionMissionsForPlatformAndUser(gp, currentUser.getId()));
    }

    /**
     * Level Rules
     */
    @Autowired
    LevelManagerService lvlMgr;

    @Operation(description = "get Level info",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/levels", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<Collection<LevelInfo>> getLevelInfo(
            @RequestParam(value = "previous", defaultValue = "-1", required = false) int previous,
            @RequestParam(value = "next", defaultValue = "-1", required = false) int next,
            @CurrentUser UserPrincipal currentUser) {
        return ResponseEntity.ok(lvlMgr.getLevelInfosFinal(currentUser.getId(), previous, next));
    }


    /**
     * Bonus Code calls
     **/
    @Operation(description = "applyBonusCode",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/affiliate/bonus/apply", consumes = "application/json",
            produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<Boolean> applyBonusCode(@RequestBody BonusCodeApplyRequest req,
                                                  @CurrentUser UserPrincipal currentUser) {
        return ResponseEntity.ok(affiliateSrv.activateBonusCodeForUser(currentUser.getId(), req.getCode()).getClaimed());
    }

    @Operation(description = "activate BonusCode",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/affiliate/bonus/activate", consumes = "application/json",
            produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<ActivateCodeResponse> activateBonusCode(
            @RequestBody BonusCodeApplyRequest req, @CurrentUser UserPrincipal currentUser) {
        return ResponseEntity.ok(affiliateSrv.activateBonusCodeForUser(currentUser.getId(), req.getCode()));
    }


    /**
     * Registration stuff
     * (extended userinfo)
     */

    @Operation(description = "get extended userinfo",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/user/details", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<UserInformation> getUserExtInfo1(@CurrentUser UserPrincipal currentUser) {
        return ResponseEntity.ok(us.getUserInfo(currentUser.getId()));
    }

    @Operation(description = "get user attributes",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/user/attr", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<UserAttributes> getUserAttributes(@CurrentUser UserPrincipal currentUser) {
        if (attSrv != null) {
            return ResponseEntity.ok(attSrv.getUserAttributes(currentUser.getId()));
        } else {
            return ResponseEntity.ofNullable(null);
        }
    }

    @Operation(description = "update extended userinfo",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping(path = "/user/details", consumes = "application/json",
            produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<UserInformation> updateUserExtInfo(@RequestBody UserInformation ui,
                                                             @CurrentUser UserPrincipal currentUser) throws Exception {
        return ResponseEntity.ok(us.updateUserInformation(currentUser.getId(), ui, currentUser));
    }

    @Operation(description = "check for unique usernnames/displaynames",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/user/displayname/check", produces = "application/json",
            consumes = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<UsernameVerificationResponse> verifyUsername(
            @RequestBody UsernameVerificationRequest req, @CurrentUser UserPrincipal currentUser) {
        return ResponseEntity.ok(us.verifyUsername(req));
    }


    /**
     * UTM Stuff
     */
    @Operation(description = "Send UTM data for a user",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/meta", consumes = "application/json", produces = "application/json")
    @RolesAllowed("USER")
    public void saveMeta(@RequestBody UpdateUtmDataRequest utmData,
                         @CurrentUser UserPrincipal currentUser) {
        us.updateUserMeta(utmData, currentUser.getId());
    }

    /**
     * LOGIN/HELLO/LOGOUT STUFF
     */

    @Operation(description = "Alternate method for signin, adds device codes and FCM TOKEN",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/hello", consumes = "application/json", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<HelloResponse> doHello(@RequestBody HelloRequest hr, @Parameter(
            hidden = true) @RequestHeader Map<String, String> headers, @Parameter(
            hidden = true) HttpServletRequest request, @CurrentUser UserPrincipal currentUser) {
        return helloService.hello(hr, currentUser.getId(), getRemoteAddress(headers, request), headers);
    }

    private static String getRemoteAddress(Map<String, String> headers, HttpServletRequest request) {
        var rmtAddr = headers.get("X-Forwarded-For");
        if (rmtAddr == null) {
            return request.getRemoteAddr();
        }
        return rmtAddr;
    }

    @Operation(description = "refresh a token FB",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/refreshToken", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<JwtAuthenticationResponse> refreshUserToken2(
            @RequestBody RefreshRequest req,
            @Parameter(hidden = true) @RequestHeader Map<String, String> headers,
            @Parameter(hidden = true) HttpServletRequest request) {
        return authenticationService.refreshToken(req, getRemoteAddress(headers, request));
    }


    @PostMapping(path = "/signin", consumes = "application/json", produces = "application/json")
    public ResponseEntity<JwtAuthenticationResponse> authenticateFBUser(
            @Valid @RequestBody LoginRequest loginRequest,
            @Parameter(hidden = true) @RequestHeader Map<String, String> headers,
            @Parameter(hidden = true) HttpServletRequest request) {
        return authenticationService.authenticate(loginRequest, getRemoteAddress(headers, request));
    }

    @Operation(description = "delete account", security = {@SecurityRequirement(name = "bearer-key")})
    @RolesAllowed("USER")
    @DeleteMapping(path = "/account")
    public ResponseEntity<String> deleteAccount(@Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
                                                @Parameter(hidden = true) HttpServletRequest request,
                                                @Parameter(hidden = true) HttpServletResponse response) {

        authenticationService.deleteAccount(currentUser.getId(), currentUser.getLocalId(), currentUser.getTokenKey());


        // Clear cookies
        SecurityContextHolder.clearContext();
        var session = request.getSession(false);
        if (session != null) {
            session.invalidate();
        }
        if (request.getCookies() != null) {
            for (var cookie : request.getCookies()) {
                cookie.setMaxAge(0);
            }
        }
        response.addHeader(HEADER_STRING, "");
        return ResponseEntity.ok("OK");
    }


    @Operation(description = "logout",
            security = {@SecurityRequirement(name = "bearer-key")})
    @RolesAllowed(
            "USER")
    @DeleteMapping(path = "/logout",
            produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<String> logout(@Parameter(hidden = true) HttpServletRequest request,
                                         @Parameter(hidden = true) HttpServletResponse response) {
        SecurityContextHolder.clearContext();
        var session = request.getSession(false);
        if (session != null) {
            session.invalidate();
        }
        if (request.getCookies() != null) {
            for (var cookie : request.getCookies()) {
                cookie.setMaxAge(0);
            }
        }
        response.addHeader(HEADER_STRING, "");
        return ResponseEntity.ok("OK");
    }


    /**
     * DOUBLE UP STUFF
     */
    @Autowired
    DoubleUpService dugService;


    @Operation(description = "create an after session doubleup",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/initialise", consumes = "application/json",
            produces = "application/json")
    @RolesAllowed("USER")
    public InitiateDoubleUpResponse initiateDoubleUp(
            @Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
            @RequestBody InitiateDoubleUpRequest request) {
        return dugService.initiateDoubleUp(currentUser.getId(), request);
    }


    @Operation(description = "gets list of 'weapons'",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/weapons", produces = "application/json")
    @RolesAllowed("USER")
    // NOT USED
    public List<GambleWeapon> getWeapons() {
        return dugService.getWeapons();
    }

    @Operation(description = "gets list of 'foes'",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/foes", produces = "application/json")
    @RolesAllowed("USER")
    // NOT USED
    public List<GambleFoe> getFoes() {
        return dugService.getFoes();
    }

    @Operation(description = "play a doubleup round",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/gamble", consumes = "application/json", produces = "application/json")
    @RolesAllowed("USER")
    public GambleResult gamble(@Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
                               @RequestBody GambleRequest gr) {
        return dugService.gamble(currentUser.getId(), gr);
    }

    @Operation(description = "end a doubleup game",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/take", consumes = "application/json", produces = "application/json")
    @RolesAllowed("USER")
    public TakeResult take(@Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
                           @RequestBody TakeRequest tr) {
        return dugService.take(currentUser.getId(), tr);
    }

    /**
     * Simplified double up
     */

    @Operation(description = "create an after session simplified doubleup",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/dug/initialise", consumes = "application/json",
            produces = "application/json")
    @RolesAllowed("USER")
    public InitiateDoubleUpResponse initiateSimplifiedDoubleUp(
            @Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
            @RequestBody InitiateDoubleUpRequest request) {
        return dugService.initiateDoubleUp(currentUser.getId(), request);
    }


    @Operation(description = "play a simplified doubleup round",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/dug/gamble", consumes = "application/json", produces = "application/json")
    @RolesAllowed("USER")
    public GambleResult gambleSimplified(@Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
                                         @RequestBody GambleRequest gr) {
        return dugService.gamble(currentUser.getId(), gr);
    }

    @Operation(description = "end a simplified doubleup game",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/dug/take", consumes = "application/json", produces = "application/json")
    @RolesAllowed("USER")
    public TakeResult takeSimplified(@Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
                                     @RequestBody TakeRequest tr) {
        return dugService.take(currentUser.getId(), tr);
    }


    /**
     * Games/Sessions/...
     */

    @Autowired
    UserGameAttributesService ugaService;

    @Autowired
    SlotRecommender recommender;

    @Autowired
    GameService gameService;


    @Autowired
    SessionService sessionService;


    // Personalisation, favs & category stuff
    @Operation(description = "returns users favorite game list",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/games/favorites", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<List<Long>> getFavorites(
            @Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
            @Parameter(hidden = true) @RequestHeader(value = "X-System-Name",
                    defaultValue = "WEB") String ua) {
        var gp = findPlatformByUserAgent("getHistory", ua);
        return ResponseEntity.ok(ugaService.getFavorites(currentUser.getId(), gp));
    }

    @Operation(description = "returns users unlocked games and vendors list",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/games/unlocks", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<CasinoUserUnlockInfo> getUnlocks(
            @Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
            @Parameter(hidden = true) @RequestHeader(value = "X-System-Name",
                    defaultValue = "WEB") String ua) {
        var gp = findPlatformByUserAgent("getUnlocks", ua);
        return ResponseEntity.ok(ugaService.getUnlocks(currentUser.getId(), gp));
    }

    @Operation(description = "add favorite game",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/games/favorites", consumes = "application/json")
    @RolesAllowed("USER")
    public void setFavorite(@Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
                            @RequestBody SetFavoriteRequest request) {
        try {
            ugaService.setFavorite(currentUser.getId(), request);
        } catch (Exception e) {
            log.warn("Cannot set Favorite", e);
        }
    }

    @Operation(description = "lists the users game history",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/games/history", produces = "application/json")
    @RolesAllowed("USER")
    public List<GameList> getHistory(
            @Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
            @Parameter(hidden = true) @RequestHeader(value = "X-System-Name",
                    defaultValue = "WEB") String ua) {
        var gp = findPlatformByUserAgent("getHistory", ua);
        return ugaService.getHistory(currentUser.getId(), gp);
    }

    @Operation(description = "returns recommendations for a user by relations",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/games/recommendations", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<List<Integer>> getRecommendations(
            @Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
            @RequestHeader(value = "X-System-Name", defaultValue = "WEB") String ua) {
        var gp = findPlatformByUserAgent("getRecommends", ua);
        return ResponseEntity.ok(recommender.calculateRecommendedSlots(new RecommendationKey(currentUser.getId(), gp)).getVal1());
    }

    @Operation(description = "returns recommendations for a user by Category",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/games/recommendations2", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<List<Integer>> getRecommendationsByCategory(
            @Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
            @RequestHeader(value = "X-System-Name", defaultValue = "WEB") String ua) {
        var gp = findPlatformByUserAgent("getRecommends", ua);
        return ResponseEntity.ok(recommender.calculateRecommendedSlots(new RecommendationKey(currentUser.getId(), gp)).getVal2());
    }

    @Operation(description = "lists all groups",
            security = {@SecurityRequirement(name = "bearer-key")},
            responses = {@ApiResponse(description = "Successful Operation", responseCode = "200",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(
                                    implementation = GlobalSearchResponse.class)))})
    @GetMapping(path = "/games/groups", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<byte[]> getGroups(
            @Parameter(hidden = true) @RequestHeader(value = "X-System-Name",
                    defaultValue = "WEB") String ua) {
        var gp = findPlatformByUserAgent("getgroups", ua);
        return ResponseEntity.ok().headers(ceGzipHeaders).body(catSrv.getGroupsAndFiltersFor(gp).getData());
    }

    //
    // SlotInfo retrieval
    //
    @Operation(description = "returns SlotInfo of a game (USR)",
            security = {@SecurityRequirement(name = "bearer-key")},
            responses = {@ApiResponse(description = "Successful Operation", responseCode = "200",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(
                                    implementation = SlotInfo.class)))})
    @GetMapping(path = "/gameinfo/{id}", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<byte[]> getSlotinfo(@PathVariable(value = "id") Long gameId,
                                              @CurrentUser UserPrincipal currentUser) {
        var slotinfo = gameService.getSlotinfo(gameId, currentUser.getLanguage());
        return slotinfo.map(slotInfoCacheItemWrapper -> new ResponseEntity<>(slotInfoCacheItemWrapper.getData(), HttpStatus.OK)).orElseGet(() -> ResponseEntity.notFound().build());
    }


    @Autowired
    GameOptionsService goService;

    @Operation(description = "returns game start options/status for a user (USR)",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/gamestatus/{id}", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<GameStartOptions> getGameStartOptions(
            @PathVariable(value = "id") int gameId,
            @CurrentUser UserPrincipal currentUser) {
        return ResponseEntity.ok(goService.getSlotStatus(gameId, currentUser.getId()));
    }

    @Operation(description = "tries to unlock a game for a user (USR)",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping(path = "/gamestatus/{id}", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<GameStartOptions> unlockGameOptions(
            @PathVariable(value = "id") int gameId,
            @CurrentUser UserPrincipal currentUser) {
        return ResponseEntity.ok(goService.unlockSlot(gameId, currentUser.getId()));
    }

    @Operation(description = "tries to unlock a game for a user (USR)",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping(path = "/gamestatus/{id}/{option}", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<GameStartOptions> unlockGameOptionsSpecific(
            @PathVariable(value = "id") int gameId,
            @CurrentUser UserPrincipal currentUser,
            @PathVariable(value = "option") GameUnlockOption option
    ) {
        return ResponseEntity.ok(goService.unlockSlot(gameId, currentUser.getId(), option));
    }


    @Operation(description = "returns list of topslots",
            responses = {@ApiResponse(description = "Successful Operation", responseCode = "200",
                    content = @Content(mediaType = "application/json",
                            array = @ArraySchema(schema = @Schema(
                                    implementation = PlayableGame.class))))})
    @GetMapping(path = "/games/topslots", produces = "application/json")
    public ResponseEntity<byte[]> getAllTopGames() {
        return ResponseEntity.ok().headers(ceGzipHeaders).body(gameService.getAllUserGamesUnpagedNew(GamePlatform.TOPSLOTS).getData());
    }

    @Operation(description = "returns all active games",
            security = {@SecurityRequirement(name = "bearer-key")},
            responses = {@ApiResponse(description = "Successful Operation", responseCode = "200",
                    content = @Content(mediaType = "application/json",
                            array = @ArraySchema(schema = @Schema(
                                    implementation = PlayableGame.class))))})
    @RolesAllowed("USER")
    @GetMapping(path = "/games/all", produces = "application/json")
    public ResponseEntity<byte[]> getAllUserGamesUnpaged(
            @Parameter(hidden = true) @RequestHeader(value = "X-System-Name",
                    defaultValue = "WEB") String ua) {
        var gp = findPlatformByUserAgent("getallgames", ua);
        if (gp == GamePlatform.IOS) {
            log.debug("Got get all IOS Games with platform:{}", ua);
        } else {
            log.debug("Got get all Games with platform:{}", ua);
        }
        return ResponseEntity.ok().headers(ceGzipHeaders).body(gameService.getAllUserGamesUnpagedNew(gp).getData());
    }


    @Operation(description = "create a gamesession",
            security = {@SecurityRequirement(name = "bearer-key")}, hidden = true)
    @RolesAllowed("USER")
    @PostMapping(path = "/session/initialise", consumes = "application/json",
            produces = "application/json")
    public InitialiseGameResponse initialiseGameNew(@RequestBody InitialiseGameRequest req,
                                                    @Parameter(
                                                            hidden = true) @CurrentUser UserPrincipal currentUser,
                                                    @Parameter(
                                                            hidden = true) HttpServletRequest request,
                                                    @Parameter(hidden = true) @RequestHeader(
                                                            value = "X-Forwarded-For",
                                                            defaultValue = "") String xfwd,
                                                    @Parameter(hidden = true) @RequestHeader(
                                                            value = "X-System-Name",
                                                            defaultValue = "WEB") String ua) {
        return initialiseGame(req, currentUser, request, xfwd, ua);
    }

    @Operation(description = "create a gamesession",
            security = {@SecurityRequirement(name = "bearer-key")})
    @RolesAllowed("USER")
    @PostMapping(path = "/games/initialise", consumes = "application/json",
            produces = "application/json")
    public InitialiseGameResponse initialiseGame(@RequestBody InitialiseGameRequest req, @Parameter(
            hidden = true) @CurrentUser UserPrincipal currentUser, @Parameter(
            hidden = true) HttpServletRequest request, @Parameter(hidden = true) @RequestHeader(
            value = "X-Forwarded-For", defaultValue = "") String xfwd,
                                                 @Parameter(hidden = true) @RequestHeader(
                                                         value = "X-System-Name",
                                                         defaultValue = "WEB") String ua) {
        if (StringUtils.isEmpty(xfwd)) {
            xfwd = request.getRemoteAddr();
        }
        return sessionService.initialiseGameStart(req, currentUser.getId(), xfwd, ua, request.getHeader("User-Agent"));
    }

    @Operation(description = "returns the list of categories",
            security = {@SecurityRequirement(name = "bearer-key")},
            responses = {@ApiResponse(description = "Successful Operation", responseCode = "200",
                    content = @Content(mediaType = "application/json",
                            array = @ArraySchema(schema = @Schema(
                                    implementation = CasinoGameCategory.class))))})
    @RolesAllowed("USER")
    @GetMapping(path = "/games/categories", produces = "application/json")
    public ResponseEntity<byte[]> getUserCategories() {
        return ResponseEntity.ok().headers(ceGzipHeaders).body(gameService.getAllGameCategories(false).getData());
    }

    /**
     * PURCHASE STUFF
     */

    @Autowired
    PurchaseService purchaseService;


    @Operation(description = "check purchase with stores",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/iap/verify", consumes = "application/json",
            produces = "application/json")
    @RolesAllowed("USER")
    public PurchaseVerificationResponse verifyPurchase(@RequestBody PurchaseVerificationRequest req,
                                                       @CurrentUser UserPrincipal currentUser) {
        try {
            return purchaseService.verify(req, currentUser.getId());
        } catch (Exception e) {
            log.error("Error verifying inapp purchase {}", req, e);
            return new PurchaseVerificationResponse(req);
        }
    }

    @Operation(description = "get payment methods for a shopper",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/iap/methods")
    @RolesAllowed("USER")
    public ResponseEntity<PaymentMethodsResponse> getPaymentMethods(
            @RequestBody PaymentMethodsRequest req,
            @CurrentUser UserPrincipal currentUser) throws Exception {
        return ResponseEntity.ok(aps.requestPaymentMethods(req.getCountry(), req.getLocale(), req.getCurrency(), req.getAmount(), currentUser.getId()));
    }

    /**
     *
     */
    @Operation(description = "checkout, payment processing",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/iap/checkout")
    @RolesAllowed("USER")
    public DropInPaymentResponse<PaymentsResponse> checkout(@RequestBody DropInPaymentRequest req,
                                                            @CurrentUser UserPrincipal currentUser) {
        try {
            return aps.paymentRequest(req, currentUser.getId());
        } catch (Exception e) {
            log.error("Error checking out drop in purchase: {} / User: {}", req, currentUser, e);
            return new DropInPaymentResponse<>();
        }
    }

    @Operation(description = "checkout 2,details processing",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/iap/details")
    @RolesAllowed("USER")
    public DropInPaymentResponse<PaymentsResponse> details(@RequestBody PaymentDetailRequest req,
                                                           @CurrentUser UserPrincipal currentUser) {
        try {
            return aps.additionalData(req, currentUser.getId());
        } catch (Exception e) {
            log.error("Error setting add. data for purchase: {} / User: {}", req, currentUser, e);
            return new DropInPaymentResponse<>();
        }
    }


    @Operation(description = "check payment processing status",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/iap/check/{orderRef}")
    @RolesAllowed("USER")
    public ResponseEntity<CheckPaymentResult> checkOrder(@RequestBody String payload,
                                                         @PathVariable("orderRef") String orderRef,
                                                         @CurrentUser UserPrincipal currentUser) {
        try {

            CheckPaymentResult checkPaymentResult = null;
            var start = System.currentTimeMillis();
            var end = start + 100;
            while (end > System.currentTimeMillis()) {
                checkPaymentResult = aps.checkPurchase(payload, orderRef, currentUser.getId());

                if (checkPaymentResult != null && (checkPaymentResult.getStatus() == PaymentStatus.ABORTED || checkPaymentResult.getStatus() == PaymentStatus.AUTHORIZED)) {
                    return ResponseEntity.ok(checkPaymentResult);
                }
            }
            if (checkPaymentResult != null) {
                return ResponseEntity.ok(checkPaymentResult);
            }
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error checking purchase purchase: {} / User: {}", orderRef, currentUser, e);
        }
        return ResponseEntity.badRequest().build();
    }

    @Operation(description = "abort payment processing",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/iap/abort/{orderRef}")
    @RolesAllowed("USER")
    public void cancelOrder(@RequestBody String payload, @PathVariable("orderRef") String orderRef,
                            @CurrentUser UserPrincipal currentUser) {
        try {
            aps.abortPurchase(payload, orderRef, currentUser.getId());
        } catch (Exception e) {
            log.error("Error aborting payment:", e);
        }
    }


    @Operation(description = "displays available products (dummy)",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/iap/products/", produces = "application/json")
    @RolesAllowed("USER")
    public AvailableProducts getProducts(@CurrentUser UserPrincipal currentUser,
                                         @Parameter(hidden = true) @RequestHeader(
                                                 value = "X-System-Name",
                                                 defaultValue = "WEB") String ua,
                                         @Parameter(hidden = true) @RequestHeader(
                                                 value = "X-Timezone-Offset",
                                                 defaultValue = "0") String tz) {
        var gp = findPlatformByUserAgent("getProducts", ua);
        var ap = new AvailableProducts();
        ap.setProducts(purchaseService.getAllActiveConsumables(currentUser.getId(), gp, tz));
        return ap;
    }

    @Operation(description = "displays available products (tutorial)",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/iap/products/tutorial", produces = "application/json")
    @RolesAllowed("USER")
    public AvailableProducts getProductsTutorial(@CurrentUser UserPrincipal currentUser,
                                                 @Parameter(hidden = true) @RequestHeader(
                                                         value = "X-System-Name",
                                                         defaultValue = "WEB") String ua,
                                                 @Parameter(hidden = true) @RequestHeader(
                                                         value = "X-Timezone-Offset",
                                                         defaultValue = "0") String tz) {
        var gp = findPlatformByUserAgent("getProducts", ua);
        var ap = new AvailableProducts();
        ap.setProducts(purchaseService.getAllActiveConsumablesTutorial(currentUser.getId(), gp, tz));
        return ap;
    }


    @Operation(description = "returns piggybank product if available",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/iap/products/piggybank", produces = "application/json")
    @RolesAllowed("USER")
    public ProductInfo getPiggyBankProduct(@CurrentUser UserPrincipal currentUser) {
        return purchaseService.getPiggyConsumable(currentUser.getId());
    }


    @Operation(description = "get all purchases (USR)",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/iap/purchases/", produces = "application/json")
    @RolesAllowed("USER")
    public PagedResponse<UserPurchase> getAllPurchasesForUser(
            @CurrentUser UserPrincipal currentUser,
            @RequestParam(value = "page", defaultValue = AppConstants.DEFAULT_PAGE_NUMBER) int page,
            @RequestParam(value = "size", defaultValue = AppConstants.DEFAULT_PAGE_SIZE) int size) {
        var allPurchasesPage = purchaseService.getAllPurchases(currentUser.getId(), page, size);
        return createPagedResponse(allPurchasesPage, UserPurchase::new);
    }

    /**
     * Session (games)
     */

    @Autowired
    FeatureConfig fConfig;

    @Operation(description = "get session statistics (USR)",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/session/statistics/{gameId}", produces = "application/json")
    @RolesAllowed("USER")
    public SessionStatistics getSessionStatistics(
            @Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
            @PathVariable("gameId") Long gameId) {

        try {
            var stats = sessionService.getStatistics(gameId, currentUser.getId());
            if (stats.getMaxDoubleAmount().longValue() == 0L && Boolean.TRUE.equals(fConfig.getTesting()) && stats.getNumberOfPlays() > 0L) {
                stats.setMaxDoubleAmount(new BigDecimal("1000.0"));
            }
            return stats;
        } catch (Exception e) {
            return new SessionStatistics();
        }
    }

    @Operation(description = "get session statistics (USR) and updated Leaderboards",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/session/statistics/lb/{gameId}", produces = "application/json")
    @RolesAllowed("USER")
    public SessionStatistics getSessionStatisticsAndLeaderboards(
            @Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
            @PathVariable("gameId") Long gameId) {

        try {
            var stats = sessionService.getStatistics(gameId, currentUser.getId());
            if (stats.getMaxDoubleAmount().longValue() == 0L && Boolean.TRUE.equals(fConfig.getTesting()) && stats.getNumberOfPlays() > 0L) {
                stats.setMaxDoubleAmount(new BigDecimal("1000.0"));
            }
            // only check for post-session leaderboards if rounds > 0
            if (stats.getNumberOfPlays() > 0) {
                GameLeaderboards gameLeaderboardsForGameAndUserAfterSession = glbService.getGameLeaderboardsForGameAndUserAfterSession(gameId.intValue(), currentUser.getId(), stats);
                stats.setLeaderboards(gameLeaderboardsForGameAndUserAfterSession);
            }
            return stats;
        } catch (Exception e) {
            return new SessionStatistics();
        }
    }


    @Operation(description = "get session statistics (USR)",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/session/statistics", produces = "application/json")
    @RolesAllowed("USER")
    public PagedResponse<UserSessionInfoPL> getSessionStatisticsUser(
            @Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
            @RequestParam(value = "page", defaultValue = AppConstants.DEFAULT_PAGE_NUMBER) int page,
            @RequestParam(value = "size", defaultValue = AppConstants.DEFAULT_PAGE_SIZE) int size) {
        var sessions = sessionService.getStatisticsForUser(currentUser.getId(), page, size);
        return createPagedResponse(sessions, Function.identity());
    }


    @Operation(description = "get all purchases (USR)",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/iap/purchases/")
    @RolesAllowed("USER")
    public PagedResponse<UserPurchase> getAllPurchasesForSelf(
            @CurrentUser UserPrincipal currentUser,
            @RequestParam(value = "page", defaultValue = AppConstants.DEFAULT_PAGE_NUMBER) int page,
            @RequestParam(value = "size", defaultValue = AppConstants.DEFAULT_PAGE_SIZE) int size) {
        var allPurchasesPage = purchaseService.getAllPurchases(currentUser.getId(), page, size);
        return createPagedResponse(allPurchasesPage, UserPurchase::new);
    }


    /**
     * tokens & txs
     */

    @Autowired
    AchievementService aSrv;

    @Operation(description = "confirm an achievement",
            security = {@SecurityRequirement(name = "bearer-key")},
            responses = {@ApiResponse(description = "Successful Operation", responseCode = "200",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(
                                    implementation = UserStats.class)))})
    @PostMapping(path = "/achievements/confirm/{aId}", consumes = APPLICATION_JSON_VALUE,
            produces = APPLICATION_JSON_VALUE)
    @RolesAllowed("USER")
    public ResponseEntity<UserStats> confirmAchievement(@RequestBody ConfirmAchievementRequest caR,
                                                        @PathVariable("aId") long aId,
                                                        @CurrentUser UserPrincipal currentUser) {
        try {
            var result = ResponseEntity.ok(aSrv.activateAchievement(aId, currentUser.getId(), currentUser.getLanguage(),
                    caR.getSecret()));
            bt.expireUserStats(currentUser.getId());
            return result;
        } catch (Exception e) {
            var result = ResponseEntity.ok(getAndPrepareUserStats(currentUser.getId(),
                    currentUser.getLanguage(), false));
            bt.expireUserStats(currentUser.getId());
            return result;
        }
    }


    @Autowired
    BridgeTools bt;


    @Operation(description = "get stats of current user (poll,sync)",
            security = {@SecurityRequirement(name = "bearer-key")})
    @RolesAllowed("USER")
    @GetMapping(path = "/stats", produces = "application/json")
    public ResponseEntity<UserStats> getUserStats(@CurrentUser UserPrincipal currentUser) {
        return ResponseEntity.ok(getAndPrepareUserStats(currentUser.getId(), currentUser.getLanguage(), false));
    }


    @Operation(description = "get stats of current user (poll/tutorial)",
            security = {@SecurityRequirement(name = "bearer-key")})
    @RolesAllowed("USER")
    @GetMapping(path = "/stats/tutorial", produces = "application/json")
    public UserStats getUserStatsTutorial(@CurrentUser UserPrincipal currentUser) {
        return getAndPrepareUserStats(currentUser.getId(), currentUser.getLanguage(), true);
    }

    private UserStats getAndPrepareUserStats(long currentUserId, String langCode,
                                             boolean isTutorial) {
        var userStats = bt.getUserStats(currentUserId, langCode);

        if (userStats != null) {
            // this might be dynamically increased when we detect high stat-poll rates
            userStats.setPollFreqMs(5000L);
            userStats.createCountDownTimeForWheel();

            if (isTutorial) {
                userStats.setAchievements(Collections.singletonList(aSrv.getTutorialModeAchievement(currentUserId, langCode)));
            }
        }
        return userStats;
    }

    @Autowired
    TokenService tks;

    //
    // Token based calls
    //
    @Operation(description = "return all tokens of a user",
            security = {@SecurityRequirement(name = "bearer-key")})
    @RolesAllowed("USER")
    @GetMapping(path = "/tokens", produces = APPLICATION_JSON_VALUE)
    public List<UserToken> getUserTokens(@CurrentUser UserPrincipal currentUser) {
        return tks.getTokens(currentUser.getId());
    }

    @Operation(description = "consume a token",
            security = {@SecurityRequirement(name = "bearer-key")})
    @RolesAllowed("USER")
    @PostMapping(path = "/tokens/use", consumes = "application/json",
            produces = "application/json")
    public UserStats useAToken(@CurrentUser UserPrincipal currentUser,
                               @RequestBody UserTokenUseRequest req) throws Exception {
        return tks.useToken(currentUser.getId(), currentUser.getLanguage(), req.getTokenId(), req.getTokenSecret());
    }


    /**
     * USER related Stuff
     */

    @Autowired
    UserService us;

    @Operation(description = "get player Profile",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/user/profile/{extId}")
    @RolesAllowed("USER")
    public ResponseEntity<PlayerProfile> getPlayerProfile(@PathVariable("extId") String extId) {
        return ResponseEntity.of(us.getPlayerProfile(extId));
    }

    @Operation(description = "send list of events to be stored in CRM",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/user/events", consumes = "application/json")
    @RolesAllowed("USER")
    public void sendEvents(@CurrentUser UserPrincipal currentUser,
                           @RequestBody ExternalEvents events) {
        log.debug("EVENTS incoming:{}", events);
        us.sendEvents(currentUser.getId(), events);
    }

    @Operation(description = "set push event",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/user/push", consumes = "application/json")
    @RolesAllowed("USER")
    public void sendPushStatus(@RequestBody CRMPushEvent pushStatus) {
        us.sendPushStatus(pushStatus);
    }


    /**
     * Wheel of fortune stuff
     */

    @Autowired
    WheelService wService;

    @Operation(description = "perform a spin",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/spin", consumes = "application/json", produces = "application/json")
    @RolesAllowed("USER")
    public SpinWheelResponse spinWheel(@RequestBody SpinWheelRequest req,
                                       @CurrentUser UserPrincipal currentUser) {
        return wService.doSpin(req, currentUser.getId());
    }

    @Operation(description = "collect the spin results",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/spin/collect", consumes = "application/json",
            produces = "application/json")
    @RolesAllowed("USER")
    public UserStats collectWheelResults(@RequestBody WheelWinCollectRequest req,
                                         @CurrentUser UserPrincipal currentUser) {
        return wService.collectResults(req, currentUser.getId());
    }

    /**
     * Contact requests
     **/
    @Autowired
    NotificationService ns;

    @PostMapping("/contact/support")
    @Operation(description = "sends a support request")
    public String contactRequest(@RequestBody ContactRequest req, @Parameter(
            hidden = true) @RequestHeader Map<String, String> headers,
                                 @Parameter(hidden = true) HttpServletRequest request) {
        ns.requestSupport(req, getRemoteAddress(headers, request), null);
        return "OK";
    }

    @PostMapping("/contact/supportU")
    @Operation(description = "sends a support request with token",
            security = {@SecurityRequirement(name = "bearer-key")})
    @RolesAllowed("USER")
    public String contactRequestLoggedIn(@CurrentUser UserPrincipal currentUser,
                                         @RequestBody ContactRequest req, @Parameter(
            hidden = true) @RequestHeader Map<String, String> headers,
                                         @Parameter(hidden = true) HttpServletRequest request) {
        ns.requestSupport(req, getRemoteAddress(headers, request), currentUser);
        return "OK";
    }

    @PostMapping("/contact/info")
    @Operation(description = "sends an info request")
//    @RolesAllowed("USER")
    public String infoRequest(
            //   @CurrentUser UserPrincipal currentUser,
            @RequestBody ContactRequest req,
            @Parameter(hidden = true) @RequestHeader Map<String, String> headers,
            @Parameter(hidden = true) HttpServletRequest request) {
        ns.requestInfoContact(req, getRemoteAddress(headers, request), null);
        return "OK";
    }

    @Autowired(required = false)
    RankingService rnkService;

    @Operation(description = "get ranking info",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/ranks", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<Map<String, RankResults>> getRanks(
            @CurrentUser UserPrincipal currentUser) {
        if (rnkService == null) {
            return ResponseEntity.of(Optional.empty());
        }
        return ResponseEntity.ok(rnkService.getRanksForUser(currentUser.getId()));
    }

    @Operation(description = "get ranking multi",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/ranks/multi", produces = "application/json")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Map<String, List<RankingResult>>> getRanksMulti() {
        if (rnkService == null) {
            return ResponseEntity.of(Optional.empty());
        }
        return ResponseEntity.ok(rnkService.getTopRanksMulti());
    }


    @Autowired
    LeaderboardService lbService;

    @Operation(description = "get all leaderboards",
            security = {@SecurityRequirement(name = "bearer-key")},
            responses = {@ApiResponse(description = "Successful Operation", responseCode = "200",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(
                                    implementation = Leaderboards.class)))})
    @GetMapping(value = "/leaderboards", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<byte[]> getLeaderboards() {
        return ResponseEntity.ok().headers(ceGzipHeaders).body(lbService.getLeaderboards().getData());
    }


    @Autowired(required = false)
    private GameLeaderboardsService glbService;

    @Operation(description = "get game leaderboards", security =
            {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/leaderboards/{gameId}", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<GameLeaderboards> getGameLeaderboards(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable("gameId") int gameId) {
        if (glbService != null) {
            return ResponseEntity.ok().body(glbService.getGameLeaderboardsForGameAndUser(gameId, currentUser.getId()));
        } else {
            return ResponseEntity.of(Optional.empty());
        }
    }


    @Parameter(hidden = true)
    @Operation(description = "end session landing page", hidden = true)
    @GetMapping(value = "/session/end/{sid}")
    public void endSessionUrlHandler(@Parameter(hidden = true) HttpServletResponse response,
                                     @PathVariable(value = "sid") Long id) throws IOException {
        response.setContentType("text/html");
        response.getOutputStream().print("<HTML><BODY style=\"background-color: black;\"></BODY></HTML>");
        response.getOutputStream().flush();
    }

    //
    // Popup Stuff
    //

    @Autowired
    PopupService popupSrv;

    @Operation(description = "retrieve a popup",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/popup/get/{key}", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<CasinoPopup> getPopup(@CurrentUser UserPrincipal currentUser,
                                                @PathVariable("key") String puKey) {
        return ResponseEntity.of(popupSrv.getPopupForUserAndLanguage(currentUser.getId(), puKey, currentUser.getLanguage()));
    }

    @Operation(description = "popup feedback",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping(path = "/popup/mark/feedback", produces = "application/json")
    @RolesAllowed("USER")
    public void markPopup(@CurrentUser UserPrincipal currentUser,
                          @RequestBody PopupFeedback feedback) {
        popupSrv.popupFeedback(currentUser.getId(), feedback);
    }


    @Operation(description = "cpopup feedback",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping(path = "/cpopup/mark", produces = "application/json")
    @RolesAllowed("USER")
    public void markCPopup(@CurrentUser UserPrincipal currentUser,
                           @RequestBody CPopupFeedback feedback) {
        if (cPuService != null) {
            cPuService.markPopupResult(currentUser.getId(), feedback.puid(), feedback.actionPerformed());
        } else {
            log.warn("Cpopups not available");
        }
    }

    //
    // Initial check
    //
    @Operation(description = "initial check")
    @PostMapping(path = "/precheck", produces = "application/json")
    public ResponseEntity<PreCheckResponse> preCheck(@RequestBody PreCheckRequest req) {
        log.debug("PRECHECK-REQ:{}", req);
        var deviceAnonUser = authenticationService.getDeviceAnonUser(req.getDi(), req.getNonce());
        var of = PreCheckResponse.of(deviceAnonUser);
        log.debug("PRECHECK-RESP:{}", of);
        return ResponseEntity.ok(of);
    }

    //
    // Initial FB check
    //
    @Operation(description = "fb check")
    @PostMapping(path = "/precheck2", produces = "application/json")
    public ResponseEntity<PreCheckResponse> preCheck2(@RequestBody PreCheckRequest2 req) {
        return retrieveOrCreateFacebookUser(req, null);
    }

    @Operation(description = "fb check",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/precheck3", produces = "application/json")
    @RolesAllowed("USER")
    public ResponseEntity<PreCheckResponse> preCheck3(@RequestBody PreCheckRequest2 req, @CurrentUser UserPrincipal currentUser) {
        return retrieveOrCreateFacebookUser(req, currentUser);
    }

    private ResponseEntity<PreCheckResponse> retrieveOrCreateFacebookUser(PreCheckRequest2 req, UserPrincipal currentUser) {
        log.debug("PRECHECK2-REQ:{}", req);
        var deviceAnonUser = authenticationService.getFacebookUser(req.getFbu(), currentUser);
        var of = PreCheckResponse.reuseOrig();
        if (deviceAnonUser != null) {
            of = PreCheckResponse.of(deviceAnonUser);
        }
        log.debug("PRECHECK-RESP:{}", of);
        return ResponseEntity.ok(of);
    }


    @Operation(description = "logout to anon")
    @PostMapping(path = "/precheck4", produces = "application/json")
    public ResponseEntity<PreCheckResponse> preCheck4(@RequestBody PreCheckRequest2 req) {
        return retrieveOrCreateAnonUser(req);
    }

    @Operation(description = "logout to anon")
    @PostMapping(path = "/precheck4d", produces = "application/json")
    public ResponseEntity<String> preCheck4d(@RequestBody PreCheckRequest2 req) {
        return retrieveOrCreateAnonUserD(req);
    }


    private ResponseEntity<PreCheckResponse> retrieveOrCreateAnonUser(PreCheckRequest2 req) {
        log.debug("PRECHECK4-REQ:{}", req);
        var deviceAnonUser = authenticationService.getOrCreateAnonUser(req.getFbu());
        var of = PreCheckResponse.reuseOrig();
        if (deviceAnonUser != null) {
            of = PreCheckResponse.of(deviceAnonUser);
        }
        log.debug("PRECHECK-RESP:{}", of);
        return ResponseEntity.ok(of);
    }

    private ResponseEntity<String> retrieveOrCreateAnonUserD(PreCheckRequest2 req) {
        StringBuilder strBuilder = new StringBuilder();
        strBuilder.append("PRECHECK4-REQ:").append(req);
        var deviceAnonUser = authenticationService.getOrCreateAnonUserD(req.getFbu(), strBuilder);

        strBuilder.append("PRECHECK-4 done simulating!");
        return ResponseEntity.ok(strBuilder.toString());
    }


    //
    // Loyalty Service
    //

    @Autowired
    LoyaltyService loyaltySrv;

    @Operation(description = "get loyalty status for user",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/bonus/loyalty")
    @RolesAllowed("USER")
    public ResponseEntity<LoyaltyBonusInfo> getLoyaltyBonusInfo(
            @CurrentUser UserPrincipal currentUser) {
        return ResponseEntity.ok(loyaltySrv.getLoyaltyInfo(currentUser.getId()));
    }

    //
    // User Messages
    //

    @Operation(description = "get user messages (pageable)", security =
            {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/msg", produces = "application/json")
    @RolesAllowed("USER")
    @JsonView(JVUser.class)
    public PagedResponse<UserMessageDto> getMessagesPaged(@CurrentUser UserPrincipal currentUser,
                                                          @ParameterObject @PageableDefault(
                                                                  size = 20, direction = DESC,
                                                                  sort =
                                                                          {"createdAt"}) Pageable pageable) {
        return createPagedResponse(usmSrv.getMessages(currentUser.getId(),
                        currentUser.getLanguage(), pageable),
                Function.identity());
    }

    @Operation(description = "get oldest popup", security =
            {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/popup", produces = "application/json")
    @RolesAllowed("USER")
    @JsonView(JVUser.class)
    public Optional<UserMessageDto> getNextPopup(@CurrentUser UserPrincipal currentUser) {
        return usmSrv.getOldestPopupMessage(currentUser.getId(), currentUser.getLanguage());
    }

    @Operation(description = "get msg by qualifier", security =
            {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/msg/{qualifier}", produces = "application/json")
    @RolesAllowed("USER")
    @JsonView(JVUser.class)
    public Optional<UserMessageDto> getMessageForQualifier(@CurrentUser UserPrincipal currentUser
            , @PathVariable("qualifier") String qualifier) {
        return usmSrv.getMessageByQualifier(currentUser.getId(), currentUser.getLanguage(), qualifier);
    }


    @Operation(description = "mark msg read", security =
            {@SecurityRequirement(name = "bearer-key")})
    @PutMapping(path = "/msg/read", produces = "application/json")
    @RolesAllowed("USER")
    public void markMessageRead(@CurrentUser UserPrincipal currentUser,
                                @RequestParam("secret") String secret) {
        log.debug("MarkMsg:{}", secret);
        if (usmSrv.markMessageRead(secret.replace(' ', '+'))) {
            bt.expireUserStats(currentUser.getId());
        }
    }

    @Operation(description = "delete msg", security =
            {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping(path = "/msg/delete", produces = "application/json")
    @RolesAllowed("USER")
    public void deleteMsg(@CurrentUser UserPrincipal currentUser,
                          @RequestParam("secret") String secret) {
        if (usmSrv.deleteMessage(secret.replace(' ', '+'))) {
            bt.expireUserStats(currentUser.getId());
        }
    }

    @Operation(description = "perform action on msg", security =
            {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(path = "/msg/action", produces = "application/json")
    @RolesAllowed("USER")
    public UserMessageActionResult doActionOnMsg(@CurrentUser UserPrincipal currentUser,
                                                 @RequestParam("secret") String secret,
                                                 @RequestParam("action") String action) {
        var result = usmSrv.doAction(secret.replace(' ', '+'), action);
        if (result.success()) {
            bt.expireUserStats(currentUser.getId());
        }
        return result;
    }


    /**
     * Session & Rounds for a user
     */

    @Operation(description = "get session statistics of a user paged",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/sessions", produces = "application/json")
    @RolesAllowed("USER")
    @JsonView(JsonViews.JVUser.class)
    public PagedResponse<SessionStatDto> getSessionStatsForUserPaged(
            @Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
            @RequestParam(value = "page", defaultValue = AppConstants.DEFAULT_PAGE_NUMBER) int page,
            @RequestParam(value = "size", defaultValue = AppConstants.DEFAULT_PAGE_SIZE) int size) {
        var sessions = sessionService.getStatsForUser(currentUser.getId(), page, size);
        return createPagedResponse(sessions, Function.identity());
    }

    @Operation(description = "get transactions/rounds of a users session",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/sessions/rounds/{sessionId}", produces = "application/json")
    @RolesAllowed("USER")
    @JsonView(JsonViews.JVUser.class)
    public List<SessionTxDto> getRoundsForUserAndSession(
            @Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
            @PathVariable("sessionId") long sessionId) {
        return sessionService.getTxForUserAndSession(currentUser.getId(), sessionId);
    }

    @Operation(description = "get all rounds of a user ordered desc (newest first)",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/sessions/rounds", produces = "application/json")
    @RolesAllowed("USER")
    @JsonView(JsonViews.JVUser.class)
    public PagedResponse<SessionTxDto> getRoundsForUser(
            @Parameter(hidden = true) @CurrentUser UserPrincipal currentUser,
            @RequestParam(value = "page", defaultValue = AppConstants.DEFAULT_PAGE_NUMBER) int page,
            @RequestParam(value = "size", defaultValue = AppConstants.DEFAULT_PAGE_SIZE) int size) {
        var rounds = sessionService.getRoundsForUser(currentUser.getId(), page, size);
        return createPagedResponse(rounds, Function.identity());

    }

    @Operation(description = "update marketing email",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/marketing-email/update/{email}")
    @RolesAllowed("USER")
    public ResponseEntity<Void> updateMarketingEmail(@CurrentUser UserPrincipal userPrincipal, @PathVariable(value = "email") String email) {
        if (marketingEmailService == null) {
            return ResponseEntity.notFound().build();
        }
        try {
            marketingEmailService.updateEmail(userPrincipal.getId(), email);
            return ResponseEntity.ok().build();
        } catch (Exception ignored) {
            return ResponseEntity.badRequest().build();
        }
    }
}

//package com.ously.gamble.controller;
//
//import com.ously.gamble.baseclasses.BaseController;
//import com.ously.gamble.exception.AppException;
//import com.ously.gamble.exception.ErrorResponse;
//import com.ously.gamble.exception.KYCException;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.media.Content;
//import io.swagger.v3.oas.annotations.media.Schema;
//import io.swagger.v3.oas.annotations.responses.ApiResponse;
//import io.swagger.v3.oas.annotations.responses.ApiResponses;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import jakarta.annotation.security.RolesAllowed;
//
//
//@RestController
//@RequestMapping("/api/app/dev")
//@Tag(name = "dev-controller", description = "Dev controller")
//public class DevController extends BaseController {
//
//    Logger log = LoggerFactory.getLogger(DevController.class);
//
//    @GetMapping("/testerr/{result}")
//    @Operation(description = "errormsgtesting")
//    @ApiResponses(value = {
//            @ApiResponse(
//                    responseCode = "200",
//                    description = "ok",
//                    content = @Content(mediaType = "text/plain", schema = @Schema(implementation =
//                            String.class))),
//            @ApiResponse(
//                    responseCode = "400",
//                    description = "Bad Request",
//                    content = @Content(mediaType = "application/json", schema = @Schema(implementation =
//                            ErrorResponse.class))),
//            @ApiResponse(
//                    responseCode = "409",
//                    description = "KYC Conflict",
//                    content = @Content(mediaType = "application/json", schema = @Schema(implementation =
//                            ErrorResponse.class)))
//    })
//    @RolesAllowed("USER")
//    public String sendMsg(@PathVariable("result") String result) {
//
//        switch (result) {
//            case "kyc":
//                throw new KYCException(200, "333", "kyc details");
//            case "app":
//                throw new AppException(200, "app exc", "app details");
//            case "npe":
//                throw new NullPointerException("NPE");
//        }
//
//        return "OK";
//    }
//
//}

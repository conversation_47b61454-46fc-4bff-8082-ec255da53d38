package com.ously.gamble.configs;

import com.ously.gamble.api.cache.CachedMap;
import com.ously.gamble.api.cache.LCacheFactory;
import com.ously.gamble.api.games.CategoryService;
import com.ously.gamble.api.games.GameManagementService;
import com.ously.gamble.api.slotfinder.RecommendationWrapper;
import com.ously.gamble.api.slotfinder.SlotRecommender;
import com.ously.gamble.api.user.UserGameAttributesManagementService;
import com.ously.gamble.services.slotfinder.SlotRecommenderImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
public class GameHandlingConfiguration {

    @SuppressWarnings("rawtypes")
    @Bean
    public SlotRecommender slotRecommender(GameManagementService gS,
                                           UserGameAttributesManagementService ugaMgmt,
                                           CachedMap<String, RecommendationWrapper> recommendationCache,
                                           CategoryService cS, LCacheFactory lcf) {
        return new SlotRecommenderImpl(gS, ugaMgmt, recommendationCache, cS, lcf);
    }

}

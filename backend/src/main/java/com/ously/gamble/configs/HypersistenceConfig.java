//package com.ously.gamble.configs;
//
//import io.hypersistence.optimizer.HypersistenceOptimizer;
//import io.hypersistence.optimizer.core.config.JpaConfig;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import jakarta.persistence.EntityManagerFactory;
//
//@Configuration
//public class HypersistenceConfig {
//
//        @Bean
//        public HypersistenceOptimizer hypersistenceOptimizer(
//                EntityManagerFactory entityManagerFactory) {
//            return new HypersistenceOptimizer(
//                    new JpaConfig(entityManagerFactory)
//            );
//        }
//    }
//
//

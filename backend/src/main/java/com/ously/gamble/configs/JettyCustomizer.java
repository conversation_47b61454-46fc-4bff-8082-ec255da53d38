package com.ously.gamble.configs;


import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.server.handler.gzip.GzipHandler;
import org.springframework.boot.web.embedded.jetty.JettyServerCustomizer;
import org.springframework.stereotype.Component;

/**
 * Exclude certain paths which deliver already compressed content
 */
@Component
public class JettyCustomizer implements JettyServerCustomizer {
    @Override
    public void customize(Server server) {
        var bean = server.getBean(GzipHandler.class);
        if (bean != null) {
            bean.addExcludedPaths("/api/app/games/groups");
            bean.addExcludedPaths("/api/app/games/all");
            bean.addExcludedPaths("/api/app/games/allp");
            bean.addExcludedPaths("/api/app/public/games/allp");
            bean.addExcludedPaths("/api/app/games/topslots");
            bean.addExcludedPaths("/api/app/public/games/all");
            bean.addExcludedPaths("/api/app/public/games/allp");
            bean.addExcludedPaths("/api/app/games/categories");
            bean.addExcludedPaths("/api/app/leaderboards");
            bean.addExcludedPaths("/api/app/stats");

        }
    }
}

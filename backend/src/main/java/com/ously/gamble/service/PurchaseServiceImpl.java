package com.ously.gamble.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.PurchaseManagementService;
import com.ously.gamble.api.PurchaseService;
import com.ously.gamble.api.StoreHandler;
import com.ously.gamble.api.assets.AssetService;
import com.ously.gamble.api.cache.LCacheFactory;
import com.ously.gamble.api.consumable.PurchaseBookedEvent;
import com.ously.gamble.api.consumable.UserConsumableService;
import com.ously.gamble.api.crm.CRMUserEvent;
import com.ously.gamble.api.crm.CRMUserUpdateRequest;
import com.ously.gamble.api.events.UserTagEvent;
import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.api.user.*;
import com.ously.gamble.api.util.LockProvider;
import com.ously.gamble.payload.TxPrice;
import com.ously.gamble.payload.TxPriceType;
import com.ously.gamble.payload.purchase.*;
import com.ously.gamble.persistence.model.PaymentPlatform;
import com.ously.gamble.persistence.model.PaymentStatus;
import com.ously.gamble.persistence.model.Purchase;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.model.messages.UserMessageContent;
import com.ously.gamble.persistence.model.messages.UserMessageType;
import com.ously.gamble.persistence.model.social.Consumable;
import com.ously.gamble.persistence.model.social.ConsumableType;
import com.ously.gamble.persistence.model.social.UserConsumableStatus;
import com.ously.gamble.persistence.model.user.UserTransactionType;
import com.ously.gamble.persistence.repository.ConsumableRepository;
import com.ously.gamble.persistence.repository.WalletRepository;
import com.ously.gamble.services.common.BaseOuslyService;
import com.ously.gamble.util.AppSettingHelper;
import com.ously.gamble.util.SpelExpressionParserUtil;
import com.ously.gamble.util.ZoneOffsetHelper;
import jakarta.persistence.OptimisticLockException;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.StaleStateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;


/**
 * First rough draft. Google & Apple verification should gather more information about the purchase.
 */
@SuppressWarnings("rawtypes")
@Service
@ConditionalOnProperty(prefix = "features", name = "jurisdiction", havingValue = "SOC",
        matchIfMissing = true)
public class PurchaseServiceImpl extends BaseOuslyService implements PurchaseService {
    final Logger log = LoggerFactory.getLogger(getClass());

    private final BigDecimal mult100 = BigDecimal.valueOf(100L);

    private final StoreHandler appleStoreHandler;
    private final StoreHandler playStoreHandler;
    private final StoreHandler adyenStoreHandler;
    private final StoreHandler huaweiStoreHandler;

    private final ConsumableRepository conRepo;
    private final UserManagementService uMgmt;
    private final LockProvider lkProv;
    private final LoadingCache<String, List<ProductInfo>> allConsumables;
    private final ObjectMapper om;
    private final FeatureConfig fConfig;
    private final SpelExpressionParserUtil spUtil;
    private final UserTransactionService uts;
    private final UserMessageGateway umGW;
    private final PurchaseManagementService purchMgmtService;
    private final AssetService aServ;
    private final WalletRepository wRepo;
    private final UserConsumableService ucService;

    @SuppressWarnings("unchecked")
    public PurchaseServiceImpl(ObjectMapper om,
                               SpelExpressionParserUtil spExpUtil, LCacheFactory lcf,
                               LockProvider lkProv, UserManagementService umgtSrv,
                               FeatureConfig fConfig, ConsumableRepository consRepo,
                               StoreHandler appleStoreHandler, StoreHandler huaweiStoreHandler,
                               StoreHandler playStoreHandler, UserTransactionService utSrv,
                               PurchaseManagementService pMgmtSrv, AssetService assSrv,
                               WalletRepository wRepo,
                               StoreHandler adyenStoreHandler,
                               UserMessageGateway umGW, UserConsumableService ucService
    ) {
        this.om = om;
        this.spUtil = spExpUtil;
        this.lkProv = lkProv;
        this.uMgmt = umgtSrv;
        this.fConfig = fConfig;
        this.conRepo = consRepo;
        this.appleStoreHandler = appleStoreHandler;
        this.adyenStoreHandler = adyenStoreHandler;
        this.playStoreHandler = playStoreHandler;
        this.huaweiStoreHandler = huaweiStoreHandler;
        this.uts = utSrv;
        this.wRepo = wRepo;
        this.aServ = assSrv;
        this.purchMgmtService = pMgmtSrv;
        this.umGW = umGW;
        this.ucService = ucService;

        allConsumables = lcf.registerCacheLoader("products", 48L, 12, 30 * 60, key -> {
            try {
                return conRepo.findAll().stream().filter(a -> isActive(a, (String) key)).map(ProductInfo::new).toList();
            } catch (Exception e) {
                log.error("Exception while trying to load products:{}", key, e);
            }
            return null;
        });


    }


    @Override
    @Transactional
    @Retryable(retryFor = {OptimisticLockException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 250, maxDelay = 3000, multiplier = 1.5))
    public PurchaseVerificationResponse verify(PurchaseVerificationRequest req,
                                               long userid) throws Exception {
        lkProv.getLockedLockTx("user", userid);

        log.info("Verification incoming for user {}:{}", userid, req);
        String campaign = "unknown";
        try {
            var opr = new OuslyPurchaseReceipt();
            opr.setUserId(userid);
            opr.setValidated(false);
            opr.setProductId(req.getProductId());
            opr.setPurchaseToken(req.getReceipt());
            opr.setShopTransactionId(req.getPurchaseId());
            long itemId;
            // 1. check that productID refs. actual product
            long consumableId = Long.parseLong(req.getId());
            ProductInfo productInfo;
            if (consumableId >= 0) {
                var optConsumable = conRepo.findById(Long.valueOf(req.getId()));
                if (optConsumable.isEmpty()) {
                    possibleFraud("given consumable does not exist. Id->" + req.getId(), userid);
                    throw new IllegalArgumentException("given consumable does not exist");
                }
                productInfo = new ProductInfo(optConsumable.get());
                itemId = productInfo.getId();
            } else {
                // Custom Offer
                Optional<ProductInfo> optUConsumable = ucService.getUserConsumable(userid, consumableId);
                if (optUConsumable.isEmpty()) {
                    possibleFraud("given consumable does not exist. Id->" + req.getId(), userid);
                    throw new IllegalArgumentException("given consumable does not exist");
                }
                productInfo = optUConsumable.get();
                campaign = productInfo.getCampaign();
                itemId = productInfo.getRefId();
            }


            if (!req.getProductId().equals(productInfo.getProductId())) {
                possibleFraud("given consumable has different productId '" + req.getProductId() + "'. Id->" + req.getId(), userid);
                throw new IllegalArgumentException("given consumable does not exist");
            }

            if ("TUTORIAL".equals(productInfo.getProductId())) {
                return purchaseTutorialItem(productInfo, userid);
            }

            // Check that this is the first verification.
            var firstVerification = isFirstVerification(req.getPurchaseId(), userid);
            if (!firstVerification) {
                log.info("Not the first verification for {}/{}", req.getPurchaseId(), userid);
                // just a security manner to avoid bombardment with verification requests. I will also throttle them a level higher
                sleepAWhile(250L);
            }

            // sofar ok, now check the receipt
            verifyPurchase(opr, req.getPlatform());
            log.info("Got OPR: {}", opr);
            // 3. apply purchase to wallet + token storage
            if (opr.getValidated() && firstVerification) {
                log.info("Adding purchase to db");
                var purchase = new Purchase();
                purchase.setAppliedItems(productInfo.getItemDefinition());
                purchase.setTransactionId(req.getPurchaseId());
                if ("test".equalsIgnoreCase(req.getPlatform())) {
                    // set a random transaction id
                    purchase.setTransactionId("TEST-" + UUID.randomUUID());
                }
                purchase.setCost(productInfo.getCost());
                purchase.setConsumableId(productInfo.getId());
                purchase.setPlatform(PaymentPlatform.valueOf(req.getPlatform()));
                purchase.setReceipt(req.getReceipt());
                purchase.setUserId(userid);
                purchase.setPlatform(createPaymentPlatformFrom(req.getPlatform()));
                if ("IOS".equalsIgnoreCase(req.getPlatform())) {
                    purchase.setPaymentMethod("APPSTORE");
                } else if ("android".equalsIgnoreCase(req.getPlatform())) {
                    purchase.setPaymentMethod("PLAYSTORE");
                } else if ("huawei".equalsIgnoreCase(req.getPlatform())) {
                    purchase.setPaymentMethod("HUAWEI");
                } else {
                    log.warn("Unknown Store platform: '{}' using UNKNOWN", req.getPlatform());
                    purchase.setPaymentMethod("UNKNOWN");
                }
                if (opr.isSandbox()) {
                    purchase.setStatus(PaymentStatus.SANDBOX);
                } else {
                    purchase.setStatus(PaymentStatus.SUCCESS);
                }
                purchase.setOrderRef(UUID.randomUUID().toString());
                purchase.setPsp(opr.getShopTransactionId());
                // does user have an active Affiliate?
                //				Optional<User> byId = uMgmt.findByIdUncached(userid);
                //				byId.ifPresent(user -> purchase.setAffiliate_id(user.getAffiliateId()));

                // event
                var txResponse = activatePurchase(purchase, productInfo, userid, opr);
                if (!txResponse.isIdempotent()) {
                    if (!opr.isSandbox()) {
                        publishEvent(new CRMUserEvent(purchase.getUserId(), "purchase", "price_cents", purchase.getCost().multiply(mult100).longValue(), "item_id", purchase.getConsumableId(), "ref_id", itemId, "platform", purchase.getPlatform(), "campaign", campaign));
                        publishEvent(new UserTagEvent(purchase.getUserId(), EUserTag.DEPOSITOR.name(), true));
                    }

                    purchase.setTransactionId(Long.toString(txResponse.getTxId()));
                    purchMgmtService.saveAndFlush(purchase);
                    log.info("Purchase {} verified and booked", req);
                    publishEvent(new PurchaseBookedEvent(purchase));
                }
            } else {
                log.info("Purchase was neither written to db and will not be booked since we already have this purchase in the db");
            }

            // return OK message
            return new PurchaseVerificationResponse(req, opr);
        } catch (Exception e) {
            log.error("Purchase Verification Error for purchaseId={}", req.getPurchaseId(), e);
            return new PurchaseVerificationResponse(req);
        }
    }

    private static PaymentPlatform createPaymentPlatformFrom(String platform) {
        if (StringUtils.isEmpty(platform)) {
            return PaymentPlatform.UNKNOWN;
        }
        return switch (platform.toUpperCase()) {
            case "IOS" -> PaymentPlatform.IOS;
            case "ANDROID" -> PaymentPlatform.ANDROID;
            case "HUAWEI" -> PaymentPlatform.HUAWEI;
            default -> PaymentPlatform.UNKNOWN;
        };
    }

    private PurchaseVerificationResponse purchaseTutorialItem(ProductInfo consumable, Long userid) {
        var alreadyPurchased = false;
        var ui = uMgmt.findInfoByIdUncached(userid);
        if (ui.isPresent()) {
            alreadyPurchased = AppSettingHelper.isTutorialShopPurchased(ui.get(), om);
        }
        if (!alreadyPurchased) {
            try {
                activateTutorialPurchase(consumable, userid);
                AppSettingHelper.markUserInfoShopItemPurchased(ui.get(), om);
                uMgmt.saveUserInfo(ui.get());
            } catch (OuslyTransactionException e) {
                log.warn("Error perf. tutorial purchase", e);
            }
        }
        var resp = new PurchaseVerificationResponse();
        resp.setVerified(true);
        var randomUUID = UUID.randomUUID().toString();
        if (alreadyPurchased) {
            resp.setPurchaseId("OK:" + randomUUID + ":IGNORED");
        } else {
            resp.setPurchaseId("OK:" + randomUUID);
        }
        resp.setProductId("TUTORIAL");
        resp.setPlatform("TUTORIAL");
        resp.setReceipt(randomUUID);
        resp.setId(randomUUID);
        return resp;
    }


    @Transactional(propagation = Propagation.MANDATORY)
    public UserTxResponse activatePurchase(Purchase purchase, ProductInfo consumable, Long userId,
                                           OuslyPurchaseReceipt opr) throws OuslyTransactionException {
        // now create list of prices, tx them and mark achievement as claimed!
        var priceDefs = consumable.getItemDefinition();
        if (priceDefs == null) {
            possibleFraud("User " + userId + " trying to purchase a product without 'rewards'", userId);
            throw new OuslyTransactionException("User " + userId + " trying to purchase product " + consumable.getId() + " which has no item defs");
        }

        // coin mult.
        long cMult = Math.max(100, consumable.getCoinMultiplier() + getShopCoinMultiplierForLevel(wRepo.getUserLevel(userId).intValue()) - 100);

        var req = new UserTxRequest();
        var prices = priceDefs.split(",");

        for (var pStr : prices) {
            var pr = new TxPrice(pStr);
            if (pr.getType() == TxPriceType.S) {
                pr.setAmount((int) ((((long) pr.getAmount()) * cMult) / 100));
            }
            req.addPrice(pr);
        }
        // Scale prices if consumable allows it (bonus mult is handled in txService)
        if (consumable.getBonusPossible()) {
            req.setPiggyPossible(true);
        }
        req.setType(UserTransactionType.PURCHASE);
        req.setUserId(userId);
        req.setDescription("PURCHASE:" + opr.getProductId() + ":('" + consumable.getItemDefinition() + "')" + ((opr.isSandbox()) ? "-TEST" : ""));
        req.setTxRef(purchase.getId() + ":" + purchase.getTransactionId());
        req.setStorePrices(true);
        log.debug("activating purchase of:{}", req);
        if (consumable.getId() < 0) {
            ucService.setUserConsumableStatus(userId, consumable.getId().intValue(), UserConsumableStatus.BOUGHT);
        }
        return uts.activatePrices(req);
    }

    @Transactional(propagation = Propagation.MANDATORY)
    public void activateTutorialPurchase(ProductInfo consumable,
                                         Long userId) throws OuslyTransactionException {

        var priceDefs = consumable.getItemDefinition();
        if (priceDefs == null) {
            possibleFraud("User " + userId + " trying to purchase a product without 'rewards'", userId);
        }

        var prices = priceDefs.split(",");
        var req = new UserTxRequest();

        for (var pStr : prices) {
            var pr = new TxPrice(pStr);
            req.addPrice(pr);
        }
        req.setType(UserTransactionType.PURCHASE);
        req.setUserId(userId);
        req.setDescription("TPURCHASE:('" + consumable.getItemDefinition() + "')");
        req.setTxRef("TPURCHASE:" + userId + ':' + UUID.randomUUID());
        req.setStorePrices(true);
        uts.activatePrices(req);
    }


    private boolean isFirstVerification(String purchaseId, Long uId) {
        var oldPurchase = purchMgmtService.findByTransactionIdAndUserId(purchaseId, uId);
        return oldPurchase.isEmpty();
    }

    // https://buy.itunes.apple.com/verifyReceipt
    // when ( 21007 ) then verify with
    // https://sandbox.itunes.apple.com/verifyReceipt
    // this is the simplest validation variant.

    private void verifyPurchase(OuslyPurchaseReceipt opr, String platform) {
        if (fConfig.getFakeValidation() && "test".equalsIgnoreCase(platform)) {
            log.warn("FakeValidation for productId='{}' ", opr.getProductId());
            opr.setValidated(true);
            opr.setShopTransactionId("TESTVALIDATION:" + UUID.randomUUID());
            return;
        }
        if ("ANDROID".equalsIgnoreCase(platform)) {
            playStoreHandler.verifyPurchase(opr);
            return;
        } else if ("HUAWEI".equalsIgnoreCase(platform)) {
            huaweiStoreHandler.verifyPurchase(opr);
            return;
        }
        appleStoreHandler.verifyPurchase(opr);
    }


    @Override
    @Transactional
    public List<ProductInfo> getAllActiveConsumables(long userId, GamePlatform gp, String tz) {
        List<ProductInfo> collect;
        try {
            collect = allConsumables.get("ALL" + ':' + tz).stream().map(ProductInfo::new).sorted().toList();
        } catch (Exception e) {
            log.warn("Error getting consumables from cache, falling back to repo:{}", e.getMessage());
            collect = conRepo.findAll().stream()
                    .filter(a -> isActive(a, tz))
                    .filter(a -> a.getItemCategory() != ConsumableType.CUSTOM_TEMPLATE)
                    .map(ProductInfo::new)
                    .toList();
        }
        // Filter by platform and userId
        collect = filterForPlatform(collect, gp, userId);

        var w = wRepo.getWalletById(userId);
        var saveUp = (w.getSaveup() == null) ? BigDecimal.ZERO : w.getSaveup();

        modifyIfPiggyBank(collect, saveUp, (fConfig.isLevelMultShopItems()) ? getShopCoinMultiplierForLevel(w.getLevel()) : 100);

        // Add custom offers
        collect.addAll(ucService.getUserConsumables(userId));

        // Set URLs for all product infos
        setAssetUrls(collect, aServ);

        return collect;
    }

    private int getShopCoinMultiplierForLevel(int level) {
        return purchMgmtService.getCoinMultiplierForLevel(level);
    }

    private static void modifyIfPiggyBank(List<ProductInfo> productInfos, BigDecimal saveUp, int levelCoinMultiplier) {
        for (var cProdInfo : productInfos) {
            if (cProdInfo.getBonusPossible() && saveUp.longValue() >= 100L) {
                cProdInfo.setBonusActive(true);
                cProdInfo.setRewardMultiplier(saveUp.add(BigDecimal.valueOf(100L)).intValue());
            } else {
                cProdInfo.setBonusActive(false);
                cProdInfo.setRewardMultiplier(100);
            }

            // check for level mult
            if (levelCoinMultiplier > 100 && (cProdInfo.getCategory() == ConsumableType.DEFAULT)) {
                cProdInfo.setCoinMultiplier(cProdInfo.getCoinMultiplier());
                cProdInfo.setBonusActive(false);
                cProdInfo.setRewardMultiplier(100);
            } else {
                cProdInfo.setCoinMultiplier(cProdInfo.getCoinMultiplier());
            }
        }
    }

    private static void setAssetUrls(List<ProductInfo> productInfos, AssetService aServ) {
        for (ProductInfo cProdInfo : productInfos) {
            if (cProdInfo.getAssetId() != null && cProdInfo.getAssetId() > -1L) {
                cProdInfo.setAssetUrl(aServ.createUrl(cProdInfo.getAssetId()));
            }
            if (cProdInfo.getBackgroundAssetId() != null && cProdInfo.getBackgroundAssetId() > -1L) {
                cProdInfo.setBackgroundAssetUrl(aServ.createUrl(cProdInfo.getBackgroundAssetId()));
            }
            if (cProdInfo.getLeftCornerAssetId() != null && cProdInfo.getLeftCornerAssetId() > -1L) {
                cProdInfo.setLeftCornerAssetUrl(aServ.createUrl(cProdInfo.getLeftCornerAssetId()));
            }
            if (cProdInfo.getRightBadgeAssetId() != null && cProdInfo.getRightBadgeAssetId() > -1L) {
                cProdInfo.setRightBadgeAssetUrl(aServ.createUrl(cProdInfo.getRightBadgeAssetId()));
            }
        }
    }

    private List<ProductInfo> filterForPlatform(List<ProductInfo> collect, GamePlatform gp,
                                                Long userId) {

        // Filter out any special-products
        var products = collect.stream().
                filter(a -> a.getCategory() != ConsumableType.SPECIALOFFER).
                filter(a -> a.getCategory() != ConsumableType.CUSTOM_TEMPLATE).
                filter(a -> isAvaibleForUser(a, userId)).
                collect(Collectors.toList());

        switch (gp) {
            case IOS, ANDROID -> {
                return products;
            }
            default -> {
            }
        }

        // WEB: if we have a special-offer type, then check if the "DEFAULT" type is in the list. If so, then replace with
        // SPECIALOFFER, set "oldPrice" on it, if not, just leave it there but sort it to front!
        // The consumables should have matching title!!
        var specialOffers = collect.stream().filter(a -> a.getCategory() == ConsumableType.SPECIALOFFER).toList();
        for (var special : specialOffers) {
            var hit = products.stream().filter(a -> a.getCategory() != ConsumableType.SPECIALOFFER && a.getTitle().equals(special.getTitle())).findFirst();
            if (hit.isPresent()) {
                var i = products.indexOf(hit.get());
                special.setOldCost(hit.get().getCost());
                products.remove(i);
                products.add(i, special);
            }
        }
        return products;
    }

    @Override
    @Transactional
    public ProductInfo getPiggyConsumable(long userId) {
        List<ProductInfo> productInfos;
        try {
            productInfos =
                    allConsumables.get("ALL").stream().filter(ProductInfo::getBonusPossible).toList();
        } catch (Exception e) {
            log.error("Error getting pgConsumable for user {}", userId);
            return ProductInfo.emptyProductInfo();
        }

        if (productInfos.isEmpty()) {
            return ProductInfo.emptyProductInfo();
        }

        var wallet = wRepo.getWalletById(userId);
        var saveUp = wallet.getSaveup();
        if (saveUp == null) {
            saveUp = BigDecimal.ZERO;
        }

        if (saveUp.longValue() < 100L) {
            return ProductInfo.emptyProductInfo();
        }

        for (var cProdInfo : productInfos) {
            cProdInfo.setBonusActive(true);
            cProdInfo.setRewardMultiplier(saveUp.add(BigDecimal.valueOf(100L)).intValue());

            if (cProdInfo.getAssetId() != null && cProdInfo.getAssetId() > -1L) {
                cProdInfo.setAssetUrl(aServ.createUrl(cProdInfo.getAssetId()));
            }
        }
        return productInfos.getFirst();
    }


    @Override
    @Transactional(readOnly = true)
    public Page<Purchase> getAllPurchases(long userId, int page, int size) {
        return purchMgmtService.findByUserIdOrderByCreatedAtDesc(userId, PageRequest.of(page, size));
    }

    @Override
    public List<ProductInfo> getAllActiveConsumablesTutorial(long userId, GamePlatform gp, String tz) {
        var all = conRepo.findAll();
        var collect = all.stream().filter(a -> isActive(a) || "TUTORIAL".equals(a.getProductId())).map(ProductInfo::new).toList();
        var saveUp = BigDecimal.ZERO;

        modifyIfPiggyBank(collect, saveUp, 0);
        setAssetUrls(collect, aServ);
        // now reorder to place TUTORIAL at first position
        List<ProductInfo> result = new ArrayList<>(15);
        for (var pi : collect) {
            if ("TUTORIAL".equals(pi.getProductId())) {
                result.addFirst(pi);
            } else {
                result.add(pi);
            }
        }

        return result;
    }

    @Override
    @Transactional
    public RefundOrCancelResponse refund(RefundOrCancelRequest req) {

        var optionalPurchase = purchMgmtService.findById(req.getPurchaseId());
        if (optionalPurchase.isEmpty()) {
            var resp = new RefundOrCancelResponse();
            resp.setPurchaseId(req.getPurchaseId());
            resp.setRefundRequested(false);
            resp.setResponse("Cannot find purchase with id: " + req.getPurchaseId());
            return resp;
        }
        var p = optionalPurchase.get();

        if (p.getStatus() == PaymentStatus.PENDING || p.getStatus() == PaymentStatus.SUCCESS || p.getStatus() == PaymentStatus.AUTHORIZED) {

            switch (p.getPlatform()) {
                case ANDROID -> {
                    return playStoreHandler.refund(req, p);
                }
                case IOS -> {
                    return appleStoreHandler.refund(req, p);
                }
                case WEB -> {
                    return adyenStoreHandler.refund(req, p);
                }
                default -> {
                    var resp = new RefundOrCancelResponse();
                    resp.setPurchaseId(req.getPurchaseId());
                    resp.setRefundRequested(false);
                    resp.setResponse("Cannot refund purchase for Platform: " + p.getPlatform());
                    return resp;
                }
            }
        }
        var resp = new RefundOrCancelResponse();
        resp.setPurchaseId(req.getPurchaseId());
        resp.setRefundRequested(false);
        resp.setResponse("Cannot refund purchase with status : " + p.getStatus());
        return resp;
    }

    boolean isActive(Consumable a) {
        return isActive(a, null);
    }

    /**
     * Check if consumable is active (incl. Day - expression)
     *
     * @param a        the consumable
     * @param timezone opt-timezone
     * @return true if consumable is active
     */
    boolean isActive(Consumable a, String timezone) {

        if (!a.isActive()) {
            return false;
        }

        var ldt = LocalDateTime.now();
        var vF = a.getValidFrom();
        var vT = a.getValidTo();

        if (StringUtils.isNotEmpty(a.getFilterExpression()) && !a.getFilterExpression().startsWith("#")) {
            if (timezone != null && !"ALL".equals(timezone)) {
                var tzComp = timezone.split(":");
                if (tzComp.length == 2) {
                    var zdtClient = Instant.now().atZone(ZoneOffsetHelper.getZoneOffset(tzComp[1]));
                    var aBoolean = spUtil.matchSpelFilter(zdtClient, a.getFilterExpression());
                    if (!aBoolean) {
                        return false;
                    }
                }
            }
        }
        return (vF == null || !vF.isAfter(ldt)) && (vT == null || !vT.isBefore(ldt));
    }

    boolean isAvaibleForUser(ProductInfo a, Long userId) {

        if (StringUtils.isNotEmpty(a.expression())) {
            if (a.expression().startsWith("#") && userId != null) {
                // Check restrictions of count/period
                try {
                    var split = a.expression().substring(1).split("#");
                    if (split.length == 3) {
                        var count = Integer.parseInt(split[0]);
                        var time = Long.parseLong(split[1]);
                        var unit = ChronoUnit.valueOf(split[2]);
                        var barrier = LocalDateTime.now().minus(time, unit);
                        var purchCount =
                                conRepo.getPurchaseCountOfConsumableForUserIdAndBarrier(a.getId()
                                        , userId, barrier.toInstant(ZoneOffset.UTC));
                        if (purchCount >= count) {
                            return false;
                        }
                    }
                } catch (Exception e) {
                    log.error("Error checking expression:{}", a.expression(), e);
                }
            }
        }
        return true;
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public Object sendPurchaseBooked(PurchaseBookedEvent evnt) {
        log.info("Adding purchase booked message for {}", evnt);
        var uid = evnt.userid();
        var type = UserMessageType.CUSTOM;
        var title = UserMessageType.PURCHASE_SUCCESS.title();
        var body = UserMessageType.PURCHASE_SUCCESS.body();

        var variables = new HashMap<String, String>();
        variables.put("ref", evnt.reference());
        variables.put("status", evnt.status().name());
        variables.put("cost", String.format("%.2f", evnt.cost()));

        DateTimeFormatter dtFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss zzz")
                .withZone(ZoneId.systemDefault());
        variables.put("date", dtFormatter.format(Instant.now()));

        var qualifier = evnt.reference();
        var umc = new UserMessageContent(type, title, body, "P60D",
                variables, null);

        var umreq = new UserMessageRequest();
        umreq.setUserId(evnt.userid());
        umreq.setPopup(false);
        umreq.setQualifier(qualifier);
        umreq.setContent(umc);
        umGW.sendUserMessage(umreq);

        // Now emit updateUserCRM event

        return new CRMUserUpdateRequest(evnt.userid());

    }

}

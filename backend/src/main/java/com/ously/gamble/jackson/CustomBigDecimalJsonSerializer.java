package com.ously.gamble.jackson;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;
import java.math.BigDecimal;

@JsonComponent
public class CustomBigDecimalJsonSerializer extends JsonSerializer<BigDecimal> {

	@Override
	public void serialize(BigDecimal dbl, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
		if (dbl == null) {
			jsonGenerator.writeNull();
		} else if (dbl.signum() == 0) {
			jsonGenerator.writeRawValue("0.0");
		} else {
			jsonGenerator.writeNumber(dbl.doubleValue());
		}
	}

}
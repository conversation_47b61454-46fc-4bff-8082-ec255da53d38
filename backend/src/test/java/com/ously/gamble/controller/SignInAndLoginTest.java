//package com.ously.gamble.controllers;
//
//import com.ously.gamble.payload.PagedResponse;
//import com.ously.gamble.persistence.dto.PlayableGame;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.http.ResponseEntity;
//import org.springframework.test.context.junit.jupiter.SpringExtension;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//
///**
// * A Simple test used to derive a general test (or common test superclass)
// */
//@ExtendWith(SpringExtension.class)
//public class SignInAndLoginTest extends AbstractRestTest {
//
//    @Configuration
//    static class TestConfig {
//    }
//
//    //@Test
//    public void testSigninLoginAndGameRetrieval() throws Exception{
//
//        // Create new User (signup) - now an activation is not needed.
//        signupAndSignin("testuser");
//
//        // now we should be able to access protected endpoint
//
//        ResponseEntity<PagedResponse>
//        games = this.restTemplate.getForEntity("http://localhost:" + port + "/api/games",  PagedResponse.class,"page",0,"size",10);
//        assertEquals(200, games.getStatusCodeValue());
//        assertEquals(6,games.getBody().getTotalElements());
//    }
//
//    /**
//     * Test to see how to use signupAndsignin multiple times
//     * @throws Exception
//     */
//    //@Test
//    public void testSigninAndDedicatedGameRetrieval() throws Exception{
//
//        // do we need to create user everytime?
//        signupAndSignin("testuser");
//
//        // now we should be able to access protected endpoint
//        ResponseEntity<PlayableGame>
//                games = this.restTemplate.getForEntity("http://localhost:" + port + "/api/games/1",  PlayableGame.class,"page",0,"size",10);
//        assertEquals(200, games.getStatusCodeValue());
//        assertEquals( "/api/g/i/1.jpg",games.getBody().getImageUrl());
//
//        // now fetch explicitely
//
//    }
//
//}

//package com.ously.gamble.controllers;
//
//import com.ously.gamble.BaseTestContext;
//import com.ously.gamble.api.games.GameService;
//import com.ously.gamble.api.games.CasinoGame;
//import com.ously.gamble.payload.PagedResponse;
//import com.ously.gamble.persistence.dto.PlayableGame;
//import org.junit.Ignore;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.ResponseEntity;
//import org.springframework.test.context.junit.jupiter.SpringExtension;
//import org.springframework.transaction.annotation.Transactional;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//
///**
// * A Simple test used to derive a general test (or common test superclass)
// */
//@ExtendWith(SpringExtension.class)
//@Transactional
//public class GameControllerTest extends BaseTestContext {
//
//    @Autowired
//    GameService gSrv;
//
//    @Test
//    public void testBasicGameAdminAPI() throws Exception {
//        // TODO: unstable test (gradle error, second build works!!)
//        // ensure admin.
//        signupAndSignin("admin");
//
//        // use admin mechanism to load,add,modify,delete games
//        long oldcount = gSrv.getAllCasinoGamesForVendor("", 0, 10).getTotalElements();
//        // Add a new Game
//        CasinoGame game = new CasinoGame();
//        game.setActive(true);
//        game.setDesktop(true);
//        game.setMobile(false);
//        game.setName("DaSlot");
//        game.setGameId("XXX_DASLOT");
//        game.setTitle("DASLOT dem Best");
//        game.setProviderName("EDICT");
//
//        CasinoGame g = gSrv.newCasinoGame(game);
//
//        assertEquals(oldcount + 1, gSrv.getAllPlayableGames("", 0, 10).getTotalElements());
//
//        // Test update
//        g.setTitle("NEWNAME");
//        gSrv.updateCasinoGame(g, g.getId());
//
//        // reload
//        g = gSrv.getCasinoGame(g.getId());
//        assertEquals("NEWNAME", g.getTitle());
//
//        // Test Deletion
//        gSrv.deleteCasinoGame(g.getId());
//        assertEquals(oldcount, gSrv.getAllPlayableGames("", 0, 10).getTotalElements());
//    }
//
//    @Test
//    @Ignore
//    public void testSigninLoginAndGameRetrieval() throws Exception {
//
//        // Create new User (signup) - now an activation is not needed.
//        signupAndSignin("testuser");
//
//        // now we should be able to access protected endpoint
//
//        ResponseEntity<PagedResponse>
//                games = this.restTemplate.getForEntity("http://localhost:" + port + "/api/games", PagedResponse.class, "page", 0, "size", 10);
//        assertEquals(200, games.getStatusCodeValue());
//        assertEquals(9, games.getBody().getTotalElements());
//    }
//
//    /**
//     * Test to see how to use signupAndsignin multiple times
//     *
//     * @throws Exception
//     */
//    @Test
//    public void testSigninAndDedicatedGameRetrieval() throws Exception {
//
//        // do we need to create user everytime?
//        signupAndSignin("testuser");
//
//        // now we should be able to access protected endpoint
//        ResponseEntity<PlayableGame>
//                games = this.restTemplate.getForEntity("http://localhost:" + port + "/api/games/1", PlayableGame.class, "page", 0, "size", 10);
//        assertEquals(200, games.getStatusCodeValue());
//        assertEquals("/api/g/i/1.jpg", games.getBody().getImageUrl());
//
//    }
//
//    @Autowired
//    CasinoUserController cuc;
//}

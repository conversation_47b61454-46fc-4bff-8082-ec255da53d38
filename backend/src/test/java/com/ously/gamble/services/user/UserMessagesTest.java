package com.ously.gamble.services.user;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.achievements.AchievementService;
import com.ously.gamble.api.localisation.LanguageCode;
import com.ously.gamble.api.localisation.LocalisationService;
import com.ously.gamble.api.localisation.LocalisationType;
import com.ously.gamble.api.localisation.Localised;
import com.ously.gamble.api.user.UserMessageService;
import com.ously.gamble.persistence.model.messages.UserMessageContent;
import com.ously.gamble.persistence.model.messages.UserMessageType;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(SpringExtension.class)
@TestMethodOrder(OrderAnnotation.class)
class UserMessagesTest extends TestContext {

    @Autowired
    UserMessageService uSrv;

    @Autowired
    LocalisationService locSrv;

    @Autowired
    AchievementService aSrv;

    @Test
    @Transactional
    @Order(3)
    void testUserMessages1() {

        var usrMsgStatus = uSrv.getMessageStatusForUser(2L);
        assertEquals(0, usrMsgStatus.getPu());
        assertEquals(0, usrMsgStatus.getUr());

        // Create a msg
        var content = new UserMessageContent(UserMessageType.CUSTOM, "TITLE1",
                "CONTENT1", null, Collections.singletonMap("VAR1", "VAL1"), Collections.emptyMap());
        var created = uSrv.createNewUserMessage(2L, "MSGQUAL1", content, true);
        assertTrue(created);
        usrMsgStatus = uSrv.getMessageStatusForUser(2L);
        assertEquals(1, usrMsgStatus.getPu());
        assertEquals(0, usrMsgStatus.getUr());

        var pgl = PageRequest.of(0, 10, Sort.by(Direction.DESC, "createdAt"));
        var pagedMsgs = uSrv.getMessages(2L, "DE", pgl);
        assertEquals(1, pagedMsgs.getTotalElements());
        assertEquals(1, pagedMsgs.getTotalPages());
        var userMessageDto = pagedMsgs.getContent().getFirst();
        assertEquals("VAL1", userMessageDto.content().variables().get("VAR1"));

        // Get Popup
        var popup = uSrv.getOldestPopupMessage(2L, "DE");
        assertTrue(popup.isPresent());
        assertFalse(popup.get().read());

        // Mark Read
        var markedRead = uSrv.markMessageRead(popup.get().secret());
        assertTrue(markedRead);
        pagedMsgs = uSrv.getMessages(2L, "DE", pgl);
        assertEquals(1, pagedMsgs.getTotalElements());
        assertEquals(1, pagedMsgs.getTotalPages());
        userMessageDto = pagedMsgs.getContent().getFirst();

        // Delete the msg
        var deleted = uSrv.deleteMessage(userMessageDto.secret());
        assertTrue(deleted);
        usrMsgStatus = uSrv.getMessageStatusForUser(2L);
        assertEquals(0, usrMsgStatus.getPu());
        assertEquals(0, usrMsgStatus.getUr());
    }


    @Test
    @Transactional
    @Order(2)
    void testUserMessagesLocalisation() {
        createLiterals();

        // Create msg with new titles/body
        var content = new UserMessageContent(UserMessageType.CUSTOM, "UMTITLE",
                "UMBODY", null, Collections.singletonMap("username", "Undertaker"),
                Collections.emptyMap());
        var created = uSrv.createNewUserMessage(2L, "MSGQUAL2", content, false);
        assertTrue(created);
        var usrMsgStatus = uSrv.getMessageStatusForUser(2L);
        assertEquals(0, usrMsgStatus.getPu());
        assertEquals(1, usrMsgStatus.getUr());

        // Get msg
        var messages = uSrv.getMessages(2L, "DE", PageRequest.of(0, 10, Sort.by(Direction.DESC
                , "createdAt")));
        assertEquals(1, messages.getTotalElements());
        var userMessageDto = messages.getContent().getFirst();

        assertFalse(userMessageDto.read());
        assertFalse(userMessageDto.popup());
        assertEquals("Hallo Undertaker", userMessageDto.title());

        messages = uSrv.getMessages(2L, "EN", PageRequest.of(0, 10, Sort.by(Direction.DESC
                , "createdAt")));
        assertEquals(1, messages.getTotalElements());
        userMessageDto = messages.getContent().getFirst();

        assertFalse(userMessageDto.read());
        assertFalse(userMessageDto.popup());
        assertEquals("Hi Undertaker!", userMessageDto.title());

        uSrv.deleteMessage(userMessageDto.secret());

        // Now create popup and check translation
        content = new UserMessageContent(UserMessageType.CUSTOM, "UMTITLE",
                "UMBODY", null, Collections.singletonMap("username", "Undertaker"),
                Collections.emptyMap());
        assertTrue(uSrv.createNewUserMessage(2L, "MSGQUAL3", content, true));
        usrMsgStatus = uSrv.getMessageStatusForUser(2L);
        assertEquals(1, usrMsgStatus.getPu());
        assertEquals(0, usrMsgStatus.getUr());

        var enPopup = uSrv.getOldestPopupMessage(2L, "EN");
        userMessageDto = enPopup.orElse(null);
        assertNotNull(userMessageDto);
        assertEquals("Hi Undertaker!", userMessageDto.title());
        assertTrue(userMessageDto.popup());

        var dePopup = uSrv.getOldestPopupMessage(2L, "DE");
        userMessageDto = dePopup.orElse(null);
        assertNotNull(userMessageDto);
        assertEquals("Hallo Undertaker", userMessageDto.title());
        assertTrue(userMessageDto.popup());


        // Check auto-literal settings

        content = new UserMessageContent(UserMessageType.CUSTOM, null,
                null, null, Collections.singletonMap("username", "Undertaker"),
                Collections.emptyMap());
        assertTrue(uSrv.createNewUserMessage(2L, "MSGQUAL11", content, true));
        usrMsgStatus = uSrv.getMessageStatusForUser(2L);

        var messageByQualifier = uSrv.getMessageByQualifier(2L, "DE", "MSGQUAL11");
        assertTrue(messageByQualifier.isPresent());
        userMessageDto = messageByQualifier.get();

        assertEquals("MSG_CUSTOM_TITLE", userMessageDto.title());
        assertEquals("MSG_CUSTOM_BODY", userMessageDto.body());

        assertEquals(2, usrMsgStatus.getPu());
        assertEquals(0, usrMsgStatus.getUr());

    }

//
//    @Test
//    @Transactional
//    @Order(1)
//    void testUserMessagesRewards() {
//
//        var usrMsgStatus = uSrv.getMessageStatusForUser(2L);
//        assertEquals(0, usrMsgStatus.getPu());
//        assertEquals(0, usrMsgStatus.getUr());
//
//        // Create a msg
//        var content = new UserMessageContent(UserMessageType.REWARD, UserMessageType.REWARD.title(),
//                UserMessageType.REWARD.body(), null, Collections.singletonMap("rewards", "S#111"),
//                Collections.emptyMap());
//        var created = uSrv.createNewUserMessage(2L, "REWQUAL1", content, true);
//        assertTrue(created);
//        usrMsgStatus = uSrv.getMessageStatusForUser(2L);
//        assertEquals(1, usrMsgStatus.getPu());
//        assertEquals(0, usrMsgStatus.getUr());
//
//        var pgl = PageRequest.of(0, 10, Sort.by(Direction.DESC, "createdAt"));
//        var pagedMsgs = uSrv.getMessages(2L, "DE", pgl);
//        assertEquals(1, pagedMsgs.getTotalElements());
//        assertEquals(1, pagedMsgs.getTotalPages());
//        var userMessageDto = pagedMsgs.getContent().get(0);
//        assertEquals("S#111", userMessageDto.content().variables().get("rewards"));
//
//
//        var dePopup = uSrv.getOldestPopupMessage(2L, "DE");
//
//        // Execute CLAIM
//        assertTrue(dePopup.isPresent());
//
//        dePopup = uSrv.getMessageByQualifier(2L, "DE", "REWQUAL1");
//
//        var result = uSrv.doAction(dePopup.orElseThrow().secret(), "CLAIM");
//        assertTrue(result.success());
//
//        // Check for achievement
//
//        var achievement = aSrv.findAchievement(2L, AchievementType.MESSAGE, "MSGREW:" + userMessageDto.qualifier());
//        assertEquals(1, achievement.size());
//
//
//        // Check second action call
//        result = uSrv.doAction(dePopup.get().secret(), "CLAIM");
//        assertFalse(result.success());
//
//
//        // Delete the msg
//        var deleted = uSrv.deleteMessage(dePopup.get().secret());
//        assertTrue(deleted);
//        usrMsgStatus = uSrv.getMessageStatusForUser(2L);
//        assertEquals(0, usrMsgStatus.getPu());
//        assertEquals(0, usrMsgStatus.getUr());
//    }


    private void createLiterals() {
        var loc = new Localised();
        loc.setLangcode(LanguageCode.DE);
        loc.setLiteralId("UMTITLE");
        loc.setTemplate("Hallo ${username}");
        loc.setType(LocalisationType.POPUP);
        var localized = locSrv.saveNewLocalised(loc);
        assertNotNull(localized.getId());

        loc = new Localised();
        loc.setLangcode(LanguageCode.EN);
        loc.setLiteralId("UMTITLE");
        loc.setTemplate("Hi ${username}!");
        loc.setType(LocalisationType.POPUP);
        localized = locSrv.saveNewLocalised(loc);
        assertNotNull(localized.getId());

        loc = new Localised();
        loc.setLangcode(LanguageCode.DE);
        loc.setLiteralId("UMBODY");
        loc.setTemplate("Lieber ${username}, siehe bonus!");
        loc.setType(LocalisationType.POPUP);
        localized = locSrv.saveNewLocalised(loc);
        assertNotNull(localized.getId());

        loc = new Localised();
        loc.setLangcode(LanguageCode.EN);
        loc.setLiteralId("UMBODY");
        loc.setTemplate("Dear ${username}, see bonus!");
        loc.setType(LocalisationType.POPUP);
        localized = locSrv.saveNewLocalised(loc);
        assertNotNull(localized.getId());
    }

}

package com.ously.gamble.services.social;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.achievements.TokenType;
import com.ously.gamble.api.features.*;
import com.ously.gamble.payload.TxPriceType;
import com.ously.gamble.persistence.model.user.UserTransactionType;
import com.ously.gamble.persistence.repository.WalletRepository;
import com.ously.gamble.persistence.repository.user.UserTransactionRepository;
import com.ously.gamble.social.api.SocialInternalTokenService;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
class WheelServiceImplTest extends TestContext {

    @Autowired
    WheelService wService;

    @Autowired
    UserTransactionRepository txRepo;

    @Autowired
    SocialInternalTokenService tkIntService;

    @Autowired
    TokenService tkService;

    @Autowired
    WalletRepository wRepo;

    @Test
    @Transactional
    @Order(1)
    void testSimpleWorkflow() {

        var w = wRepo.getWalletById(1L);
        w.setSpintimer(Instant.now().plusSeconds(100));
        wRepo.saveAndFlush(w);

        // now add a spin token

        var ut = new UserToken();
        ut.setType(TxPriceType.WF);
        ut.setCount(1);

        List<UserToken> uts = new ArrayList<>();
        uts.add(ut);
        tkService.addTokens(1L, uts);

        var tokens = tkIntService.getTokens(1L).stream().filter(a -> a.getType() == TokenType.SPIN).toList();
        assertEquals(1, tokens.size());
        assertEquals(1, tokens.getFirst().getCount());
        // now play a wheel

        var req = new SpinWheelRequest();
        req.setCurrentTokenCount(1L);
        req.setFreeSpin(false);
        var spinWheelResponse = wService.doSpin(req, 1L);

        assertEquals(0, spinWheelResponse.getCurrentTokenCount());

        // now claim
        var ously = txRepo.findByUserIdAndType(1L, UserTransactionType.WHEELSPIN);

        var req2 = new WheelWinCollectRequest();
        req2.setSecret(spinWheelResponse.getSecret());
        var userStats = wService.collectResults(req2, 1L);
        if (userStats.getTokens() != null && userStats.getTokens().size() == 1) {
            assertEquals(0, userStats.getTokens().getFirst().getCount());
        }
        // now check transactions
        var ouslyA = txRepo.findByUserIdAndType(1L, UserTransactionType.WHEELSPIN);
        assertTrue(ously.size() < ouslyA.size());
    }

    @Test
    @Order(2)
    @Transactional
    void testUsingDiamondsForWheelspinWorkflow() {

        var w = wRepo.getWalletById(1L);
        w.setSpintimer(Instant.now().plusSeconds(100));
        wRepo.saveAndFlush(w);

        // now add a Diamond token
        var ut = new UserToken();
        ut.setType(TxPriceType.D);
        ut.setCount(10);


        List<UserToken> uts = new ArrayList<>();
        uts.add(ut);
        tkService.addTokens(1L, uts);

        var tokens = tkIntService.getTokens(1L).stream().filter(a -> a.getType() == TokenType.DIAMOND).toList();
        assertEquals(1, tokens.size());
        assertEquals(10, tokens.getFirst().getCount());
        // now play a wheel

        var req = new SpinWheelRequest();
        req.setCurrentTokenCount(0L);
        req.setFreeSpin(false);
        var spinWheelResponse = wService.doSpin(req, 1L);

        assertEquals(0, spinWheelResponse.getCurrentTokenCount());
        assertTrue(spinWheelResponse.isSpinned());
        assertEquals(2, spinWheelResponse.getUsedDiamonds());

        // now claim
        var ously = txRepo.findByUserIdAndType(1L, UserTransactionType.WHEELSPIN);
        var req2 = new WheelWinCollectRequest();
        req2.setSecret(spinWheelResponse.getSecret());
        var userStats = wService.collectResults(req2, 1L);
        if (userStats.getTokens() != null && userStats.getTokens().size() == 1) {
            assertEquals(8, userStats.getTokens().getFirst().getCount());
        }
        // now check transactions
        var ouslyA = txRepo.findByUserIdAndType(1L, UserTransactionType.WHEELSPIN);
        assertTrue(ously.size() < ouslyA.size());
        txRepo.flush();
        // new check diamond count
        tokens = tkIntService.getTokens(1L).stream().filter(a -> a.getType() == TokenType.DIAMOND).toList();
        assertEquals(1, tokens.size());
        assertEquals(8, tokens.getFirst().getCount());

//        // We should be able to spin 4 times again, then fail
//        for (int i = 0; i < 4; i++) {
//            spinWheelResponse = wService.doSpin(req, 1L);
//            assertTrue(spinWheelResponse.isSpinned());
//            assertEquals(2, spinWheelResponse.getUsedDiamonds());
//        }
//
//        spinWheelResponse = wService.doSpin(req, 1L);
//        assertFalse(spinWheelResponse.isSpinned());
//        assertEquals(0, spinWheelResponse.getUsedDiamonds());


    }


}
package com.ously.gamble.services.session;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.bridge.BridgeTools;
import com.ously.gamble.api.features.TokenService;
import com.ously.gamble.api.features.UserToken;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.user.UserTransactionService;
import com.ously.gamble.api.user.UserTxRequest;
import com.ously.gamble.events.ActivatePricesUserTxEvent;
import com.ously.gamble.payload.TxPrice;
import com.ously.gamble.payload.TxPriceType;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.Wallet;
import com.ously.gamble.persistence.model.user.UserTransactionType;
import com.ously.gamble.persistence.repository.WalletRepository;
import com.ously.gamble.services.common.TransactionServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.testcontainers.shaded.com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

@SuppressWarnings("unchecked")
@ExtendWith(SpringExtension.class)
class TransactionTests extends TestContext {


    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    TransactionServiceImpl txService;

    @Autowired
    UserTransactionService utxService;

    @Autowired
    BridgeTools bbt;

    @Autowired
    WalletRepository wRepo;

    @Autowired
    TokenService tkService;

    @Autowired
    TransactionTemplate txTemplate;

    @Autowired
    ApplicationEventPublisher eventPublisher;

    @Test
    void testCommit() {
        var execute = (Wallet) txTemplate.execute((TransactionCallback) status -> {
            var one = wRepo.findById(1L).get();
            var vers = one.getVersion();
            one.setGamesPlayed(44L);
            var save = wRepo.saveAndFlush(one);
            assertEquals(vers + 1, save.getVersion());
            return save;
        });
        // ...
        System.out.println("Something after the commit..." + execute.getVersion());
    }

    @Test
    void testCommit2() {
        var execute = (Wallet) txTemplate.execute((TransactionCallback) status -> {
            var one = wRepo.findById(1L).get();
            var vers = one.getVersion();
            one.setGamesPlayed(45L);
            var save = wRepo.save(one);
            assertEquals(vers, save.getVersion());
            return save;
        });


        // ...
        System.out.println("Something after the commit..." + execute.getVersion());
    }

    @Test
    @Transactional
    void testNewTxWithPerks() throws OuslyTransactionException, InterruptedException {

        long numTxBegin = getNumberOfTx(1L);
        var wAmount = getAmountFromWallet(1L);

        var req = createRequest(1000, TransactionType.WIN);
        txService.newTransaction(req);
        assertEquals(numTxBegin + 1, getNumberOfTx(1L));
        assertEquals(wAmount + 1000, getAmountFromWallet(1L));

        // activating double Spin perk
        var req2 = createPerkRequest(TxPriceType.MS, 200, 2);
        req2.setStorePrices(false);
        eventPublisher.publishEvent(new ActivatePricesUserTxEvent(req2));
        assertEquals(numTxBegin + 1, getNumberOfTx(1L));

        assertEquals(wAmount + 1000, getAmountFromWallet(1L));

        // now add another 1000 (WIN) which should be an effective 2000 win
        req = createRequest(1000, TransactionType.WIN);
        txService.addTxFromProvider(req);
        assertEquals(numTxBegin + 2, getNumberOfTx(1L));
        assertEquals(wAmount + 3000, getAmountFromWallet(1L));

        // but we should see the perk gone after a while!!
        Thread.sleep(3500);

        req = createRequest(1000, TransactionType.WIN);
        txService.addTxFromProvider(req);
        assertEquals(numTxBegin + 3, getNumberOfTx(1L));
        assertEquals(wAmount + 4000, getAmountFromWallet(1L));
    }

    @Test
    @Transactional
    void testCountDecreaseWithPerks() throws Exception {
        var userStats = bbt.getUserStats(1L, null);
        var req = createPerkRequest(TxPriceType.MS, 200, 180);
        // store, not activate
        req.setStorePrices(true);
        eventPublisher.publishEvent(new ActivatePricesUserTxEvent(req));

        // check the perk is activatable
        userStats = bbt.getUserStats(1L, null);

        var tkList = userStats.getTokens().stream().filter(a -> a.getBoost() == 200).toList();

        assertEquals(1, tkList.size());
        var userToken = tkList.getFirst();
        var stats = tkService.useToken(1L, "DE", userToken.getTokenId(), userToken.getSecret());
        var stats2 = bbt.getUserStats(1L, null);
        assertEquals(stats2.getTokens().size(), stats.getTokens().size());
        tkList = stats.getTokens().stream().filter(a -> a.getBoost() == 200).toList();
        assertEquals(0, tkList.size());
    }


    @Test
    @Transactional
    void testAddTokenNoPerk() throws Exception {
        var userStats = bbt.getUserStats(1L, null);

        var txPrice = new TxPrice();
        txPrice.setCount(5L);
        txPrice.setType(TxPriceType.TT);
        List<UserToken> userTokens = tkService.addTokensFromAchievement(1L, Lists.newArrayList(txPrice));
        assertEquals(1, userTokens.size());
        bbt.expireUserStats(1L);
        userStats = bbt.getUserStatsUnthrottled(1L, null);
        assertEquals(1, userStats.getTokens().size());
        assertEquals(TxPriceType.TT, userStats.getTokens().get(0).getType());
        assertEquals(5, userStats.getTokens().get(0).getCount());
    }

    private Object createTokenAddRequest(TxPriceType txPriceType, int count) {
        return null;
    }


    private Long getNumberOfTx(Long wId) {
        return txService.getTransactionsForWallet(1L, 0, 100).getTotalElements();
    }

    private long getAmountFromWallet(Long wId) {
        var one = wRepo.getWalletById(wId);
        return one.getBalance().longValue();
    }

    private UserTxRequest createPerkRequest(TxPriceType type, int multiplierPercent,
                                            long duration) {

        var req = new UserTxRequest();
        req.setType(UserTransactionType.ACHIEVEMENT);

        var price = new TxPrice();
        price.setAmount(multiplierPercent);
        price.setDurationInSeconds(duration);
        price.setType(type);
        price.setCount(1L);
        req.addPrice(price);
        req.setUserId(1L);
        req.setTxRef("ACH:" + UUID.randomUUID());
        req.setDescription("ACH:" + UUID.randomUUID());
        return req;
    }

    private TxRequest createRequest(int amount, TransactionType tType) {
        var req = new TxRequest();

        req.setUserId(1L);
        req.setGameId(1L);
        req.setExternal_tx_ref("EXTREF" + UUID.randomUUID());
        req.setExternalOrigId("EXTORIG" + UUID.randomUUID());
        req.setType(tType);
        switch (tType) {
            case BET -> req.setBet(BigDecimal.valueOf(amount));
            case WIN -> req.setWin(BigDecimal.valueOf(amount));
        }

        return req;
    }


}

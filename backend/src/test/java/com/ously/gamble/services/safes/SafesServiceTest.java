package com.ously.gamble.services.safes;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.achievements.TokenType;
import com.ously.gamble.api.features.UserToken;
import com.ously.gamble.payload.TxPriceType;
import com.ously.gamble.safes.api.PlayerSafeInfo;
import com.ously.gamble.safes.services.SafeServiceImpl;
import com.ously.gamble.social.service.tokens.TokenServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(SpringExtension.class)
class SafesServiceTest extends TestContext {

    @Autowired
    SafeServiceImpl safeService;

    @Autowired
    TokenServiceImpl tokenService;

    @Test
    @Transactional
    void testAddingSafesToLimit() {

        var wallet = getWallet(1L);
        var wVal = wallet.getBalance();

        PlayerSafeInfo playerSafeInfo1 = safeService.addSafe(1L, TokenType.TRESOR_EPIC);
        assertEquals(1, playerSafeInfo1.activeSafes().size());

        for (int i = 0; i < 1000; i++) {
            safeService.getPlayersSafeInfo(1L);
        }

        PlayerSafeInfo playerSafeInfo2 = safeService.addSafe(1L, TokenType.TRESOR_WOOD);
        assertEquals(2, playerSafeInfo2.activeSafes().size());

        PlayerSafeInfo playerSafeInfo3 = safeService.addSafe(1L, TokenType.TRESOR_SILVER);
        assertEquals(3, playerSafeInfo3.activeSafes().size());

        PlayerSafeInfo playerSafeInfo4 = safeService.addSafe(1L, TokenType.TRESOR_LEGENDARY);
        assertEquals(3, playerSafeInfo4.activeSafes().size());
        assertEquals(1, playerSafeInfo4.waiting());

        // Add a LOCKPICK TOKEN
        tokenService.addTokens(1L, List.of(new UserToken(TxPriceType.TLP, 0, 0, 1)));

        // now we should check unlocking via token
        Optional<String> s = safeService.claimSafe(1L, playerSafeInfo4.activeSafes().getFirst().id(), true);
        assertTrue(s.isPresent());
        PlayerSafeInfo playersSafeInfo5 = safeService.getPlayersSafeInfo(1L);
        assertEquals(3, playersSafeInfo5.activeSafes().size());
        assertEquals(0, playersSafeInfo5.waiting());

    }


}
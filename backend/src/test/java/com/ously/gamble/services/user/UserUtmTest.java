package com.ously.gamble.services.user;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.user.UpdateUtmDataRequest;
import com.ously.gamble.api.user.UserService;
import com.ously.gamble.persistence.repository.user.UserUtmRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(SpringExtension.class)
public class UserUtmTest extends TestContext {

    @Autowired
    UserService uSrv;

    @Autowired
    UserUtmRepository uuRepo;

    @Test
    @Transactional
    void testUtmDataPush() {

        var utmData = new UpdateUtmDataRequest();
        utmData.set("utm_campaign", "camp1");
        utmData.set("utm_medium", "med1");
        utmData.set("utm_source", "src1");

        uSrv.updateUserMeta(utmData, 2L);
        uuRepo.flush();
        var utmEntity = uuRepo.findById(2L);
        assertEquals("camp1", utmEntity.get().getUtmCampaign());
        assertEquals("med1", utmEntity.get().getUtmMedium());
        assertEquals("src1", utmEntity.get().getUtmSource());
        var utmData1 = utmEntity.get().getUtmData();
        assertEquals(3, utmData1.any().size());

        utmData.set("utm_campaign", "camp2");
        utmData.set("utm_term", "term1");
        utmData.set("utm_other", "other1");

        uSrv.updateUserMeta(utmData, 2L);
        uuRepo.flush();
        utmEntity = uuRepo.findById(2L);
        assertEquals("camp2", utmEntity.get().getUtmCampaign());
        assertEquals("med1", utmEntity.get().getUtmMedium());
        assertEquals("src1", utmEntity.get().getUtmSource());
        utmData1 = utmEntity.get().getUtmData();
        assertEquals(5, utmData1.any().size());


    }

}

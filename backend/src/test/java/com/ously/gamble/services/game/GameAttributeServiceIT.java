package com.ously.gamble.services.game;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.user.CasinoUserUnlockInfo;
import com.ously.gamble.api.user.UserGameAttributesManagementService;
import com.ously.gamble.api.user.UserGameAttributesService;
import com.ously.gamble.api.user.UserTransactionService;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.projections.UserTransactionDailyBreakdownPJ;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.event.RecordApplicationEvents;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(SpringExtension.class)
@RecordApplicationEvents
class GameAttributeServiceIT extends TestContext {

    @Autowired
    UserGameAttributesService ugaService;

    @Autowired
    UserGameAttributesManagementService ugaMService;

    @Autowired
    UserTransactionService utxService;

    @Test
    @Transactional
    void testUnlocks() throws Exception {

        ugaService.unlockGame(1L, 1);
        CasinoUserUnlockInfo unlocks = ugaService.getUnlocks(1L, GamePlatform.TEST);
        assertArrayEquals(new int[]{1}, unlocks.getUnlockedGames());
        ugaService.unlockGame(1L, 2);

        int i1 = utxService.replaceDailyBreakdownByType(LocalDate.now(ZoneOffset.UTC));

        Instant now = LocalDate.now(ZoneOffset.UTC).atStartOfDay().toInstant(ZoneOffset.UTC);
        Instant to = LocalDate.now(ZoneOffset.UTC).plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC);
        List<UserTransactionDailyBreakdownPJ> dailyBreakdownByType = utxService.getDailyBreakdownByType(now, to);

        boolean found = false;
        for (UserTransactionDailyBreakdownPJ i : dailyBreakdownByType) {
            if (i.getType().equals("UNLOCK_GAME")) {
                found = true;
            }
        }
        assertTrue(found);

        unlocks = ugaService.getUnlocks(1L, GamePlatform.TEST);
        assertArrayEquals(new int[]{1, 2}, unlocks.getUnlockedGames());

        ugaService.unlockVendor(1L, 124);
        unlocks = ugaService.getUnlocks(1L, GamePlatform.TEST);
        assertArrayEquals(new int[]{124}, unlocks.getUnlockedVendors());

    }

}
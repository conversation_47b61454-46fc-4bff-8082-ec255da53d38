package com.ously.gamble.services.social;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.achievements.AchievementService;
import com.ously.gamble.api.achievements.TokenType;
import com.ously.gamble.api.features.LevelInfo;
import com.ously.gamble.api.features.LevelManager;
import com.ously.gamble.api.features.TokenService;
import com.ously.gamble.api.features.UserToken;
import com.ously.gamble.api.user.UserStats;
import com.ously.gamble.payload.TxPriceType;
import com.ously.gamble.persistence.repository.WalletRepository;
import com.ously.gamble.persistence.repository.user.UserTransactionRepository;
import com.ously.gamble.social.api.SocialInternalTokenService;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
class LevelupTokenTest extends TestContext {

    @Autowired
    AchievementService achievementService;

    @Autowired
    UserTransactionRepository txRepo;

    @Autowired
    SocialInternalTokenService tkIntService;

    @Autowired
    TokenService tkService;

    @Autowired
    WalletRepository wRepo;

    @Autowired
    LevelManager lvMgr;


    @Test
    @Transactional
    @Order(1)
    void testLevelUpToken() throws Exception {


        int baseLevel = 20;
        LevelInfo levelInfo = lvMgr.getRules().get(baseLevel);
        // Set level
        var w = wRepo.getWalletById(1L);
        w.setLevel(levelInfo.getLevel());
        w.setXp(levelInfo.getStartXp());
        wRepo.saveAndFlush(w);


        // now add a Levelup token
        var ut = new UserToken();
        ut.setType(TxPriceType.LU);
        ut.setCount(1);

        List<UserToken> uts = new ArrayList<>();
        uts.add(ut);
        tkService.addTokens(1L, uts);

        var tokens = tkIntService.getTokens(1L).stream().filter(a -> a.getType() == TokenType.LEVELUP).toList();
        assertEquals(1, tokens.size());
        assertEquals(1, tokens.getFirst().getCount());

        // now execute token (UserStats level = level+1)
        UserStats de = tkService.useToken(1L, "DE", tokens.get(0).getId(), tokens.get(0).getSecret());
        assertEquals(baseLevel + 2, de.getLevel());
        // Check i
        LevelInfo levelInfoNext = lvMgr.getRules().get(baseLevel + 1);
        assertEquals(levelInfoNext.getStartXp(), de.getXp());


        assertEquals(1, de.getAchievements().size());

    }


}
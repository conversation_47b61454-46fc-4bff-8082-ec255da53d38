package com.ously.gamble.service;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.configuration.ConfigPropertyService;
import com.ously.gamble.configprops.AffiliateConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(SpringExtension.class)
class ConfigPropertyServiceImplTest extends TestContext {

    private static final int[] EMPTY_INT_ARRAY = new int[0];
    @Autowired
    ConfigPropertyService cfgService;

    @Autowired
    AffiliateConfig cfg;

    @Test
    @Transactional
    void testBasicPropertyStuff() {

        int resint = cfgService.getPropertyValue("test.key.int", 22, int.class, "-/-");
        assertEquals(22, resint);

        cfgService.setPropertyValue("test.key.int", "45", "-/-");
        resint = cfgService.getPropertyValue("test.key.int", 22, int.class, "-/-");
        assertEquals(45, resint);

        var resdbl = cfgService.getPropertyValue("test.key.double", 22.2d, double.class, "-/-");
        assertEquals(22.2d, resdbl, 0.001);

        double resdbl2 = cfgService.getPropertyValue("test.key.double", 22.2d, double.class, "-/-");
        assertEquals(22.2d, resdbl, 0.001);

        var resbigint = cfgService.getPropertyValue("test.key.bigint", new BigInteger(
                "999999999999999999999"), BigInteger.class, "-/-");
        assertEquals(new BigInteger(
                "999999999999999999999"), resbigint);

        var resintarr = cfgService.getPropertyValue("test.key.intarr", new int[]{1, 2, 3, 4},
                int[].class, "-/-");
        assertEquals(4, resintarr.length);

        var allProperties = cfgService.getAllProperties();
        var first = allProperties.stream().filter(a -> "test.key.intarr".equals(a.key())).findFirst();
        assertTrue(first.isPresent());
        assertEquals("1,2,3,4", first.get().value());


        cfgService.setPropertyValue("test.key.intarr", "45,5,12", "-/-");
        resintarr = cfgService.getPropertyValue("test.key.intarr", EMPTY_INT_ARRAY, int[].class, "-/-");
        assertEquals(3, resintarr.length);
        assertEquals(12, resintarr[2]);
        cfgService.setPropertyValue("test.key.intarr", "1,2,3", "-/-");
        resintarr = cfgService.getPropertyValue("test.key.intarr", EMPTY_INT_ARRAY, int[].class, "-/-");
        assertEquals(1, resintarr[0]);
        assertEquals(2, resintarr[1]);
        assertEquals(3, resintarr[2]);

    }

    @Test
    @Transactional
    void testAnnot() {
        var dbl1 = cfg.getRevshareDeposit();
        assertEquals(0.0d, dbl1, 0.01);

        var first = cfgService.getAllProperties().stream().filter(a -> "affiliate.revsharedeposit".equals(a.key())).findFirst();
        assertTrue(first.get().description().startsWith("The deposit revshare percentage"));

        cfgService.setPropertyValue("affiliate.revsharedeposit", "0.321", "-/-");
        dbl1 = cfg.getRevshareDeposit();
        assertEquals(0.321d, dbl1, 0.01);

        first = cfgService.getAllProperties().stream().filter(a -> "affiliate.revsharedeposit".equals(a.key())).findFirst();
        assertTrue(first.get().description().startsWith("The deposit revshare percentage"));

    }

}
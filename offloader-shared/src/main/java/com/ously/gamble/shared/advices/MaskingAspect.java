package com.ously.gamble.shared.advices;

import com.ously.gamble.api.masking.MaskedPayload;
import com.ously.gamble.api.security.UserPrincipal;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;

/**
 * Mask results of typ MaskedPaylod on annotated (PerformMasking) methods in controllers.
 */
@SuppressWarnings("rawtypes")
@Component
@Aspect
public class MaskingAspect {


    @Pointcut("execution(@com.ously.gamble.api.masking.PerformMasking Iterable+ com.ously.gamble.controller.*.*(..))")
    private void maskIterables() {
    }

    @Pointcut("execution(@com.ously.gamble.api.masking.PerformMasking org.springframework.http.ResponseEntity com.ously.gamble.controller.*.*(..))")
    private void maskResponseEntities() {
    }

    @Pointcut("execution(com.ously.gamble.api.masking.MaskedPayload+ com.ously.gamble.controller.*.*(..))")
    private void maskMaskedPayload() {
    }


    /**
     * Iterables of MaskingSupport
     */
    @AfterReturning(value = "maskIterables()  ", returning = "pResp")
    public void checkIterables(JoinPoint joinPoint, Iterable pResp) {
        checkMaskingSupportList(joinPoint, pResp);
    }

    /**
     * Response Entities (containing a list or singelton)
     */
    @AfterReturning(value = "maskResponseEntities()  ", returning = "pResp")
    public void checkResponseEntities(JoinPoint joinPoint, ResponseEntity pResp) {
        if (pResp.hasBody()) {
            if (pResp.getBody() instanceof Iterable) {
                checkMaskingSupportList(joinPoint, (Iterable) (pResp.getBody()));
            } else {
                checkMaskingSupportList(joinPoint, Collections.singletonList(pResp.getBody()));
            }
        }
    }

    /**
     * direct entities
     */
    @AfterReturning(value = "maskMaskedPayload()  ", returning = "markingSupport")
    public void checkMarkingSupportEntities(JoinPoint joinPoint, MaskedPayload markingSupport) {
        checkMaskingSupportList(joinPoint, Collections.singletonList(markingSupport));
    }

    @SuppressWarnings("unchecked")
    void checkMaskingSupportList(JoinPoint joinPoint, Iterable pResp) {
        var first = Arrays.stream(joinPoint.getArgs()).filter(a -> a instanceof UserPrincipal).findFirst();

        if (first.isEmpty()) {
        } else {
            var up = (UserPrincipal) first.get();
            if (up.getAuthorities().stream().filter(a -> "USER_UNMASKED".equals(a.getAuthority())).findFirst().isEmpty()) {
                pResp.forEach(a -> {
                    if (a instanceof MaskedPayload) {
                        ((MaskedPayload) a).applyMasking();
                    }
                });
            }
        }
    }


}
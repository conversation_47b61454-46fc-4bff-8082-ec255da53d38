package com.ously.gamble.shared.listeners;

import com.fasterxml.jackson.annotation.JsonView;
import com.ously.gamble.api.user.UserMessageGateway;
import com.ously.gamble.api.user.UserMessageRequest;
import com.ously.gamble.api.user.UserMessageService;
import com.ously.gamble.persistence.dto.JsonViews.JVAdmin;
import com.ously.gamble.services.common.BaseOuslyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Detect players which have played and might need an update on crm (balance, tokens, ...)
 */
@Component
@ConditionalOnProperty(prefix = "features", name = "messages", havingValue = "true")
@RabbitListener(queues = {UserMessageGateway.INCOMING_MESSAGE_QUEUE}, containerFactory =
        "directRabbitListenerContainerFactory", id = "userMessageContainer")
public class UserMessageListener extends BaseOuslyService {

    final Logger log = LoggerFactory.getLogger(getClass());
    private final UserMessageService uMsgService;

    public UserMessageListener(UserMessageService umSrv) {
        this.uMsgService = umSrv;
    }

    //    @RabbitListener(queues = UserMessageGateway.INCOMING_MESSAGE_QUEUE, containerFactory =
//            "directRabbitListenerContainerFactory",
//            concurrency = "2")
    @RabbitHandler
    @Transactional
    @JsonView(JVAdmin.class)
    public void handleIncomingUserMessage(UserMessageRequest umRequest) {
        try {
            if (uMsgService.createNewUserMessage(umRequest.getUserId(), umRequest.getQualifier(),
                    umRequest.getContent(), umRequest.isPopup())) {
//                publishEvent(new UserStatsExpiryEvent(umRequest.getUserId()));
            }
        } catch (Exception e) {
            log.error("Error handling UserMessageRequest for {}", umRequest);
        }
    }


}

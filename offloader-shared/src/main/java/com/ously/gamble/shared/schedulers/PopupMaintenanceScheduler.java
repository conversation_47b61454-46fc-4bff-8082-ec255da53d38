package com.ously.gamble.shared.schedulers;

import com.ously.gamble.api.maintenance.ScheduleExecutionService;
import com.ously.gamble.api.maintenance.ScheduledTaskInformation;
import com.ously.gamble.api.popups.PopupManagementService;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class PopupMaintenanceScheduler {
    private final PopupManagementService puMgmt;
    private final ScheduleExecutionService schedSrv;

    public PopupMaintenanceScheduler(PopupManagementService puMgmt, ScheduleExecutionService schedSrv) {
        this.puMgmt = puMgmt;
        this.schedSrv = schedSrv;
    }

    @Scheduled(cron = "0 30 * * * ?")
    @SchedulerLock(name = "popupExpiryMaintenance")
    public void performPopupExpiry() {
        schedSrv.doSchedule(new ScheduledTaskInformation("base", "popupExpiry", null), this::doPopupExpiry);
    }

    private ScheduledTaskInformation doPopupExpiry(ScheduledTaskInformation sti) {
        puMgmt.removeExpiredPopups();
        return sti;
    }
}

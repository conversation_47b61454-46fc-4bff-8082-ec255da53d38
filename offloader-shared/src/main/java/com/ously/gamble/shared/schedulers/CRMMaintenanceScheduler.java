package com.ously.gamble.shared.schedulers;

import com.ously.gamble.api.crm.CRMConfiguration;
import com.ously.gamble.api.crm.CRMDelayedUserUpdate;
import com.ously.gamble.api.maintenance.ScheduleExecutionService;
import com.ously.gamble.api.maintenance.ScheduledTaskInformation;
import com.ously.gamble.api.user.UserAdminService;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * TODO: move into cio integration
 * TODO+: Then extract common crm stuff (needed also for other crm integrations) into base-crm-module
 * <p>
 * Detect players which have played and might need an update on crm (balance, tokens, ...)
 */
@Component
@Profile("!test")
@ConditionalOnProperty(prefix = "crm", name = "enabled", havingValue = "true"
)
public class CRMMaintenanceScheduler {

    final Logger log = LoggerFactory.getLogger(getClass());

    private final UserAdminService uaService;

    private final RabbitTemplate rabbitTemplate;

    private final CRMConfiguration crmConfig;

    private final ScheduleExecutionService schedSrv;


    public CRMMaintenanceScheduler(UserAdminService uaService,
                                   @Qualifier("rabbitTemplateNoTx") RabbitTemplate rabbitTemplate,
                                   CRMConfiguration crmConfig,
                                   ScheduleExecutionService schedSrv) {
        this.uaService = uaService;
        this.rabbitTemplate = rabbitTemplate;
        this.crmConfig = crmConfig;
        this.schedSrv = schedSrv;
    }


    static final int MAX_BATCHSIZE = 50;
    static final int MAX_SCHEDULESIZE = 5000;

    @Scheduled(fixedRateString = "720000", initialDelayString = "240314")
    @SchedulerLock(name = "crmUpdateForActivePlayers", lockAtLeastFor = "PT120S")
    @Transactional
    public void selectUsersToUpdate() {
        schedSrv.doSchedule(new ScheduledTaskInformation("crm", "crmUpdateActiveUsers", null), this::doCrmUpdateForActiveSelection);
    }

    private ScheduledTaskInformation doCrmUpdateForActiveSelection(ScheduledTaskInformation sti) {
        List<CRMDelayedUserUpdate> batches = new ArrayList<>(10);
        var position = 0;
        Set<Long> usersBatched = new HashSet<>();
        do {
            var usersToUpdate = uaService.findUsersToUpdate(position, MAX_BATCHSIZE);
            if (!usersToUpdate.isEmpty()) {

                var upd = new CRMDelayedUserUpdate();
                Set<Long> tmpSet = new HashSet<>(usersToUpdate);
                tmpSet.removeAll(usersBatched);
                upd.getIds().addAll(tmpSet);
                if (!upd.getIds().isEmpty()) {
                    if (log.isDebugEnabled()) {
                        log.debug("adding batch for crm update with size {}", upd.getIds().size());
                    }
                    batches.add(upd);
                }
                usersBatched.addAll(tmpSet);
                position += MAX_BATCHSIZE;
            } else {
                break;
            }
            if (usersToUpdate.size() < MAX_BATCHSIZE) {
                break;
            }
            if (batches.size() * MAX_BATCHSIZE > MAX_SCHEDULESIZE) {
                break;
            }
        } while (true);

        // now send batches
        var batchnum = 0;
        for (var upd : batches) {
            log.debug("CRMUpdate->Sending Batch {} with {} entries", batchnum, upd.getIds().size());
            if (!upd.getIds().isEmpty()) {
                rabbitTemplate.convertAndSend("user.crmcheck", upd);
                batchnum++;
            }
        }
        return sti;
    }


    // old
    // HH:22:45 (once per hour)
    //    @Scheduled(cron = "22 45 * * * ?")

    // once per day /early morning
//    @Scheduled(cron = "0 45 6 * * ?")
//    @SchedulerLock(name = "crmDeviceDeduplication", lockAtLeastFor = "PT180S")
//    @Transactional()
//    // TODO: turn off when fcm-token is no longer fetched from here
//    public void findDeviceDuplicates() {
//        if (crmConfig.isEnabled() && crmConfig.getDevices().isEnabled()) {
//            schedSrv.doSchedule(new ScheduledTaskInformation("crm", "crmDeviceDuplicationHandling", null), this::processCrmDeviceDuplicates);
//        }
//    }
//
//    // TODO: turn off when fcm-token is no longer fetched from here
//    private ScheduledTaskInformation processCrmDeviceDuplicates(ScheduledTaskInformation sti) {
//        log.info("Starting fcmDedup");
//        var dDevices = uaService.findDuplicateFcmTokens();
//        for (var item : dDevices) {
//            rabbitTemplate.convertAndSend("user.fcmdedup", item);
//        }
//        log.info("Finished fcmDedup, got {} items to deduplicate", dDevices.size());
//        return sti;
//    }

}

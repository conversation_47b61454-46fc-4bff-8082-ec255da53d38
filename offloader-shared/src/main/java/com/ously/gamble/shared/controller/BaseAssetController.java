package com.ously.gamble.shared.controller;

import com.ously.gamble.api.assets.AssetService;
import com.ously.gamble.api.assets.GameAsset;
import com.ously.gamble.api.assets.GameAssetType;
import com.ously.gamble.persistence.model.Asset;
import com.querydsl.core.types.Predicate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.querydsl.binding.QuerydslPredicate;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

public class BaseAssetController {

    @Autowired
    AssetService assService;

    @Operation(description = "get list of assets",
               security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/assets")
    @RolesAllowed("ADMIN")
    public List<GameAsset> findAssetsQDSL(
            @Parameter(hidden = true) @QuerydslPredicate(root = Asset.class) Predicate predicate
    ) {
        return assService.getAssets(predicate);
    }

    @Operation(description = "get one asset",
               security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/assets/{id}")
    @RolesAllowed("ADMIN")
    public Optional<GameAsset> getOneAsset(@PathVariable(value = "id") Long id) {
        return assService.getGameAsset(id);
    }

    @Operation(description = "get asset types",
               security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/assets/types")
    @RolesAllowed("ADMIN")
    public List<GameAssetType> getAssetTypes() {
        return Arrays.stream(GameAssetType.values()).toList();
    }


    @Operation(description = "update one asset",
               security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/assets/{id}")
    @RolesAllowed("ADMIN")
    public Optional<GameAsset> updateAsset(@RequestBody GameAsset ga,
                                           @PathVariable(value = "id") Long id) {
        return assService.updateAsset(id, ga);
    }

    @Operation(description = "create an asset",
               security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/assets/")
    @RolesAllowed("ADMIN")
    public Optional<GameAsset> createAsset(@RequestBody GameAsset ga) {
        return assService.newAsset(ga);
    }

    @Operation(description = "delete an asset",
               security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/assets/{id}")
    @RolesAllowed("ADMIN")
    public void deleteAsset(@PathVariable(value = "id") Long id) {
        assService.deleteAsset(id);
    }


    // GAMES
    @SuppressWarnings("MVCPathVariableInspection")
    @Operation(description = "upload an asset-image",
               security = {@SecurityRequirement(name = "bearer-key")})
    @RolesAllowed("ADMIN")
    @PostMapping(value = "/assets/{id}/{type}", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public Optional<GameAsset> uploadAsset(
            @Parameter(content = @Content(mediaType = MediaType.APPLICATION_OCTET_STREAM_VALUE))
            @RequestParam(value = "file") MultipartFile file
            , @PathVariable(value = "id") Long id) {
        return assService.setImage(file, id);
    }

}

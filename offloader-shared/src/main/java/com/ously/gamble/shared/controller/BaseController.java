package com.ously.gamble.shared.controller;

import com.ously.gamble.payload.PagedResponse;
import com.ously.gamble.persistence.model.game.GamePlatform;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;

import java.util.Collections;
import java.util.function.Function;

public class BaseController {
    private final Logger bclog = LoggerFactory.getLogger(getClass());

    public static <R, O> PagedResponse<R> createPagedResponse(Page<O> page, Function<O, R> fnc) {
        if (page == null) {
            return new PagedResponse<>(Collections.emptyList(), 0, 0, 0L, 0, true);
        }

        var lg = page.getContent().stream().map(fnc).toList();

        return new PagedResponse<>(lg, page.getPageable().getPageNumber(), page.getPageable().getPageSize(),
                page.getTotalElements(), page.getTotalPages(), page.isLast());
    }

    /**
     * User Agent detection
     */

    public GamePlatform findPlatformByUserAgent(String method, String sCode) {
        var gp = GamePlatform.WEB;
        if ("ios".equalsIgnoreCase(sCode)) {
            gp = GamePlatform.IOS;
        } else if ("android".equalsIgnoreCase(sCode)) {
            gp = GamePlatform.ANDROID;
        }
        bclog.debug("{} - DEVICE DETECT: {}=>{}", method, sCode, gp);
        return gp;
    }

}

package com.ously.gamble.shared.controller;

import com.ously.gamble.api.features.LevelInfo;
import com.ously.gamble.api.features.LevelManagerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;

public class BaseLevelController {

    @Autowired
    LevelManagerService lService;

    @Operation(description = "get all Levels", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/levels")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Collection<LevelInfo>> getLevels() {
        return ResponseEntity.ok(lService.getLevelInfos());
    }


    @Operation(description = "get all Levels CSV", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/levelsc")
    @RolesAllowed("ADMIN")
    public ResponseEntity<String> getLevelsCsv() {
        return ResponseEntity.ok(lService.getLevelInfosCsv());
    }


    @Operation(description = "updateLevel", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/levels/{lId}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<LevelInfo> updateLevelInfo(@PathVariable("lId") Integer lId, @RequestBody LevelInfo lInfo) {
        return ResponseEntity.ok(lService.updateLevel(lId, lInfo));
    }

}

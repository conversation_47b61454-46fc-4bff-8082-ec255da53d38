package com.ously.gamble.shared.controller;

import com.ously.gamble.api.session.SessionAdminService;
import com.ously.gamble.api.statistics.StatisticsService;
import com.ously.gamble.api.statistics.WalletChangeEntry;
import com.ously.gamble.api.user.DailyStatsService;
import com.ously.gamble.api.user.UserAdminService;
import com.ously.gamble.api.user.UserDailyStatsService;
import com.ously.gamble.persistence.dto.RevRolling30Days;
import com.ously.gamble.persistence.dto.UserBalanceStatsDto;
import com.ously.gamble.statistics.DataTableStatisticsWrapper;
import com.ously.gamble.util.DateRange;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.List;

public class BaseStatsController extends BaseStatisticsController {

    @Autowired
    UserAdminService uas;

    @Autowired
    StatisticsService ss;

    @Autowired
    SessionAdminService sessService;

    @Autowired
    UserDailyStatsService usdService;

    @Autowired
    DailyStatsService dailyStatsService;

    @RolesAllowed("ADMIN")
    @Operation(description = "Wallet change breakdown", security = {@SecurityRequirement(name =
            "bearer-key")})
    @GetMapping(value = "/users/walletchange/{from}/{to}")
    public ResponseEntity<List<WalletChangeEntry>> getWalletChangeBreakdown(
            @PathVariable(value = "from") String from,
            @PathVariable(value = "to") String to) {

        return ResponseEntity.ok(ss.getWalletChangeBreakdown(LocalDate.parse(from),
                LocalDate.parse(to)));
    }

    @RolesAllowed("ADMIN")
    @Operation(description = "Recreate userTransaction aggregation", security = {@SecurityRequirement(name =
            "bearer-key")})
    @GetMapping(value = "/users/txagg/{from}/{to}")
    public void requestUserTxAggregation(
            @PathVariable(value = "from") String from,
            @PathVariable(value = "to") String to) {
        log.info("Got request for queueing utxAgg requests from {} to {}", from, to);
        usdService.queueUserTransactionAggRequests(LocalDate.parse(from), LocalDate.parse(to));
    }


    @RolesAllowed("ADMIN")
    @Operation(description = "New Users", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/users/new/{from}/{to}")
    public ResponseEntity<DataTableStatisticsWrapper> newUsers(
            @Parameter(hidden = true) HttpServletResponse response,
            @PathVariable(value = "from") String begin,
            @PathVariable(value = "to") String end) {
        var dr = DateRange.of(begin, end);

        var newUsersDaily = ss.getNewUsersDaily(dr.getFrom(), dr.getTo());
        var newUsers = convertToTable("New Users", newUsersDaily, "dt:date", "l:count");
        newUsers = fillInMissingDates(newUsers, dr);
        newUsers = fillInMissingValues(newUsers);
        return ResponseEntity.ok(new DataTableStatisticsWrapper("new users daily", newUsers, dr));
    }


    @RolesAllowed("ADMIN")
    @Operation(description = "Purchase statistics",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/purchases/{from}/{to}")
    public ResponseEntity<DataTableStatisticsWrapper> purchases(@PathVariable(value = "from") String begin,
                                                       @PathVariable(value = "to") String end) {

        var dr = DateRange.of(begin, end);

        var purchaseStatsDaily = ss.getPurchaseStatsDaily(dr.getFrom(), dr.getTo());
        var purchases = convertToTable("Purchases", purchaseStatsDaily, "dt:date", "l:count", "d:amount", "s:platform");
        purchases = fillInMissingDates(purchases, dr);
        purchases = fillInMissingValues(purchases);
        return ResponseEntity.ok(new DataTableStatisticsWrapper("purchases", purchases, dr));
    }

    @RolesAllowed("ADMIN")
    @Operation(description = "User Age distributions",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/users/agedist/{from}/{to}")
    public ResponseEntity<DataTableStatisticsWrapper> userAgeDistChart(
            @PathVariable(value = "from") String from,
            @PathVariable(value = "to") String to) {
        var dr = DateRange.of(from, to);

        var userDistributionFromTo = uas.getUserDistributionFromTo(dr.getFrom(), dr.getTo());
        var user_distribution = convertToTable("User Distribution", userDistributionFromTo, "dt:date", "l:b0", "l:b1", "l:b5", "l:b10", "l:b20", "l:b30", "l:b40", "l:b50", "l:bmore");
        user_distribution = fillInMissingDates(user_distribution, dr);
        user_distribution = fillInMissingValues(user_distribution);
        return ResponseEntity.ok(new DataTableStatisticsWrapper("user age distribution", user_distribution, dr));
    }

    @RolesAllowed("ADMIN")
    @Operation(description = "First time depositors",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/users/ftd/{from}/{to}")
    public ResponseEntity<DataTableStatisticsWrapper> userFtdStats(
            @PathVariable(value = "from") String begin,
            @PathVariable(value = "to") String end) {

        var dr = DateRange.of(begin, end);
        var userDistributionFromTo = ss.getFirstTimeDepositors(dr.getFrom(), dr.getTo());
        var user_ftd_table = convertToTable("User Distribution", userDistributionFromTo, "dt:date", "i:numFtd");
        user_ftd_table = fillInMissingDates(user_ftd_table, dr);
        user_ftd_table = fillInMissingValues(user_ftd_table);
        return ResponseEntity.ok(new DataTableStatisticsWrapper("FTD counts per day", user_ftd_table, dr));
    }

    // recreate user daily stats update from/to
    @Operation(description = "request daily aggregation update using from,to",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/users/dailyagg/{from}/{to}/recreate")
    @RolesAllowed("ADMIN")
    public List<LocalDate> recreateDailyUserAggStats(@PathVariable(value = "from") String begin,
                                                     @PathVariable(value = "to") String end,
                                                     @RequestParam(name = "newtx", defaultValue = "false", required = false) boolean newTX
    ) {

        var dr = DateRange.of(begin, end);
        return usdService.updateDailyUserStats(dr.getFrom(), dr.getTo(), newTX);
    }

    @Operation(description = "request daily aggregation update using from,to",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/platform/dailyagg/{from}/{to}/recreate")
    @RolesAllowed("ADMIN")
    public List<LocalDate> recreateDailyPlatformAggStats(@PathVariable(value = "from") String begin,
                                                         @PathVariable(value = "to") String end) {

        var dr = DateRange.of(begin, end);
        return dailyStatsService.requestUpdateDailyStats(dr.getFrom(), dr.getTo());
    }


    // Get active Users broken down to days
    @Operation(description = "request statistics of active users for from,to",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/users/active/{from}/{to}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<DataTableStatisticsWrapper> getActiveUsersFromTo(
            @PathVariable(value = "from") String begin, @PathVariable(value = "to") String end) {

        var dr = DateRange.of(begin, end);

        var activeUserStatsDaily = ss.getActiveUserStatsDaily(dr.getFrom(), dr.getTo());
        var active_users = convertToTable("Active users", activeUserStatsDaily, "dt:date", "l:activeUsers");
        active_users = fillInMissingDates(active_users, dr);
        active_users = fillInMissingValues(active_users);
        return ResponseEntity.ok(new DataTableStatisticsWrapper("active users", active_users, dr));
    }

    @Operation(description = "request statistics of all game sessions for to,from",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/games/sessions/{from}/{to}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<DataTableStatisticsWrapper> getGameSessionAggregationFromTo(
            @PathVariable(value = "from") String begin, @PathVariable(value = "to") String end) {
        var dr = DateRange.of(begin, end);
        var sessionDaysByPeriod = ss.getSessionDaysByPeriod(dr.getFrom(), dr.getTo());
        var session_stats = convertToTable("Active users", sessionDaysByPeriod, "dt:date", "d:saldo", "l:spins", "d:bets", "d:wins", "d:bonus", "d:maxWin", "d:maxBet", "d:maxMultiplier", "l:sessions");
        session_stats = fillInMissingDates(session_stats, dr);
        session_stats = fillInMissingValues(session_stats);
        return ResponseEntity.ok(new DataTableStatisticsWrapper("Session statistics per Day", session_stats, dr));
    }

    @Operation(description = "request statistics of all game sessions for to,from",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/provider/sessions/{provider}/{from}/{to}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<DataTableStatisticsWrapper> getProviderPerformance(
            @PathVariable(value = "provider") String provider,
            @PathVariable(value = "from") String begin, @PathVariable(value = "to") String end) {
        var dr = DateRange.of(begin, end);

        var monthlyProviderStats = sessService.getMonthlyProviderStats(provider, dr.getFrom(), dr.getTo(), true, true);
        var session_stats = convertToTable("Provider Performance by day", monthlyProviderStats, "dt:date", "s:provider", "d:sumBet", "d:sumWin", "l:countSpins",
                "l:countSessions", "l:distinctUsers", "d:avgSessionDuration");
        session_stats = fillInMissingDates(session_stats, dr);
        session_stats = fillInMissingValues(session_stats, "provider", provider);
        return ResponseEntity.ok(new DataTableStatisticsWrapper("Provider statistics per Day", session_stats, dr));
    }

    @Operation(description = "request list of users balance-relevant stats (to,from)",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/user/balance/history/{userId}/{from}/{to}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<List<UserBalanceStatsDto>> getUserBalanceStats(
            @PathVariable(value = "userId") Long userId,
            @PathVariable(value = "from") String begin,
            @PathVariable(value = "to") String end) {
        var dr = DateRange.of(begin, end);

        var stats = uas.getUserBalanceStatistics(userId, dr.getFrom(), dr.getTo());
        return ResponseEntity.ok(stats);

    }

    @RolesAllowed("ADMIN")
    @Operation(description = "Rolling 30-day revenue", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/rollingrevenue")
    public ResponseEntity<List<RevRolling30Days>> getRollingRevenue() {
        List<RevRolling30Days> rollingData = ss.getRevRolling30Days();
        return ResponseEntity.ok(rollingData);
    }
}

package com.ously.gamble.shared.notification.handlers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.api.notification.*;
import com.ously.gamble.config.SupportConfig;
import com.ously.gamble.persistence.model.NotificationType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;


@Component
public class SupportMailHandler implements NotificationHandler {

    final Logger log = LoggerFactory.getLogger(SupportMailHandler.class);

    private final SupportConfig sConfig;

    private final JavaMailSender mailSender;

    private final FeatureConfig features;

    private final ObjectMapper om;


    public SupportMailHandler(SupportConfig cC, JavaMailSender jms, FeatureConfig fConf,
                              ObjectMapper om) {
        this.om = om;
        this.sConfig = cC;
        this.mailSender = jms;
        this.features = fConf;
    }

    @Override
    public NotificationType supportedType() {
        return NotificationType.SUPPORT;
    }

    @Override
    public Receipt sendOut(Message msg) {

        var stage = features.getStage();
        var to = msg.getDestination();
        try {
            var text = createTicketText(msg);
            var mailmsg = new SimpleMailMessage();
            mailmsg.setText(text);

            mailmsg.setFrom(sConfig.getEmail());
            mailmsg.setReplyTo(msg.getDestination());
            mailmsg.setTo(sConfig.getEmail());
            if ("LH".equalsIgnoreCase(sConfig.getSystem())) {
                mailmsg.setSubject(stage + " LHN-JSON " + msg.getSubject());
            } else {
                mailmsg.setSubject(stage + '/' + msg.getTopic() + '-' + msg.getSubject());
            }
            mailSender.send(mailmsg);
        } catch (Exception e) {
            log.error("Sending mail to {} failed: {}", to, e.getMessage());
            return Receipt.error(msg, e.getMessage());
        }
        return Receipt.ok(msg, "OK:none");
    }

    private String createTicketText(Message msg) throws JsonProcessingException {
        if ("LH".equals(sConfig.getSystem())) {
            var tk = new LHNTicket(msg);
            var sTk = new SupportTicket();
            sTk.setTicket(tk);
            return om.writeValueAsString(sTk);
        }

        var strBld = new StringBuilder(256);
        strBld.append("--------------------\n");
        msg.getProperties().forEach((key, value) -> strBld.append(key).append('=').append(value).append('\n'));
        strBld.append("--------------------\n").append("Topic:").append(msg.getTopic()).append('\n').append("Email:").append(msg.getDestination()).append('\n').append("Name:").append(msg.getName()).append('\n').append("Req-Type:").append(msg.getType()).append('\n').append("UId:").append(msg.getuId()).append('\n').append("--------------------\n").append("Subject:").append(msg.getSubject()).append("\n \n \n \n").append(msg.getMsg());
        return strBld.toString();

    }

}

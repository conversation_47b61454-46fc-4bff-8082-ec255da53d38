package com.ously.gamble.shared.controller;

import com.ously.gamble.api.security.CurrentUser;
import com.ously.gamble.api.security.UserPrincipal;
import com.ously.gamble.payment.api.PaymentAdminService;
import com.ously.gamble.payment.api.PaymentCallback;
import com.ously.gamble.payment.api.PaymentManager;
import com.ously.gamble.payment.api.handler.PaymentHandler;
import com.ously.gamble.payment.api.handler.PaymentHandlerInfo;
import com.ously.gamble.payment.payload.deposit.UserDeposit;
import com.ously.gamble.payment.payload.deposit.UserDepositStep;
import com.ously.gamble.payment.payload.payout.*;
import com.ously.gamble.persistence.model.session.Jurisdiction;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static org.springframework.data.domain.Sort.Direction.ASC;

public class BasePaymentController {
    private final PaymentManager paymentManager;
    private final PaymentAdminService paymentAdminService;


    public BasePaymentController(PaymentManager pmMgr, PaymentAdminService paymentAdminService) {
        this.paymentManager = pmMgr;
        this.paymentAdminService = paymentAdminService;
    }

    // handler list

    @Operation(description = "get list of active handlers", security = {@SecurityRequirement(name =
            "bearer-key")})
    @GetMapping("/handler")
    @RolesAllowed("ADMIN")
    public List<PaymentHandlerInfo> getHandlers() {
        Objects.requireNonNull(paymentManager);
        return paymentManager.getActiveHandlers().stream().map(PaymentHandler::getInfo).toList();
    }

    @Operation(description = "get list of handlers for jurisdiction", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @GetMapping("/handler/jurisdiction/{jd}")
    @RolesAllowed("ADMIN")
    public List<PaymentHandlerInfo> getHandlersForJurisdiction(
            @PathVariable("jd") Jurisdiction jd) {
        Objects.requireNonNull(paymentManager);
        return paymentManager.getActiveHandlersForJurisdiction(jd).stream().map(PaymentHandler::getInfo).toList();
    }

    @Operation(description = "get list of deposits for a user", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @GetMapping("/user/{uid}/deposits/{from}/{to}")
    @RolesAllowed("ADMIN")
    public Page<UserDeposit> getDepositsForUser(@PathVariable("uid") Long uid,
                                                @ParameterObject @PageableDefault(size = 20,
                                                                                  direction = ASC,
                                                                                  sort =
                                                                                          {
                                                                                                  "depositId"}) Pageable pageable,
                                                @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                @Parameter(schema = @Schema(format = "string",
                                                                            example = "2022-01-01"))
                                                @PathVariable("from") LocalDate from,
                                                @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                @Parameter(schema = @Schema(format = "string",
                                                                            example = "2023-01-01"))
                                                @PathVariable("to") LocalDate to
    ) {
        Objects.requireNonNull(paymentAdminService);
        return paymentAdminService.getDepositsForUser(uid, from, to, pageable);
    }


    @Operation(description = "get list of all deposits", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @GetMapping("/deposits/{from}/{to}")
    @RolesAllowed("ADMIN")
    public Page<UserDeposit> getAllDeposits(@ParameterObject @PageableDefault(size = 20,
                                                                              direction = ASC,
                                                                              sort =
                                                                                      {"depositId"}) Pageable pageable,
                                            @DateTimeFormat(pattern = "yyyy-MM-dd")
                                            @Parameter(schema = @Schema(format = "string",
                                                                        example = "2022-01-01"))
                                            @PathVariable("from") LocalDate from,
                                            @DateTimeFormat(pattern = "yyyy-MM-dd")
                                            @Parameter(schema = @Schema(format = "string",
                                                                        example = "2023-01-01"))
                                            @PathVariable("to") LocalDate to) {
        Objects.requireNonNull(paymentAdminService);
        return paymentAdminService.getAllDeposits(from, to, pageable);
    }


    @Operation(description = "get payouts requiring approval", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @GetMapping("/payouts/approval")
    @RolesAllowed("ADMIN")
    public Page<UserPayout> getPayoutsNeedingApproval(
            @ParameterObject @PageableDefault(size = 20,
                                              direction = ASC,
                                              sort =
                                                      {"depositId"}) Pageable pageable) {
        Objects.requireNonNull(paymentAdminService);
        return paymentAdminService.getPayoutsNeedingApproval(pageable);
    }

    @Operation(description = "get payouts requiring approval", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @GetMapping("/payouts/intervention")
    @RolesAllowed("ADMIN")
    public Page<UserPayout> getPayoutsNeedingIntervention(
            @ParameterObject @PageableDefault(size = 20,
                                              direction = ASC,
                                              sort =
                                                      {"depositId"}) Pageable pageable) {
        Objects.requireNonNull(paymentAdminService);
        return paymentAdminService.getPayoutsNeedingIntervention(pageable);
    }

    @Operation(description = "get valid payouts", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @GetMapping("/payouts/valid/{from}/{to}")
    @RolesAllowed("ADMIN")
    public Page<UserPayout> getValidPayouts(
            @ParameterObject @PageableDefault(size = 20,
                                              direction = ASC,
                                              sort =
                                                      {"depositId"}) Pageable pageable,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @Parameter(schema = @Schema(format = "string", example = "2022-01-01"))
            @PathVariable("from") LocalDate from,
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            @Parameter(schema = @Schema(format = "string", example = "2023-01-01"))
            @PathVariable("to") LocalDate to) {
        Objects.requireNonNull(paymentAdminService);
        return paymentAdminService.getValidPayouts(from, to, pageable);
    }


    @Operation(description = "get list of steps for a deposit", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @GetMapping("/user/{uid}/deposits/{dpId}/steps")
    @RolesAllowed("ADMIN")
    public List<UserDepositStep> getDepositStepsForUser(@PathVariable("uid") Long uid,
                                                        @PathVariable(
                                                                "dpId") String depsitId) {
        Objects.requireNonNull(paymentAdminService);
        return paymentAdminService.getDepositStepsForUserAndDepositId(uid, depsitId);
    }


    @Operation(description = "get list of payouts for a user", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @GetMapping("/user/{uid}/payouts/{from}/{to}")
    @RolesAllowed("ADMIN")
    public Page<UserPayout> getPayoutsForUser(@PathVariable("uid") Long uid,
                                              @ParameterObject @PageableDefault(size = 20,
                                                                                direction = ASC,
                                                                                sort =
                                                                                        {
                                                                                                "payoutId"}) Pageable pageable,
                                              @DateTimeFormat(pattern = "yyyy-MM-dd")
                                              @Parameter(schema = @Schema(format = "string",
                                                                          example = "2022-01-01"))
                                              @PathVariable("from") LocalDate from,
                                              @DateTimeFormat(pattern = "yyyy-MM-dd")
                                              @Parameter(schema = @Schema(format = "string",
                                                                          example = "2023-01-01"))
                                              @PathVariable("to") LocalDate to) {
        Objects.requireNonNull(paymentAdminService);
        return paymentAdminService.getPayoutsForUser(uid, from, to, pageable);
    }

    @Operation(description = "get list of steps for a payout", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @GetMapping("/user/{uid}/payouts/{poId}/steps")
    @RolesAllowed("ADMIN")
    public List<UserPayoutStep> getPayoutStepsForUser(@PathVariable("uid") Long uid, @PathVariable(
            "poId") String payoutId) {
        Objects.requireNonNull(paymentAdminService);
        return paymentAdminService.getPayoutStepsForUserAndPayoutId(uid, payoutId);
    }

    @Operation(description = "get callback notification", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @GetMapping("/notification/{notMd5}")
    @RolesAllowed("ADMIN")
    public Optional<PaymentCallback> getCallbackNotification(@PathVariable("notMd5") String md5) {
        Objects.requireNonNull(paymentAdminService);
        return paymentAdminService.getNotification(md5);
    }

    @Operation(description = "requeue callback notification", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @GetMapping("/notification/{notMd5}/requeue")
    @RolesAllowed("ADMIN")
    public Optional<PaymentCallback> requeueCallbackNotification(
            @PathVariable("notMd5") String md5) {
        Objects.requireNonNull(paymentAdminService);
        return paymentAdminService.requeueNotification(md5);
    }

    @Operation(description = "approve a payout", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @PutMapping("/payouts/approval")
    @RolesAllowed("ADMIN")
    public Optional<UserPayout> approvePayout(
            @CurrentUser UserPrincipal adminUser, @RequestBody PayoutApprovalRequest req
    ) {
        Objects.requireNonNull(paymentManager);
        paymentManager.performPayoutApproval(req, adminUser.getId());
        return paymentManager.getUserPayout(req.userId(), req.payoutId());
    }

    @Operation(description = "abort/cancel a payout", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @PutMapping("/payouts/cancel")
    @RolesAllowed("ADMIN")
    public Optional<UserPayout> abortPayout(
            @CurrentUser UserPrincipal adminUser, @RequestBody PayoutAbortRequest req
    ) {
        Objects.requireNonNull(paymentManager);
        paymentManager.performPayoutAbort(req, adminUser.getId());
        return paymentManager.getUserPayout(req.userId(), req.payoutId());
    }

    @Operation(description = "get user deposit addressess", security = {@SecurityRequirement(name =
            "bearer-key")})
    @GetMapping("/user/{uid}/addresses")
    @RolesAllowed("ADMIN")
    public List<UserDepositAddress> getUserDepositAddresses(@PathVariable("uid") long userId) {
        Objects.requireNonNull(paymentManager);
        return paymentManager.getUserDepositAddresses(userId);
    }

}

package com.ously.gamble.shared.controller;


import com.ously.gamble.api.issues.CasinoIssue;
import com.ously.gamble.api.issues.CasinoIssueComment;
import com.ously.gamble.api.issues.IssueService;
import com.ously.gamble.api.security.CurrentUser;
import com.ously.gamble.api.security.UserPrincipal;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

public class BaseIssueController extends BaseController {

    @Autowired
    IssueService is;

    // Get a single issue
    @Operation(description = "get an existing issue",
               security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/issues/{id}")
    @RolesAllowed("ADMIN")
    public CasinoIssue getIssueA(@PathVariable(value = "id") Long id) {
        return is.getIssue(id);
    }

    // create new issue
    @Operation(description = "create an issue",
               security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/issues")
    @RolesAllowed("ADMIN")
    public CasinoIssue createIssueA(@RequestBody CasinoIssue ci) {
        return is.createCasinoIssue(ci);
    }

    @Operation(description = "update an issue",
               security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/issues/{id}/reassign/{uid}")
    @RolesAllowed("ADMIN")
    public CasinoIssue reassignIssueA(@PathVariable(value = "id") Long id,
                                      @PathVariable(value = "uid") Long uid,
                                      @CurrentUser UserPrincipal currentUser) {
        return is.reassignToUserId(id, uid, currentUser.getId());
    }

    // create new issue comment
    @Operation(description = "create a new issue comment",
               security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/issues/{id}/comment")
    @RolesAllowed("ADMIN")
    public CasinoIssue createIssueCommentA(@PathVariable("id") Long issueId,
                                           @RequestBody CasinoIssueComment cic,
                                           @CurrentUser UserPrincipal currentUser) {
        return is.addComment(cic, issueId, currentUser.getId());
    }


}

package com.ously.gamble.shared.sync.components;

import com.ously.gamble.api.gamemanager.GMProvider;
import com.ously.gamble.api.vendor.CasinoSlotProvider;
import com.ously.gamble.api.vendor.VendorManagementService;
import com.ously.gamble.shared.sync.SyncComponent;
import com.ously.gamble.shared.sync.SyncContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class VendorSyncer implements SyncComponent<GMProvider> {
    private static final Logger log = LoggerFactory.getLogger(VendorSyncer.class);

    private final VendorManagementService vMgmt;

    public VendorSyncer(VendorManagementService vMgmt) {
        this.vMgmt = vMgmt;
    }


    @Override
    @Transactional(timeout = 120)
    public boolean doSync(List<GMProvider> vendorsRemote, SyncContext ctx, boolean simulate) {
        var vendorsLocal = vMgmt.findAll();
        log.info("Got {} vendors from gamemanager, have {} vendors locally", vendorsRemote.size(), vendorsLocal.size());
        return performVendorSync(vendorsLocal, vendorsRemote, simulate);
    }


    private boolean performVendorSync(List<CasinoSlotProvider> vendorsLocal,
                                      List<GMProvider> vendorsRemote, boolean simulate) {
        var localProviders = vendorsLocal.stream().collect(Collectors.toMap(CasinoSlotProvider::getId, Function.identity()));
        var remoteProviders = vendorsRemote.stream().collect(Collectors.toMap(GMProvider::getId, Function.identity()));

        // 1.) iterate local and apply changes
        Map<Integer, CasinoSlotProvider> changedProviders = new HashMap<>();
        Map<Integer, CasinoSlotProvider> missingProviders = new HashMap<>();

        for (var csp : localProviders.values()) {
            var gmp = remoteProviders.get(csp.getId());
            if (gmp == null) {
                missingProviders.put(csp.getId(), csp);
            } else {
                // Check for changes
                var changed = false;

                if (!Objects.equals(csp.getProviderName(), gmp.getProviderName())) {
                    if (gmp.getProviderName() != null) {
                        csp.setProviderName(gmp.getProviderName());
                        changed = true;
                    }
                }

                if (!Objects.equals(csp.getBridgeName(), gmp.getBridgeName())) {
                    csp.setBridgeName(gmp.getBridgeName());
                    changed = true;
                }

                if (!Objects.equals(csp.getSlotCatalogName(), gmp.getSlotCatalogName())) {
                    csp.setSlotCatalogName(gmp.getSlotCatalogName());
                    changed = true;
                }

                if (!Objects.equals(csp.getBlockedCountries(), gmp.getBlockedCountries())) {
                    csp.setBlockedCountries(gmp.getBlockedCountries());
                    changed = true;
                }

                if (!Objects.equals(csp.getProviderHomepage(), gmp.getProviderHomepage())) {
                    csp.setProviderHomepage(gmp.getProviderHomepage());
                    changed = true;
                }

                if (!Objects.equals(csp.getCreatedAt(), gmp.getCreatedAt())) {
                    csp.setCreatedAt(gmp.getCreatedAt());
                    changed = true;
                }

                if (!Objects.equals(csp.getUpdatedAt(), gmp.getUpdatedAt())) {
                    csp.setUpdatedAt(gmp.getUpdatedAt());
                    changed = true;
                }

                if (csp.getOrder() != gmp.getOrder()) {
                    csp.setOrder(gmp.getOrder());
                    changed = true;
                }

                if (changed) {
                    changedProviders.put(csp.getId(), csp);
                }
                remoteProviders.remove(csp.getId());
            }
        }
        Map<Integer, CasinoSlotProvider> newProviders = new HashMap<>();
        for (var gmp : remoteProviders.values()) {
            var csp = new CasinoSlotProvider();
            csp.setId(gmp.getId());
            csp.setOrder(gmp.getOrder());
            csp.setUpdatedAt(gmp.getUpdatedAt());
            csp.setCreatedAt(gmp.getCreatedAt());
            csp.setProviderName(gmp.getProviderName());
            csp.setBridgeName(gmp.getBridgeName());
            csp.setSlotCatalogName(gmp.getSlotCatalogName());
            csp.setBlockedCountries(gmp.getBlockedCountries());
            csp.setProviderHomepage(gmp.getProviderHomepage());
            csp.setActive(false);
            newProviders.put(gmp.getId(), csp);
        }

        // Checking uniqueness of vendorName,bridgeName && slotCatalogName

        List<CasinoSlotProvider> allCSPs = new ArrayList<>(changedProviders.values());
        allCSPs.addAll(missingProviders.values());
        allCSPs.addAll(newProviders.values());

        var scNames = allCSPs.stream().map(CasinoSlotProvider::getSlotCatalogName).collect(Collectors.toSet());
        var vNames = allCSPs.stream().map(CasinoSlotProvider::getProviderName).collect(Collectors.toSet());
        var bNames = allCSPs.stream().map(CasinoSlotProvider::getBridgeName).collect(Collectors.toSet());

        var numCSPs = allCSPs.size();
        var success = true;
        if (scNames.size() != numCSPs) {
            log.warn("slotcatalogName duplicates. Exp. {}, got {}", numCSPs, scNames.size());
            success = false;
        }
        if (vNames.size() != numCSPs) {
            log.warn("vendorName duplicates. Exp. {}, got {}", numCSPs, vNames.size());
            success = false;
        }
        if (bNames.size() != numCSPs) {
            log.warn("bridgeName duplicates. Exp. {}, got {}", numCSPs, bNames.size());
            success = false;
        }


        // Continue

        log.info("Got {} vendors locally no longer in gamemanager, potential removal candidates", missingProviders.size());
        log.info("Got {} vendors changed", changedProviders.size());
        log.info("Got {} new vendors, creation candidates", newProviders.size());

        if (!simulate) {
            if (success) {
                // 1. perform updates
                if (updateVendors(changedProviders)) {
                    // 2. add new vendors
                    updateVendors(newProviders);
                }
            }
        }

        return success;
    }

    private boolean updateVendors(Map<Integer, CasinoSlotProvider> changedProviders) {
        changedProviders.values().forEach(vMgmt::updateOrSaveCSP);
        return true;
    }


}

package com.ously.gamble.shared.maintenance;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

@Service
public class DBMaintenanceService {

    private final JdbcTemplate jdbcTmpl;

    public DBMaintenanceService(JdbcTemplate jdbcTmpl) {
        this.jdbcTmpl = jdbcTmpl;
    }


    @Transactional(timeout = 7200)
    public void archiveTransactions(String partition, int maxAge) {
        jdbcTmpl.execute("call ARCHIVE_TRANSACTIONS('" + partition + "'," + maxAge + ')');
    }

    @Transactional
    public long getCountForTable(String table) {
        return Objects.requireNonNullElse(jdbcTmpl.queryForObject("select count(*) from " + table, Long.class), 0L);
    }

    @Transactional
    public void optimizeTable(String table) {
        jdbcTmpl.execute("optimize table " + table);
    }

    @Transactional
    public void analyzeTable(String table) {
        jdbcTmpl.execute("analyze table " + table);
    }

    @Transactional
    public void analyzePartition(String table, String partition) {
        jdbcTmpl.execute("ALTER TABLE " + table + " ANALYZE PARTITION " + partition);
    }

    @Transactional(timeout = 3600)
    public void optimizePartition(String table, String partition) {
        jdbcTmpl.execute("ALTER TABLE " + table + " REBUILD PARTITION " + partition);
    }

    @Transactional(timeout = 7200)
    public void analyzeAll(int mode) {
        jdbcTmpl.execute("call DB_MAINTENANCE(" + mode + ')');
    }

}

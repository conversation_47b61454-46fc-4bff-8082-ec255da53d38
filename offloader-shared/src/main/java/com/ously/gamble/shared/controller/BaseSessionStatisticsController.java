package com.ously.gamble.shared.controller;

import com.ously.gamble.api.session.SessionStatisticService;
import com.ously.gamble.persistence.model.session.SessionStatisticsRD;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.List;

public class BaseSessionStatisticsController extends BaseController {

    private final SessionStatisticService sessionStatService;


    public BaseSessionStatisticsController(SessionStatisticService ssSrv) {
        this.sessionStatService = ssSrv;
    }


    @Operation(description = "get session statistics aggregated as daily rows",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/statistics/session/{sessionId}/{userId}/daily", produces = "application/json")
    @RolesAllowed("ADMIN")
    public List<SessionStatisticsRD> getAggregationByDay(
            @PathVariable("userId") long userId,
            @PathVariable("sessionId") long sessionId,
            @RequestParam(value = "replace", defaultValue = "false", required = false) boolean saveOrUpdate) {
        return sessionStatService.aggregateSession(sessionId, userId, saveOrUpdate);
    }

    @Operation(description = "do from/to session statistics aggregated as daily rows",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(path = "/statistics/session/{from}/{to}/period", produces = "application/json")
    @RolesAllowed("ADMIN")
    public int doAggregationsByDayForPeriod(
            @PathVariable("from") String from,
            @PathVariable("to") String to,
            @RequestParam(value = "replace", defaultValue = "false", required = false) boolean saveOrUpdate,
            @RequestParam(value = "batchsize", defaultValue = "50", required = false) int batchsize
    ) {

        LocalDate fromDt = LocalDate.parse(from);
        LocalDate toDt = LocalDate.parse(to);

        return sessionStatService.enqueueSessionsForRecalc(fromDt, toDt, batchsize, saveOrUpdate);
    }

}

package com.ously.gamble.shared.configs;

import com.ously.gamble.api.geoip.UserEventRouter;
import com.ously.gamble.services.geoip.UserEventRouterImpl;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
public class UserEventConfiguration {

    @Bean
    public UserEventRouter geoIpLocationService(ApplicationEventPublisher evntPub) {
        return new UserEventRouterImpl(evntPub);
    }

}

package com.ously.gamble.shared.service;

import com.dynatrace.oneagent.sdk.api.IncomingRemoteCallTracer;
import com.dynatrace.oneagent.sdk.api.OneAgentSDK;
import com.ously.gamble.api.bridge.*;
import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.model.session.Jurisdiction;
import jakarta.annotation.PreDestroy;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
public class BridgeRemoteDelegate implements RemoteVendorBridge {
    private final Logger log = LoggerFactory.getLogger(BridgeRemoteDelegate.class);

    private final RedissonClient client;

    private final VendorBridge vendorBridgeImpl;

    private final FeatureConfig fConfig;

    private final OneAgentSDK optOneAgent;


    public BridgeRemoteDelegate(RedissonClient client, @Qualifier("bridgeVB")
    VendorBridge vendorBridgeImpl, FeatureConfig fConfig, Optional<OneAgentSDK> optOneAgent) {
        this.client = client;
        this.vendorBridgeImpl = vendorBridgeImpl;
        this.fConfig = fConfig;
        this.optOneAgent = optOneAgent.orElse(null);
        log.info("Registering BridgeRemoteDelegate");
        var remoteService = client.getRemoteService();
        remoteService.register(RemoteVendorBridge.class, this, 20);
        log.info("Registered BridgeRemoteDelegate:{}", remoteService);
    }


    @PreDestroy
    public void beforeDestroy() {
        log.info("deregistering VendorBridge");
        client.getRemoteService().deregister(RemoteVendorBridge.class);
    }

    @Override
    public CreateFreespinsResponse createFreespins(CreateFreespinsRequest req, String tag) {
        log.info("Got cfs via remote");
        return execute(() -> vendorBridgeImpl.createFreespins(req), getIncomingRemoteCallTracer(tag,
                "createFreespins"));
    }

    @Override
    public CreateSessionResponse createGameSession(CreateSessionRequest req, String tag) {
        log.debug("Got cgi via remote for jurisdiction={}", req.getJurisdiction());
        req.getSettings().setStage(fConfig.getStage());
        return execute(() -> vendorBridgeImpl.createGameSession(req), getIncomingRemoteCallTracer(tag, "createSession"));
    }

    @Override
    public GameInstance createGameInstance(Long userId, CasinoGame game, GamePlatform platform,
                                           GameSettings settings, Long nonce, String crypt,
                                           Jurisdiction jd, String tag) throws Exception {
        log.debug("Got cgi via remote for jurisdiction={}", jd);
        settings.setStage(fConfig.getStage());
        return executeChecked(() -> vendorBridgeImpl.createGameInstance(userId, game, platform, settings, nonce, crypt, jd), getIncomingRemoteCallTracer(tag, "createGameInstance"));
    }


    @Override
    public List<String> getProviderNames(String tag) {
        return execute(vendorBridgeImpl::getProviderNames, getIncomingRemoteCallTracer(tag, "getProviderNames"));
    }

    @Override
    public List<AvailableGame> getAvailableGamesFromVendor(String vendorName, String tag) {
        return execute(() -> vendorBridgeImpl.getAvailableGamesFromVendor(vendorName), getIncomingRemoteCallTracer(tag, "getAvailableGamesFromVendor", "vendor", vendorName));
    }


    @Override
    public Map<String, List<AvailableGame>> getAvailableGames(String tag) {
        return execute(vendorBridgeImpl::getAvailableGames, getIncomingRemoteCallTracer(tag, "getAvailableGames"));
    }

    private <T> T executeChecked(CheckedBridgeMethod<T> producer,
                                 IncomingRemoteCallTracer incomingRemoteCallTracer) throws Exception {
        try {
            return producer.apply();
        } catch (Exception e) {
            if (incomingRemoteCallTracer != null) {
                incomingRemoteCallTracer.error(e);
                throw e;
            }
        } finally {
            if (incomingRemoteCallTracer != null) {
                incomingRemoteCallTracer.end();
            }
        }
        return null;
    }

    private <T> T execute(BridgeMethod<T> producer,
                          IncomingRemoteCallTracer incomingRemoteCallTracer) {
        try {
            return producer.apply();
        } catch (Exception e) {
            if (incomingRemoteCallTracer != null) {
                incomingRemoteCallTracer.error(e);
            }
        } finally {
            if (incomingRemoteCallTracer != null) {
                incomingRemoteCallTracer.end();
            }
        }
        return null;
    }

    private IncomingRemoteCallTracer getIncomingRemoteCallTracer(String tag, String method,
                                                                 String... requestAttributes) {
        IncomingRemoteCallTracer incomingRemoteCallTracer = null;
        if (optOneAgent != null) {
            incomingRemoteCallTracer = optOneAgent.traceIncomingRemoteCall(method, "remoteVendorBridge", "redisson://" + method);
            incomingRemoteCallTracer.setDynatraceStringTag(tag);
            incomingRemoteCallTracer.setProtocolName("redisson/remoting");

            if (requestAttributes.length > 0 && requestAttributes.length % 2 == 0) {
                for (var i = 0; i < requestAttributes.length; i += 2) {
                    optOneAgent.addCustomRequestAttribute(requestAttributes[i], requestAttributes[1]);
                }
            }

            incomingRemoteCallTracer.start();
        }
        return incomingRemoteCallTracer;
    }

}

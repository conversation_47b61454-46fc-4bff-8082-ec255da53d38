package com.ously.gamble.localisation.service;

import com.github.benmanes.caffeine.cache.LoadingCache;
import com.ously.gamble.api.cache.CachedMap;
import com.ously.gamble.api.cache.LCacheFactory;
import com.ously.gamble.api.consumable.ConsumableService;
import com.ously.gamble.api.games.GameManagementService;
import com.ously.gamble.api.localisation.*;
import com.ously.gamble.conditions.ConditionalOnNotMonitor;
import com.ously.gamble.localisation.persistence.model.LocalisedString;
import com.ously.gamble.localisation.persistence.repository.LocalisedStringRepository;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@ConditionalOnNotMonitor
public class LocalisationServiceImpl implements LocalisationService {

    static final LanguageCode DEFAULT_LANGCODE = LanguageCode.EN;

    static final Logger log = LoggerFactory.getLogger(LocalisationServiceImpl.class);


    private final LocalisedStringRepository lsRepo;

    private final Configuration freemarkerConfig;

    private final CachedMap<String, Localised> locales;

    private final LoadingCache<String, Template> templates;

    private final GameManagementService gmMgmt;

    private final Optional<ConsumableService> consSrv;


    public LocalisationServiceImpl(LocalisedStringRepository lsr, Configuration fmConfiguration,
                                   CachedMap<String, Localised> locales,
                                   LCacheFactory<String, Template> lcf,
                                   GameManagementService gmM,
                                   Optional<ConsumableService> conService) {
        this.lsRepo = lsr;
        this.freemarkerConfig = fmConfiguration;
        this.locales = locales;
        this.gmMgmt = gmM;
        this.consSrv = conService;
        this.templates = lcf.registerCacheLoader("localisedTemplates", 500, 5, 15 * 60,
                name -> getTemplateInternal(name, locales.get(name).getTemplate()));
    }

    private void addOrReplace(Localised localised) {
        locales.put(localised.getKey(), localised);
    }

    @Override
    public Localised findLocalised(String literal, LanguageCode langcode) {
        var key = literal + ':' + langcode;
        var localised = locales.get(key);
        if (localised == null) {
            var byLiteralIdAndLangcode = lsRepo.findByLiteralIdAndLangcode(literal, langcode);
            if (byLiteralIdAndLangcode.isPresent()) {
                localised = byLiteralIdAndLangcode.get().toDto();
            } else {
                localised = new Localised();
                localised.setId(-1L);
                localised.setLangcode(langcode);
                localised.setType(LocalisationType.TRANSIENT);
                localised.setTemplate(literal);
                localised.setLiteralId(literal);
            }
            addOrReplace(localised);
        }
        return localised;
    }

    @Override
    public List<Localised> getAll() {
        return lsRepo.findAll().stream().map(LocalisedString::toDto).toList();
    }

    @Override
    @Transactional
    public Localised updateLocalised(Localised localised) {
        var byId = lsRepo.findById(localised.getId());
        if (byId.isEmpty()) {
            return null;
        }
        var lsStr = byId.get();
        lsStr.updateModel(localised);
        lsRepo.save(lsStr);
        locales.put(localised.getKey(), localised);
        templates.invalidate(localised.getKey());
        return localised;
    }

    @Override
    @Transactional
    public Localised saveNewLocalised(Localised localised) {
        if (localised.getId() != null) {
            updateLocalised(localised);
        }
        var lStr = new LocalisedString();
        lStr.updateModel(localised);
        lStr = lsRepo.saveAndFlush(lStr);
        locales.put(localised.getKey(), localised);
        templates.invalidate(localised.getKey());
        return lStr.toDto();
    }

    @Override
    @Transactional
    public void deleteLocalised(Localised localised) {
        var byId = lsRepo.findById(localised.getId());
        if (byId.isEmpty()) {
            return;
        }
        lsRepo.delete(byId.get());
        locales.remove(localised.getKey());
        templates.invalidate(localised.getKey());
    }

    /**
     * Resolving templates
     */


    static Map<String, String> convertToVariableMap(String vars) {
        if (StringUtils.isBlank(vars)) {
            return Collections.emptyMap();
        }
        return Arrays.stream(vars.split(",")).map(v -> v.split("=")).filter(keyval -> keyval.length == 2).collect(Collectors.toMap(keyval -> keyval[0], keyval -> keyval[1], (a, a2) -> a2));
    }

    @Override
    public Map<String, String> resolveTemplatesForLang(String vars, LanguageCode code,
                                                       String... literals) {
        return resolveTemplatesForLang(convertToVariableMap(vars), code, literals);
    }

    @Override
    public Map<String, String> resolveTemplatesForLang(Map<String, String> varsIn,
                                                       LanguageCode code,
                                                       String...
                                                               literals) {
        Map<String, String> vars = varsIn == null ? Collections.emptyMap() : varsIn;
        Map<String, Object> objects = new HashMap<>(2);
        if (vars.containsKey("gameid")) {
            try {
                var gameid = gmMgmt.findById(Long.valueOf(vars.get("gameid")), null);
                gameid.ifPresent(casinoGame -> objects.put("game", casinoGame));
            } catch (Exception e) {
                log.warn("Error while trying to add game object to template-data-model", e);
            }
        }

        // productId (-> ConsumableId)
        if (consSrv.isPresent()) {
            if (vars.containsKey("productid")) {
                try {
                    var product = consSrv.get().getConsumable(Long.valueOf(vars.get("productid")));
                    if (product != null) {
                        objects.put("product", product);
                    }
                } catch (Exception e) {
                    log.warn("Error while trying to add product object to template-data-model", e);
                }
            }
        }

        Map<String, String> result = new HashMap<>(literals.length);
        for (var literal : literals) {
            try {
                var localised = findLocalised(literal, code);
                if (localised != null && localised.getType() != LocalisationType.TRANSIENT) {
                    var t = templates.get(localised.getKey());
                    if (t != null) {
                        result.put(literal, resolveFreemarkerTemplate(t, vars, objects));
                    } else {
                        result.put(literal, literal);
                    }
                } else {
                    var t = getTransientTemplate(literal);
                    if (t != null) {
                        result.put(literal, resolveFreemarkerTemplate(t, vars, objects));
                    } else {
                        result.put(literal, literal);
                    }
                }
            } catch (Exception e) {
                log.warn("Exception trying to resolve '{}_{}'", literal, code, e);
                result.put(literal, literal);
            }
        }
        return result;
    }

    @Override
    public Optional<Localised> getLocalised(long id) {
        return lsRepo.findById(id).map(LocalisedString::toDto);
    }

    @Override
    public Optional<Localised> updateLocalised(long id, Localised loc) {
        var byId = lsRepo.findById(id);
        if (byId.isEmpty()) {
            return Optional.empty();
        }

        byId.get().updateModel(loc);
        lsRepo.saveAndFlush(byId.get());

        var localised = byId.get().toDto();
        locales.put(localised.getKey(), localised);
        templates.invalidate(localised.getKey());

        return Optional.of(localised);
    }

    @Override
    public Optional<Localised> newLocalised(Localised loc) {
        var ls = new LocalisedString();
        ls.updateModel(loc);
        ls = lsRepo.saveAndFlush(ls);

        var localised = ls.toDto();
        locales.put(localised.getKey(), localised);
        templates.invalidate(localised.getKey());
        return Optional.of(localised);
    }

    @Override
    public Optional<LocalisedResult> testLocalised(long id, LocalisedRequest req) {
        return lsRepo.findById(id).map(a ->
                new LocalisedResult(resolveTemplatesForLang(req.variables(), a.getLangcode(),
                        a.getLiteralId()).get(a.getLiteralId())));
    }


    // Freemarker Templates

    private Template getTemplateInternal(String name, String templateBody) {
        try {
            return new Template(name, new StringReader(templateBody), freemarkerConfig);
        } catch (IOException e) {
            log.error("Error creating template {}/{}", name, templateBody, e);
        }
        return null;
    }

    private Template getTransientTemplate(String templateBody) {
        try {
            return new Template(templateBody, new StringReader(templateBody),
                    freemarkerConfig);
        } catch (IOException e) {
            log.error("Error creating transient template {}", templateBody, e);
        }
        return null;
    }

    private static String resolveFreemarkerTemplate(Template t, Map<String, String> vars,
                                                    Map<String, Object> objectMap) throws TemplateException, IOException {
        Map<Object, Object> root = new HashMap<>(vars.size() + objectMap.size());
        root.putAll(vars);
        root.putAll(objectMap);
        var strWrt = new StringWriter();
        t.process(root, strWrt);
        strWrt.flush();
        return strWrt.toString();
    }

}

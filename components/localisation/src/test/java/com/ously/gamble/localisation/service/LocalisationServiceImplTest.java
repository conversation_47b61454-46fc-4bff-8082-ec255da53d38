package com.ously.gamble.localisation.service;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class LocalisationServiceImplTest {

    @Test
    void convertToVariableMap() {

        var varMap = LocalisationServiceImpl.convertToVariableMap("");
        assertEquals(0, varMap.size());

        varMap = LocalisationServiceImpl.convertToVariableMap(null);
        assertEquals(0, varMap.size());

        varMap = LocalisationServiceImpl.convertToVariableMap("a");
        assertEquals(0, varMap.size());

        varMap = LocalisationServiceImpl.convertToVariableMap("a=1");
        assertEquals(1, varMap.size());
        assertEquals("1", varMap.get("a"));

        varMap = LocalisationServiceImpl.convertToVariableMap("a=1,b,c=2");
        assertEquals(2, varMap.size());
        assertEquals("1", varMap.get("a"));
        assertEquals("2", varMap.get("c"));


    }
}
plugins {
    id 'java-library'
}

repositories {
    mavenCentral()
}

dependencies {
    implementation project(':api')
    implementation project(':persistence')
    implementation "org.springframework.security:spring-security-crypto"
    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5',
            // Uncomment the next line if you want to use RSASSA-PSS (PS256, PS384, PS512) algorithms:
            //'org.bouncycastle:bcprov-jdk15on:1.60',
            'io.jsonwebtoken:jjwt-jackson:0.11.5' // or 'io.jsonwebtoken:jjwt-gson:0.11.2' for gson
    implementation group: 'org.bouncycastle', name: 'bcpkix-jdk15on', version: '1.70'

    testImplementation 'org.junit.jupiter:junit-jupiter-api'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine'
}

test {
    useJUnitPlatform()
}
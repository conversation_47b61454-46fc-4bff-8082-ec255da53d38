package com.ously.gamble.auth.firebase.payload;

public class FBSignInWithCustomTokenRequest {

    String token;
    boolean returnSecureToken;

    public FBSignInWithCustomTokenRequest() {
    }

    public FBSignInWithCustomTokenRequest(String token, boolean returnSecureToken) {
        this.token = token;
        this.returnSecureToken = returnSecureToken;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public boolean isReturnSecureToken() {
        return returnSecureToken;
    }

    public void setReturnSecureToken(boolean returnSecureToken) {
        this.returnSecureToken = returnSecureToken;
    }
}

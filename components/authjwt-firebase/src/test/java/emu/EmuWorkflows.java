package emu;

import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.internal.EmulatorCredentials;

import java.util.Map;

public class EmuWorkflows {

    public static void main(String[] args) {


        try {

            FirebaseApp.initializeApp(FirebaseOptions.builder().setProjectId("sa-emu").setCredentials(new EmulatorCredentials()).build());

            FirebaseAuth instance = FirebaseAuth.getInstance();
            String customToken = instance.createCustomToken("TESTID", Map.of("K1", "V1", "K2", "V2"));
            System.out.println(customToken);


        } catch (Exception e) {
            e.printStackTrace();
        }


    }


}

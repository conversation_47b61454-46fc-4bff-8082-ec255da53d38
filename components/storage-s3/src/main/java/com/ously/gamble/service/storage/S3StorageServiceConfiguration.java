package com.ously.gamble.service.storage;

import com.ously.gamble.api.storage.ImageStorageService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;


@Configuration
public class S3StorageServiceConfiguration {

    @Value("${cloud.aws.region.static:eu-central-1}")
    String region;

    @Value("${aws.s3.accessKey:X}")
    String s3AccessKey;
    @Value("${aws.s3.secretKey:X}")
    String s3SecretKey;
    @Value("${aws.s3.bucketname:spinarena-assets}")
    String bucketname;

    @Bean
    S3Client getAmazonS3() {
        return S3Client.builder().
                region(Region.of(region)).
                credentialsProvider(
                        StaticCredentialsProvider.create(AwsBasicCredentials.create(s3AccessKey, s3SecretKey)
                        ))
                .build();
    }

    @Bean
    ImageStorageService imageStorage(S3Client s3C) {
        return new S3ImageStorageServiceImpl(s3C, bucketname);
    }


}

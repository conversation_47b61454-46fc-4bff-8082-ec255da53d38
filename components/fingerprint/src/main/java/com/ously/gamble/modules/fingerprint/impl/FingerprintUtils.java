package com.ously.gamble.modules.fingerprint.impl;

import com.ously.gamble.modules.fingerprint.persistence.model.FPVisitor;

import java.util.Collections;
import java.util.Map;

final class FingerprintUtils {
    private static final Map<Object, Object> EMPTY_MAP = Collections.emptyMap();

    private FingerprintUtils() {
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    static void setExtractedAttributes(FPVisitor visitor) {
        if (visitor.getContent() == null || visitor.getContent().any().isEmpty()) {
            return;
        }
        Map<String, Object> any = visitor.getContent().any();
        visitor.setIp((String) any.getOrDefault("ip", null));
        Map ipLocation = (Map) any.getOrDefault("ipLocation", EMPTY_MAP);
        Map country = (Map) ipLocation.getOrDefault("country", EMPTY_MAP);
        if (!country.isEmpty()) {
            visitor.setCountry((String) country.getOrDefault("code", null));
        }
        Map browserDetails = (Map) any.getOrDefault("browserDetails", EMPTY_MAP);
        if (!browserDetails.isEmpty()) {
            visitor.setOs((String) browserDetails.getOrDefault("os", null));
            visitor.setOsVersion((String) browserDetails.getOrDefault("osVersion", null));
        }
    }

}

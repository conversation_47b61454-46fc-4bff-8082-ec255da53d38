package com.ously.gamble.monitoring;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.ously.gamble.monitoring.MonitoringConfig.MonitorTransportType.DIRECT;

@ConfigurationProperties(prefix = "monitoring")
@ConditionalOnProperty(prefix = "monitoring", name = "enabled", havingValue = "true")
@Component
public class MonitoringConfig {

    boolean enabled = false;

    boolean actions = false;

    String txQueueName = "mon_tx_q";

    String monitoredActionQueueName = "mon_action_q";

    /**
     * delay after which batches are sent into queue
     */
    int txQueueDelayMs = 1000;

    /**
     * max buffer Size of sink
     */
    int bufferSize = 100;

    /**
     * delay after which source batching is finished
     */
    int txSourceDelay = 1000;
    /**
     * max source batching size
     */
    int txSourceBuffer = 100;

    public boolean isActions() {
        return actions;
    }

    public void setActions(boolean actions) {
        this.actions = actions;
    }

    public String getMonitoredActionQueueName() {
        return monitoredActionQueueName;
    }

    public void setMonitoredActionQueueName(String monitoredActionQueueName) {
        this.monitoredActionQueueName = monitoredActionQueueName;
    }

    public int getTxSourceDelay() {
        return txSourceDelay;
    }

    public void setTxSourceDelay(int txSourceDelay) {
        this.txSourceDelay = txSourceDelay;
    }

    public int getTxSourceBuffer() {
        return txSourceBuffer;
    }

    public void setTxSourceBuffer(int txSourceBuffer) {
        this.txSourceBuffer = txSourceBuffer;
    }

    public String getTxQueueName() {
        return txQueueName;
    }

    public void setTxQueueName(String txQueueName) {
        this.txQueueName = txQueueName;
    }

    public int getTxQueueDelayMs() {
        return txQueueDelayMs;
    }

    public void setTxQueueDelayMs(int txQueueDelayMs) {
        this.txQueueDelayMs = txQueueDelayMs;
    }

    public int getBufferSize() {
        return bufferSize;
    }

    public void setBufferSize(int bufferSize) {
        this.bufferSize = bufferSize;
    }


    public enum MonitorTransportType {REDISSON, RABBIT, DIRECT}

    public static class MonitorConfig {

        /**
         * defines if that monitoring type is enabled
         */
        private boolean enabled;

        /**
         * fully qualified name of type class
         */
        private String itemClassname;

        /**
         * the target queue mechanism
         */
        private MonitorTransportType transportType=DIRECT;

        /**
         * Name of queue. The prefix "monitoring." is automatically added
         */
        private String queueName = "undef-" + UUID.randomUUID();

        /**
         * send batch if buffer reaches sendBatchSize
         */
        private int sendBatchSize=100;

        /**
         * send batch even if not fully filled after sendMaxDelay milliseconds
         */
        private int sendMaxDelay=1000;

        public String getItemClassname() {
            return itemClassname;
        }

        public void setItemClassname(String itemClassname) {
            this.itemClassname = itemClassname;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public MonitorTransportType getTransportType() {
            return transportType;
        }

        public void setTransportType(MonitorTransportType transportType) {
            this.transportType = transportType;
        }

        public String getQueueName() {
            return queueName;
        }

        public void setQueueName(String queueName) {
            this.queueName = queueName;
        }

        public int getSendBatchSize() {
            return sendBatchSize;
        }

        public void setSendBatchSize(int sendBatchSize) {
            this.sendBatchSize = sendBatchSize;
        }

        public int getSendMaxDelay() {
            return sendMaxDelay;
        }

        public void setSendMaxDelay(int sendMaxDelay) {
            this.sendMaxDelay = sendMaxDelay;
        }
    }

    private Map<String,MonitorConfig> monitor = new HashMap<>();

    public Map<String, MonitorConfig> getMonitor() {
        return monitor;
    }

    public void setMonitor(Map<String, MonitorConfig> monitor) {
        this.monitor = monitor;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

}

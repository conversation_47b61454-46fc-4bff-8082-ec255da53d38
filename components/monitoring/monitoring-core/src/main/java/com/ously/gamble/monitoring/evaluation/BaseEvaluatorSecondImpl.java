package com.ously.gamble.monitoring.evaluation;

import com.ously.gamble.api.monitoring.MonitoredGameTx;
import com.ously.gamble.api.monitoring.MonitoredItemsBatchEvent;
import com.ously.gamble.api.monitoring.MonitoredUpdateItem;
import com.ously.gamble.api.monitoring.MonitoredUpdateType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@ConditionalOnProperty(prefix = "monitor.core", name = "samples", havingValue = "true")
public class BaseEvaluatorSecondImpl {

    private final static Logger log = LoggerFactory.getLogger(BaseEvaluatorSecondImpl.class);

    @EventListener
    public MonitoredItemsBatchEvent<MonitoredUpdateItem> handleTx(MonitoredItemsBatchEvent<MonitoredGameTx> txItems) {
        // Just swallow and simulate some actions
        log.debug("Got (2) MonitoredGameTx batch of {} items", txItems.getItems().size());
        sleep(txItems.getItems().size() / 200);

        // create updates
        List<MonitoredUpdateItem> updates = new ArrayList<>(txItems.getItems().size());
        for (var tx : txItems.getItems()) {
            if (tx.userId() % 2 == 0) {
                updates.add(new MonitoredUpdateItem(MonitoredUpdateType.MISSION, tx.userId(), tx.gameId(), tx.bet() + tx.win()));
            }
        }
        if (!updates.isEmpty()) {
            return new MonitoredItemsBatchEvent<>(MonitoredUpdateItem.optimize(updates));
        } else {
            return null;
        }
    }

    private void sleep(long msToSleep) {
        try {
            Thread.sleep(msToSleep);
        } catch (InterruptedException ignored) {
        }
    }

}

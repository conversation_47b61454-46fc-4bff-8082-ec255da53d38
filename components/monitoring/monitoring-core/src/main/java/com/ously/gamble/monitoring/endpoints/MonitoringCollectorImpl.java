package com.ously.gamble.monitoring.endpoints;

import com.ously.gamble.api.monitoring.MonitoringSender;
import jakarta.annotation.PreDestroy;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.BaseCodec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.Trigger;
import org.springframework.scheduling.TriggerContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Indexed;

import java.lang.reflect.Array;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Buffers items that are added via addItem(). Periodically sends them in batches to the queue with the given name.
 */
@Indexed
public class MonitoringCollectorImpl<T> implements Runnable, Trigger, MonitoringSender<T> {
    final Logger log = LoggerFactory.getLogger(MonitoringCollectorImpl.class);

    private static final int MAX_DRAINSIZE = 500;
    private static final int MAX_DRAINWAIT = 1000;

    private final RBlockingQueue<T[]> sinkQueue;
    private final String name;

    private final List<T> updates = new ArrayList<>(MAX_DRAINSIZE);
    private final ThreadPoolTaskScheduler tpTS;
    private long drainageWaitTime = MAX_DRAINWAIT;
    private final BlockingQueue<T> queue = new LinkedBlockingQueue<>(25000);
    private ScheduledFuture<?> routerSchedule;
    private final AtomicLong sentBatches = new AtomicLong(0L);
    private final AtomicLong sentItems = new AtomicLong(0L);

    public MonitoringCollectorImpl(String drainToQueueName, RedissonClient redClient,
                                   ThreadPoolTaskScheduler tpTS, BaseCodec codec) {
        this.name = drainToQueueName;
        this.sinkQueue = redClient.getBlockingQueue(drainToQueueName, codec);
        this.tpTS = tpTS;
        startSchedule();
    }

    @PreDestroy
    public void shutdown() {
        log.info("Shutting down monitoring router '{}'", this.name);
        stopSchedule();
    }

    private void startSchedule() {
        if (tpTS != null) {
            routerSchedule = tpTS.schedule(this, this);
            log.info("Started monitoring router '{}'", name);
        }
    }

    private void stopSchedule() {
        if (routerSchedule != null) {
            if (!routerSchedule.isCancelled()) {
                routerSchedule.cancel(false);
            }

            while (!routerSchedule.isDone()) {
                try {
                    Thread.sleep(50);
                } catch (InterruptedException e) {
                    //
                }
            }
            routerSchedule = null;
        }
    }


    @Override
    public void addItem(T e) {
        var added = false;
        try {
            while (!added) {
                added = queue.offer(e, 250, TimeUnit.MILLISECONDS);
            }
            sentItems.incrementAndGet();
        } catch (Exception ex) {
            log.error("Error adding item", ex);
        }
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public int getCountOfSentBatches() {
        return sentBatches.intValue();
    }

    @Override
    public int getCountOfSentItems() {
        return sentItems.intValue();
    }

    @Override
    public void run() {

        var num = queue.drainTo(updates, MAX_DRAINSIZE);
        if (num > 0) {
            forwardItems(updates);
            updates.clear();
        }
        recalcWaitTime(num);
    }

    private void recalcWaitTime(int numDrained) {
        if (numDrained == MAX_DRAINSIZE) {
            drainageWaitTime = 0;
        } else if (numDrained > (MAX_DRAINSIZE / 2)) {
            drainageWaitTime -= 100;
        } else if (numDrained == 0) {
            drainageWaitTime += 100;
        } else {
            drainageWaitTime -= 50;
        }
        drainageWaitTime = Math.max(0, Math.min(drainageWaitTime, MAX_DRAINWAIT));
    }

    protected void forwardItems(List<T> updates) {
        if (!updates.isEmpty()) {
            sentBatches.incrementAndGet();
            T[] ts = (T[]) Array.newInstance(updates.getFirst().getClass(), updates.size());
            boolean sent = false;
            T[] batch = updates.toArray(ts);
            while (!sent) {
                try {
                    sent = this.sinkQueue.offer(batch, 5, TimeUnit.SECONDS);
                } catch (Throwable e) {
                    log.warn("offer failed, retrying", e);
                }
            }
        }
    }


    @Override
    public Instant nextExecution(TriggerContext triggerContext) {
        if (triggerContext.lastCompletion() == null) {
            return Instant.now().plus(2, ChronoUnit.SECONDS);
        }
        var lastFinishedEpochMilli = triggerContext.lastCompletion().toEpochMilli();
        return Instant.ofEpochMilli(lastFinishedEpochMilli + drainageWaitTime);
    }
}

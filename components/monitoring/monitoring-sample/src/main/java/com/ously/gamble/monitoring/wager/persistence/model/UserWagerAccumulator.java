package com.ously.gamble.monitoring.wager.persistence.model;

import jakarta.persistence.*;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "mon_user_wager_acc")
public class UserWagerAccumulator implements Persistable<Long> {

    @Transient
    boolean wasLoaded;


    @Id
    @Column(name = "user_id", nullable = false)
    long userId;

    @Column(name = "wager", nullable = false)
    long wager;

    @Column(name = "edge", nullable = false)
    long edge;

    @PostLoad
    @PostPersist
    public void setTransientLoaded() {
        this.wasLoaded = true;
    }

    @Override
    public Long getId() {
        return userId;
    }

    @Override
    public boolean isNew() {
        return !wasLoaded;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public long getWager() {
        return wager;
    }

    public void setWager(long wager) {
        this.wager = wager;
    }

    public long getEdge() {
        return edge;
    }

    public void setEdge(long edge) {
        this.edge = edge;
    }
}

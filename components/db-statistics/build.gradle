buildscript {

    dependencies {

        if (project.hasProperty("swagger")) {
            println "** NO bytecode enhancements"
        } else {
            println "** Using bytecode enhancements (dep)"
            classpath "org.hibernate.orm:hibernate-gradle-plugin:$hibernateVersion"
        }
    }
}

plugins {
    id 'java-library'
}


if (project.hasProperty("swagger")) {
    println "** NOT Using bytecode enhancements"

} else {
    println "** Using bytecode enhancements"
    apply plugin: 'org.hibernate.orm'
}

repositories {
    mavenCentral()
}

dependencies {
    implementation project(':api')
    implementation project(':persistence')
}

test {
    useJUnitPlatform()
}
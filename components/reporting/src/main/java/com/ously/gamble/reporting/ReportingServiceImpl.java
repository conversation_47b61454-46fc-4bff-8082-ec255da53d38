package com.ously.gamble.reporting;

import com.ously.gamble.api.session.SessionAdminService;
import com.ously.gamble.api.statistics.DailyProviderStatistics;
import com.ously.gamble.api.statistics.ReportingService;
import com.ously.gamble.api.vendor.VendorService;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Indexed;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("ALL")
@Indexed
public class ReportingServiceImpl extends BaseReportingService implements ReportingService {
    final Logger log = LoggerFactory.getLogger(ReportingServiceImpl.class);

    final
    SessionAdminService sessionService;

    final
    VendorService vService;

    public ReportingServiceImpl(SessionAdminService sessionService, VendorService vService) {
        this.sessionService = sessionService;
        this.vService = vService;
    }

    @Override
    public byte[] reportTotalsForMonth(int year, int month, String provider,
                                       Boolean excludeOusly) throws Exception {
        log.info("Start reporting for {}", (provider == null) ? "ALL providers" : provider);
        var wb = createWorkbookFromResource("/reports/playReport.xlsx");

        // now update totals
        var total = updateTotals(wb, year, month, excludeOusly);
        log.info("Report: totals loaded ({} sessions)", total.sessions);

        if (provider != null) {
            var nSheet = cloneSheet(wb, "Provider", provider);
            addProvider(wb, year, month, provider, excludeOusly);
        } else {
            List<ProviderTotal> providerTotals = new ArrayList<>();
            // ALL Providers!
            var allProviders = vService.getAllProviders();
            for (var csp : allProviders) {
                if (csp.getActive()) {
                    try {
                        log.info("Report: creating sheet for provider {}", csp.getBridgeName());
                        var nSheet = cloneSheet(wb, "Provider", csp.getBridgeName());
                        providerTotals.add(addProvider(wb, year, month, csp.getBridgeName(), excludeOusly));
                    } catch (Exception e) {
                        log.error("Error reporting:", e);
                    }
                }
            }

            // Calculate RevShare
            log.info("Report: calculating revshares");
            calculateRevShare(providerTotals, total);

            // now add totals for each providers
            var rowNum = 4;
            for (var pt : providerTotals) {
                var tSheet = wb.getSheet("Total");
                setCellValueString(tSheet, rowNum, 8, pt.name);
                setCellValueNumeric(tSheet, rowNum, 9, pt.spins);
                setCellValueNumeric(tSheet, rowNum, 10, pt.sessions);
                setCellValueNumeric(tSheet, rowNum, 11, pt.sumBet);
                setCellValueNumeric(tSheet, rowNum, 12, pt.sumWin);
                setCellValueNumeric(tSheet, rowNum, 13, pt.revShare);
                rowNum++;
            }

        }
        log.info("Report: finalizing report");
        // remove template sheet for providers
        var prTmpl = wb.getSheet("Provider");
        wb.removeSheetAt(wb.getSheetIndex(prTmpl));
        return writeWorkbookToByteArray(wb);
        // TODO: Finally wb.close
    }

    private void calculateRevShare(List<ProviderTotal> providerTotals, ProviderTotal total) {
        var totalSpins = new BigDecimal(total.spins);
        var totalBets = total.sumBet;

        log.info("Calc RevShare (spins={}, bets={})", totalSpins, totalBets);
        for (var pt : providerTotals) {
            log.info("Calc Share for {} using spins={},bets={}", pt.name, pt.spins, pt.sumBet);
            try {
                var mc = new MathContext(4, RoundingMode.HALF_UP);
                var shareSpins = (pt.spins == 0) ? BigDecimal.ZERO : BigDecimal.valueOf(pt.spins * 100).divide(totalSpins, mc).setScale(2, RoundingMode.HALF_UP);
                var shareBets = (pt.sumBet.signum() == 0) ? BigDecimal.ZERO : pt.sumBet.multiply(BigDecimal.valueOf(100)).divide(totalBets, mc).setScale(2, RoundingMode.HALF_UP);
                pt.revShare = shareSpins.add(shareBets).divide(BigDecimal.valueOf(2), mc).setScale(2, RoundingMode.HALF_UP);
            } catch (Exception e) {
                log.warn("error calc Revshare: {}", e.getMessage(), e);
                pt.revShare = BigDecimal.ZERO;
            }
        }

    }

    private ProviderTotal addProvider(Workbook wb, int year, int month, String provider,
                                      Boolean excludeOusly) {
        var sTotal = wb.getSheet(provider);
        setCellValueString(sTotal, 0, 1, provider);
        setCellValueNumeric(sTotal, 1, 1, (month + 1));
        setCellValueNumeric(sTotal, 2, 1, (year));

        var from = LocalDate.of(year, month + 1, 1);
        var to = from.plusMonths(1);

        var monthlyTotal = sessionService.getMonthlyProviderStats(provider, from, to, true, excludeOusly);
        var pt = fillTotals(sTotal, monthlyTotal);
        pt.name = provider;
        // Add games statistic
        var providerGameStats = sessionService.getProviderGameStats(provider, from, to, excludeOusly);
        var row = 4;
        for (var gs : providerGameStats) {
            setCellValueNumeric(sTotal, row, 8, gs.getGameId());
            setCellValueString(sTotal, row, 9, gs.getGameName());
            setCellValueString(sTotal, row, 10, gs.getGameKey());
            setCellValueNumeric(sTotal, row, 11, gs.getSessionCount());
            setCellValueNumeric(sTotal, row, 12, gs.getSpins());
            setCellValueNumeric(sTotal, row, 13, gs.getSumBet());
            setCellValueNumeric(sTotal, row, 14, gs.getSumWin());
            setCellValueNumeric(sTotal, row, 15, gs.getAvgSessionDuration().longValue());
            setCellValueString(sTotal, row, 16, gs.getActive().toString());
            setCellValueString(sTotal, row, 17, gs.getActiveIOS().toString());
            setCellValueString(sTotal, row, 18, gs.getActiveAndroid().toString());
            row++;
        }

        return pt;

    }


    /**
     * @param wb    the workbook to use
     * @param year  the year (absolute)
     * @param month the month (0 based)
     */
    private ProviderTotal updateTotals(Workbook wb, long year, int month, boolean excludeOusly) {
        var sTotal = wb.getSheet("Total");
        setCellValueString(sTotal, 0, 1, "All providers");
        setCellValueNumeric(sTotal, 1, 1, (month + 1));
        setCellValueNumeric(sTotal, 2, 1, (year));
        var monthlyTotal = sessionService.getMonthlyTotal((int) year, month + 1, true, excludeOusly);
        return fillTotals(sTotal, monthlyTotal);
    }

    private static ProviderTotal fillTotals(Sheet sTotal,
                                            List<DailyProviderStatistics> monthlyTotal) {
        var row = 4;
        var sessionTotal = 0L;
        var spinsTotal = 0L;
        var sumBet = BigDecimal.ZERO;
        var sumWin = BigDecimal.ZERO;
        for (var ds : monthlyTotal) {
            setCellValueNumeric(sTotal, row, 0, ds.getDate().getDayOfMonth());
            setCellValueNumeric(sTotal, row, 1, ds.getCountSessions());
            setCellValueNumeric(sTotal, row, 2, ds.getAvgSessionDuration().longValue());
            setCellValueNumeric(sTotal, row, 3, ds.getCountSpins());
            setCellValueNumeric(sTotal, row, 4, ds.getSumBet());
            setCellValueNumeric(sTotal, row, 5, ds.getSumWin());

            sessionTotal += ds.getCountSessions();
            spinsTotal += ds.getCountSpins();
            sumBet = sumBet.add(ds.getSumBet());
            sumWin = sumWin.add(ds.getSumWin());

            row++;
        }

        setCellValueNumeric(sTotal, 0, 3, sessionTotal);
        setCellValueNumeric(sTotal, 1, 3, spinsTotal);
        setCellValueNumeric(sTotal, 0, 5, sumBet);
        setCellValueNumeric(sTotal, 1, 5, sumWin);

        var pTotal = new ProviderTotal();
        pTotal.sessions = sessionTotal;
        pTotal.spins = spinsTotal;
        pTotal.sumBet = sumBet;
        pTotal.sumWin = sumWin;
        return pTotal;
    }


    static class ProviderTotal {
        String name;
        BigDecimal sumWin;
        BigDecimal sumBet;
        Long sessions;
        Long spins;
        BigDecimal revShare;
    }

}

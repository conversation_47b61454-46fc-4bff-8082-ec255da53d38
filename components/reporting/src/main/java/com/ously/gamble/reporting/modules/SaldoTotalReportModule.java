package com.ously.gamble.reporting.modules;

import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.conditions.ConditionalOnSocial;
import com.ously.gamble.persistence.projections.UserTransactionDailyBreakdownPJ;
import com.ously.gamble.reporting.BaseReportingContext;
import com.ously.gamble.reporting.BaseReportingService;
import com.ously.gamble.reporting.ReportModule;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Component
@ConditionalOnOffloader
@ConditionalOnSocial
public class SaldoTotalReportModule extends BaseReportingService implements ReportModule {

    public record SaldoRow(LocalDate day, Map<String, BigDecimal> sum) {
    }

    public record SaldoTotals(Set<String> types, List<SaldoRow> rows) {
    }


    @Override
    public void updateSheet(Workbook wb, BaseReportingContext ctx, Instant from, Instant to) {
        updateSaldoSheet(wb, ctx, from, to);
    }

    private void updateSaldoSheet(Workbook wb, BaseReportingContext ctx, Instant from, Instant to) {
        List<UserTransactionDailyBreakdownPJ> dailyBreakdown = (List<UserTransactionDailyBreakdownPJ>) ctx.getObject("utxtypedaily", Collections.emptyList());

        if (dailyBreakdown.isEmpty()) {
            return;
        }

        var sheet = wb.getSheet("SaldoData");
        var data = pivotTotals(dailyBreakdown);
        var row = 1;
        var types = data.types();
        var dtFormat = getDateCellStyle(wb);

        var col = 0;
        setCellValueString(sheet, 0, col++, "Date");
        for (String type : types) {
            setCellValueString(sheet, 0, col++, type);
        }
        setCellValueString(sheet, 0, col, "Total");


        for (var d : data.rows()) {
            BigDecimal total = BigDecimal.ZERO;
            setCellValueDate(sheet, row, 0, d.day, dtFormat);
            col = 1;
            for (String type : types) {
                BigDecimal val = d.sum().getOrDefault(type, BigDecimal.ZERO);
                setCellValueNumeric(sheet, row, col, val);
                total = total.add(val);
                col++;
            }

            setCellValueNumeric(sheet, row, col, total);
            row++;
        }

    }

    public static SaldoTotals pivotTotals(List<UserTransactionDailyBreakdownPJ> data) {

        List<LocalDate> dates = data.stream().map(UserTransactionDailyBreakdownPJ::getDay).collect(Collectors.toSet()).stream().sorted().toList();
        Set<String> types = new TreeSet<>();
        Map<LocalDate, SaldoRow> rows = new HashMap<>();

        for (var d : data) {
            SaldoRow saldoRow = rows.computeIfAbsent(d.getDay(), key -> new SaldoRow(key, new HashMap<>()));
            saldoRow.sum().put(d.getType(), d.getCoins());
            types.add(d.getType());
        }

        // Now reorder
        var sortedRows = rows.keySet().stream().sorted().map(rows::get).toList();
        return new SaldoTotals(types, sortedRows);
    }

}

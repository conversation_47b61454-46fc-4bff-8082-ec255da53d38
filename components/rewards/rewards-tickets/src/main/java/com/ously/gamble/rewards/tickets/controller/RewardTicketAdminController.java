package com.ously.gamble.rewards.tickets.controller;

import com.ously.gamble.api.rewards.tickets.AwardedRewardTicket;
import com.ously.gamble.api.rewards.tickets.RewardTicketDto;
import com.ously.gamble.api.rewards.tickets.RewardTicketService;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@ConditionalOnOffloader
@RestController
@RequestMapping("/api/admin/rewards/tickets")
public class RewardTicketAdminController {
    final RewardTicketService rewTicketService;

    public RewardTicketAdminController(@Autowired(required = false)
            RewardTicketService rewTicketService) {
        this.rewTicketService = rewTicketService;
    }


    @Operation(description = "add reward ticket",
            security = {@SecurityRequirement(name = "bearer-key")})
    @RolesAllowed("ADMIN")
    @PostMapping("/")
    public ResponseEntity<RewardTicketDto> addNewRewardTicket(
            @RequestBody AwardedRewardTicket req) {
        if (rewTicketService == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.of(rewTicketService.createNewTicket(req));
    }


}

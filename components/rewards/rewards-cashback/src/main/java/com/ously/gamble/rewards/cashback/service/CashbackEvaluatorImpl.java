package com.ously.gamble.rewards.cashback.service;

import com.ously.gamble.rewards.cashback.CashbackConfig;
import com.ously.gamble.rewards.cashback.api.CashbackEvalData;
import com.ously.gamble.rewards.cashback.api.CashbackEvalResult;
import com.ously.gamble.rewards.cashback.api.CashbackEvaluator;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class CashbackEvaluatorImpl implements CashbackEvaluator {

    @Override
    public CashbackEvalResult evaluateCashback(CashbackEvalData data, CashbackConfig cbConfig) {

        // Resulting cashback amount

        BigDecimal cbAmount = BigDecimal.ZERO;

        // Do stats summing
        BigDecimal sumDeposits = BigDecimal.ZERO;
        BigDecimal sumDepositsScaled;
        for (var deposit : data.getDeposits()) {
            if (deposit.bookedAt().isAfter(data.getStartOfEvalPeriod().minusSeconds(1)) && deposit.bookedAt().isBefore(data.getEndOfEvalPeriod())) {
                sumDeposits = sumDeposits.add(deposit.targetAmount());
            }
        }

        if (data.getDepositFactorOverride() != null && data.getDepositFactorOverride().signum() > 0) {
            sumDepositsScaled = sumDeposits.multiply(data.getDepositFactorOverride());
        } else {
            sumDepositsScaled = sumDeposits.multiply(cbConfig.getCashbackFactorForDepositAmount(sumDeposits));
        }

        BigDecimal sumWithdraws = BigDecimal.ZERO;
        for (var withdraw : data.getPayouts()) {
            if (withdraw.createdAt().isAfter(data.getStartOfEvalPeriod()) && withdraw.createdAt().isBefore(data.getEndOfEvalPeriod())) {
                sumWithdraws = sumWithdraws.add(withdraw.srcAmount());
            }
        }

        BigDecimal cbFactor = BigDecimal.ZERO;
        BigDecimal lostAmount = sumDeposits.subtract(sumWithdraws).subtract(data.getLatestBalance().subtract(data.getStartBalance()));
        // 1000 depo, 0 lost => factor 0
        // 1000 depo, 500 lost => factor 0,5

        lostAmount = lostAmount.min(sumDeposits);
        if (lostAmount.doubleValue() < 0.5d) {
            lostAmount = BigDecimal.ZERO;
        }

        if (lostAmount.signum() > 0 && sumDeposits.signum() > 0) {
            cbFactor = lostAmount.divide(sumDeposits, 4, RoundingMode.HALF_UP);
        }


        if (cbFactor.signum() > 0 && sumDepositsScaled.signum() > 0) {
            cbAmount = sumDepositsScaled.multiply(cbFactor);
        }

        // EvalResult

        CashbackEvalResult result = new CashbackEvalResult();
        result.setSumDeposits(sumDeposits);
        result.setSumWithdraws(sumWithdraws);
        result.setCashbackAmount(cbAmount);
        result.setCashbackPercentOfDeposits(cbFactor);
        result.setLostAmount(lostAmount);
        result.setSumFactoredDepositAmounts(sumDepositsScaled);
        result.setWager(15);
        return result;
    }


}

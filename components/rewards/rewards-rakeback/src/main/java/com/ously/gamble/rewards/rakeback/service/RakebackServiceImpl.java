package com.ously.gamble.rewards.rakeback.service;

import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.crm.CRMUserEvent;
import com.ously.gamble.api.crm.CRMUserUpdateRequest;
import com.ously.gamble.api.features.LevelManagerService;
import com.ously.gamble.api.rewards.*;
import com.ously.gamble.api.user.UserTransactionService;
import com.ously.gamble.api.user.UserTxRequest;
import com.ously.gamble.api.util.LockProvider;
import com.ously.gamble.persistence.model.user.UserTransactionType;
import com.ously.gamble.persistence.repository.WalletRepository;
import com.ously.gamble.persistence.repository.statistics.UserStatsDailyRepository;
import com.ously.gamble.rewards.rakeback.persistence.model.UserRakeback;
import com.ously.gamble.rewards.rakeback.persistence.model.UserRakebackId;
import com.ously.gamble.rewards.rakeback.persistence.repository.UserRakebackRepository;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

@Service
@ConditionalOnProperty(prefix = "features", name = "rakebacks", havingValue = "true")
public class RakebackServiceImpl implements RakebackService {
    private static final BigDecimal BD100 = new BigDecimal(100L);
    private static final Logger log = LoggerFactory.getLogger(RakebackServiceImpl.class);
    private final UserRakebackRepository urbRepo;

    private final UserStatsDailyRepository usdRepo;

    private final UserTransactionService utxSrv;

    private final LockProvider lkProvider;

    private final WalletRepository walletRepo;

    private final LevelManagerService lvlService;
    private final ApplicationEventPublisher eventPublisher;

    public RakebackServiceImpl(UserRakebackRepository urbRepo, UserStatsDailyRepository usdRep,
                               LockProvider lkP, UserTransactionService utxS,
                               ApplicationEventPublisher eventPublisher,
                               WalletRepository wRep, LevelManagerService lvlService) {
        this.urbRepo = urbRepo;
        this.usdRepo = usdRep;
        this.lkProvider = lkP;
        this.utxSrv = utxS;
        this.walletRepo = wRep;
        this.lvlService = lvlService;
        this.eventPublisher = eventPublisher;
    }

    @PostConstruct
    @SuppressWarnings("all")
    public void prepareService() {
        // TODO: level parameter map
    }

    @Override
    public RakebackEstimationResult getRakebackEstimationResult(
            long userId) {
        var nDate = LocalDate.now();
        var dDate = nDate.minusDays(1);
        var wDate = nDate.minusDays(nDate.getDayOfWeek().getValue() - 1).minusWeeks(1);
        var mDate = nDate.withDayOfMonth(1).minusMonths(1);

        var lParams =
                lvlService.getLevelInfo(Math.toIntExact(walletRepo.getUserLevel(userId))).getParameter();
        var dResult = calculateRakeback(userId, RakebackPeriod.DAILY
                , dDate);
        var wResult = calculateRakeback(userId, RakebackPeriod.WEEKLY
                , wDate);
        var mResult = calculateRakeback(userId, RakebackPeriod.MONTHLY
                , mDate);

        return new RakebackEstimationResult(lParams, dResult, wResult, mResult);
    }

    @Override
    @Transactional
    public void checkUserRakebacks(long userId, LocalDate dDate, LocalDate wDate, LocalDate mDate) {

        Lock rakebackCheckLock = null;
        try {
            rakebackCheckLock = lkProvider.getLockedLockTx("rakebackChecks", userId, 2,
                    TimeUnit.SECONDS);

            if (dDate != null) {
                if (!urbRepo.existsById(new UserRakebackId(userId, dDate, RakebackPeriod.DAILY))) {
                    log.info("No daily Rakeback for user {} and date {} found. ", userId, dDate);

                    var rcResult = calculateRakeback(userId, RakebackPeriod.DAILY
                            , dDate);

                    createRakebackForPeriod(userId, dDate, rcResult.amount(), RakebackPeriod.DAILY,
                            dDate.atStartOfDay().plusDays(2).toInstant(ZoneOffset.UTC), rcResult.valuesUsed());
                }
            }
            if (wDate != null) {
                if (!urbRepo.existsById(new UserRakebackId(userId, wDate, RakebackPeriod.WEEKLY))) {
                    log.info("No weekly Rakeback for user {} and date {} found. ", userId, wDate);

                    var rcResult = calculateRakeback(userId, RakebackPeriod.WEEKLY
                            , wDate);


                    createRakebackForPeriod(userId, wDate, rcResult.amount(), RakebackPeriod.WEEKLY,
                            wDate.atStartOfDay().plusWeeks(2).toInstant(ZoneOffset.UTC), rcResult.valuesUsed());
                }
            }
            if (mDate != null) {
                if (!urbRepo.existsById(new UserRakebackId(userId, mDate, RakebackPeriod.MONTHLY))) {
                    log.info("No monthly Rakeback for user {} and date {} found. ", userId, mDate);

                    var rcResult = calculateRakeback(userId, RakebackPeriod.MONTHLY
                            , mDate);

                    createRakebackForPeriod(userId, mDate, rcResult.amount(), RakebackPeriod.MONTHLY,
                            mDate.atStartOfDay().plusMonths(2).toInstant(ZoneOffset.UTC), rcResult.valuesUsed());
                }
            }
        } catch (Exception e) {
            log.error("Error checking rakebacks for user ({}): ", userId, e);
        } finally {
            if (rakebackCheckLock != null) {
                rakebackCheckLock.unlock();
            }
        }
    }

    private RakebackCalculationResult calculateRakeback(long userId, RakebackPeriod period,
                                                        LocalDate from) {
        var to = switch (period) {
            case DAILY -> from.plusDays(1);
            case WEEKLY -> from.plusWeeks(1);
            case MONTHLY -> from.plusMonths(1);
        };

        var usdAgg = usdRepo.aggregateDailyStatsForUserAndPeriod(userId, from, to);

        var lParams =
                lvlService.getLevelInfo(Math.toIntExact(walletRepo.getUserLevel(userId))).getParameter();
        if (lParams == null) {
            return new RakebackCalculationResult(0, "");
        }
        var amountD = switch (period) {
            case DAILY -> (lParams.daily().enabled() ? 1 : 0) *
                    (usdAgg.bets().doubleValue() * lParams.daily().wagerFactor() +
                            usdAgg.hedge().doubleValue() * lParams.daily().hedgeFactor() +
                            (usdAgg.bets().doubleValue() - usdAgg.wins().doubleValue()) * lParams.daily().ggrFactor());
            case WEEKLY -> (lParams.weekly().enabled() ? 1 : 0) *
                    (usdAgg.bets().doubleValue() * lParams.weekly().wagerFactor() +
                            usdAgg.hedge().doubleValue() * lParams.weekly().hedgeFactor() +
                            (usdAgg.bets().doubleValue() - usdAgg.wins().doubleValue()) * lParams.weekly().ggrFactor());
            case MONTHLY -> (lParams.monthly().enabled() ? 1 : 0) *
                    (usdAgg.bets().doubleValue() * lParams.monthly().wagerFactor() +
                            usdAgg.hedge().doubleValue() * lParams.monthly().hedgeFactor() +
                            (usdAgg.bets().doubleValue() - usdAgg.wins().doubleValue()) * lParams.monthly().ggrFactor());
        };

        return new RakebackCalculationResult((int) (amountD * 100), String.format(Locale.US, "%.2f,%" +
                        ".2f,%" +
                        ".2f",
                usdAgg.bets(), usdAgg.wins(), usdAgg.hedge()));

    }

    private void createRakebackForPeriod(long userId, LocalDate dDate, int amount,
                                         RakebackPeriod period, Instant expiry, String values) {
        var urb = new UserRakeback();
        urb.setAmount(amount);
        urb.setUserId(userId);
        urb.setrDate(dDate);
        urb.setPeriod(period);
        urb.setExpireAt(expiry);
        urb.setValues(values);
        urbRepo.saveAndFlush(urb);

        publishEvent(new CRMUserEvent(userId, "RAKEBACK_ISSUED", "amount", BigDecimal.valueOf(amount).divide(BD100, 2, RoundingMode.HALF_UP), "period", period.name(), "values", values));
    }

    @Override
    @Transactional
    public List<UserRakebackDto> getUnclaimedRakebacksForUser(long userId) {
        return urbRepo.getUnclaimedRakebacksForUser(userId);
    }

    @Override
    @Transactional
    public List<UserRakebackDto> getRakebacksForUser(long userId, Pageable pgl) {
        return urbRepo.getRakebacksForUser(userId, pgl.getPageNumber() * pgl.getPageSize(), pgl.getPageSize());
    }

    @Override
    @Transactional
    public List<UserRakebackDto> claimUnclaimedRakebacks(long userId) {
        Lock claimLock = null;
        try {
            claimLock = lkProvider.getLockedLockTx("claimRB", userId, 5, TimeUnit.SECONDS);
            List<UserRakebackDto> claimedRBs = new ArrayList<>(0);
            var unclaimedRakebacksForUser = urbRepo.getUnclaimedRakebacksForUser(userId);
            for (var rb : unclaimedRakebacksForUser) {
                claimedRBs.add(claimRakeback(rb));
            }
            publishEvent(new CRMUserUpdateRequest(userId));
            return claimedRBs;
        } catch (Exception e) {
            log.error("Error claiming users ({}) rakebacks:", userId, e);
        } finally {
            if (claimLock != null) {
                claimLock.unlock();
            }
        }
        return Collections.emptyList();
    }

    private UserRakebackDto claimRakeback(UserRakebackDto rb) throws OuslyTransactionException {

        var utReq = new UserTxRequest();
        utReq.setUserId(rb.userId());
        utReq.setAddWager(BigDecimal.ZERO);
        utReq.setCredit(BigDecimal.valueOf(rb.amount()).divide(BD100, 2, RoundingMode.DOWN));
        utReq.setWithdraw(BigDecimal.ZERO);
        utReq.setPiggyPossible(false);
        utReq.setStorePrices(false);
        utReq.setType(UserTransactionType.RAKEBACK_BONUS);
        utReq.setDescription("RAKEBACK:" + rb.period() + ':' + rb.rDate());
        utReq.setTxRef(utReq.getDescription());
        var userTxResponse = utxSrv.addTransaction(utReq);
        var byId = urbRepo.findById(new UserRakebackId(rb.userId(), rb.rDate(),
                RakebackPeriod.fromDesignator(rb.period()))).get();
        byId.setUtxId(userTxResponse.getTxId());
        urbRepo.save(byId);
        publishEvent(new CRMUserEvent(rb.userId(), "RAKEBACK_CLAIMED", "amount", userTxResponse.getCredit(), "period", rb.period(), "values", rb.vals()));

        return new UserRakebackDto(rb.userId(), rb.rDate(), rb.period(), rb.amount(),
                rb.expireAt(), userTxResponse.getTxId(), rb.vals());
    }

    private void publishEvent(Object event) {
        if (eventPublisher != null) {
            eventPublisher.publishEvent(event);
        }
    }


}

package com.ously.gamble.appsflyer.scheduler;

import com.ously.gamble.api.appsflyer.AFCampaignInfoService;
import com.ously.gamble.api.maintenance.ScheduleExecutionService;
import com.ously.gamble.api.maintenance.ScheduledTaskInformation;
import com.ously.gamble.appsflyer.configuration.AppsflyerConfiguration;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@ConditionalOnOffloader
@Component
@ConditionalOnProperty(prefix = "appsflyer", name = "scheduleEnabled", havingValue = "true")

public class AppsflyerUpdateScheduler {
    private static final Logger log = LoggerFactory.getLogger(AppsflyerUpdateScheduler.class);
    private final AppsflyerConfiguration afConfig;
    private final ScheduleExecutionService schedSrv;
    private final AFCampaignInfoService afCampaignInfoService;

    public AppsflyerUpdateScheduler(AppsflyerConfiguration afConfig,
                                    ScheduleExecutionService schedSrv,
                                    @Autowired(required = false) AFCampaignInfoService afSrv) {
        this.afConfig = afConfig;
        this.schedSrv = schedSrv;
        this.afCampaignInfoService = afSrv;
    }


    // Once a day 05:15:01
    @Scheduled(cron = "5 15 1 * * ?")
    @SchedulerLock(name = "appsflyer_daily_update", lockAtLeastFor = "PT5M")
    void closeExpiredDoubleUps() {
        schedSrv.doSchedule(new ScheduledTaskInformation("appsflyer", "daily_update", null), this::doActualDailyAppsflyerUpdate);
    }

    private ScheduledTaskInformation doActualDailyAppsflyerUpdate(ScheduledTaskInformation sti) {
        if (afCampaignInfoService == null) {
            log.info("Appsflyer disabled");
            return sti;
        } else {
            afCampaignInfoService.updateInfos();
        }

        return sti;
    }

}

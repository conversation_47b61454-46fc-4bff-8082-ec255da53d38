package com.ously.gamble.appsflyer.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "appsflyer")
public class AppsflyerConfiguration {

    /**
     * appsflyer pull api enabled
     */
    boolean enabled = true;

    /**
     * appsflyer predefined data instead of calling the api
     * (due to limits)
     */
    boolean mockData = true;

    /**
     * enable the appsflyer update schedule
     */
    boolean scheduleEnabled;


    String pullToken = "eyJhbGciOiJBMjU2S1ciLCJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwidHlwIjoiSldUIiwiemlwIjoiREVGIn0.9PvovgZxKLb7QGTKPwvCSkJMm7ex0BWijbIRIH-T-gSuqlHkD4PuLQ.TTEKt1b9_cBjKYJL.uinnaUEM9l3SAXZxJ_dAWvdP5vdVf_CzSITa9fskIgvs7lrXMRxCDUhimFMKW6_pzSZfX0wZQf2F5Kb5M01M5ck3VLEmAIv1usrQf2Gwqza-STAFDEObvfriF6YN4qeTDv_OmmMdrCFW3cpjrYTglPZLaGCPTM6a2sMxcOgV4eVcqEwpxCwMXS8e4cHfvdPEJfCGpmK6pW5THeNPO0mjSW7720uTHYoC5fH341I8WyM-pdyGWj3yLla3PsT3c0N_TvoCvSqUgy9PedyvPulk6MlHQsnJ4pgsu4i5IU16U5UhwbOXZEf_LaL_eePj946L3OLtGkf1cocpgAL2OYOwDmY0Pg.xj1al6NFwrGK35G8AFDrDA";

    /**
     * the ios app id on appsflyer
     */
    String iosId = "id1491713965";
    /**
     * the android app id on appsflyer
     */
    String androidId = "de.ouslygames.spinarena";

    String afPullUrl = "https://hq1.appsflyer.com/api/agg-data/export/app/##ID##/partners_by_date_report/v5";


    /**
     * Number of days to include in regular update
     */
    int updateLookbackDays = 30;


    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isScheduleEnabled() {
        return scheduleEnabled;
    }

    public void setScheduleEnabled(boolean scheduleEnabled) {
        this.scheduleEnabled = scheduleEnabled;
    }


    public int getUpdateLookbackDays() {
        return updateLookbackDays;
    }

    public void setUpdateLookbackDays(int updateLookbackDays) {
        this.updateLookbackDays = updateLookbackDays;
    }

    public String getPullToken() {
        return pullToken;
    }

    public void setPullToken(String pullToken) {
        this.pullToken = pullToken;
    }

    public String getIosId() {
        return iosId;
    }

    public void setIosId(String iosId) {
        this.iosId = iosId;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId;
    }

    public String getAfPullUrl() {
        return afPullUrl;
    }

    public void setAfPullUrl(String afPullUrl) {
        this.afPullUrl = afPullUrl;
    }

    public boolean isMockData() {
        return mockData;
    }

    public void setMockData(boolean mockData) {
        this.mockData = mockData;
    }
}

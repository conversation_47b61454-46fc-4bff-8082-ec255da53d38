package com.ously.gamble.appsflyer.service;

import com.ously.gamble.api.appsflyer.CampaignInfo;
import com.ously.gamble.api.appsflyer.Platform;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.IOUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class CsvToCampaignInfoHelper {

    public static List<CampaignInfo> convertCSVToCampaignInfo(String csv, Platform platform) {
        return convertCSVToCampaignInfo(IOUtils.toInputStream(csv, "UTF-8"), platform);
    }

    public static List<CampaignInfo> convertCSVToCampaignInfo(InputStream csv, Platform platform) {

        try (BufferedReader fileReader = new BufferedReader(new InputStreamReader(csv, StandardCharsets.UTF_8));

             CSVParser csvParser = new CSVParser(fileReader,
                     CSVFormat.DEFAULT.withFirstRecordAsHeader().withIgnoreHeaderCase().withTrim())) {

            List<CampaignInfo> campaignInfos = new ArrayList<>();

            Iterable<CSVRecord> csvRecords = csvParser.getRecords();

            for (CSVRecord csvRecord : csvRecords) {
                CampaignInfo info = new CampaignInfo(
                        LocalDate.parse(csvRecord.get("Date")),
                        platform,
                        csvRecord.get("Agency/PMD (af_prt)"),
                        csvRecord.get("Media Source (pid)"),
                        csvRecord.get("Campaign (c)"),
                        getIntegerFromValue(csvRecord.get("Impressions")),
                        getIntegerFromValue(csvRecord.get("Clicks")),
                        getDecimalFromValue(csvRecord.get("CTR")),
                        getIntegerFromValue(csvRecord.get("Installs")),
                        getDecimalFromValue(csvRecord.get("Conversion Rate")),

                        getIntegerFromValue(csvRecord.get("Sessions")),
                        getIntegerFromValue(csvRecord.get("Loyal Users")),

                        getDecimalFromValue(csvRecord.get("Loyal Users/Installs")),

                        getDecimalFromValue(csvRecord.get("Total Revenue")),
                        getDecimalFromValue(csvRecord.get("Total Cost")),
                        getDecimalFromValue(csvRecord.get("ROI")),
                        getDecimalFromValue(csvRecord.get("ARPU")),
                        getDecimalFromValue(csvRecord.get("Average eCPI")),

                        getIntegerFromValue(csvRecord.get("af_login (Unique users)")),
                        getIntegerFromValue(csvRecord.get("af_login (Event counter)")),
                        getIntegerFromValue(csvRecord.get("af_purchase (Unique users)")),
                        getIntegerFromValue(csvRecord.get("af_purchase (Event counter)")),

                        getDecimalFromValue(csvRecord.get("af_purchase (Sales in EUR)")),
                        getIntegerFromValue(csvRecord.get("af_tutorial_completion (Unique users)"))
                );

                campaignInfos.add(info);
            }

            return campaignInfos;
        } catch (IOException e) {
            throw new RuntimeException("fail to parse CSV file: " + e.getMessage());
        }

    }

    private static Integer getIntegerFromValue(String stringValue) {
        if ("N/A".equals(stringValue) || stringValue == null || stringValue.trim().isEmpty()) {
            return null;
        }
        return Integer.parseInt(stringValue);
    }

    private static BigDecimal getDecimalFromValue(String stringValue) {
        if ("N/A".equals(stringValue) || stringValue == null || stringValue.trim().isEmpty()) {
            return null;
        }
        return new BigDecimal(stringValue);
    }


    public static List<CampaignInfo> getInfosMocked(LocalDate from, LocalDate to) {

        try {
            String data =
                    IOUtils.toString(CampaignInfoServiceImpl.class.getResourceAsStream("/mock/ANDROID.csv"), Charset.defaultCharset());
            List<CampaignInfo> campaignInfos = CsvToCampaignInfoHelper.convertCSVToCampaignInfo(data, Platform.ANDROID);
            data =
                    IOUtils.toString(CampaignInfoServiceImpl.class.getResourceAsStream("/mock/IOS.csv"), Charset.defaultCharset());
            campaignInfos.addAll(CsvToCampaignInfoHelper.convertCSVToCampaignInfo(data, Platform.IOS));
            // now filter by date
            return campaignInfos.stream().filter(a -> !a.rdate().isBefore(from) && !a.rdate().isAfter(to)
            ).toList();
        } catch (IOException e) {
            return Collections.emptyList();
        }

    }
}

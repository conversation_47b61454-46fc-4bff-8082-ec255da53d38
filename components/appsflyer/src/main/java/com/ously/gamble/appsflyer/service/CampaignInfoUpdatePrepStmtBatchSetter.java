package com.ously.gamble.appsflyer.service;

import com.ously.gamble.api.appsflyer.CampaignInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;

import java.sql.JDBCType;
import java.sql.PreparedStatement;
import java.util.List;

public class CampaignInfoUpdatePrepStmtBatchSetter implements BatchPreparedStatementSetter {
    private final static Logger log = LoggerFactory.getLogger(CampaignInfoUpdatePrepStmtBatchSetter.class);
    private final List<CampaignInfo> items;

    public CampaignInfoUpdatePrepStmtBatchSetter(List<CampaignInfo> items) {
        super();
        this.items = items;
    }

    @Override
    public void setValues(PreparedStatement ps, int i) {


        try {
            var item = items.get(i);

            ps.setString(1, item.rdate().toString());
            ps.setString(2, item.platform().toString());
            ps.setString(3, item.mediasource());
            ps.setString(4, item.campaign());
            // setObject allow null values
            ps.setObject(5, item.impressions(), JDBCType.INTEGER);
            ps.setObject(6, item.clicks(), JDBCType.INTEGER);
            ps.setObject(7, item.ctr(), JDBCType.DECIMAL, 4);
            ps.setObject(8, item.installs(), JDBCType.INTEGER);
            ps.setObject(9, item.conversionRate(), JDBCType.DECIMAL, 4);
            ps.setObject(10, item.sessions(), JDBCType.INTEGER);
            ps.setObject(11, item.loyalUsers(), JDBCType.INTEGER);
            ps.setObject(12, item.loyalPerInstall(), JDBCType.DECIMAL, 4);
            ps.setObject(13, item.totalRev(), JDBCType.DECIMAL, 4);

            ps.setObject(14, item.totalCost(), JDBCType.DECIMAL, 4);
            ps.setObject(15, item.roi(), JDBCType.DECIMAL, 4);
            ps.setObject(16, item.arpu(), JDBCType.DECIMAL, 4);
            ps.setObject(17, item.avgECpi(), JDBCType.DECIMAL, 4);
            ps.setObject(18, item.loginsUnique(), JDBCType.INTEGER);
            ps.setObject(19, item.loginsCount(), JDBCType.INTEGER);
            ps.setObject(20, item.purchUniqueUsers(), JDBCType.INTEGER);
            ps.setObject(21, item.purchCount(), JDBCType.INTEGER);
            ps.setObject(22, item.purchSum(), JDBCType.DECIMAL, 4);
            ps.setObject(23, item.tutorialDoneUnique(), JDBCType.INTEGER);
        } catch (Exception e) {
            log.error("Error preparing batch at pos {} for {}", i, items.get(i), e);
        }
    }

    @Override
    public int getBatchSize() {
        return items.size();
    }
}
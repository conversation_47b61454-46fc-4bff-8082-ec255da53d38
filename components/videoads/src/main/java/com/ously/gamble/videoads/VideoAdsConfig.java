package com.ously.gamble.videoads;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "videoads")
@Component
public class VideoAdsConfig {

    /**
     * if true, entries are saved/updated, if false only log entries are generated
     */
    private boolean enabled;

    /**
     * log level for logging audit entries when in logging mode
     */
    private String loglevel = "debug";

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getLoglevel() {
        return loglevel;
    }

    public void setLoglevel(String loglevel) {
        this.loglevel = loglevel;
    }
}

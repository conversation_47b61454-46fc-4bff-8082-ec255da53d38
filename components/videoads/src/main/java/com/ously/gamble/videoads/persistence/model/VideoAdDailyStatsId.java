package com.ously.gamble.videoads.persistence.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Embeddable
public class VideoAdDailyStatsId implements Serializable {

    @Column(name = "rDate")
    private Date rDate;

    @Column(name = "position")
    private String position;

    // Constructors, getters, and setters

    public VideoAdDailyStatsId() {
        // Default constructor
    }

    public VideoAdDailyStatsId(Date rDate, String position) {
        this.rDate = rDate;
        this.position = position;
    }

    // Getters and setters

    public Date getRDate() {
        return rDate;
    }

    public void setRDate(Date rDate) {
        this.rDate = rDate;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    // equals and hashCode methods

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        VideoAdDailyStatsId that = (VideoAdDailyStatsId) o;
        return Objects.equals(rDate, that.rDate) &&
                Objects.equals(position, that.position);
    }

    @Override
    public int hashCode() {
        return Objects.hash(rDate, position);
    }
}
package com.ously.gamble.videoads.service;

import com.ously.gamble.api.notification.Message;
import com.ously.gamble.api.notification.NotificationSendService;
import com.ously.gamble.api.notification.NotificationService;
import com.ously.gamble.api.videoads.CrmVideoAdRequest;
import com.ously.gamble.api.videoads.VideoAdPushService;
import com.ously.gamble.api.videoads.VideoAdsService;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.persistence.model.NotificationType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Service
@ConditionalOnProperty(prefix = "videoads", name = "enabled", havingValue = "true")
@ConditionalOnOffloader
public class VideoAdPushServiceImpl implements VideoAdPushService {
    private final VideoAdsService videoAdService;
    private final NotificationService notificationService;
    private final NotificationSendService notificationSendService;
    private final static String FORCED_VIDEO_AD_POSITION = "FORCED_VIDEO_AD";

    public VideoAdPushServiceImpl(VideoAdsService videoAdService,
                                  @Autowired(required = false) NotificationService notificationService,
                                  @Autowired(required = false) NotificationSendService notificationSendService) {
        this.videoAdService = videoAdService;
        this.notificationService = notificationService;
        this.notificationSendService = notificationSendService;
    }

    @Override
    public void sendVideoAdPush(long userId) {
        if (notificationSendService == null || notificationService == null) {
            throw new IllegalArgumentException("Cannot find notification service and notification send service");
        }

        var ad = videoAdService.prepare(new CrmVideoAdRequest(userId, FORCED_VIDEO_AD_POSITION, null, null));
        var fcm = notificationService.getCurrentFCM(userId);

        if (StringUtils.isNotBlank(fcm)) {
            var message = new Message();
            message.setType(NotificationType.MOBILE_PUSH);
            message.setuId(userId);
            HashMap<String, String> config = new HashMap<>();
            config.put("action", "force_video_ad");
            config.put("uuid", ad.uuid().toString());

            message.setProperties(config);
            message.setDestination(fcm);
            notificationSendService.sendNotification(message);
        }
    }

    @Override
    public void sendVideoAdPush(long userId, String position, Double coinAmount, String context) {
        if (notificationSendService == null || notificationService == null) {
            throw new IllegalArgumentException("Cannot find notification service and notification send service");
        }

        // Use the position provided or fall back to default
        String adPosition = (position != null && !position.isBlank()) ? position : FORCED_VIDEO_AD_POSITION;

        // Prepare the video ad with the specified parameters
        var ad = videoAdService.prepare(new CrmVideoAdRequest(userId, adPosition, coinAmount, context));
        var fcm = notificationService.getCurrentFCM(userId);

        if (StringUtils.isNotBlank(fcm)) {
            var message = new Message();
            message.setType(NotificationType.MOBILE_PUSH);
            message.setuId(userId);
            HashMap<String, String> config = new HashMap<>();
            config.put("action", "force_video_ad");
            config.put("uuid", ad.uuid().toString());

            message.setProperties(config);
            message.setDestination(fcm);
            notificationSendService.sendNotification(message);
        }
    }
}

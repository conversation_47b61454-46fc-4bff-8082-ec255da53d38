package com.ously.gamble.videoads.service;

import java.time.LocalDate;

public interface UserVideoAdLimitService {

    /**
     * Gets the current view count for a user on a specific date.
     *
     * @param userId   The user's ID.
     * @param viewDate The date to check the count for.
     * @return The number of views, or 0 if none recorded.
     */
    int getViewCount(long userId, LocalDate viewDate);

    /**
     * Increments the view count for a user for the current day (UTC).
     * Creates a new record if one doesn't exist for the day.
     *
     * @param userId The user's ID.
     * @return The new view count after incrementing.
     */
    int incrementViewCount(long userId);

    /**
     * Resets the view count for a user for the current day (UTC) to 0.
     *
     * @param userId The user's ID.
     */
    void resetViewCount(long userId);

    /**
     * Resets the view count for a user for a specific date to 0.
     *
     * @param userId The user's ID.
     * @param date   The date to reset the count for.
     */
    void resetViewCount(long userId, LocalDate date);


    /**
     * Deletes daily count records older than the specified retention date.
     *
     * @param retentionDate Records with a viewDate *before* this date will be deleted.
     * @return The number of records deleted.
     */
    int cleanupOldCounts(LocalDate retentionDate);
}
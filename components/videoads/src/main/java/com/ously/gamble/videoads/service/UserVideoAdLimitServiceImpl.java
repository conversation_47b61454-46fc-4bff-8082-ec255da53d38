package com.ously.gamble.videoads.service;

import com.ously.gamble.videoads.config.VideoAdConfiguration;
import com.ously.gamble.videoads.persistence.model.UserVideoAdDailyCount;
import com.ously.gamble.videoads.persistence.model.UserVideoAdDailyCountId;
import com.ously.gamble.videoads.persistence.repository.UserVideoAdDailyCountRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneOffset;

@Service
public class UserVideoAdLimitServiceImpl implements UserVideoAdLimitService {

    private static final Logger log = LoggerFactory.getLogger(UserVideoAdLimitServiceImpl.class);

    private final UserVideoAdDailyCountRepository repository;
    private final VideoAdConfiguration videoAdConfiguration; // Needed for max views

    public UserVideoAdLimitServiceImpl(UserVideoAdDailyCountRepository repository, VideoAdConfiguration videoAdConfiguration) {
        this.repository = repository;
        this.videoAdConfiguration = videoAdConfiguration;
    }

    @Override
    @Transactional(readOnly = true)
    public int getViewCount(long userId, LocalDate viewDate) {
        return repository.findByIdUserIdAndIdViewDate(userId, viewDate)
                .map(UserVideoAdDailyCount::getViewCount)
                .orElse(0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public int incrementViewCount(long userId) {
        LocalDate today = LocalDate.now(ZoneOffset.UTC);
        UserVideoAdDailyCountId id = new UserVideoAdDailyCountId(userId, today);

        // Try to increment existing record first for performance
        int updated = repository.incrementCount(id);

        if (updated == 0) {
            // Record doesn't exist or count couldn't be incremented (e.g., concurrent delete), create/update it
            UserVideoAdDailyCount count = repository.findById(id)
                    .orElseGet(() -> new UserVideoAdDailyCount(userId, today));
            count.incrementViewCount();
            repository.saveAndFlush(count);
            log.debug("Incremented video ad view count for user {} on {}. New count: {}", userId, today, count.getViewCount());
            return count.getViewCount();
        } else {
            // Successfully incremented existing record
            int currentCount = getViewCount(userId, today); // Re-fetch to be sure
             log.debug("Incremented video ad view count for user {} on {}. New count: {}", userId, today, currentCount);
            return currentCount;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void resetViewCount(long userId) {
       resetViewCount(userId, LocalDate.now(ZoneOffset.UTC));
    }

     @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void resetViewCount(long userId, LocalDate date) {
        int updated = repository.resetCount(userId, date);
         if (updated > 0) {
            log.info("Reset video ad view count for user {} on {}", userId, date);
         } else {
             // If no record existed, no reset is needed, but log for info
             log.debug("No video ad view count record found to reset for user {} on {}", userId, date);
         }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public int cleanupOldCounts(LocalDate retentionDate) {
        int deletedCount = repository.deleteByViewDateBefore(retentionDate);
        if (deletedCount > 0) {
            log.info("Cleaned up {} old UserVideoAdDailyCount records before {}", deletedCount, retentionDate);
        }
        return deletedCount;
    }
}
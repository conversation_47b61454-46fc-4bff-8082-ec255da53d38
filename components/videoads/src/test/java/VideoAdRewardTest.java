import com.ously.gamble.api.videoads.VideoAdReward;
import com.ously.gamble.videoads.service.VideoAdsRewardServiceImpl;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;

public class VideoAdRewardTest {
    @Test
    public void testExpectedValue() {
        var videoAdsRewardService = new VideoAdsRewardServiceImpl();
        List<VideoAdReward> rewards = videoAdsRewardService.getRewards(5);
        double expectedValue = 0.0;
        for (VideoAdReward reward : rewards) {
            double probability = reward.probability();
            double coins = reward.coins();
            expectedValue += probability * coins;
        }

        assertTrue(isInRange(expectedValue));
    }

    @Test
    public void testGetRandomReward() {
        var videoAdsRewardService = new VideoAdsRewardServiceImpl();

        List<VideoAdReward> rewards = videoAdsRewardService.getRewards(5);

        int iterations = 100000;
        double sum = 0;

        for (int i = 0; i < iterations; i++) {
            var reward = videoAdsRewardService.getRandomReward(rewards);
            sum += reward.coins();
        }

        var avg = sum / iterations;

        assertTrue(isInRange(avg));
    }

    private boolean isInRange(double value) {
        return value > 75 && value < 85;
    }
}

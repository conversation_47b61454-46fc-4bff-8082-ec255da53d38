package com.ously.gamble.postbacks.listeners;

import com.ously.gamble.api.postbacks.PostbackAdapter;
import com.ously.gamble.api.postbacks.PostbackSendoutRequest;
import com.ously.gamble.api.postbacks.PostbackSendoutService;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@ConditionalOnOffloader
@ConditionalOnProperty(prefix = "postbacks", name = "enabled", havingValue = "true")
public class PostbackListener {
    final Logger log = LoggerFactory.getLogger(getClass());

    final PostbackSendoutService postbackSendoutService;

    public PostbackListener(
            PostbackSendoutService postbackSendoutService) {
        this.postbackSendoutService = postbackSendoutService;
    }

    @Transactional
    @RabbitListener(queues = {PostbackAdapter.POSTBACK_QUEUE_SENDOUT}, containerFactory =
            "directRabbitListenerContainerFactory", id = "postbackContainer",
                    concurrency = "1")
    public void sendPostback(PostbackSendoutRequest req) {
        try {
            postbackSendoutService.doSendout(req);
        } catch (Exception e) {
            log.error("Error calculating campaign statement for {}", req);
            throw new RuntimeException(e);
        }
    }
}

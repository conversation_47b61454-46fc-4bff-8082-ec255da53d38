package com.ously.gamble.postbacks;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "postbacks")
@Component
public class PostbacksConfig {

    /**
     * if true, entries are saved/updated, if false only log entries are generated
     */
    private boolean enabled;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

}

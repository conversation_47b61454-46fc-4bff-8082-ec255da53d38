package com.ously.gamble.kyc.persistence.idclasses;

import java.io.Serializable;
import java.util.Objects;

public class KYCDataId implements Serializable {

    Long userId;

    Long taskId;
    Long dataId;

    public KYCDataId() {
    }

    public KYCDataId(Long userId, Long taskId, Long dataId) {
        this.userId = userId;
        this.taskId = taskId;
        this.dataId = dataId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getDataId() {
        return dataId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        var kycDataId = (KYCDataId) o;

        if (!userId.equals(kycDataId.userId)) {
            return false;
        }
        if (!taskId.equals(kycDataId.taskId)) {
            return false;
        }
        return Objects.equals(dataId, kycDataId.dataId);
    }

    @Override
    public int hashCode() {
        int result = userId.hashCode();
        result = 31 * result + taskId.hashCode();
        result = 31 * result + (dataId != null ? dataId.hashCode() : 0);
        return result;
    }
}

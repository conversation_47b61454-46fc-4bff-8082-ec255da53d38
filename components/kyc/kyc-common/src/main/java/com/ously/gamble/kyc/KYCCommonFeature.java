package com.ously.gamble.kyc;

import com.ously.gamble.api.features.FeatureDescription;
import com.ously.gamble.api.features.PlatformFeature;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class KYCCommonFeature implements PlatformFeature {
    @Override
    public FeatureDescription getDescription() {
        return new FeatureDescription("KYC Common feature", "Custom KYC feature- Cash/Crypto only");
    }

    public List<String> getDailyAnalyzeTables() {
        return List.of("kyc_tasks");
    }

    public List<String> getWeeklyAnalyzeTables() {
        return List.of("kyc_data");
    }
}

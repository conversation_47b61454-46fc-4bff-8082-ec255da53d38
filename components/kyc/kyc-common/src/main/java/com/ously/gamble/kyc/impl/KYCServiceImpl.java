package com.ously.gamble.kyc.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.kyc.*;
import com.ously.gamble.api.security.UserPrincipal;
import com.ously.gamble.api.user.EUserTag;
import com.ously.gamble.api.user.UserInformation;
import com.ously.gamble.api.user.UserService;
import com.ously.gamble.api.user.UserTagService;
import com.ously.gamble.exception.AppException;
import com.ously.gamble.kyc.persistence.idclasses.KYCTaskId;
import com.ously.gamble.kyc.persistence.model.KYCData;
import com.ously.gamble.kyc.persistence.model.KYCTask;
import com.ously.gamble.kyc.persistence.repository.KYCDataRepository;
import com.ously.gamble.kyc.persistence.repository.KYCTaskRepository;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class KYCServiceImpl implements KYCService {
    private final Logger log = LoggerFactory.getLogger(KYCServiceImpl.class);

    private final KYCTaskRepository kycRepo;
    private final KYCDataRepository kycDataRepo;

    private final UserTagService uTagService;
    private final UserService uService;
    private final ObjectMapper om;

    private static final int MAX_DOCUMENT_COUNT = 5;

    public KYCServiceImpl(KYCTaskRepository kycTRepo,
                          KYCDataRepository dRep,
                          UserTagService uts,
                          UserService uSrv,
                          ObjectMapper om) {
        this.kycDataRepo = dRep;
        this.kycRepo = kycTRepo;
        this.uTagService = uts;
        this.uService = uSrv;
        this.om = om;
    }

    @Override
    public Optional<KYCTaskDto> getTask(long userId, long taskId) {
        var optTask = kycRepo.findById(new KYCTaskId(userId, taskId));
        return optTask.map(KYCTask::toDto);
    }

    @Override
    public List<KYCTaskDto> getTasks(long userId) {
        return kycRepo.findAllByUserIdOrderByCreatedAtDesc(userId).stream().map(KYCTask::toDto).toList();
    }

    @Override
    public Page<KYCTaskDto> getTasksForStatusIn(Collection<KYCStatus> statusList,
                                                Pageable pageable) {
        var tasks = kycRepo.findAllByKycStatusIn(statusList, pageable);
        return new PageImpl<>(tasks.stream().map(KYCTask::toDto).collect(Collectors.toList()),
                pageable,
                tasks.getTotalElements());
    }

    @Override
    @Transactional
    public Optional<KYCTaskDto> createTask(KYCTaskDto newTask) {
        var nT = new KYCTask();
        nT.setUserId(newTask.userId());
        nT.setCreatedAt(Instant.now());
        nT.setUpdatedAt(Instant.now());
        nT.setKycId(newTask.kycId());
        nT.setKycStatus(newTask.status());
        nT.setProvider(newTask.provider());
        nT.setDataPath(newTask.dataPath());
        nT.setReqReason(newTask.reason());
        nT.setType(newTask.type());

        var setTag = checkTaskForUserTagChanges(newTask);
        var kycTaskHistoryEntry = new KYCTaskHistoryEntry();

        kycTaskHistoryEntry.setPublicComment("Created KYC Task");
        kycTaskHistoryEntry.setInternalComment("Set Tag " + setTag.name() + " to user " + newTask.userId());
        kycTaskHistoryEntry.setStatusAfter(newTask.status());
        kycTaskHistoryEntry.setChangeTimestamp(Instant.now());
        var kycTaskHistory = new KYCTaskHistory();
        var kycTHistList = new ArrayList<KYCTaskHistoryEntry>(1);
        kycTHistList.add(kycTaskHistoryEntry);
        kycTaskHistory.setHistory(kycTHistList);
        nT.setHistory(kycTaskHistory);
        var kycTaskDto = KYCTask.toDto(kycRepo.saveAndFlush(nT));
        return Optional.of(kycTaskDto);

    }


    @Override
    @Transactional
    public List<KYCDataDto> attachDocumentsToTask(KYCTaskDto theTask, Collection<KYCDataDto> docs) {
        var kycDataDtos =
                docs.stream().map(a -> attachDocumentToTask(theTask, a)).filter(Optional::isPresent).map(Optional::get).toList();
        if (!kycDataDtos.isEmpty()) {
            if (theTask.status() == KYCStatus.VERIFIED) {
                return kycDataDtos;
            }
            if (theTask.status() != KYCStatus.SUPPORT) {
                changeStatusInternal(theTask, KYCStatus.SUPPORT, "new documents added", "new documents added", null);
            }
        }
        return kycDataDtos;
    }

    private Optional<KYCDataDto> attachDocumentToTask(KYCTaskDto theTask, KYCDataDto doc) {

        try {
            var nD = new KYCData();
            nD.setTaskId(theTask.taskId());
            nD.setUserId(theTask.userId());
            nD.setCreatedAt(Instant.now());
            nD.setUpdatedAt(Instant.now());
            nD.setComment(doc.comment());
            nD.setFilename(doc.filename());
            nD.setFormat(doc.format());
            nD.setType(doc.type());
            nD.setData(doc.data());
            kycDataRepo.saveAndFlush(nD);
            return Optional.of(KYCData.toDto(nD));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Optional.empty();
    }

    @Override
    @Transactional
    public List<KYCDataDto> getAllDataForTask(long userId, long taskId, boolean withData) {
        return kycDataRepo.findAllByUserIdAndTaskIdOrderByCreatedAtDesc(userId, taskId).stream().map(withData ? KYCData::toDto : KYCData::toDtoWithoutData).toList();
    }

    @Override
    @Transactional
    public KYCDataDto getDataForTask(long userId, long taskId, long dataId) {
        var data = kycDataRepo.findByUserIdAndTaskIdAndDataId(userId, taskId, dataId);
        if (data != null) {
            return KYCData.toDto(data);
        }
        throw new AppException(404, "Document not found");
    }

    @Override
    @Transactional
    public Optional<KYCTaskDto> changeStatus(long userId, long taskId, KYCStatus newStatus,
                                             String pComment, String iComment,
                                             Long adminId) {
        var optTask = getTask(userId, taskId);
        if (optTask.isEmpty()) {
            return Optional.empty();
        }
        return changeStatusInternal(optTask.get(), newStatus, pComment, iComment, adminId);
    }


    private Optional<KYCTaskDto> changeStatusInternal(KYCTaskDto kycTaskDto, KYCStatus newStatus, String pComment,
                                                      String iComment,
                                                      Long adminId) {

        var kycTaskHistoryEntry = new KYCTaskHistoryEntry();
        kycTaskHistoryEntry.setAdminId(adminId);
        kycTaskHistoryEntry.setPublicComment(pComment);
        kycTaskHistoryEntry.setPublicComment(iComment);
        kycTaskHistoryEntry.setStatusBefore(kycTaskDto.status());
        kycTaskHistoryEntry.setStatusAfter(newStatus);
        kycTaskHistoryEntry.setChangeTimestamp(Instant.now());
        var history = kycTaskDto.history();
        if (history == null) {
            history = new KYCTaskHistory();
            history.setHistory(new ArrayList<>(1));
        }
        history.getHistory().add(kycTaskHistoryEntry);

        kycTaskDto = new KYCTaskDto(kycTaskDto.userId(), kycTaskDto.taskId(), kycTaskDto.provider(), kycTaskDto.kycId(),
                kycTaskDto.type(), newStatus, kycTaskDto.reason(), kycTaskDto.createdAt(), Instant.now(), kycTaskDto.dataPath(),
                kycTaskDto.history());

        kycTaskDto = updateTaskDto(kycTaskDto, adminId);
        return Optional.ofNullable(kycTaskDto);
    }

    @Override
    public Map<KYCType, KYCState> getKycStatesForUser(long userId) {
        Map<KYCType, KYCState> stMap = new EnumMap<>(KYCType.class);
        kycRepo.findAllByUserIdOrderByCreatedAtDesc(userId).forEach(a -> {
            if (!stMap.containsKey(a.getType())) {
                stMap.put(a.getType(),
                        getStateForKYCTask(a)
                );
            }
        });
        return stMap;
    }

    @Override
    @Transactional
    public List<KYCDataDto> addDocumentsToTask(long userId, long taskId, List<KYCDocument> documents, String publicComment,
                                               String internalComment) {

        var task = getTask(userId, taskId).orElse(null);
        if (task == null) {
            return Collections.emptyList();
        }
        var existingDocumentsCount = getAllDataForTask(userId, taskId, false).size();

        if (documents.size() + existingDocumentsCount > MAX_DOCUMENT_COUNT) {
            throw new AppException(413, "You can upload only 5 documents per level");
        }

        // now convert documents to internal entity rep
        var dataDtos = documents.stream().map(a -> new KYCDataDto(
                        userId,
                        taskId,
                        0L,
                        a.getType(),
                        a.getFormat(),
                        a.getFilename(),
                        publicComment,
                        a.getData(),
                        Instant.now(),
                        Instant.now()
                )
        ).toList();

        return this.attachDocumentsToTask(task, dataDtos);
    }

    @Override
    @Transactional(readOnly = true)
    public KYCSelfDisclosureData getSelfDisclosureData(long userId) throws IOException {
        var allSDTasks = kycRepo.findAllByUserIdAndTypeOrderByCreatedAtDesc(userId,
                KYCType.SELF_DIS);
        var sdTask = allSDTasks.getFirst();
        if (sdTask != null) {
            var allData = kycDataRepo.findAllByUserIdAndTaskIdOrderByCreatedAtDesc(userId,
                    sdTask.getTaskId());

            if (allData.isEmpty()) {
                return null;
            }

            var data = allData.getFirst().getData();
            return om.readValue(data, KYCSelfDisclosureData.class);
        }
        return null;
    }

    @Override
    @Transactional
    public boolean setSelfDisclosureData(UserPrincipal up, KYCSelfDisclosureData sdData) {
        long userId = up.getId();
        var allSDTasks = kycRepo.findAllByUserIdAndTypeOrderByCreatedAtDesc(up.getId(), KYCType.SELF_DIS);
        if (allSDTasks.isEmpty()) {
            // Create and set to OK after check!!
            if (checkSelfDisclosureData(sdData)) {
                try {
                    var dTs = Instant.now();
                    var hsEntry = new KYCTaskHistoryEntry();
                    hsEntry.setPublicComment("User created SelfDisclosure Task");
                    hsEntry.setInternalComment("");
                    hsEntry.setChangeTimestamp(dTs);
                    hsEntry.setStatusAfter(KYCStatus.VERIFIED);
                    var hist = new KYCTaskHistory();
                    hist.setHistory(Collections.singletonList(hsEntry));

                    var newTask = new KYCTaskDto(
                            up.getId(),
                            0L,
                            "self",
                            UUID.randomUUID().toString(),
                            KYCType.SELF_DIS,
                            KYCStatus.VERIFIED,
                            KYCRequestReason.SELF_DISCLOSURE,
                            dTs,
                            dTs,
                            "selfdisc",
                            hist
                    );

                    var task = createTask(newTask).orElseThrow();
                    // Add selfdisc as json doc
                    var selfDiscJson = om.writeValueAsString(sdData);
                    addDocumentsToTask(userId, task.taskId(), Collections.singletonList(new KYCDocument("selfdisc.json", selfDiscJson.getBytes(StandardCharsets.UTF_8), KYCDataType.JSON)), "user added selfdisclosure data",
                            "");

                    // Change UserInfo (update seld-discl. data)
                    changeUserInfo(up, sdData);
                    return true;
                } catch (Exception e) {
                    log.error("Error adding SelfDisclosure data", e);
                }
            }
        }
        return false;
    }

    private void changeUserInfo(UserPrincipal up, KYCSelfDisclosureData sdData) {
        UserInformation userInfo = uService.getUserInfo(up.getId());
        if (userInfo != null) {
            boolean changed = false;
            if (!StringUtils.isAllBlank(sdData.birthdate())) {
                try {
                    var ld = LocalDate.parse(sdData.birthdate());
                    userInfo.setBirthdate(ld);
                    changed = true;
                } catch (Exception ignored) {
                }
            }

            if (!StringUtils.isAllBlank(sdData.birthplace())) {
                userInfo.setBirthplace(StringUtils.abbreviate(sdData.birthplace(), 99));
                changed = true;
            }

            if (!StringUtils.isAllBlank(sdData.name())) {
                userInfo.setName(StringUtils.abbreviate(sdData.name(), 99));
                changed = true;
            }

            if (!StringUtils.isAllBlank(sdData.surname())) {
                userInfo.setSurname(StringUtils.abbreviate(sdData.surname(), 99));
                changed = true;
            }

            if (!StringUtils.isAllBlank(sdData.city())) {
                userInfo.setCity(StringUtils.abbreviate(sdData.city(), 99));
                changed = true;
            }

            if (!StringUtils.isAllBlank(sdData.street())) {
                userInfo.setStreet(StringUtils.abbreviate(sdData.street(), 99));
                changed = true;
            }
            if (!StringUtils.isAllBlank(sdData.zipcode())) {
                userInfo.setPostcode(StringUtils.abbreviate(sdData.zipcode(), 19));
                changed = true;
            }

            if (!StringUtils.isAllBlank(sdData.country())) {
                userInfo.setCountry(StringUtils.abbreviate(sdData.country(), 9));
                changed = true;
            }


            if (changed) {
                try {
                    uService.updateUserInformation(
                            up.getId(), userInfo, up
                    );
                } catch (Exception e) {
                    log.error("Error updating userInfo from kyc selfdisc", e);
                }
            }

        }
    }

    @Override
    @Transactional
    public Optional<KYCTaskDto> requestKYCForUser(Long userId, KYCType type, Long adminId) {
        var dTs = Instant.now();

        var hsEntry = new KYCTaskHistoryEntry();
        hsEntry.setPublicComment("Please add info to allow KYC");
        if (adminId != null) {
            hsEntry.setAdminId(adminId);
            hsEntry.setInternalComment("Admin " + adminId + " requested KYC");
        } else {
            hsEntry.setInternalComment("Automatically requested KYC");
        }
        hsEntry.setChangeTimestamp(dTs);
        hsEntry.setStatusAfter(KYCStatus.WAITING_FOR_DATA);
        var hist = new KYCTaskHistory();
        hist.setHistory(Collections.singletonList(hsEntry));

        var reqTask = new KYCTaskDto(
                userId,
                0L,
                "srsl",
                UUID.randomUUID().toString(),
                type,
                KYCStatus.WAITING_FOR_DATA,
                KYCRequestReason.AGENT_REQUESTED,
                dTs,
                dTs,
                "",
                hist
        );

        return createTask(reqTask);
    }

    private static boolean checkSelfDisclosureData(KYCSelfDisclosureData sdData) {
        return !StringUtils.isAnyBlank(sdData.birthdate(), sdData.occupation(), sdData.name(), sdData.surname(),
                sdData.city(), sdData.country(), sdData.street(), sdData.zipcode());
    }

    private static KYCState getStateForKYCTask(KYCTask a) {
        var action = switch (a.getKycStatus()) {
            case PEP, NONE, ABORTED, REJECTED, VERIFIED, EXPIRED -> KYCUserAction.NONE;
            case SUPPORT, PENDING -> KYCUserAction.NONE_IN_REVIEW;
            case MISMATCH, CONTACT_SUPPORT -> KYCUserAction.CONTACT_SUPPORT;
            case WAITING_FOR_DATA -> KYCUserAction.SEND_DOCUMENT;
            case EXTERNAL_RUNNING -> KYCUserAction.CONTINUE_KYC;
        };

        return new KYCState(a.getKycStatus(), a.getTaskId(), action, action.comment(), a.getUpdatedAt());
    }

    private KYCTaskDto updateTaskDto(KYCTaskDto kycTaskDto, Long adminId) {
        var byId = kycRepo.findById(new KYCTaskId(kycTaskDto.userId(), kycTaskDto.taskId()));
        if (byId.isEmpty()) {
            return null;
        }
        var kycTask = byId.get();

        if (kycTask.getKycStatus() != kycTaskDto.status()) {
            var eUserTag = checkTaskForUserTagChanges(kycTaskDto);

            var kycTaskHistoryEntry = new KYCTaskHistoryEntry();
            kycTaskHistoryEntry.setAdminId(adminId);
            kycTaskHistoryEntry.setPublicComment("Status changed");
            kycTaskHistoryEntry.setInternalComment("Status changed and set UserTag " + eUserTag.name());
            kycTaskHistoryEntry.setStatusBefore(kycTask.getKycStatus());
            kycTaskHistoryEntry.setStatusAfter(kycTaskDto.status());
            kycTaskHistoryEntry.setChangeTimestamp(Instant.now());
            var kycTaskHistoryEntries = new ArrayList<>(kycTaskDto.history().getHistory());
            kycTaskHistoryEntries.add(kycTaskHistoryEntry);
            kycTaskDto.history().setHistory(kycTaskHistoryEntries);
        }
        kycTask.setHistory(kycTaskDto.history());

        kycTask.setKycStatus(kycTaskDto.status());
        kycTask.setKycId(kycTaskDto.kycId());
        kycTask.setProvider(kycTaskDto.provider());
        kycRepo.saveAndFlush(kycTask);
        return KYCTask.toDto(kycTask);
    }

    private EUserTag checkTaskForUserTagChanges(KYCTaskDto taskDto) {

        var userId = taskDto.userId();
        var type = taskDto.type();
        var status = taskDto.status();
        if (status == KYCStatus.VERIFIED) {
            uTagService.removeTagFromUser(userId, type.reqTag().id());
            uTagService.addTagToUser(userId, type.doneTag());
            return type.doneTag();
        }
        uTagService.removeTagFromUser(userId, type.doneTag().id());
        uTagService.addTagToUser(userId, type.reqTag());
        return type.reqTag();

    }

}

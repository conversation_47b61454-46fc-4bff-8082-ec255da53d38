package com.ously.gamble.kyc.persistence.repository;

import com.ously.gamble.api.kyc.KYCStatus;
import com.ously.gamble.api.kyc.KYCType;
import com.ously.gamble.kyc.persistence.idclasses.KYCTaskId;
import com.ously.gamble.kyc.persistence.model.KYCTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;
import java.util.List;

public interface KYCTaskRepository extends JpaRepository<KYCTask, KYCTaskId> {
    List<KYCTask> findAllByUserIdOrderByCreatedAtDesc(long userId);

    List<KYCTask> findAllByUserIdAndTypeOrderByCreatedAtDesc(long userId, KYCType type);

    Page<KYCTask> findAllByKycStatusIn(Collection<KYCStatus> statusList, Pageable pageable);
}

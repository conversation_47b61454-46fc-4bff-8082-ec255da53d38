package com.ously.gamble.kyc.persistence.model;

import com.ously.gamble.api.kyc.KYCDataDto;
import com.ously.gamble.api.kyc.KYCDataFormat;
import com.ously.gamble.api.kyc.KYCDataType;
import com.ously.gamble.kyc.persistence.idclasses.KYCDataId;
import jakarta.persistence.*;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.time.Instant;

@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "kyc_data")
@IdClass(KYCDataId.class)
public class KYCData implements Persistable<KYCDataId>, Serializable {

    public static KYCDataDto toDto(KYCData dt) {
        return new KYCDataDto(dt.userId, dt.taskId, dt.dataId, dt.type, dt.format, dt.filename,
                dt.comment,
                dt.getData(), dt.createdAt, dt.updatedAt);
    }

    public static KYCDataDto toDtoWithoutData(KYCData dt) {
        return new KYCDataDto(dt.userId, dt.taskId, dt.dataId, dt.type, dt.format, dt.filename,
                dt.comment,
                null, dt.createdAt, dt.updatedAt);
    }

    @Transient
    private boolean isNew = true;

    @Override
    public KYCDataId getId() {
        return new KYCDataId(userId, taskId, dataId);
    }

    @Override
    @Transient
    public boolean isNew() {
        return this.isNew;
    }

    @PostLoad
    @PrePersist
    void trackNotNew() {
        this.isNew = false;
    }


    @Id
    @Column(name = "user_id", updatable = false)
    Long userId;

    @Id
    @Column(name = "task_id", updatable = false)
    Long taskId;

    @Id
    @GeneratedValue(generator = "pooledKYCData")
    @GenericGenerator(name = "pooledKYCData",  type = org.hibernate.id.enhanced.TableGenerator.class,
                      parameters = {
                              @Parameter(name = "table_name",
                                         value = "custom_sequences"),
                              @Parameter(name = "value_column_name",
                                         value = "sequence_next_hi_value"),
                              @Parameter(
                                      name = "prefer_entity_table_as_segment_value",
                                      value = "true"),
                              @Parameter(name = "optimizer",
                                         value = "pooled-lo"),
                              @Parameter(name = "initial_value",
                                         value = "100"),
                              @Parameter(name = "increment_size",
                                         value = "10"),
                              @Parameter(name = "segment_value",
                                         value = "kycdata")
                      })
    @Column(name = "data_id", updatable = false)
    Long dataId;

    @Column(name = "type", length = 20)
    @Enumerated(EnumType.STRING)
    KYCDataType type;

    @Column(name = "format", length = 20)
    @Enumerated(EnumType.STRING)
    KYCDataFormat format;

    @Column(name = "filename", length = 100)
    String filename;

    @Column(name = "comment", length = 200)
    String comment;

    @Column(name = "data", length = 1024 * 1024 * 16)
    @Lob
    @Basic(fetch = FetchType.LAZY)
    byte[] data;

    @CreatedDate
    @Column(name = "created_at", updatable = false)
    Instant createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    Instant updatedAt;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    public KYCDataType getType() {
        return type;
    }

    public void setType(KYCDataType type) {
        this.type = type;
    }

    public KYCDataFormat getFormat() {
        return format;
    }

    public void setFormat(KYCDataFormat format) {
        this.format = format;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }
}

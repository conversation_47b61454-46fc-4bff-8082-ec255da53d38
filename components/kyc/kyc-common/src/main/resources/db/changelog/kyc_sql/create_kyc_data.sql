CREATE TABLE kyc_data
(
    `user_id`    bigint unsigned                     NOT NULL,
    `data_id`    bigint unsigned                     NOT NULL,
    `type`       varchar(20)                         NOT NULL,
    `format`     varchar(20)                         NOT NULL,
    `filename`   varchar(100)                        NOT NULL,
    `comment`    varchar(200),
    `data`       BLOB,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (`user_id`, `data_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;


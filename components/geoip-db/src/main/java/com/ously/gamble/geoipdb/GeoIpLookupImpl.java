package com.ously.gamble.geoipdb;

import com.github.benmanes.caffeine.cache.LoadingCache;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.DatabaseReader.Builder;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import com.ously.gamble.api.cache.LCacheFactory;
import com.ously.gamble.api.cache.LCacheLoader;
import com.ously.gamble.api.events.GeoIpLookup;
import com.ously.gamble.api.resource.ManagedResourceComponent;
import com.ously.gamble.api.resource.ResourceService;
import com.ously.gamble.persistence.model.resource.ResourceDataDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@SuppressWarnings("rawtypes")
@Component
public class GeoIpLookupImpl implements GeoIpLookup, ManagedResourceComponent {
    private final Logger log = LoggerFactory.getLogger(GeoIpLookupImpl.class);
    private static final String NO_COUNTRY_LITERAL = "XX";
    private final ResourceService resourceService;
    private DatabaseReader dbReader;
    private final LoadingCache<InetAddress, String> countriesByAddr;


    @Autowired
    public GeoIpLookupImpl(ResourceService resService, LCacheFactory lcf) {
        this(resService, lcf, "/geoip/GeoLite2-Country.mmdb");
    }

    @SuppressWarnings({"unchecked"})
    GeoIpLookupImpl(ResourceService resSrv, LCacheFactory lcf, String dbLoc) {
        log.info("Setting up cached geoIp resolver!");
        this.resourceService = resSrv;
        rebuildDb(dbLoc);
        countriesByAddr = lcf.registerCacheLoader("countriesByIp", 100000, 15, 10 * 60,
                (LCacheLoader<InetAddress, String>) this::getCountryForInetAddress);
    }

    private boolean rebuildDb(String dbLoc) {
        InputStream dbStream;
        boolean useRes = false;
        Optional<ResourceDataDto> activeResourceData = resourceService.getActiveResourceData(getComponentName(), "GeoLite2-Country.mmdb");
        if (activeResourceData.isPresent()) {
            log.info("Rebuilding GeoIP DB from managed resource version {}", activeResourceData.get().version());
            dbStream = new ByteArrayInputStream(activeResourceData.get().data());
            useRes = true;

        } else {
            log.info("Rebuilding GeoIP DB from local resource");
            dbStream = GeoIpLookupImpl.class.getResourceAsStream(dbLoc);
        }

        try {
            dbReader = new Builder(dbStream).build();
            return true;
        } catch (IOException e) {
            log.error("Error setting up DatabaseReaser for ip->country db");
            if (useRes) {
                log.info("Rebuilding GeoIP DB from local resource");
                dbStream = GeoIpLookupImpl.class.getResourceAsStream(dbLoc);
                try {
                    dbReader = new Builder(dbStream).build();
                    return true;
                } catch (IOException ex) {
                    log.error("Error setting up DatabaseReaser for ip->country db", ex);
                }
            }
        }
        return false;
    }

    private String getCountryForInetAddress(InetAddress key) throws IOException, GeoIp2Exception {
        if (dbReader == null) {
            return NO_COUNTRY_LITERAL;
        }
        var countryResponse = dbReader.tryCountry(key);
        if (countryResponse.isPresent()) {
            return countryResponse.get().getCountry().getIsoCode();
        }
        return NO_COUNTRY_LITERAL;
    }

    @Override
    public String getCountry(InetAddress ipAdr) {

        if (countriesByAddr != null && ipAdr != null) {
            try {
                return Objects.requireNonNull(countriesByAddr.get(ipAdr), "DE");
            } catch (Exception e) {
                log.warn("Exc getting cntr for {}", ipAdr);
                return NO_COUNTRY_LITERAL;
            }
        }
        return NO_COUNTRY_LITERAL;
    }

    @Override
    public String getComponentName() {
        return "GEOIP";
    }

    @Override
    public List<String> getResourceNames() {
        return Collections.singletonList("GeoLite2-Country.mmdb");
    }

    @Override
    public boolean handleResourceActivation(String component, String name) {
        log.info("Got Activation Trigger for new resource ({}->{})", component, name);
        return rebuildDb("/geoip/GeoLite2-Country.mmdb");
    }
}

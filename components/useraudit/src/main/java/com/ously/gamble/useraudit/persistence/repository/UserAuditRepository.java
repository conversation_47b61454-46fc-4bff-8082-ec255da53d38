package com.ously.gamble.useraudit.persistence.repository;

import com.ously.gamble.useraudit.persistence.model.UserAuditDao;
import com.ously.gamble.useraudit.persistence.model.UserAuditId;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

@ConditionalOnProperty(prefix = "useraudit", name = "enabled", havingValue = "true")
public interface UserAuditRepository extends JpaRepository<UserAuditDao, UserAuditId> {


    @Query(nativeQuery = true, value =
            """
                            select coalesce(max(num),0)+1 from user_audit where user_id= :userId
                    """)
    int getNextFreeNumForUser(@Param("userId") long userId);

    Page<UserAuditDao> findAllByUserId(@Param("userId") long userId, Pageable pg);

}

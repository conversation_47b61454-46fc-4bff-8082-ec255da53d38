package com.ously.gamble.huawei.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * see <a href="https://developer.huawei.com/consumer/en/doc/HMSCore-References/https-send-api-0000001050986197#EN-US_TOPIC_0000001562768322__p153162820230">...</a>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HWPushMessageRequest {
    @JsonProperty("validate_only")
    boolean validateOnly;
    HWPushMessage message;

    public HWPushMessageRequest() {
    }

    public HWPushMessageRequest(boolean validateOnly, HWPushMessage message) {
        this.validateOnly = validateOnly;
        this.message = message;
    }

    public boolean isValidateOnly() {
        return validateOnly;
    }

    public void setValidateOnly(boolean validateOnly) {
        this.validateOnly = validateOnly;
    }

    public HWPushMessage getMessage() {
        return message;
    }

    public void setMessage(HWPushMessage message) {
        this.message = message;
    }
}

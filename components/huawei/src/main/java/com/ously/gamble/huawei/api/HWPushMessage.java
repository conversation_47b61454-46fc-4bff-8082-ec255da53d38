package com.ously.gamble.huawei.api;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Arrays;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class HWPushMessage {
    /**
     * data of the msg (can be json)
     */
    String data;
    HWPushAndroidConfig android;
    String[] token;
    String topic;
    String condition;

    public HWPushMessage() {
    }

    public HWPushMessage(String data, HWPushAndroidConfig android, String token, String topic, String condition) {
        this.data = data;
        this.android = android;
        this.token = new String[]{token};
        this.topic = topic;
        this.condition = condition;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public HWPushAndroidConfig getAndroid() {
        return android;
    }

    public void setAndroid(HWPushAndroidConfig android) {
        this.android = android;
    }

    public String[] getToken() {
        return token;
    }

    public void setToken(String[] token) {
        this.token = token;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    @Override
    public String toString() {
        return "HWPushMessage{" +
                "data='" + data + '\'' +
                ", android=" + android +
                ", token=" + Arrays.toString(token) +
                ", topic='" + topic + '\'' +
                ", condition='" + condition + '\'' +
                '}';
    }
}

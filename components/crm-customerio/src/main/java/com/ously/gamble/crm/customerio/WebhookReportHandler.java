package com.ously.gamble.crm.customerio;

import com.ously.gamble.crm.customerio.exceptions.ReportHandlingException;
import com.ously.gamble.crm.customerio.payload.CIORepType;
import com.ously.gamble.crm.customerio.payload.CIORepWH;
import com.ously.gamble.crm.customerio.reporthandlers.WebhookHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.EnumMap;
import java.util.Map;


@Component
@ConditionalOnProperty(prefix = "customerio", name = "enabled", havingValue = "true")
public class WebhookReportHandler {
    private static final Logger log = LoggerFactory.getLogger(WebhookReportHandler.class);

    final Map<CIORepType, WebhookHandler> handlers = new EnumMap<>(CIORepType.class);

    public WebhookReportHandler(Collection<WebhookHandler> availableHandlers) {
        availableHandlers.forEach(a -> handlers.put(a.getType(), a));
    }

    public void handleReport(CIORepWH report) {

        var handler = handlers.get(report.getObjectType());
        if (handler != null) {
            try {
                handler.handle(report);
            } catch (ReportHandlingException e) {
                log.error("Error handling report of type '{}' and metric '{}'", e.getReport().getObjectType(), e.getReport().getMetric());
            }
        } else {
            log.warn("No handler registered for type '{}'", report.getObjectType());
        }

    }
}



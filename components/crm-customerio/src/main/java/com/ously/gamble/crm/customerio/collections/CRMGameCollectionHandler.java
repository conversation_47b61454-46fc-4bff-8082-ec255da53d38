package com.ously.gamble.crm.customerio.collections;

import com.ously.gamble.api.crm.CRMCollectionHandler;
import com.ously.gamble.api.crm.CRMGameItem;
import com.ously.gamble.api.games.GameService;
import com.ously.gamble.persistence.dto.PlayableGame;
import com.ously.gamble.persistence.model.game.GamePlatform;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CRMGameCollectionHandler implements CRMCollectionHandler<CRMGameItem> {


    private final GameService gmService;

    public CRMGameCollectionHandler(@Autowired(required = false) GameService gmService) {
        this.gmService = gmService;
    }

    @Override
    public String getCollectionName() {
        return "games";
    }

    @Override
    public List<CRMGameItem> getCollectionData() {
        List<PlayableGame> allUserGamesUnpaged = gmService.getAllUserGamesUnpaged(GamePlatform.UNKNOWN);
        return allUserGamesUnpaged.stream().map(
                a -> new CRMGameItem((int) a.getId(),
                        a.getName(),
                        a.getProvider(),
                        a.getVendorId(),
                        a.getRtp(),
                        a.getReleaseDate(), a.getLevel(), a.getBetLevels())).toList();
    }
}

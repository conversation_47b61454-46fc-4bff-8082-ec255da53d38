package com.ously.gamble.crm.customerio.payload;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ously.gamble.api.crm.CRMDevice;

public class CIODevice {
    @JsonProperty("id" )
    String id;
    @JsonProperty("platform" )
    String platform;
    @JsonProperty("last_used" )
    long lastUsed;

    public CIODevice(CRMDevice cd) {
        this.id = cd.getId();
        this.platform = cd.getPlatform();
        this.lastUsed = cd.getLastUsed();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public long getLastUsed() {
        return lastUsed;
    }

    public void setLastUsed(long lastUsed) {
        this.lastUsed = lastUsed;
    }
}

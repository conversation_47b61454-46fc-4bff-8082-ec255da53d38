package com.ously.gamble.crm.customerio.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.crm.*;
import com.ously.gamble.crm.customerio.CustomerIOAppApi;
import com.ously.gamble.crm.customerio.CustomerIOConfiguration;
import com.ously.gamble.crm.customerio.WebhookReportHandler;
import com.ously.gamble.crm.customerio.payload.CIOCollection;
import com.ously.gamble.crm.customerio.payload.CIOCollections;
import com.ously.gamble.crm.customerio.payload.CIORepWH;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Detect players which have played and might need an update on crm (balance, tokens, ...)
 */
@Component
@Profile("!test")
@ConditionalOnProperty(prefix = "customerio", name = "enabled", havingValue = "true")
@RabbitListener(queues = {CustomerIOConfiguration.CUSTOMER_IO_WEBHOOK_QUEUE, CRMAdapter.CRM_QUEUE_USEREVENT,
        CRMAdapter.CRM_QUEUE_USERDEVICE,
        CRMAdapter.CRM_QUEUE_USERUPDATE, CRMAdapter.CRM_QUEUE_USERUPDATES,
        CRMAdapter.CRM_QUEUE_USERREMOVE, CRMAdapter.CRM_QUEUE_USERTAG, CRMAdapter.CRM_QUEUE_COLLECTIONS},
        containerFactory = "directRabbitListenerContainerFactory", id = "customerIOListenerContainer",
        concurrency = "2")
public class CRMListener {
    private final ObjectMapper om;
    private final WebhookReportHandler handler;
    private final Logger log = LoggerFactory.getLogger(CRMListener.class);
    private final CRMAdapter crmService;
    private final CustomerIOAppApi appApi;

    public CRMListener(CRMAdapter crmService, ObjectMapper om, WebhookReportHandler whHandler, CustomerIOAppApi aApi) {
        this.crmService = crmService;
        this.om = om;
        this.handler = whHandler;
        this.appApi = aApi;
    }



    @RabbitHandler
    public void handleCrmEvent(CRMUserEvent cioEvent) {
        crmService.storeEvent(cioEvent);
    }


    @RabbitHandler
    public void handleCrmDevice(CRMDeviceMessage cioDevice) {
        crmService.storeDevice(cioDevice);
    }


    @RabbitHandler
    public void handleUserCrmUpdate(CRMUserUpdate cioUUpdate) {
        crmService.storeUserUpdate(cioUUpdate);
    }


    @RabbitHandler
    public void handleUserCrmUpdates(CRMUserUpdates cioUUpdates) {
        cioUUpdates.getUpdates().forEach(crmService::storeUserUpdate);
    }


    @RabbitHandler
    public void handleUserCrmRemove(CRMUserRemovalEvent uid) {
        crmService.deleteUID(uid.crmUid(), uid.suppress());
    }


    @RabbitHandler
    public void handleUserTagChange(CRMUserTagChangeEvent tagChangeEvent) {
        crmService.doUsertagChange(tagChangeEvent);
    }


    @RabbitHandler
    public void handleCollectionUpdateRequest(CRMCollectionUpdateRequest updateRequest) {
        Optional<CIOCollections> collections = appApi.getCollections();
        if (collections.isPresent()) {
            Optional<CIOCollection> first = collections.get().getCollections().stream().filter(a -> updateRequest.getName().equals(a.getName())).findFirst();
            if (first.isPresent()) {
                int id = first.get().getId();
                appApi.updateCollection(id, updateRequest.getName(), updateRequest.getData());
            } else {
                appApi.createCollection(updateRequest.getName(), updateRequest.getData());
            }
        }
    }


    @RabbitHandler
    public void handleCIOWebhook(String whData) {
        try {
            var repItem = om.readValue(whData, CIORepWH.class);
            handler.handleReport(repItem);
            log.debug("CIO-WH-Data handled: type='{}', metric='{}'", repItem.getObjectType(), repItem.getMetric());
        } catch (Exception e) {
            log.error("CIO-WH-Data could not be handled:{}->{}", whData, e.getMessage());
        }

    }
}

package com.ously.gamble.crm.customerio;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "customerio" )
public class CustomerIOConfig {

    Boolean enabled = false;
    String siteid = "da5a4f8df7db311d20c6";
    String apikey = "457e254b64304e051fa8";

    String appkey = "2b30d6a342e41c59f57e287ff84468d1";
    String baseUrl = "https://track-eu.customer.io/api/v1";

    String appUrl = "https://api-eu.customer.io/v1";

    String whsignkey = "4239265c20cc9ab0c8ce63403bfe97a5723b05b6e41d1bf75d7539f7b0f1335f";

    String removalSegment = "CLEANUP_CANDIDATES";

    /**
     * number of users to remove per call
     */
    int batchsize = 250;

    public int getBatchsize() {
        return batchsize;
    }

    public void setBatchsize(int batchsize) {
        this.batchsize = batchsize;
    }

    public String getSiteid() {
        return siteid;
    }

    public void setSiteid(String siteid) {
        this.siteid = siteid;
    }

    public String getApikey() {
        return apikey;
    }

    public void setApikey(String apikey) {
        this.apikey = apikey;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getWhsignkey() {
        return whsignkey;
    }

    public void setWhsignkey(String whsignkey) {
        this.whsignkey = whsignkey;
    }

    public String getAppkey() {
        return appkey;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    public String getAppUrl() {
        return appUrl;
    }

    public void setAppUrl(String appUrl) {
        this.appUrl = appUrl;
    }


    public String getRemovalSegment() {
        return removalSegment;
    }

    public void setRemovalSegment(String removalSegment) {
        this.removalSegment = removalSegment;
    }
}

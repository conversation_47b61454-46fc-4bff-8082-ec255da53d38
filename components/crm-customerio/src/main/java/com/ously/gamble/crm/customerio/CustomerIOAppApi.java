package com.ously.gamble.crm.customerio;

import com.ously.gamble.api.crm.CRMConfiguration;
import com.ously.gamble.crm.customerio.payload.*;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;


@Service
@ConditionalOnProperty(prefix = "customerio", name = "enabled", havingValue = "true")
public class CustomerIOAppApi {
    private final Logger log = LoggerFactory.getLogger(CustomerIOAppApi.class);
    private final Object segLock = new Object();

    private final String baseUrl;
    private final RestTemplate rTemplate;
    private final HttpHeaders headers;
    private final CRMConfiguration crmConfig;
    private Map<String, CIOSegment> segments = new ConcurrentHashMap<>(0);

    public CustomerIOAppApi(RestTemplate rT, CustomerIOConfig cioCfg, CRMConfiguration crmCfg) {
        this.rTemplate = rT;
        this.crmConfig = crmCfg;
        if (cioCfg.baseUrl.endsWith("v1")) {
            this.baseUrl = cioCfg.appUrl + '/';
        } else {
            this.baseUrl = cioCfg.appUrl;
        }
        log.info("Setting up Customer.IO app adapter with siteId={}, baseUrl={}", cioCfg.getSiteid()
                , baseUrl);
        var authHeader =
                "Bearer " + cioCfg.appkey;
        this.headers = new HttpHeaders();
        headers.add("Authorization", authHeader);
        headers.add("Content-Type", "application/json");


    }

    @PostConstruct
    public void initialiseSegments() {
        if (crmConfig.getUserTags().isEnabled()) {
            loadSegments();
            if (!segments.containsKey("UT_TEST")) {
                createSegment("UT_TEST", "Testing creation of segments");
            }
        }
    }


    private void loadSegments() {
        synchronized (this.segLock) {
            getSegments().ifPresent(a -> {
                Map<String, CIOSegment> segs = new ConcurrentHashMap<>(5);
                a.getSegments().stream().filter(seg -> seg.getName().startsWith("UT_") && seg.getType() == CIOSegmentType.manual).forEach(b -> segs.put(b.getName(), b));
                this.segments = segs;
            });
        }
    }


    public Optional<CIOSegmentList> getSegments() {
        try {
            var request = new HttpEntity<>(null, headers);
            var url = baseUrl + "segments";
            var exchange = this.rTemplate.exchange(url, HttpMethod.GET, request,
                    new CIOSegmentListParameterizedTypeReference());
            if (exchange.getStatusCode().is2xxSuccessful()) {
                return Optional.ofNullable(exchange.getBody());
            }
            log.error("Error retrieving CIO-Segments:{}", exchange);
        } catch (Exception e) {
            log.error("Error sending CIO-UsrUpdate:{}", e.getMessage());
        }
        return Optional.empty();
    }


    public Optional<CIOCollections> getCollections() {
        try {
            var request = new HttpEntity<>(null, headers);
            var url = baseUrl + "collections";
            var exchange = this.rTemplate.exchange(url, HttpMethod.GET, request,
                    CIOCollections.class);
            if (exchange.getStatusCode().is2xxSuccessful()) {
                return Optional.ofNullable(exchange.getBody());
            }
            log.error("Error retrieving Collections:{}", exchange);
        } catch (Exception e) {
            log.error("Error getting collections:{}", e.getMessage());
        }
        return Optional.empty();
    }


    public Optional<String> createCollection(String name, List<?> data) {
        try {
            var asReq = new CIOUpdateCollectionRequest(name, data);

            var request = new HttpEntity<>(asReq, headers);
            var url = baseUrl + "collections";
            var exchange = this.rTemplate.exchange(url, HttpMethod.POST, request,
                    String.class);
            if (exchange.getStatusCode().is2xxSuccessful()) {
                return Optional.ofNullable(exchange.getBody());
            }
            log.error("Error creating collection:{}", name);
        } catch (Exception e) {
            log.error("Error creating collection:{}", e.getMessage());
        }
        return Optional.empty();
    }


    public Optional<String> updateCollection(int id, String name, List<?> data) {
        try {
            var asReq = new CIOUpdateCollectionRequest(name, data);

            var request = new HttpEntity<>(asReq, headers);
            var url = baseUrl + "collections/" + id;
            var exchange = this.rTemplate.exchange(url, HttpMethod.PUT, request,
                    String.class);
            if (exchange.getStatusCode().is2xxSuccessful()) {
                return Optional.ofNullable(exchange.getBody());
            }
            log.error("Error updating collection:{}", name);
        } catch (Exception e) {
            log.error("Error updating collection:{}", e.getMessage());
        }
        return Optional.empty();
    }


    public Optional<CIOAddSegmentResponse> createSegment(String name, String description) {
        try {
            var asReq = new CIOAddSegmentRequest(name, description);

            var request = new HttpEntity<>(asReq, headers);
            var url = baseUrl + "segments";
            var exchange = this.rTemplate.exchange(url, HttpMethod.POST, request,
                    new CIOAddSegmentResponseParameterizedTypeReference());
            if (exchange.getStatusCode().is2xxSuccessful()) {
                if (name.equals(exchange.getBody().getSegment().getName())) {
                    segments.put(name, exchange.getBody().getSegment());
                }
                return Optional.ofNullable(exchange.getBody());
            }
            log.error("Error creating CIO-Segment:{}", exchange);
        } catch (Exception e) {
            log.error("Error creating CIO-Segment:{}", e.getMessage());
        }
        return Optional.empty();
    }

    public Optional<CIOSegmentMembership> getSegmentMembership(long segmentId, long limit) {
        try {
            var request = new HttpEntity<>(null, headers);
            var url = baseUrl + "segments/{segmentId}/membership?limit={limit}";
            var exchange = this.rTemplate.exchange(url, HttpMethod.GET, request,
                    new CIOSegmentMembershipParameterizedTypeReference(), segmentId, limit);
            if (exchange.getStatusCode().is2xxSuccessful()) {
                return Optional.ofNullable(exchange.getBody());
            }
            log.error("Error retrieving CIO-Segment-Membership:{}", exchange);
        } catch (Exception e) {
            log.error("Error retrieving CIO-Segment-Membership:{}", e.getMessage());
        }
        return Optional.empty();
    }


    private static class CIOSegmentListParameterizedTypeReference extends ParameterizedTypeReference<CIOSegmentList> {
    }

    private static class CIOSegmentMembershipParameterizedTypeReference extends ParameterizedTypeReference<CIOSegmentMembership> {
    }

    private static class CIOAddSegmentResponseParameterizedTypeReference extends ParameterizedTypeReference<CIOAddSegmentResponse> {
    }
}

package com.ously.gamble.crm.customerio.payload;

import com.ously.gamble.api.crm.CRMDeviceMessage;
import com.ously.gamble.api.crm.CRMUtils;

public class CIODeviceMessage extends CIOMessage {
    boolean delete;
    CIODevice device;
    String uid;

    public CIODeviceMessage() {
        super();
    }

    public CIODeviceMessage(CRMDeviceMessage cd) {
        super(cd.getTimestamp());
        this.delete = cd.isDelete();
        this.device = new CIODevice(cd.getDevice());
        this.uid = CRMUtils.createUserId(cd.getUid());
    }

    public boolean isDelete() {
        return delete;
    }

    public void setDelete(boolean delete) {
        this.delete = delete;
    }

    public CIODevice getDevice() {
        return device;
    }

    public void setDevice(CIODevice device) {
        this.device = device;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

}

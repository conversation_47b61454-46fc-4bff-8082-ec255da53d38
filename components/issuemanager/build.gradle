buildscript {

    dependencies {

        if (project.hasProperty("swagger")) {
            println "** NO bytecode enhancements"
        } else {
            println "** Using bytecode enhancements (dep)"
            classpath "org.hibernate.orm:hibernate-gradle-plugin:$hibernateVersion"
        }
    }
}


plugins {
    id 'java-library'
}

if (project.hasProperty("swagger")) {
    println "** NOT Using bytecode enhancements"

} else {
    println "** Using bytecode enhancements"
    apply plugin: 'org.hibernate.orm'
}


repositories {
    mavenCentral()
}

dependencies {
    implementation project(':api')
    implementation project(':persistence')
    implementation 'io.hypersistence:hypersistence-utils-hibernate-62:3.4.3'


    implementation "org.springframework.amqp:spring-amqp"
    implementation "org.springframework.amqp:spring-rabbit"

    implementation("org.springframework.security:spring-security-core")
    implementation "net.javacrumbs.shedlock:shedlock-spring:${shedlockSpringVersion}"

    testImplementation 'org.junit.jupiter:junit-jupiter-api'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine'
}

test {
    useJUnitPlatform()
}
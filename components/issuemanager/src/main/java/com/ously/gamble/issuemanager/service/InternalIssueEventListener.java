package com.ously.gamble.issuemanager.service;

import com.ously.gamble.api.issuemanager.AbstractBaseIssue;
import com.ously.gamble.api.issuemanager.InternalIssueItem;
import com.ously.gamble.api.issuemanager.IssueDetails;
import com.ously.gamble.issuemanager.configuration.InternalIssueConfiguration;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class InternalIssueEventListener {

    private final InternalIssueConfiguration iiConfig;
    private final RabbitTemplate rbTmpl;

    public InternalIssueEventListener(InternalIssueConfiguration iCf, RabbitTemplate tbT) {
        this.iiConfig = iCf;
        this.rbTmpl = tbT;
    }

    @EventListener
    public void queueIssueEvent(InternalIssueItem issueItem) {
        try {
            rbTmpl.convertAndSend(iiConfig.getQueue(), issueItem);
        } catch (Exception e) {
            //TODO: circular refs in exception -> we should avoid sending exceptions via json
            issueItem.setDetails(new IssueDetails(issueItem.getDetails().type(), issueItem.getDetails().details()));
            rbTmpl.convertAndSend(iiConfig.getQueue(), issueItem);
        }
    }

    @EventListener
    public void queueGenericIssueEvent(AbstractBaseIssue aBaseIssue) {
        var issueItem = aBaseIssue.toInternalIssue();
        try {
            rbTmpl.convertAndSend(iiConfig.getQueue(), issueItem);
        } catch (Exception e) {
            //TODO: circular refs in exception -> we should avoid sending exceptions via json
            issueItem.setDetails(new IssueDetails(issueItem.getDetails().type(), issueItem.getDetails().details()));
            rbTmpl.convertAndSend(iiConfig.getQueue(), issueItem);
        }
    }

}

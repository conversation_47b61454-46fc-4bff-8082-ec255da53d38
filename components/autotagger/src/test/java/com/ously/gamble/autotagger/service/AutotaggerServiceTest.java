package com.ously.gamble.autotagger.service;

import com.ously.gamble.api.user.UserAutoTagModel;
import com.ously.gamble.autotagger.config.DroolsConfig;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class AutotaggerServiceTest {

    @Test
    void testVIPTagging() {

        DroolsConfig dc = new DroolsConfig();
        AutotaggerService atService = new AutotaggerService(dc.tagRulesKieContainer(), null, null);

        UserAutoTagModel uatm = new UserAutoTagModel(
                1L,
                Instant.now(),
                Instant.now(),
                1000.0,
                800.0,
                20,
                Instant.now(),
                Instant.now(),
                List.of("T1", "T2")
        );

        var result = atService.doAutotagging(uatm);
        assertTrue(result.isNeedsUpdate());
        assertTrue(result.getTags().contains("VIP"));
        assertTrue(result.getTags().contains("PS_WHALE"));
        assertTrue(result.getTags().contains("WT_BABY"));
        assertTrue(result.getTags().contains("VIP_SILVER"));

        assertEquals(6, result.getTags().size());


        // now see if tags are properly removed

        uatm = new UserAutoTagModel(
                1L,
                Instant.now(),
                Instant.now(),
                1000.0,
                800.0,
                20,
                Instant.now().minus(50, ChronoUnit.DAYS),
                Instant.now(),
                List.of("T1", "T2", "VIP", "PS_DOLPHIN", "VIP_BRONZE")
        );
        result = atService.doAutotagging(uatm);
        assertTrue(result.isNeedsUpdate());
        assertFalse(result.getTags().contains("VIP"));
        assertFalse(result.getTags().contains("VIP_BRONZE"));
        assertTrue(result.getTags().contains("PS_WHALE"));
        assertTrue(result.getTags().contains("WT_BABY"));
        assertEquals(4, result.getTags().size());


    }

}
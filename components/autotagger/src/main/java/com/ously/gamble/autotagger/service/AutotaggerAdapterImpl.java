package com.ously.gamble.autotagger.service;

import com.ously.gamble.api.autotagger.AutotaggerEvent;
import com.ously.gamble.conditions.ConditionalOnBackendOrOffloader;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@ConditionalOnBackendOrOffloader
@ConditionalOnProperty(prefix = "autotagger", name = "enabled", havingValue = "true", matchIfMissing = true)
@Component
public class AutotaggerAdapterImpl implements AutotaggerAdapter {

    private final RabbitTemplate rbTemplate;


    public AutotaggerAdapterImpl(@Qualifier("rabbitTemplateNoTx") RabbitTemplate tbTemplate) {
        rbTemplate = tbTemplate;
    }

    @EventListener
    public void handleAutotagEvent(AutotaggerEvent req) {
        evalUser(req.userId());
    }

    private void evalUser(long userId) {
        rbTemplate.convertAndSend(AutotaggerAdapter.AUTOTAGGER_QUEUE, userId);
    }
}

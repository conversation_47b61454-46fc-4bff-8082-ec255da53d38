package com.ously.gamble.firestore.configuration;

import com.ously.gamble.api.clouddb.CloudDbAdapter;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(prefix = "clouddb", name = "enabled", havingValue = "true")
@ConditionalOnOffloader
public class FirestoreQueueConfiguration {

    final Logger logger = LoggerFactory.getLogger(this.getClass());


    @Bean
    DirectExchange clouddbExchange() {
        return ExchangeBuilder.directExchange("clouddb").durable(true).build();
    }

    @Bean
    Binding clouddbAddEntryBinding() {
        return BindingBuilder.bind(cloudDbAddQueue()).to(clouddbExchange()).withQueueName();
    }


    @Bean
    Queue cloudDbAddQueue() {
        return QueueBuilder.durable(CloudDbAdapter.CLOUDDB_ADDENTRY)
                .withArgument("x-dead-letter-exchange", "")
                .withArgument("x-dead-letter-routing-key",
                        cloudDbAddQueueQueueDLQ().getName())
                .build();
    }


    @Bean
    Queue cloudDbAddQueueQueueDLQ() {
        return QueueBuilder.durable(CloudDbAdapter.CLOUDDB_ADDENTRY + "DLQ").build();
    }

    @Bean
    Binding clouddbDelEntryBinding() {
        return BindingBuilder.bind(cloudDbDelQueue()).to(clouddbExchange()).withQueueName();
    }


    @Bean
    Queue cloudDbDelQueue() {
        return QueueBuilder.durable(CloudDbAdapter.CLOUDDB_DELETEENTRY)
                .withArgument("x-dead-letter-exchange", "")
                .withArgument("x-dead-letter-routing-key",
                        cloudDbDelQueueQueueDLQ().getName())
                .build();
    }


    @Bean
    Queue cloudDbDelQueueQueueDLQ() {
        return QueueBuilder.durable(CloudDbAdapter.CLOUDDB_DELETEENTRY + "DLQ").build();
    }

}

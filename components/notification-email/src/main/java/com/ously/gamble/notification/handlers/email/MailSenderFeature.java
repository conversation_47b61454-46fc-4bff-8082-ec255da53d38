package com.ously.gamble.notification.handlers.email;

import com.ously.gamble.api.features.AbstractPlatformFeature;
import com.ously.gamble.api.features.FeatureDescription;
import com.ously.gamble.api.features.PlatformFeature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@ConditionalOnProperty(prefix = "semail", name = "enabled", havingValue = "true")
public class MailSenderFeature extends AbstractPlatformFeature implements PlatformFeature {
    @Override
    public FeatureDescription getDescription() {
        return new FeatureDescription("Mail Sender feature", "Custom mailsender via direct mailserver integration");
    }

    @Override
    public List<String> getWeeklyAnalyzeTables() {
        return List.of("email_out"
        );
    }
}

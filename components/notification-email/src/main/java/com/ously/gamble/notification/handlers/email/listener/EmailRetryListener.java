package com.ously.gamble.notification.handlers.email.listener;

import com.ously.gamble.notification.handlers.email.UserMessageMailHandler;
import com.ously.gamble.notification.handlers.email.config.SEmailConfiguration;
import com.ously.gamble.notification.handlers.email.persistence.model.MailOutId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Detect players which have played and might need an update on crm (balance, tokens, ...)
 */
@Component
@ConditionalOnProperty(prefix = "semail", name = "enabled", havingValue = "true")
public class EmailRetryListener {

    final Logger log = LoggerFactory.getLogger(getClass());

    private final UserMessageMailHandler umHandler;

    public EmailRetryListener(UserMessageMailHandler umHandler) {
        this.umHandler = umHandler;
    }


    @RabbitListener(queues = SEmailConfiguration.SEMAIL_SEND_QUEUE, containerFactory =
            "directRabbitListenerContainerFactory",
                    concurrency = "2")
    @Transactional
    public void handleCrmEvent(MailOutId mOutId) {
        umHandler.retrySend(mOutId.getUserId(), mOutId.getQualifier());
    }


}

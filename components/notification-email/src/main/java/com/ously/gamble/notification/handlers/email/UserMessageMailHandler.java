package com.ously.gamble.notification.handlers.email;

import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.api.localisation.LanguageCode;
import com.ously.gamble.api.localisation.LocalisationService;
import com.ously.gamble.api.notification.EmailHandler;
import com.ously.gamble.api.notification.MailOutLog;
import com.ously.gamble.api.notification.MailOutLogEntry;
import com.ously.gamble.api.notification.MailOutSentContent;
import com.ously.gamble.api.user.UserService;
import com.ously.gamble.notification.handlers.email.config.SEmailConfiguration;
import com.ously.gamble.notification.handlers.email.persistence.model.MailOut;
import com.ously.gamble.notification.handlers.email.persistence.repository.MailOutRepository;
import com.ously.gamble.persistence.model.messages.UserMessageContent;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

@Component
@ConditionalOnProperty(prefix = "semail", name = "enabled", havingValue = "true")
public class UserMessageMailHandler implements EmailHandler {

    private final Logger log = LoggerFactory.getLogger(UserMessageMailHandler.class);

    private final JavaMailSender mailSender;
    private final FeatureConfig features;
    private final MailOutRepository moRepo;
    private final LocalisationService locService;

    private final RabbitTemplate rabbitTemplate;
    private final UserService userService;

    public UserMessageMailHandler(JavaMailSender mailSender, UserService uSrv,
                                  FeatureConfig features, MailOutRepository moR,
                                  LocalisationService locSrv, RabbitTemplate rbT) {
        this.mailSender = mailSender;
        this.features = features;
        this.moRepo = moR;
        this.locService = locSrv;
        this.rabbitTemplate = rbT;
        this.userService = uSrv;
    }

    @Override
    public void sendOut(long userId, String qualifier, UserMessageContent msg) {

        var byId = moRepo.findByUserIdAndQualifier(userId, qualifier);
        if (byId.isPresent()) {
            log.warn("Potential duplicate mail request, ignoring");
            return;
        }

        // Store in mailOut
        var mo = new MailOut();
        mo.setUserId(userId);
        mo.setQualifier(qualifier);
        mo.setContent(msg);
        mo.setNextSendAt(null);
        mo.setSentAt(null);
        mo.setRetries(0);
        mo.setLogs(new MailOutLog());

        // Try sending
        try {

            var logcontPair = sendMail(userId, qualifier, msg);
            mo.getLogs().addEntry(logcontPair.getValue0());
            mo.setSentContent(logcontPair.getValue1());
            if (logcontPair.getValue0().isSend()) {
                mo.setSentAt(Instant.now());
                mo.setNextSendAt(null);
            } else {
                mo.setRetries(mo.getRetries() + 1);
                mo.setNextSendAt(getBackoffInstant(mo.getRetries()));
            }

        } catch (Exception e) {
            mo.setRetries(mo.getRetries() + 1);
            mo.setNextSendAt(getBackoffInstant(mo.getRetries()));
        }
        // add logs and either mark sent or set next_send_at to now+ backoff
        moRepo.saveAndFlush(mo);


    }

    @Override
    @Transactional
    public void removeOldEntries(int days) {
        var barrier = Instant.now().minus(days, ChronoUnit.DAYS);
        var delCnt = moRepo.deleteAllBySentAtBeforeAndSentAtIsNotNull(barrier);
        if (delCnt > 0) {
            log.info("Removed {} mailout entries older than {} days", delCnt, days);
        }
    }

    @Override
    @Transactional
    public void requeueRetries() {
        // load X mails where resend_at is not null && resend_at < now() order by resend_at ASC
        var retryCandidates =
                moRepo.findFirst500BySentAtIsNullAndNextSendAtIsNotNullAndNextSendAtIsBeforeAndRetriesIsLessThanOrderByNextSendAt(Instant.now(), 50);
        retryCandidates.stream().map(MailOut::getId).forEach(a2 ->
                rabbitTemplate.convertAndSend(SEmailConfiguration.SEMAIL_SEND_QUEUE, a2));
    }

    private Pair<MailOutLogEntry, MailOutSentContent> sendMail(long userId, String qualifier,
                                                               UserMessageContent msg) {


        var to = Objects.requireNonNullElseGet(msg.variables().get("email"),
                () -> getUsersEmail(userId));

        var from = features.getMailfrom();
        var moSContent = new MailOutSentContent();
        try {
            var mailmsg = mailSender.createMimeMessage();
            var helper = new MimeMessageHelper(mailmsg,
                    MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
                    StandardCharsets.UTF_8.name());

            var stringStringMap = locService.resolveTemplatesForLang(msg.variables(),
                    LanguageCode.EN, msg.title(),
                    msg.content());
            var title = stringStringMap.get(msg.title());
            var body = stringStringMap.get(msg.content());

            var isHtml = false;
            if (body.charAt(0) == '@') {
                body = body.substring(1);
                isHtml = true;
            }
            moSContent.setBody(body);
            moSContent.setHtml(isHtml);
            moSContent.setTitle(title);
            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(title);
            helper.setText(body, isHtml);

            //            var attachements = msg.getAttachements();
//            for (var attachement : attachements.entrySet()) {
//                helper.addInline(attachement.getKey(), new ClassPathResource(attachement.getValue()));
//            }
//
//            if (to.endsWith("@nomail.nomail")) {
//                log.warn("Trying to send a mail to unset/unvalidated email for user->{}", msg.getuId());
//                return Receipt.error(msg, "Trying to send a mail to unset/unvalidated email");
//            }
//
            mailSender.send(mailmsg);
        } catch (Exception e) {
            log.error("Sending mail to {} failed: {}", to, e.getMessage());
            return Pair.with(new MailOutLogEntry(false, e.getMessage()), moSContent);
        }


        return Pair.with(new MailOutLogEntry(true, "OK"), moSContent);
    }

    private String getUsersEmail(long userId) {
        try {
            return userService.getCasinoUser(userId).getEmail();
        } catch (Exception e) {
//
        }
        return null;
    }

    private static Instant getBackoffInstant(int retries) {
        var backoffMult = Math.min(Math.max((long) retries ^ 2, 1L), 120L);
        return Instant.now().plusSeconds(backoffMult * 60L);
    }

    public void retrySend(long userId, String qualifier) {
        var optMailOut = moRepo.findByUserIdAndQualifier(userId, qualifier);
        if (optMailOut.isEmpty()) {
            return;
        }
        var mailOut = optMailOut.get();

        if (mailOut.getNextSendAt() == null && mailOut.getRetries() > 0) {
            return;
        }

        if (mailOut.getSentAt() != null) {
            return;
        }

        try {

            var logcontPair = sendMail(userId, qualifier, mailOut.getContent());
            mailOut.getLogs().addEntry(logcontPair.getValue0());
            if (logcontPair.getValue0().isSend()) {
                mailOut.setSentAt(Instant.now());
                mailOut.setNextSendAt(null);
                mailOut.setSentContent(logcontPair.getValue1());
            } else {
                mailOut.setRetries(mailOut.getRetries() + 1);
                mailOut.setNextSendAt(getBackoffInstant(mailOut.getRetries()));
                mailOut.setSentContent(logcontPair.getValue1());
            }

        } catch (Exception e) {
            mailOut.setRetries(mailOut.getRetries() + 1);
            mailOut.setNextSendAt(getBackoffInstant(mailOut.getRetries()));
        }

        if (mailOut.getRetries() > 20) {
            mailOut.setNextSendAt(null);
        }

        // add logs and either mark sent or set next_send_at to now+ backoff
        moRepo.saveAndFlush(mailOut);
    }
}

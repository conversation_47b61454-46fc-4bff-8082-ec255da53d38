package com.ously.gamble.googleanalytics;

import com.google.analytics.data.v1beta.*;

public final class GATester {

    private GATester() {
    }

    public static void main(String[] args) throws Exception {
        /*
          TODO(developer): Replace this variable with your Google Analytics 4 property ID before
          running the sample.
         */
        var propertyId = "273835062";
        sampleRunReport(propertyId);
    }

    // This is an example snippet that calls the Google Analytics Data API and runs a simple report
    // on the provided GA4 property id.
    static void sampleRunReport(String propertyId) throws Exception {
        /*
          TODO(developer): Uncomment this variable and replace with your Google Analytics 4 property ID
          before running the sample.
         */
        // propertyId = "YOUR-GA4-PROPERTY-ID";

        // Using a default constructor instructs the client to use the credentials
        // specified in GOOGLE_APPLICATION_CREDENTIALS environment variable.
        try (var analyticsData = BetaAnalyticsDataClient.create()) {

            var request =
                    RunReportRequest.newBuilder()
                            .setProperty("properties/" + propertyId)
                            .addDimensions(Dimension.newBuilder().setName("country"))
                            .addMetrics(Metric.newBuilder().setName("activeUsers"))
                            .addDateRanges(DateRange.newBuilder().setStartDate("2022-01-31").setEndDate("today"))
                            .build();

            // Make the request.
            var response = analyticsData.runReport(request);

            System.out.println("Report result:");
            // Iterate through every row of the API response.
            for (var row : response.getRowsList()) {
                System.out.printf(
                        "%s, %s%n", row.getDimensionValues(0).getValue(), row.getMetricValues(0).getValue());
            }
        }
    }
}

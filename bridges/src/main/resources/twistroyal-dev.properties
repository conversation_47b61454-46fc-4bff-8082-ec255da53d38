spring.config.import=classpath:common-config-twistroyal/shared-dev.properties
features.custombetsizes=true
bridge.localhostname=http://localhost:8080/
#
# Spinomenal Stuff
#
spinomenal.partner_id=gameplay-dev
spinomenal.private_key=dsf3454324
spinomenal.url=https://social-api-dev.spinomenal.com/GameLauncher/LaunchSocial
spinomenal.tokenExpiryInHours=2400
#
# EDICT Stuff
#
edict.callerId=edict_TEST_accnt
edict.callerPw=ZTre63324_Funz88
edict.casinoName=ously-fm
edict.baseUrl=https://edu013-i.edictmaltaservices.com.mt/gamestart.html?
#
# WAZDAN Stuff
#
wazdan.auth_user=wazdan
wazdan.auth_password=staging
wazdan.lang=de
wazdan.partner=ously
wazdan.moneymode=real
wazdan.launcherurl=https://gl-staging.wazdanep.com/OUSLY/gamelauncher?
wazdan.activeCurrency=SPN
#
# Netent Stuff
#
netent.lang=en_US
netent.activeCurrency=SPN
netent.regUserUrl=https://socialcasinocontainerwebapp-development.azurewebsites.net/netent-social/v2/register-player
netent.casinoName=ously
netent.apiKey=JkGC2NDAt1QtuyXPXiZ4pkH015qj0ZcFCk5Wi82pKpFN0
netent.jwtSecret=VNolang234Perf88%57
#
# Switches
#
settings.datasourceproxy.enabled=false
settings.customerio.enabled=false
#
#
#
monitoring.enabled=true

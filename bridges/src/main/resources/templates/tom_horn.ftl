<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>TomHorn</title>
    <style>
        html, body {
            width: 100%;
            height: 100%;
            background-color: #000000;
        }

        body, #gameClientPlaceholder {
            margin: 0;
            padding: 0;
            color: #000000;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<script type="text/javascript" src="${BaseURL}ClientUtils.js"></script>

<body>
<div id="gameClientPlaceholder">
    <h2>Game Client starting procedure failed!</h2>
</div>
<script type="text/javascript" src="${BaseURL}/ClientUtils.js"></script>
<script type="text/javascript">
    var params = {
        <#list params as key,value>
        '${key}': '${value}',
        </#list>
    };
    renderClient(params, 'gameClientPlaceholder');

</script>
</body>
</html>

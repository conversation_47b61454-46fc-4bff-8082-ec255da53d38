#
# Settings / features
#
features.baseUrl=http://localhost:8080
features.autoActive=false
features.mailfrom=<EMAIL>
features.fakeValidation=true
features.stage=dev
features.testing=true
features.popupsEnabled=true
features.jurisdiction=SOC
features.thumbsBase=https://d2do3nvj2be99e.cloudfront.net/shared_local
# RTP Service settings
seriously.rtp.minspins=10
#
# Spring DATASOURCE (DataSourceAutoConfiguration & DataSourceProperties)
#
spring.datasource.url=********************************************************************************************************************
spring.datasource.username=cpp
spring.datasource.password=fatass
#
# Support
#
support.email=<EMAIL>
support.system=LC
#
# Actuator/mgmt
#
management.endpoint.mappings.enabled=true
management.endpoint.startup.enabled=true
management.endpoints.jmx.exposure.include=health,mappings
management.endpoints.web.exposure.include=health,prometheus,info,startup
management.endpoints.jmx.exposure.exclude=
#
# Liquibase
#
spring.liquibase.contexts=${features.stage}
#
# Datasource-Proxy to log/display SQL
#
decorator.datasource.enabled=true
decorator.datasource.datasource-proxy.query.enable-logging=true
decorator.datasource.datasource-proxy.slow-query.enable-logging=true
decorator.datasource.p6spy.tracing.include-parameter-values=true
dynatrace.tracer.replica.enabled=false
#
# RabbitMQ
#
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.addresses=amqp://guest:guest@localhost
#
# Our own redisson properties
#
ously.redisson.host=localhost
ously.redisson.password=fatass69
ously.redisson.port=6379
ously.redisson.ssl=false
# SINGLE, REPLICATED
ously.redisson.type=SINGLE
# SLAVE,MASTER,MASTER_SLAVE (for replicated)
ously.redisson.mode=MASTER_SLAVE
#
# Gamemanager
#
#seriously.gamemanager.url=http://service-gamemanager.seriously-shared:9001/api/v1
seriously.gamemanager.key=gmgrDevfij393f3r
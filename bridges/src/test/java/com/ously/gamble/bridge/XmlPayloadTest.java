package com.ously.gamble.bridge;

import com.ously.gamble.bridge.microgaming.MicrogamingUtils;
import com.ously.gamble.bridge.microgaming.payload.*;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;

import java.io.StringReader;
import java.io.StringWriter;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;


public class XmlPayloadTest {

    JAXBContext getContext() throws JAXBException {
        return JAXBContext.newInstance(LoginRequest.class, LoginResponse.class, LoginPkt.class);
    }

    @Test
    public void unwrapLoginTest() throws Exception {
        var incoming = IOUtils.resourceToString("/microgaming/mgLoginReq.xml", StandardCharsets.UTF_8);
        incoming = incoming.replace("<pkt>", "").replace("</pkt>", "");

        var o = (LoginRequest) MicrogamingUtils.parseRequest(incoming.getBytes());

        assertNotNull(o);
        assertEquals("24971455-aecc-4a69-8494-f544d49db3da", o.getCall().getSeq());
        assertEquals("someothertoken", o.getCall().getToken());

        //
        var jc = getContext();
        var lmc = (LoginRequest) jc.createUnmarshaller().unmarshal(new StringReader(incoming));
        assertEquals("test", lmc.getAuth().getLogin());

        var lr = new LoginResponse();
        lr.getResult().setSeq("SEQ");
        lr.getResult().setToken("TOK");
        lr.getResult().setLoginname("login");
        lr.getResult().setCurrency("SPINZ");
        lr.getResult().setCountry("DEUTSCHELAND");
        lr.getResult().setCity("HANUA");
        lr.getResult().setBalance(BigInteger.TEN);
        lr.getResult().setBonusbalance(BigInteger.ZERO);
        var sr = new StringWriter();
        var marshaller = jc.createMarshaller();
        // Check response is wrapped
        var s = MicrogamingUtils.marshallLoginResponse(lr);
        assertTrue(s.contains("<pkt>"));
    }

    @Test
    public void responseMarshallingTest() throws Exception {

        var pr = new PlayResponse();
        pr.getResult().setBalance(BigInteger.ONE);
        pr.getResult().setToken("TOK");
        pr.getResult().setBonusbalance(BigInteger.ZERO);
        pr.getResult().setExttransactionid("something");
        pr.getResult().setSeq("jjj");
        var s = MicrogamingUtils.marshallPlayResponse(pr);
        assertTrue(s.contains("<pkt>"));
        assertTrue(s.contains("something"));

        var er = new GetBalanceResponse();
        er.getResult().setBalance(BigInteger.ONE);
        er.getResult().setSeq("SEQ");
        er.getResult().setBonusbalance(BigInteger.ZERO);
        er.getResult().setToken("something");
        s = MicrogamingUtils.marshallGetBalanceResponse(er);
        assertTrue(s.contains("<pkt>"));
        assertTrue(s.contains("something"));

        var rr = new RefreshTokenResponse();
        er.getResult().setBalance(BigInteger.ONE);
        er.getResult().setSeq("SEQ");
        er.getResult().setBonusbalance(BigInteger.ZERO);
        er.getResult().setToken("something");
        s = MicrogamingUtils.marshallGetBalanceResponse(er);
        assertTrue(s.contains("<pkt>"));
        assertTrue(s.contains("something"));
    }
}

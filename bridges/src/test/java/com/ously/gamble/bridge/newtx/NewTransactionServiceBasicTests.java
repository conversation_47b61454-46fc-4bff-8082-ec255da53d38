package com.ously.gamble.bridge.newtx;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.session.NewTransactionService;
import com.ously.gamble.api.session.TxRequest;
import com.ously.gamble.api.session.TxRequest.RoundMode;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.Wallet;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.model.session.Jurisdiction;
import com.ously.gamble.persistence.repository.WalletRepository;
import com.ously.gamble.persistence.repository.session.SessionTransactionRepository;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(SpringExtension.class)
@TestMethodOrder(OrderAnnotation.class)
public class NewTransactionServiceBasicTests extends TestContext {

    @Autowired
    NewTransactionService nTxSrv;

    @Autowired
    SessionTransactionRepository ssTxRepo;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    WalletRepository wRepo;

    private TransactionTemplate transactionTemplate;

    @BeforeEach
    public void setup() {
        if (transactionTemplate == null) {
            transactionTemplate = new TransactionTemplate(transactionManager);
            transactionTemplate.setIsolationLevel(TransactionDefinition.ISOLATION_DEFAULT);
            transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        }
    }

    @Test
    @Order(1)
    public void newTxSrvTestsP1() {

        var w = getWalletFor(2L);

        var balance = w.getBalance();

        var req1 = new TxRequest();
        req1.setBet(BigDecimal.ONE);
        req1.setWin(BigDecimal.ZERO);
        req1.setSessionId(100L);
        req1.setUserId(2L);
        req1.setGameId(1L);
        req1.setRoundRef("rnd1");
        req1.setRoundMode(RoundMode.NONE);
        req1.setExternalOrigId("bet1");
        req1.setVendorName("egal");
        req1.setGp(GamePlatform.WEB);
        req1.setJurisdiction(Jurisdiction.SOC);
        req1.setType(TransactionType.BET);
        var txResponse = transactionTemplate.execute(a -> {
            try {
                return nTxSrv.addTxFromProvider(req1);
            } catch (OuslyTransactionException e) {
                return null;
            }
        });
        assert txResponse != null;
        assertEquals(balance.longValue() - 1, txResponse.getNewBalance().longValue());

        req1.setType(TransactionType.WIN);
        req1.setWin(BigDecimal.TEN);
        req1.setBet(BigDecimal.ZERO);
        req1.setExternalOrigId("win1");
        txResponse = transactionTemplate.execute(a -> {
            try {
                return nTxSrv.addTxFromProvider(req1);
            } catch (OuslyTransactionException e) {
                return null;
            }
        });
        assert txResponse != null;
        assertEquals(balance.longValue() - 1 + 10, txResponse.getNewBalance().longValue());
    }

    @Test
    @Order(2)
    public void newTxSrvTestsP2() {
        assertTrue(ssTxRepo.existsByUserIdAndSessionIdAndExternalOrigTxId(2L, 100L, "bet1"));
        var txBet = nTxSrv.findByUserIdAndSessionIdAndOrigId(2L, 100L, "bet1");
        assertNotNull(txBet);
        assertTrue(ssTxRepo.existsByUserIdAndSessionIdAndExternalOrigTxId(2L, 100L, "win1"));
        var txWin = nTxSrv.findByUserIdAndSessionIdAndOrigId(2L, 100L, "win1");
        assertNotNull(txWin);
        var rnd11 = nTxSrv.findRoundTransactionsForUserIdAndSessionId(2L, 100L, "rnd1");
        assertEquals(2, rnd11.size());
    }


    @Test
    @Order(3)
    public void newTxSrvTestsP3() {

        var w = getWalletFor(2L);

        var balance = w.getBalance();

        var req1 = new TxRequest();
        req1.setBet(BigDecimal.ONE);
        req1.setWin(BigDecimal.ZERO);
        req1.setSessionId(101L);
        req1.setUserId(2L);
        req1.setGameId(1L);
        req1.setRoundRef("rnd2");
        req1.setRoundMode(RoundMode.OPEN);
        req1.setExternalOrigId("bet2");
        req1.setVendorName("egal");
        req1.setGp(GamePlatform.WEB);
        req1.setJurisdiction(Jurisdiction.SOC);
        req1.setType(TransactionType.BET);
        var txResponse = transactionTemplate.execute(a -> {
            try {
                return nTxSrv.addTxFromProvider(req1);
            } catch (OuslyTransactionException e) {
                return null;
            }
        });
        assert txResponse != null;
        assertEquals(balance.longValue() - 1, txResponse.getNewBalance().longValue());

        req1.setType(TransactionType.WIN);
        req1.setWin(BigDecimal.TEN);
        req1.setBet(BigDecimal.ZERO);
        req1.setRoundMode(RoundMode.CLOSE);
        req1.setExternalOrigId("win2");
        txResponse = transactionTemplate.execute(a -> {
            try {
                return nTxSrv.addTxFromProvider(req1);
            } catch (OuslyTransactionException e) {
                return null;
            }
        });
        assert txResponse != null;
        assertEquals(balance.longValue() - 1 + 10, txResponse.getNewBalance().longValue());
    }

    @Test
    @Order(4)
    public void newTxSrvTestsP4Rollbacks() {
        var txBet = nTxSrv.findByUserIdAndSessionIdAndOrigId(2L, 101L, "bet2");
        assertNotNull(txBet);
        var txWin = nTxSrv.findByUserIdAndSessionIdAndOrigId(2L, 101L, "win2");
        assertNotNull(txWin);

        var rnd11 = nTxSrv.findRoundTransactionsForUserIdAndSessionId(2L, 101L, "rnd2");
        assertEquals(2, rnd11.size());

        var sRound = nTxSrv.findRoundForSessionIdAndRoundReference(2L, 101L, "rnd2");
        assertNotNull(sRound);


        // Rollback the bet
        final var wallet = getWalletFor(2L);
        var balanceBefore = wallet.getBalance();

        var txResponse = transactionTemplate.execute(a -> {
            try {
                return nTxSrv.performRollback(txBet, wallet);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        });

        assertNotNull(txResponse);
        assertEquals(balanceBefore.longValue() + 1, txResponse.getNewBalance().longValue());
        balanceBefore = txResponse.getNewBalance();


        final var wallet2 = getWalletFor(2L);
        txResponse = transactionTemplate.execute(a -> {
            try {
                return nTxSrv.performRollback(txWin, wallet2);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        });

        assertNotNull(txResponse);
        assertEquals(balanceBefore.longValue() - 10, txResponse.getNewBalance().longValue());
    }

    @Test
    @Order(5)
    public void newTxSrvTestsP5JurisdictionViolation() {

        var w = getWalletFor(2L);

        var req1 = new TxRequest();
        req1.setBet(BigDecimal.valueOf(100L));
        req1.setWin(BigDecimal.ZERO);
        req1.setSessionId(103L);
        req1.setUserId(2L);
        req1.setGameId(1L);
        req1.setRoundRef("rnd5");
        req1.setRoundMode(RoundMode.OPEN);
        req1.setExternalOrigId("bet5");
        req1.setVendorName("egal");
        req1.setGp(GamePlatform.WEB);
        req1.setJurisdiction(Jurisdiction.DE);
        req1.setType(TransactionType.BET);
        var txResponse = transactionTemplate.execute(a -> {
            try {
                var txResponse1 = nTxSrv.addTxFromProvider(req1);
                fail("Should throw exception due to bet limit");
                return txResponse1;
            } catch (OuslyTransactionException e) {
                return null;
            }
        });
        assertNull(txResponse, "Must be null since bet limit was hit");
    }



    /**
     * check simple round OPEN/CLOSE modes (manual open and manual close)
     * (we should add a field in TxResponse which signals if a round was opened/closed so we can do manual error handling if needed)
     */
    @Test
    @Order(9)
    public void newTxSrvTestsRndMgmtP1() {

        var w = getWalletFor(2L);

        var req1 = new TxRequest();
        req1.setBet(BigDecimal.valueOf(100));
        req1.setWin(BigDecimal.ZERO);
        req1.setSessionId(110L);
        req1.setUserId(2L);
        req1.setGameId(1L);
        req1.setRoundRef("rnd22");
        req1.setRoundMode(RoundMode.OPEN);
        req1.setExternalOrigId("betRndA1");
        req1.setVendorName("egal");
        req1.setGp(GamePlatform.WEB);
        req1.setJurisdiction(Jurisdiction.SOC);
        req1.setType(TransactionType.BET);
        var txResponse = transactionTemplate.execute(a -> {
            try {
                return nTxSrv.addTxFromProvider(req1);
            } catch (OuslyTransactionException e) {
                e.printStackTrace();
                return null;
            }
        });
        assertNotNull(txResponse, "Must not be null since tx inside bet limit");
        // check round was created
        var rnd1 = nTxSrv.findRoundForSessionIdAndRoundReference(2L, req1.getSessionId(), req1.getRoundRef());
        assertNotNull(rnd1);
        assertTrue(rnd1.isOpen());

        // now add another transaction (bet) which should reuse the round opened before
        req1.setExternalOrigId("betRndA2");
        txResponse = transactionTemplate.execute(a -> {
            try {
                return nTxSrv.addTxFromProvider(req1);
            } catch (OuslyTransactionException e) {
                e.printStackTrace();
                return null;
            }
        });
        assertNotNull(txResponse, "Must not be null since tx inside bet limit");
        var rnd1b = nTxSrv.findRoundForSessionIdAndRoundReference(req1.getUserId(), req1.getSessionId(), req1.getRoundRef());
        assertNotNull(rnd1b);
        assertTrue(rnd1b.isOpen());

        var txsForOpenRound = nTxSrv.findRoundTransactionsForUserIdAndSessionId(req1.getUserId(), req1.getSessionId(), req1.getRoundRef());
        assertEquals(2, txsForOpenRound.size());

        // now we close
        req1.setRoundMode(RoundMode.CLOSE);
        req1.setBet(BigDecimal.ZERO);
        req1.setWin(BigDecimal.valueOf(300));
        req1.setType(TransactionType.WIN);
        req1.setExternalOrigId("winRndA");
        txResponse = transactionTemplate.execute(a -> {
            try {
                return nTxSrv.addTxFromProvider(req1);
            } catch (OuslyTransactionException e) {
                e.printStackTrace();
                return null;
            }
        });
        assertNotNull(txResponse, "Must not be null since tx inside bet limit");
        rnd1b = nTxSrv.findRoundForSessionIdAndRoundReference(req1.getUserId(), req1.getSessionId(), req1.getRoundRef());
        assertNotNull(rnd1b);
        assertFalse(rnd1b.isOpen());

        txsForOpenRound = nTxSrv.findRoundTransactionsForUserIdAndSessionId(req1.getUserId(), req1.getSessionId(), req1.getRoundRef());
        assertEquals(3, txsForOpenRound.size());
    }

    /**
     * check simple round OPEN_CLOSE mode (manual round with only one tx - directwin)
     */
    @Test
    @Order(9)
    public void newTxSrvTestsRndMgmtP2() {

        var w = getWalletFor(2L);

        var req1 = getTxRequest();
        var txResponse = transactionTemplate.execute(a -> {
            try {
                return nTxSrv.addTxFromProvider(req1);
            } catch (OuslyTransactionException e) {
                e.printStackTrace();
                return null;
            }
        });
        assertNotNull(txResponse, "Must not be null since tx inside bet limit");
        // check round was created
        var rnd1 = nTxSrv.findRoundForSessionIdAndRoundReference(req1.getUserId(), req1.getSessionId(), req1.getRoundRef());
        assertNotNull(rnd1);
        assertFalse(rnd1.isOpen());


        var txsForOpenRound = nTxSrv.findRoundTransactionsForUserIdAndSessionId(req1.getUserId(), req1.getSessionId(), req1.getRoundRef());
        assertEquals(1, txsForOpenRound.size());
    }

    @Test
    @Order(10)
    public void newTxSrvTestsLowBalance1() {

        var w = getWalletFor(2L);

        var balance = w.getBalance();

        var req1 = new TxRequest();
        req1.setBet(BigDecimal.valueOf(balance.longValue() / 2 + 1L));
        req1.setWin(BigDecimal.ZERO);
        req1.setSessionId(101L);
        req1.setUserId(2L);
        req1.setGameId(1L);
        req1.setRoundRef("rnd2");
        req1.setRoundMode(RoundMode.OPEN);
        req1.setExternalOrigId("bet2");
        req1.setVendorName("egal");
        req1.setGp(GamePlatform.WEB);
        req1.setJurisdiction(Jurisdiction.SOC);
        req1.setType(TransactionType.BET);
        var txResponse = transactionTemplate.execute(a -> {
            try {
                return nTxSrv.addTxFromProvider(req1);
            } catch (OuslyTransactionException e) {
                return null;
            }
        });
        assert txResponse != null;
    }


    @NotNull
    private static TxRequest getTxRequest() {
        var req1 = new TxRequest();
        req1.setBet(BigDecimal.valueOf(100));
        req1.setWin(BigDecimal.valueOf(200));
        req1.setSessionId(120L);
        req1.setUserId(2L);
        req1.setGameId(1L);
        req1.setRoundRef("rnddw22");
        req1.setRoundMode(RoundMode.OPENCLOSE);
        req1.setExternalOrigId("directwinRnd1");
        req1.setVendorName("egal");
        req1.setGp(GamePlatform.WEB);
        req1.setJurisdiction(Jurisdiction.SOC);
        req1.setType(TransactionType.DIRECTWIN);
        return req1;
    }

    private Wallet getWalletFor(Long userId) {

        return transactionTemplate.execute(a -> {
            try {
                return wRepo.getWalletById(userId);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        });

    }


}

package com.ously.gamble.bridge.zeusV3;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ously.gamble.TestContext;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.games.ActiveGameManagementService;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.games.GameManagementService;
import com.ously.gamble.api.user.UserManagementService;
import com.ously.gamble.bridge.zeusV3.exception.ZeusV3Exception;
import com.ously.gamble.bridge.zeusV3.payload.request.GameRoundRequest;
import com.ously.gamble.bridge.zeusV3.payload.request.GameRoundRequest.Bet;
import com.ously.gamble.bridge.zeusV3.payload.request.GameRoundRequest.SessionRound;
import com.ously.gamble.bridge.zeusV3.payload.request.GameRoundRequest.Win;
import com.ously.gamble.bridge.zeusV3.payload.response.PlayerWalletResponse;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.SessionState;
import com.ously.gamble.persistence.model.game.Game;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.model.session.Session;
import com.ously.gamble.persistence.model.user.User;
import com.ously.gamble.persistence.repository.session.SessionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.Objects;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(SpringExtension.class)
@Transactional
@Import(ZeusV3TestConfig.class)
public class ZeusV3ServiceTests extends TestContext {

    @Autowired
    private ZeusV3ServiceImpl zeusV3Service;

    @Autowired
    private ZeusV3Configuration config;

    @Autowired
    private UserManagementService userManagementService;

    @Autowired
    private GameManagementService gameManagementService;

    @Autowired
    private ActiveGameManagementService gameInstanceRepository;

    @Autowired
    private SessionRepository sessionRepository;

    private User user;
    private Game game;

    @BeforeEach
    void setUp() {
        var userId = createTestUserWithBalance(BigDecimal.valueOf(10000));
        user = userManagementService.getByIdUncached(userId);
        game = createTestGame();
    }

    @Test
    void testCreateSessionAndBetWinCancelWorkflow() throws ZeusV3Exception, JsonProcessingException {
        CasinoUser casinoUser = new CasinoUser(user);
        CasinoGame casinoGame = new CasinoGame(game, null);
        GameSettings gameSettings = new GameSettings();

        // Step 1: Create game session
        GameInstance gameInstance = zeusV3Service.createNewGameInstanceRaw(
                casinoUser,
                casinoGame,
                GamePlatform.MOBILE,
                gameSettings
        );

        gameInstanceRepository.save(gameInstance);
        createSessionFromGameInstance(gameInstance);

        assertNotNull(gameInstance.getToken());
        assertNotNull(gameInstance.getGameUrl());
        assertNotNull(gameInstance.getGameHtml());
        assertNotNull(gameInstance.getExpiryTime());
        assertEquals(user.getId(), gameInstance.getUserId());
        assertEquals(game.getId(), gameInstance.getGameId());

        // Verify that the session was created correctly
        Session session = sessionRepository.findBySessionIdAndUserId(gameInstance.getId(), user.getId()).orElse(null);
        assertEquals(gameInstance.getToken(), session.getToken());

        BigDecimal initialBalance = getWallet(user.getId()).getBalance();

        // Step 2: Place a bet (betAmount > 0, winAmount = 0)
        BigDecimal betAmount = new BigDecimal("100.00");
        GameRoundRequest betRequest = createGameRoundRequest(
                gameInstance.getToken(),
                "bet-tx-1",
                1,
                betAmount,
                BigDecimal.ZERO,
                true
        );

        PlayerWalletResponse betResponse = zeusV3Service.processGameRound(betRequest);

        // Assert - Bet was processed and balance decreased
        assertNotNull(betResponse);

        BigDecimal balanceAfterBet = getWallet(user.getId()).getBalance();

        assertEquals(initialBalance.subtract(betAmount).setScale(2, BigDecimal.ROUND_DOWN).doubleValue(),
                    balanceAfterBet.setScale(2, BigDecimal.ROUND_DOWN).doubleValue());
        assertEquals(balanceAfterBet.setScale(2, BigDecimal.ROUND_DOWN).doubleValue(),
                    betResponse.playerWallet().amount().doubleValue());

        // Step 3: Process a win (betAmount = 0, winAmount > 0)
        BigDecimal winAmount = new BigDecimal("200.00");
        GameRoundRequest winRequest = createGameRoundRequest(
                gameInstance.getToken(),
                "win-tx-1",
                2,
                BigDecimal.ZERO,
                winAmount,
                true
        );

        PlayerWalletResponse winResponse = zeusV3Service.processGameRound(winRequest);

        // Assert - Win was processed and balance increased
        assertNotNull(winResponse);

        BigDecimal balanceAfterWin = getWallet(user.getId()).getBalance();

        assertEquals(balanceAfterBet.add(winAmount).setScale(2, BigDecimal.ROUND_DOWN).doubleValue(),
                    balanceAfterWin.setScale(2, BigDecimal.ROUND_DOWN).doubleValue());
        assertEquals(balanceAfterWin.setScale(2, BigDecimal.ROUND_DOWN).doubleValue(),
                    winResponse.playerWallet().amount().doubleValue());

        // Step 4: Process a combined bet and win (both betAmount and winAmount > 0)
        BigDecimal combinedBetAmount = new BigDecimal("50.00");
        BigDecimal combinedWinAmount = new BigDecimal("75.00");
        GameRoundRequest combinedRequest = createGameRoundRequest(
                gameInstance.getToken(),
                "combined-tx-1",
                3,
                combinedBetAmount,
                combinedWinAmount,
                true
        );

        BigDecimal balanceBeforeCombined = getWallet(user.getId()).getBalance();
        PlayerWalletResponse combinedResponse = zeusV3Service.processGameRound(combinedRequest);

        // Assert - Combined transaction was processed correctly
        assertNotNull(combinedResponse);

        BigDecimal balanceAfterCombined = getWallet(user.getId()).getBalance();
        BigDecimal expectedBalance = balanceBeforeCombined.subtract(combinedBetAmount).add(combinedWinAmount);

        assertEquals(expectedBalance.setScale(2, BigDecimal.ROUND_DOWN).doubleValue(),
                    balanceAfterCombined.setScale(2, BigDecimal.ROUND_DOWN).doubleValue());
        assertEquals(balanceAfterCombined.setScale(2, BigDecimal.ROUND_DOWN).doubleValue(),
                    combinedResponse.playerWallet().amount().doubleValue());

        // Step 5: Cancel the last game round
        PlayerWalletResponse cancelResponse = zeusV3Service.cancelGameRound(combinedRequest);

        // Assert - Cancellation was processed and balance reverted
        assertNotNull(cancelResponse);

        BigDecimal balanceAfterCancel = getWallet(user.getId()).getBalance();

        assertEquals(balanceAfterWin.setScale(2, BigDecimal.ROUND_DOWN).doubleValue(),
                    balanceAfterCancel.setScale(2, BigDecimal.ROUND_DOWN).doubleValue());
        assertEquals(balanceAfterCancel.setScale(2, BigDecimal.ROUND_DOWN).doubleValue(),
                    cancelResponse.playerWallet().amount().doubleValue());

        // Step 6: Process a freeBet without Win (betAmount > 0, winAmount == 0)
        // betAmount should not be taken from the wallet
        initialBalance = getWallet(user.getId()).getBalance();
        betAmount = new BigDecimal("100.00");

        GameRoundRequest freeBetRequest = createGameRoundRequest(
                gameInstance.getToken(),
                "free-tx-1",
                3,
                betAmount,
                BigDecimal.ZERO,
                false
        );

        PlayerWalletResponse freeBetResponse = zeusV3Service.processGameRound(freeBetRequest);

        // Assert - balance is still the same
        assertNotNull(freeBetResponse);

        BigDecimal balanceAfterFreeBet = getWallet(user.getId()).getBalance();

        assertEquals(balanceAfterFreeBet.setScale(2, BigDecimal.ROUND_DOWN).doubleValue(),
                initialBalance.setScale(2, BigDecimal.ROUND_DOWN).doubleValue());
        assertEquals(balanceAfterFreeBet.setScale(2, BigDecimal.ROUND_DOWN).doubleValue(),
                freeBetResponse.playerWallet().amount().doubleValue());
    }

    private Game createTestGame() {
        var game = new Game();
        game.setGameId("ZEUS_TEST_GAME");
        game.setId(99999L);
        game.setActive(true);
        game.setAndroid(true);
        game.setCreatedAt(Instant.now());
        game.setCategories(Set.of());
        game.setDesktop(false);
        game.setGameInfo(null);
        game.setIos(false);
        game.setProviderName(config.getVendorName());
        game.setMobile(true);
        game.setName("Zeus V3 Test Game");
        game.setSortOrder(1000);
        game.setUpdatedAt(Instant.now());
        gameManagementService.saveNew(game);
        return game;
    }

    private void createSessionFromGameInstance(GameInstance gameInstance) {
        Session session = new Session();
        session.setUserId(gameInstance.getUserId());
        session.setGameId(gameInstance.getGameId());
        session.setCountry(gameInstance.getCountry());
        session.setSessionId(gameInstance.getId());
        session.setCreationTime(gameInstance.getCreationTime().toInstant(ZoneOffset.UTC));
        session.setLastActionTime(gameInstance.getCreationTime().toInstant(ZoneOffset.UTC));
        session.setHandlerVersion(gameInstance.getHandlerVersion());
        session.setJurisdiction(gameInstance.getJurisdiction());
        session.setPlatform(Objects.requireNonNullElse(gameInstance.getPlatform(), GamePlatform.UNKNOWN));
        session.setStatus(SessionState.ACTIVE);
        session.setToken(gameInstance.getToken());
        session = sessionRepository.saveAndFlush(session);
        assertNotNull(session);
    }

    private GameRoundRequest createGameRoundRequest(String sessionId, String transactionId,
                                                  int roundNumber, BigDecimal betAmount,
                                                  BigDecimal winAmount, Boolean normalBet) {
        String betType = normalBet ? "normalBet" : "freeBet";

        return new GameRoundRequest(
                sessionId,
                transactionId,
                "some_parent_session_id",
                user.getId().toString(),
                config.getCurrency(),
                game.getGameId(),
                new SessionRound(
                        roundNumber,
                        0,
                        false
                ),
                new Bet(
                        betAmount,
                        betType
                ),
                new Win(
                        winAmount
                )
        );
    }
}

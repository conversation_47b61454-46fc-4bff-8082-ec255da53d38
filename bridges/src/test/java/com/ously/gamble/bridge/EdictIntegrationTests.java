package com.ously.gamble.bridge;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.games.ActiveGameManagementService;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.games.GameManagementService;
import com.ously.gamble.api.user.UserManagementService;
import com.ously.gamble.bridge.edict.*;
import com.ously.gamble.bridge.edict.model.authorize.AuthorizationRequest;
import com.ously.gamble.bridge.edict.model.authorize.AuthorizePlayer;
import com.ously.gamble.bridge.edict.model.gamesession.MarkGameSessionClosed;
import com.ously.gamble.bridge.edict.model.gamesession.MarkGameSessionClosedRequest;
import com.ously.gamble.bridge.edict.model.wallet.*;
import com.ously.gamble.persistence.model.SessionState;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.model.session.Session;
import com.ously.gamble.persistence.repository.session.SessionRepository;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.ZoneOffset;
import java.util.Objects;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;


@ExtendWith(SpringExtension.class)
@Transactional
class EdictIntegrationTests extends TestContext {

    @Autowired
    EdictConfiguration eConf;

    @Autowired
    EdictServiceImpl eCtrl;
    @Autowired
    UserManagementService uMgr;
    @Autowired
    GameManagementService gmMgmt;

    @Autowired
    ActiveGameManagementService giRepo;

    @Autowired
    EdictGameSessionEndpoint gsEP;
    @Autowired
    EdictAuthorizationEndpoint authEP;
    @Autowired
    EdictWalletEndpoint wallEP;


    @Test
    void testUrlAndAuth() throws Exception {
        var u = uMgr.getCasinoUserById(1L).orElse(null);
        var g = gmMgmt.getByIdUncached(1L);
        var gI = eCtrl.createNewGameInstanceRaw(u, new CasinoGame(g, null), GamePlatform.WEB, new GameSettings());
        gI.setUserId(u.getId());
        gI.setGameId(g.getId());
        var url = gI.getGameUrl();
        giRepo.save(gI);

        createSessionFromGameInstance(gI);

        var parameters =
                UriComponentsBuilder.fromUriString(url).build().getQueryParams();

        // get original token
        var sessionToken = parameters.getFirst("sessionToken");

        // now build Auth Request
        var ar = new AuthorizationRequest();
        ar.setCallerId(eConf.getCallerId());
        ar.setCallerPassword(eConf.getCallerPw());
        ar.setSessionToken(sessionToken);
        ar.setPlayerName("1");

        var ap = new AuthorizePlayer();
        ap.setAuthorizationRequest(ar);
        var authorizePlayerResponseJAXBElement = authEP.authorizePlayer(ap);
        var value = authorizePlayerResponseJAXBElement.getValue();
        var newSID = value.getReturn().getSessionId();
        assertFalse(StringUtils.isEmpty(newSID));

        // now get Balance
        var gb = new GetBalance();
        var sbr = new BalanceRequest();
        sbr.setCallerId(eConf.getCallerId());
        sbr.setCallerPassword(eConf.getCallerPw());
        sbr.setPlayerName("1");
        sbr.setCurrency("FUM");
        sbr.setSessionId(newSID);
        gb.setBalanceRequest(sbr);
        var balance = wallEP.getBalance(gb);
        var balance1 = balance.getValue().getReturn().getBalance();

        // we need a fresh! GameInstance
        var gameInstance = eCtrl.createNewGameInstanceRaw(u, new CasinoGame(g, null), GamePlatform.WEB, new GameSettings());
        gameInstance.setGameId(g.getId());
        gameInstance.setUserId(u.getId());
        gameInstance = giRepo.save(gameInstance);
        createSessionFromGameInstance(gameInstance);
        newSID = gameInstance.getToken();
        // withdraw & deposit and withdrawAndDeposit next items to finalize
        var wd = new Withdraw();
        var wdr = new WithdrawRequest();
        wd.setWithdrawRequest(wdr);

        wdr.setAmount(20);
        wdr.setBonusBet(0.0);
        wdr.setCallerId(eConf.getCallerId());
        wdr.setCallerPassword(eConf.getCallerPw());
        wdr.setCurrency("FUM");
        wdr.setGameId("EGAL");
        wdr.setSessionId(gameInstance.getToken());
        wdr.setGameRoundRef(getUniqueTxRef());
        wdr.setTransactionRef(getUniqueTxRef());
        wdr.setPlayerName(gameInstance.getUserId().toString());
        var wresp = wallEP.withdraw(wd);
        assertEquals(1239980, wresp.getValue().getReturn().getBalance(), 0.000001);

        // now do getBalance again
        gb.getBalanceRequest().setSessionId(gameInstance.getToken());
        balance = wallEP.getBalance(gb);
        assertEquals(1239980.0, balance.getValue().getReturn().getBalance(), 0.000001);

        // NOW DEPOSIT
        var d = new Deposit();
        var dpr = new DepositRequest();
        d.setDepositRequest(dpr);
        dpr.setAmount(20);
        dpr.setBonusWin(0.0);
        dpr.setCallerId(eConf.getCallerId());
        dpr.setCallerPassword(eConf.getCallerPw());
        dpr.setCurrency("FUM");
        dpr.setGameId("EGAL");
        dpr.setSessionId(gameInstance.getToken());
        dpr.setGameRoundRef(getUniqueTxRef());
        dpr.setTransactionRef(getUniqueTxRef());
        dpr.setPlayerName(gameInstance.getUserId().toString());
        var dresp = wallEP.deposit(d);
        assertEquals(balance1, dresp.getValue().getReturn().getBalance(), 0.000001);

        // Instant win tx
        var wad = new WithdrawAndDeposit();
        var wadr = new WithdrawAndDepositRequest();
        wad.setWithdrawAndDepositRequest(wadr);

        wadr.setDeposit(55);
        wadr.setWithdraw(15);
        wadr.setBonusWin(0.0);
        wadr.setCallerId(eConf.getCallerId());
        wadr.setCallerPassword(eConf.getCallerPw());
        wadr.setCurrency("FUM");
        wadr.setGameId("EGAL");
        wadr.setSessionId(gameInstance.getToken());
        wadr.setGameRoundRef(getUniqueTxRef());
        wadr.setTransactionRef(getUniqueTxRef());
        wadr.setPlayerName(gameInstance.getUserId().toString());

        var wadResp = wallEP.withdrawAndDeposit(wad);
        assertEquals(1240040, wadResp.getValue().getReturn().getNewBalance(), 0.000001);

        // now close session
        var mgsc = new MarkGameSessionClosed();
        var mgscr = new MarkGameSessionClosedRequest();
        mgsc.setMarkGameSessionClosedRequest(mgscr);
        mgscr.setCallerId(eConf.getCallerId());
        mgscr.setCallerPassword(eConf.getCallerPw());
        mgscr.setSessionId(newSID);
        var markGameSessionClosedResponseJAXBElement = gsEP.markSessionClosed(mgsc);


        // now we check the wallet to see if everything is reflected

        var uW = getWallet(1L);
        System.out.println(uW);
        assertEquals(2L, uW.getGamesPlayed());
        assertEquals(2L, uW.getGamesWon());
        assertEquals(0.75, uW.getSumWin().doubleValue(), 0.00001);
        assertEquals(0.35, uW.getSumBet().doubleValue(), 0.000001);
        assertEquals((balance1 - 20 + 20 + 40) / 100, uW.getBalance().doubleValue(), 0.00001);

    }

    @Autowired
    SessionRepository ssRepo;

    private void createSessionFromGameInstance(GameInstance gI) {
        // Create session
        var s = new Session();
        s.setUserId(gI.getUserId());
        s.setGameId(gI.getGameId());
        s.setCountry(gI.getCountry());
        s.setSessionId(gI.getId());
        s.setCreationTime(gI.getCreationTime().toInstant(ZoneOffset.UTC));
        s.setHandlerVersion(gI.getHandlerVersion());
        s.setJurisdiction(gI.getJurisdiction());
        s.setPlatform(Objects.requireNonNullElse(gI.getPlatform(), GamePlatform.UNKNOWN));
        s.setStatus(SessionState.ACTIVE);
        s.setToken(gI.getToken());
        s.setCountry("DE");
        s = ssRepo.saveAndFlush(s);
        assertNotNull(s);
    }


    private String getUniqueTxRef() {
        return UUID.randomUUID().toString();
    }

}

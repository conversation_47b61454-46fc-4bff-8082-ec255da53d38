package com.ously.gamble.bridge;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.bridge.GameSettings;
import com.ously.gamble.api.games.ActiveGameManagementService;
import com.ously.gamble.api.games.CasinoGame;
import com.ously.gamble.api.games.GameManagementService;
import com.ously.gamble.api.user.UserManagementService;
import com.ously.gamble.bridge.wazdan.WazdanService;
import com.ously.gamble.bridge.wazdan.payload.*;
import com.ously.gamble.persistence.dto.CasinoUser;
import com.ously.gamble.persistence.model.SessionState;
import com.ously.gamble.persistence.model.game.Game;
import com.ously.gamble.persistence.model.game.GameInstance;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.persistence.model.session.Session;
import com.ously.gamble.persistence.model.user.User;
import com.ously.gamble.persistence.repository.session.SessionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneOffset;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;


@ExtendWith(SpringExtension.class)
@Transactional
public class WazdanBridgeTests extends TestContext {

    @Autowired
    WazdanService wzSrv;
    @Autowired
    GameManagementService gmMgmt;
    @Autowired
    UserManagementService uMgr;
    @Autowired
    ActiveGameManagementService giRepo;

    final AtomicLong roundId = new AtomicLong((1L));
    final AtomicLong txId = new AtomicLong(1L);

    Game game;
    User user;

    @BeforeEach
    public void prepareTestdata() {
        game = gmMgmt.getByIdUncached(7L);
        user = uMgr.getByIdUncached(1L);
    }

    @Test
    public void testAuthAndMultipleBetWinCycles() throws Exception {
        prepareTestdata();
        var gi = wzSrv.createNewGameInstanceRaw(new CasinoUser(user), new CasinoGame(game, null), GamePlatform.TEST, new GameSettings());
        gi.setUserId(user.getId());
        gi.setGameId(game.getId());
        giRepo.save(gi);
        assertNotNull(gi);

        createSessionFromGameInstance(gi);

        var token = gi.getToken();
        assertFalse(token.isEmpty());

        //  now play a round

        var stake = wzSrv.getStake(createBetRequest(1000L, token));
        assertNotNull(stake);
        assertEquals(11400, stake.getFunds().getBalance());
        var betRequest = createBetRequest(4L, token);
        stake = wzSrv.getStake(betRequest);
        assertNotNull(stake);


        // now for the wins ...
        var win = wzSrv.returnWin(createWinRequest(4L, token));
        assertNotNull(win);
        roundId.incrementAndGet();

        // Lets get the funds
        var funds = wzSrv.getFunds(createWDGetFundsRequest(token));
        assertNotNull(funds);

        // now rollback and reget wallet and assert both are the same and 14400
        var rollback = wzSrv.rollbackStake(createRollbackRequest(token, betRequest.getTransactionId()));
        assertNotNull(rollback);
        assertEquals(11404, rollback.getFunds().getBalance());
        funds = wzSrv.getFunds(createWDGetFundsRequest(token));
        assertEquals(funds.getFunds().getBalance(), rollback.getFunds().getBalance());

        // close game
        wzSrv.gameClose(createGameCloseReq(token));
        // now get funds should fail
        funds = wzSrv.getFunds(createWDGetFundsRequest(token));
        assertEquals(1, funds.getStatus());
    }

    private WDGameCloseRequest createGameCloseReq(String token) {
        var req = new WDGameCloseRequest();
        req.setUser(new WDUser());
        req.getUser().setToken(token);
        req.getUser().setId("UI#" + user.getId());
        return req;
    }

    private WDRollbackStakeRequest createRollbackRequest(String token, String transactionId) {
        var req = new WDRollbackStakeRequest();

        req.setOriginalTransactionId(transactionId);
        req.setTransactionId("" + txId.incrementAndGet());
        req.setUser(new WDUser());
        req.getUser().setToken(token);
        req.getUser().setId("UI#" + user.getId());
        return req;
    }

    private WDGetFundsRequest createWDGetFundsRequest(String token) {
        return new WDGetFundsRequest("UI#" + user.getId(), token);
    }

    private WDGetStakeRequest createBetRequest(long l, String token) {
        var wdgs = new WDGetStakeRequest();
        wdgs.setAmount(Double.valueOf(l));
        wdgs.setFreeRound(false);
        wdgs.setFreeSpin(false);
        wdgs.setGameId(1);
        wdgs.setRoundId("" + roundId.getAndIncrement());
        wdgs.setTransactionId("" + txId.getAndIncrement());
        var wdUser = new WDUser();
        wdUser.setToken(token);
        wdUser.setId("UI#" + user.getId());
        wdgs.setUser(wdUser);
        return wdgs;
    }

    private WDReturnWinRequest createWinRequest(long l, String token) {
        var wdgs = new WDReturnWinRequest();
        wdgs.setAmount(Double.valueOf(l));
        wdgs.setGameId(1);
        wdgs.setRoundId("" + roundId.get());
        wdgs.setTransactionId("" + txId.getAndIncrement());
        var wdUser = new WDUser();
        wdUser.setToken(token);
        wdUser.setId("UI#" + user.getId());
        wdgs.setUser(wdUser);
        return wdgs;
    }

    @Autowired
    SessionRepository ssRepo;

    private void createSessionFromGameInstance(GameInstance gI) {
        // Create session
        var s = new Session();
        s.setUserId(gI.getUserId());
        s.setGameId(gI.getGameId());
        s.setCountry(gI.getCountry());
        s.setSessionId(gI.getId());
        s.setCreationTime(gI.getCreationTime().toInstant(ZoneOffset.UTC));
        s.setHandlerVersion(gI.getHandlerVersion());
        s.setJurisdiction(gI.getJurisdiction());
        s.setPlatform(Objects.requireNonNullElse(gI.getPlatform(), GamePlatform.UNKNOWN));
        s.setStatus(SessionState.ACTIVE);
        s.setToken(gI.getToken());
        s.setCountry("DE");
        s = ssRepo.saveAndFlush(s);
        assertNotNull(s);
    }
}

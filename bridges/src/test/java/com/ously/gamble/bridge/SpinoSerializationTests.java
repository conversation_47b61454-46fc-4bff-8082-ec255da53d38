package com.ously.gamble.bridge;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.bridge.spinomenal.SpinomenalConfiguration;
import com.ously.gamble.bridge.spinomenal.payload.GenerateTokenRequest;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;


public class SpinoSerializationTests {

    final SpinomenalConfiguration spinoSec = new SpinomenalConfiguration();
    final ObjectMapper om = new ObjectMapper();

    @Test
    public void testTokenGenerationReqSerDeser() throws IOException {
        var expectedMessage =
                om.readTree(IOUtils.resourceToString("/spinomenal/rqToken1.json", StandardCharsets.UTF_8)).toString();

        var gtr = new GenerateTokenRequest();
        gtr.setPartnerId(spinoSec.getPartnerId());
        gtr.setTimeStamp("20190101081533");
        gtr.setCurrencyCode("EUR");
        gtr.setExternalId("KLEEMANN_ANON");
        gtr.setGameCode("SPINO_BOOKOFAAH");
        gtr.setHomeUrl("www.schundbuch.de");
        gtr.setName("Jens Kleemann");
        gtr.setSound(true);
        assertEquals("20190101081533KLEEMANN_ANON",
                gtr.getSigPart());
        var sig = spinoSec.createSignature(gtr.getSigPart());
        assertEquals("93433f95a9d50bf3871e7c0427873534",
                sig);
        gtr.setSignature(sig);
        var json = om.writeValueAsString(gtr);
        assertTrue(json.contains("gameplay-dev"));
    }

}

package com.ously.gamble.test;

import org.testcontainers.containers.GenericContainer;
import org.testcontainers.images.PullPolicy;
import org.testcontainers.utility.DockerImageName;

@SuppressWarnings("rawtypes")
public class RedisContainer extends GenericContainer<RedisContainer> {

    private static RedisContainer container;

    public RedisContainer(final DockerImageName name) {
        super(name);
    }

    public static GenericContainer getInstance() {
        if (container == null) {
            container = new RedisContainer(DockerImageName.parse("bitnami/redis:latest")).withExposedPorts(6379).withEnv("REDIS_PASSWORD", "fatass69").withImagePullPolicy(PullPolicy.alwaysPull());
        }
        return container;
    }

    @Override
    public void stop() {
//        try {
//            super.stop();
//        } catch (Exception e) {
//            // Log exception
//        }
    }

}

spring.config.import=classpath:common-config/shared-kubtest.properties
#
# Feature and base properties
#
features.fakeValidation=true
features.mailVerification=false
# avoid massive cpu load on regexp testing headers and parameters
firewall.allowAll=true
#
# RTP min spin barrier
# (def. 1000,0.5,2.0,300)
features.livertp.minspins=10
features.livertp.min=0.3
features.livertp.max=2.0
features.livertp.ttl=120
#
# Switches
#
settings.datasourceproxy.enabled=false
#
# Loyalty Factors & min/max
#
loyalty.minimums=100,150,200,350,500,700,1000
loyalty.maximums=150,250,350,550,750,1000,1500
loyalty.levelfactor=0.01
loyalty.wagerfactor=0.025
#
# wheel
#
wheel.use-diamonds=true
#
# Monitoring props
#
monitor.wager.enabled=false
monitor.milestones.enabled=true
monitor.rankings.enabled=true
package com.ously.gamble.monitor.rankings;

import com.ously.gamble.TestContext;
import com.ously.gamble.api.cache.*;
import com.ously.gamble.api.monitoring.MonitoredGameTx;
import com.ously.gamble.api.monitoring.MonitoredItemsBatchEvent;
import com.ously.gamble.api.rankings.*;
import com.ously.gamble.api.user.UserManagementService;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GamePlatform;
import com.ously.gamble.ranking.config.RankingConfiguration;
import com.ously.gamble.ranking.management.ActiveRanking;
import com.ously.gamble.ranking.monitoring.RankingMonitorResultUpdater;
import com.ously.gamble.ranking.monitoring.RankingMonitoringEvaluatorImpl;
import com.ously.gamble.ranking.persistence.repository.RankingLinkageRepository;
import com.ously.gamble.ranking.persistence.repository.RankingRepository;
import com.ously.gamble.ranking.persistence.repository.RankingScheduleRepository;
import com.ously.gamble.ranking.service.RankingSchedulerServiceImpl;
import com.ously.gamble.ranking.service.RankingServiceImpl;
import com.ously.gamble.ranking.service.RankingUserNamesHandlerImpl;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.commons.logging.Logger;
import org.junit.platform.commons.logging.LoggerFactory;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.LongCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings({"rawtypes", "unchecked"})
@ExtendWith(SpringExtension.class)
class RankingTests extends TestContext {
    private static final Logger log = LoggerFactory.getLogger(RankingTests.class);

    @Autowired
    CachedMap<String, RankingAggregation> rankAggCachedMap;

    @Autowired
    CachedMap<String, TournamentPositions> rankPositionsCachedMap;

    @Autowired
    RankingConfiguration rankingConfiguration;

    @Autowired
    RedisRMap<Long, String> userTNameMap;
    @Autowired
    RankingLinkageRepository rankLinkRepo;

    @Autowired
    RankingScheduleRepository rsRepo;

    @Autowired
    RankingRepository rankRepo;

    @Autowired
    RankingMonitoringEvaluatorImpl sstxMon;

    @Autowired
    RankingScriptService rScriptSrv;

    @Autowired
    RedissonClient rC;

    @Autowired
    UserManagementService umService;

    @Autowired
    CachedMapFactory<String, Long> cmFact;

    @Autowired
    LCacheFactory lCacheFactory;

    @Autowired
    ScriptFactory scFact;

    @Autowired
    RankingMonitorResultUpdater resultUpdater;

    @Autowired
    ApplicationEventPublisher evntPub;

    @Test
    @Disabled
    void testRankClosing() throws IOException {


        TournamentPositions notExistingPos = rScriptSrv.getCurrentPositions("notExistingPos");
        assertEquals(0, notExistingPos.getPositions().size());
        RankingUserNamesHandler ruHandler = new RankingUserNamesHandlerImpl(userTNameMap, rankLinkRepo);

        rankRepo.deleteAll();
        rankRepo.flush();

        var currentTimestamp = Instant.now();

        var rService = new RankingServiceImpl(umService, cmFact,
                lCacheFactory, rankRepo,
                rScriptSrv, null, rankingConfiguration, evntPub);
        var rsServ = new RankingSchedulerServiceImpl(rsRepo, rankRepo, null, null, null);

        List<RankingDto> activeRankings = rsServ.getActiveRankings();
        assertEquals(0, activeRankings.size());

        // Add special ranking for a specific game
        var rank1 = new RankingDto(currentTimestamp.atOffset(ZoneOffset.UTC).toLocalDate(),
                "testrank99",
                RankingStatus.ACTIVE,
                currentTimestamp.minusSeconds(60),
                currentTimestamp.plusSeconds(60),
                "60M",
                RankingType.WAGER_SUM,
                TournamentType.DEFAULT, null,
                "gid=99",
                "", 0, 1500,
                null);

        rsServ.persistRankings(Collections.singletonList(rank1));

        sleep(500);
        rScriptSrv.reloadRankings();


        activeRankings = rsServ.getActiveRankings();
        assertEquals(1, activeRankings.size());

        sstxMon.reloadRankings();
        Arrays.stream(sstxMon.getCurrentRankings()).forEach(a -> {
            a.addUserLinkage(666);
            a.addUserLinkage(667);
            a.addUserLinkage(668);

        });
        assertEquals(1, sstxMon.getCurrentRankings().length);

        // Add usernames
        ruHandler.updatePlayernameForUser(666, "003301234566Player666");
        ruHandler.updatePlayernameForUser(667, "004401234567Player667");
        ruHandler.updatePlayernameForUser(668, "005501234568Player668");


        // add updates
        List<MonitoredGameTx> updates = new ArrayList<>(4);

        updates.add(new MonitoredGameTx(666L, 99, 2, GamePlatform.DESKTOP
                , TransactionType.DIRECTWIN,
                5000, 500,
                0, (short) 0, false, 95.4f
        ));
        updates.add(new MonitoredGameTx(667L, 99, 2, GamePlatform.DESKTOP
                , TransactionType.DIRECTWIN,
                7500, 500,
                0, (short) 0, false, 95.4f
        ));
        updates.add(new MonitoredGameTx(668L, 99, 2, GamePlatform.DESKTOP
                , TransactionType.DIRECTWIN,
                1500, 500,
                0, (short) 0, false, 95.4f
        ));

        long initialUpdateTime = resultUpdater.getLastUpdateTime();
        rankPositionsCachedMap.clear();
        MonitoredItemsBatchEvent mibe = new MonitoredItemsBatchEvent(updates);
        sstxMon.handleTx(mibe);


        // wait for updateSchedule
        while (resultUpdater.getLastUpdateTime() <= initialUpdateTime) {
            sleep(500);
        }

//        // allow for the putFast to be really effective
        sleep(2000);

//        assertEquals(rank1.rankId(), rankPositionsCachedMap.get(rank1.rankId()).getTournamentId());


        // wait a while
        ScoredSet<Long> scoredSet = cmFact.createScoredSet(rank1.rankId(), Long.class);
        var i = 0;
        while (scoredSet.size() < 3 && i < 100) {
            sleep(100);
            i++;
        }

        // now check the ranking
        List<ScoredSet.ScoredEntry<Long>> rankEntries = scoredSet.getRankEntries(0, 3);
        assertEquals(3, rankEntries.size());
        // compare with script entries
        TournamentPositions testrank99 = rService.getCurrentPositions("testrank99");
        assertEquals(3, testrank99.getPositions().size());
        // assert that both "sets" are equal in terms of score
        for (int c = 0; c < 3; c++) {
            assertEquals(testrank99.getPositions().get(c).getScore(), (rankEntries.get(c).getScore() * 100));
        }


        RankingAggregation aggSum1 = rankAggCachedMap.get(rank1.rankId());
        assertEquals(3, aggSum1.getCount());

        // std. sum function
        assertEquals(140d, rService.getSumScoresOfRanking(rank1.rankId()), 0.01d);
        // agg. function
        RankingAggregation aggScoresOfRanking = rService.getAggScoresOfRanking(rank1.rankId());
        assertEquals(140d, aggScoresOfRanking.getSum(), 0.003d);
        assertEquals(3, aggScoresOfRanking.getCount());
        assertEquals(15d, aggScoresOfRanking.getMin(), 0.003d);
        assertEquals(75d, aggScoresOfRanking.getMax(), 0.003d);

        // results with names
        var tPos = rService.getCurrentPositions(rank1.rankId());
        assertEquals(3, tPos.getPositions().size());

        assertEquals(7500L, tPos.getPositions().getFirst().getScore());
        assertEquals("01234567", tPos.getPositions().getFirst().getExtId());
        assertEquals("Player667", tPos.getPositions().getFirst().getPlayername());


        rScriptSrv.reloadRankings();

        // Now check the postion struct
        TournamentPositions currentPositions = rScriptSrv.getCurrentPositions(rank1.rankId());
        sleep(1000);

        assertEquals(3, currentPositions.getPositions().size());
        assertEquals(1640, currentPositions.getPricePool());

        RankingResults results = rService.closeRanking(rank1.rDate(), rank1.rankId());
        assertEquals(0.0d, rService.getSumScoresOfRanking(rank1.rankId()), 0.01d);

        assertEquals(3, results.getNumUsers());
        assertEquals(140.0d, results.getSumScores(), 0.001d);
        assertEquals(667, results.getResults().get(0).u());
        assertEquals(666, results.getResults().get(1).u());
        assertEquals(668, results.getResults().get(2).u());

    }


    @Test
    void testRankRefresh1() {
        rankRepo.deleteAll();
        rankRepo.flush();

        var rsServ = new RankingSchedulerServiceImpl(rsRepo, rankRepo, null, null, null);

        var currentTimestamp = Instant.now();

        // Add new Ranking
        var rank1 = new RankingDto(currentTimestamp.atOffset(ZoneOffset.UTC).toLocalDate(),
                "testrank1",
                RankingStatus.ACTIVE,
                currentTimestamp.minusSeconds(60),
                currentTimestamp.plusSeconds(60),
                "60M",
                RankingType.WAGER_SUM,
                TournamentType.DEFAULT, null,
                "",
                "", 0, 0,
                null);

        rsServ.persistRankings(Collections.singletonList(rank1));
        var activeRankings = rsServ.getActiveRankings();
        assertEquals(1, activeRankings.size());
        sstxMon.reloadRankings();
        Arrays.stream(sstxMon.getCurrentRankings()).forEach(a -> {
            a.addUserLinkage(666);
            a.addUserLinkage(667);
            a.addUserLinkage(668);

        });
        assertEquals(1, sstxMon.getCurrentRankings().length);

        // Add another
        var rank2 = new RankingDto(currentTimestamp.atOffset(ZoneOffset.UTC).toLocalDate(),
                "testrank2",
                RankingStatus.ACTIVE,
                currentTimestamp.minusSeconds(60),
                currentTimestamp.plusSeconds(60),
                "60M",
                RankingType.WIN_SUM, TournamentType.DEFAULT, null,
                "", "", 0, 0,
                null);
        rsServ.persistRankings(Collections.singletonList(rank2));
        activeRankings = rsServ.getActiveRankings();
        assertEquals(2, activeRankings.size());
        sstxMon.reloadRankings();
        assertEquals(2, sstxMon.getCurrentRankings().length);

        // Add fourth to test sorting
        var rank3 = new RankingDto(currentTimestamp.atOffset(ZoneOffset.UTC).toLocalDate(),
                "testrank3",
                RankingStatus.ACTIVE,
                currentTimestamp.minusSeconds(60),
                currentTimestamp.plusSeconds(120),
                "60M",
                RankingType.WIN_MAX,
                TournamentType.DEFAULT, null,
                "", "", 0, 0,
                null);
        rsServ.persistRankings(Collections.singletonList(rank3));
        activeRankings = rsServ.getActiveRankings();
        assertEquals(3, activeRankings.size());
        sstxMon.reloadRankings();
        assertEquals(3, sstxMon.getCurrentRankings().length);

        var currentRankings = sstxMon.getCurrentRankings();
        var srtArr = Arrays.copyOf(currentRankings, currentRankings.length);
        Arrays.sort(srtArr, (a, b) -> a.compareTo(b) * -1);
        Arrays.sort(srtArr, ActiveRanking::compareTo);
        // rankings with higher endTS should be sorted at the end ?
        assertSame(RankingType.WIN_MAX, srtArr[2].getrType());

        // Add fourth to test sorting
        var rank4 = new RankingDto(currentTimestamp.atOffset(ZoneOffset.UTC).toLocalDate(),
                "testrank4",
                RankingStatus.ACTIVE,
                currentTimestamp.plusSeconds(60),
                currentTimestamp.plusSeconds(120),
                "1M",
                RankingType.WIN_MAX,
                TournamentType.DEFAULT, null,
                "", "", 0, 0,
                null);
        rsServ.persistRankings(Collections.singletonList(rank4));
        activeRankings = rsServ.getActiveRankings();
        assertEquals(4, activeRankings.size());
        sstxMon.reloadRankings();

        for (ActiveRanking a : sstxMon.getCurrentRankings()) {
            a.addUserLinkage(2);
        }

        assertEquals(4, sstxMon.getCurrentRankings().length);

        // Ok, we now have 4 rankings in the mon, one with ts in the future
        List<MonitoredGameTx> updates = new ArrayList<>(4);

        updates.add(new MonitoredGameTx(2L, 2, 2, GamePlatform.DESKTOP
                , TransactionType.DIRECTWIN,
                5000, 500,
                0, (short) 0, false, 95.4f
        ));
        updates.add(new MonitoredGameTx(2L, 2, 2, GamePlatform.DESKTOP
                , TransactionType.DIRECTWIN,
                2500, 250,
                0, (short) 0, false, 95.4f
        ));
        updates.add(new MonitoredGameTx(2L, 2, 2, GamePlatform.DESKTOP
                , TransactionType.DIRECTWIN,
                2000, 200,
                0, (short) 0, false, 95.4f
        ));
        updates.add(new MonitoredGameTx(2L, 2, 2, GamePlatform.DESKTOP
                , TransactionType.DIRECTWIN,
                500, 50,
                0, (short) 0, false, 95.4f
        ));

        MonitoredItemsBatchEvent mibe = new MonitoredItemsBatchEvent(updates);
        sstxMon.handleTx(mibe);


        var testrank1 = rC.getScoredSortedSet(rank1.rankId(), LongCodec.INSTANCE);
        Double score = null;
        for (int i = 0; i < 300; i++) {
            score = testrank1.getScore(2L);
            if (score != null) {
                break;
            }
            sleep(60);
        }
        assertTrue(score.intValue() >= 100);


        var testrank2 = rC.getScoredSortedSet(rank2.rankId(), LongCodec.INSTANCE);
        score = testrank2.getScore(2L);
        assertTrue(score.intValue() >= 10);

        var testrank3 = rC.getScoredSortedSet(rank3.rankId(), LongCodec.INSTANCE);
        score = testrank3.getScore(2L);
        assertTrue(score.intValue() >= 5);

        var testrank4 = rC.getScoredSortedSet(rank4.rankId(), LongCodec.INSTANCE);
        score = testrank4.getScore(2L);
        assertNull(score);


    }

    private void sleep(int i) {
        try {
            Thread.sleep(i);
        } catch (InterruptedException ignore) {
        }
    }


    @Test
    @Disabled
    void testRankMaxWinClosing() throws IOException {


        TournamentPositions notExistingPos = rScriptSrv.getCurrentPositions("notExistingPos");
        assertEquals(0, notExistingPos.getPositions().size());
        RankingUserNamesHandler ruHandler = new RankingUserNamesHandlerImpl(userTNameMap, rankLinkRepo);

        rankRepo.deleteAll();
        rankRepo.flush();

        var currentTimestamp = Instant.now();

        var rService = new RankingServiceImpl(umService, cmFact,
                lCacheFactory, rankRepo,
                rScriptSrv, null, rankingConfiguration, evntPub);
        var rsServ = new RankingSchedulerServiceImpl(rsRepo, rankRepo, null, null, null);

        List<RankingDto> activeRankings = rsServ.getActiveRankings();
        assertEquals(0, activeRankings.size());

        // Add special ranking for a specific game
        var rank1 = new RankingDto(currentTimestamp.atOffset(ZoneOffset.UTC).toLocalDate(),
                "testrank101",
                RankingStatus.ACTIVE,
                currentTimestamp.minusSeconds(60),
                currentTimestamp.plusSeconds(60),
                "60M",
                RankingType.WIN_MAX,
                TournamentType.DEFAULT, null,
                "gid=99",
                "", 0, 1500,
                null);

        rsServ.persistRankings(Collections.singletonList(rank1));

        sleep(500);
        rScriptSrv.reloadRankings();


        activeRankings = rsServ.getActiveRankings();
        assertEquals(1, activeRankings.size());

        sstxMon.reloadRankings();
        Arrays.stream(sstxMon.getCurrentRankings()).forEach(a -> {
            a.addUserLinkage(666);
            a.addUserLinkage(667);
            a.addUserLinkage(668);

        });
        assertEquals(1, sstxMon.getCurrentRankings().length);

        // Add usernames
        ruHandler.updatePlayernameForUser(666, "003301234566Player666");
        ruHandler.updatePlayernameForUser(667, "004401234567Player667");
        ruHandler.updatePlayernameForUser(668, "005501234568Player668");


        // add updates
        List<MonitoredGameTx> updates = new ArrayList<>(4);

        updates.add(new MonitoredGameTx(666L, 99, 2, GamePlatform.DESKTOP
                , TransactionType.DIRECTWIN,
                5000, 1500,
                0, (short) 0, false, 95.4f
        ));
        updates.add(new MonitoredGameTx(667L, 99, 2, GamePlatform.DESKTOP
                , TransactionType.DIRECTWIN,
                7500, 2500,
                0, (short) 0, false, 95.4f
        ));
        updates.add(new MonitoredGameTx(668L, 99, 2, GamePlatform.DESKTOP
                , TransactionType.DIRECTWIN,
                1500, 3500,
                0, (short) 0, false, 95.4f
        ));
        updates.add(new MonitoredGameTx(666L, 99, 2, GamePlatform.DESKTOP
                , TransactionType.DIRECTWIN,
                5000, 1500,
                0, (short) 0, false, 95.4f
        ));
        long initialUpdateTime = resultUpdater.getLastUpdateTime();
        rankPositionsCachedMap.clear();
        MonitoredItemsBatchEvent mibe = new MonitoredItemsBatchEvent(updates);
        sstxMon.handleTx(mibe);


        // wait for updateSchedule
        while (resultUpdater.getLastUpdateTime() <= initialUpdateTime) {
            sleep(500);
        }

//        // allow for the putFast to be really effective
        sleep(2000);

        assertEquals(rank1.rankId(), rankPositionsCachedMap.get(rank1.rankId()).getTournamentId());


        // wait a while
        ScoredSet<Long> scoredSet = cmFact.createScoredSet(rank1.rankId(), Long.class);
        var i = 0;
        while (scoredSet.size() < 3 && i < 100) {
            sleep(100);
            i++;
        }

        // now check the ranking
        List<ScoredSet.ScoredEntry<Long>> rankEntries = scoredSet.getRankEntries(0, 3);
        assertEquals(3, rankEntries.size());

        RankingAggregation aggSum1 = rankAggCachedMap.get(rank1.rankId());
        assertEquals(3, aggSum1.getCount());

        // std. sum function
        assertEquals(75, rService.getSumScoresOfRanking(rank1.rankId()), 0.01d);
        // agg. function
        RankingAggregation aggScoresOfRanking = rService.getAggScoresOfRanking(rank1.rankId());
        assertEquals(75d, aggScoresOfRanking.getSum(), 0.003d);
        assertEquals(3, aggScoresOfRanking.getCount());
        assertEquals(15d, aggScoresOfRanking.getMin(), 0.003d);
        assertEquals(35d, aggScoresOfRanking.getMax(), 0.003d);

        // results with names
        var resWithNames = rService.getCurrentPositions(rank1.rankId());
        assertEquals(3, resWithNames.getPositions().size());
        var r1 = resWithNames.getPositions().getFirst();

        assertEquals(668L, r1.getUserId());
        assertEquals(3500L, r1.getScore());
        assertEquals("01234568", r1.getExtId());
        assertEquals("Player668", r1.getPlayername());
        assertEquals(55, r1.getLevel());


        rScriptSrv.reloadRankings();

        // Now check the postion struct
        TournamentPositions currentPositions = rScriptSrv.getCurrentPositions(rank1.rankId());
        sleep(1000);

        assertEquals(3, currentPositions.getPositions().size());
        assertEquals(1800, currentPositions.getPricePool());

        RankingResults results = rService.closeRanking(rank1.rDate(), rank1.rankId());
        assertEquals(0.0d, rService.getSumScoresOfRanking(rank1.rankId()), 0.01d);

        assertEquals(3, results.getNumUsers());
        assertEquals(75.0d, results.getSumScores(), 0.001d);
        assertEquals(668, results.getResults().get(0).u());
        assertEquals(667, results.getResults().get(1).u());
        assertEquals(666, results.getResults().get(2).u());
        assertEquals("Player666", results.getResults().get(2).playername());

    }

}

spring.main.lazy-initialization=true
# for testing we do not need any exposed actuator stuff
management.endpoints.web.exposure.exclude=*
management.endpoint.health.enabled=false
management.endpoint.metrics.enabled=false
management.enabled=false
spring.datasource.url=${DB_URL}
spring.datasource.readUrl=${DB_URL}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# legacy
# BalanceReportTest depends on liquibase context != unittest
spring.liquibase.contexts=dev
#
# Users are active automatically
#
features.autoActive=true
features.jurisdiction=SOC


#
# Firestore emulator testing
#
clouddb.enabled=true


#
# RankingRewards test
#
monitor.rankings.rewards[0].max-player=5
monitor.rankings.rewards[0].reward-factors=1.0
monitor.rankings.rewards[1].max-player=10
monitor.rankings.rewards[1].reward-factors=0.65,0.35
monitor.rankings.rewards[2].max-player=20
monitor.rankings.rewards[2].reward-factors=0.60,0.30,0.1
monitor.rankings.rewards[3].max-player=50
monitor.rankings.rewards[3].reward-factors=0.55,0.25,0.15,0.05
monitor.rankings.rewards[4].max-player=-1
monitor.rankings.rewards[4].reward-factors=0.5,0.25,0.15,0.075,0.025

#
# enable jackpots to allow testing the admin (offloader) based maintenance
#
jackpots.enabled=true

#
# enable safes feature
#
safes.enabled=true


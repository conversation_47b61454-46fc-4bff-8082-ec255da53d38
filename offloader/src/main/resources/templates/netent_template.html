<html>
<head>
    <title>My game page</title>
    <script type="text/javascript"
            src="https://netent-static.casinomodule.com/gameinclusion/library/gameinclusion.js"></script>
    <script type="text/javascript">
      var startGame = function () {
        var config = {
          gameId: "${gameId}",
          staticServerURL: "https://netent-static.casinomodule.com/",
          gameServerURL: "https://socialcasino-dev.casinomodule.com/gs-api/v3/ously",
	        sessionId: "${jwt}",
	        targetElement: "gameArea"
        };
 
        // Game launch successful.
        var success = function (netEntExtend) {
          
        };
        // Error handling here.
        var error = function (e) {
        };
   
        netent.launch(config, success, error);
      };
   
      window.onload = startGame;

    </script>
</head>
<body>
<div id="gameArea"></div>
</body>

</html>


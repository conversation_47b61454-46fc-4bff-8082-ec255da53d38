package com.ously.gamble.controller;


import com.ously.gamble.api.PurchaseManagementService;
import com.ously.gamble.payload.PagedResponse;
import com.ously.gamble.payload.purchase.UserPurchase;
import com.ously.gamble.shared.controller.BaseController;
import com.ously.gamble.util.AppConstants;
import com.ously.gamble.util.DateRange;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.*;


/**
 * Controller to allow admins querying purchase infos for users
 */

@RestController
@RequestMapping("/api/admin/purchases")
public class PurchaseController extends BaseController {

    @Autowired
    PurchaseManagementService purchRepo;

    @Operation(description = "get all purchases of a user",
               security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/{userId}")
    @RolesAllowed("ADMIN")
    public PagedResponse<UserPurchase> getAllPurchasesForUserA(
            @PathVariable(value = "userId") Long userId,
            @RequestParam(value = "page", defaultValue = AppConstants.DEFAULT_PAGE_NUMBER) int page,
            @RequestParam(value = "size", defaultValue = AppConstants.DEFAULT_PAGE_SIZE) int size) {
        var allPurchasesPage = purchRepo.findByUserIdOrderByCreatedAtDesc(userId, PageRequest.of(page, size));
        return createPagedResponse(allPurchasesPage, UserPurchase::new);
    }


    @Operation(description = "get all purchases for a period",
               security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/{from}/{to}")
    @RolesAllowed("ADMIN")
    public PagedResponse<UserPurchase> getAllPurchases(
            @PathVariable(value = "from") String begin,
            @PathVariable(value = "to") String end,
            @RequestParam(value = "page", defaultValue = AppConstants.DEFAULT_PAGE_NUMBER) int page,
            @RequestParam(value = "size", defaultValue = AppConstants.DEFAULT_PAGE_SIZE) int size) {

        var dr = DateRange.of(begin, end);
        var allPurchasesPage = purchRepo.findAllPageableWithinPeriod(dr.getFrom().atStartOfDay(),
                dr.getTo().atStartOfDay(), PageRequest.of(page, size));
        return createPagedResponse(allPurchasesPage, UserPurchase::new);
    }

}

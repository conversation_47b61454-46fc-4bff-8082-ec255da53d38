package com.ously.gamble.controller;

import com.ously.gamble.api.features.DoubleUpService;
import com.ously.gamble.api.features.GambleWeapon;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/admin/du")
public class DoubleUpController {

    private final DoubleUpService dugService;

    public DoubleUpController(DoubleUpService dus){
        this.dugService=dus;
    }

    //
    // Managing the weapons
    //
    @Operation(description = "create a weapon", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/weapon")
    @RolesAllowed("ADMIN")
    public GambleWeapon newWeaponA(GambleWeapon req) {
        return dugService.createNewWeapon(req);
    }

    @Operation(description = "update a weapon", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/weapon/{id}")
    @RolesAllowed("ADMIN")
    public GambleWeapon updateWeaponA(@PathVariable(value = "id") Long id, GambleWeapon req) {
        return dugService.updateWeapon(id, req);
    }

    @Operation(description = "delete a weapon", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/weapon/{id}")
    @RolesAllowed("ADMIN")
    public void deleteWeaponA(@PathVariable(value = "id") Long id) {
        dugService.deleteWeapon(id);
    }

    @Operation(description = "get a weapon", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/weapon/{id}")
    @RolesAllowed("ADMIN")
    public void getWeaponA(@PathVariable(value = "id") Long id) {
        dugService.getWeapon(id);
    }
}

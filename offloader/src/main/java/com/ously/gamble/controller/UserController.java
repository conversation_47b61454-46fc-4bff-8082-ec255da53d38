package com.ously.gamble.controller;


import com.ously.gamble.api.features.UserToken;
import com.ously.gamble.api.security.CurrentUser;
import com.ously.gamble.api.security.UserPrincipal;
import com.ously.gamble.api.user.BalanceStatus;
import com.ously.gamble.api.user.UserBalanceStatus;
import com.ously.gamble.shared.controller.BaseUserController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/admin")
public class UserController extends BaseUserController {


    @Operation(description = "gets tokens of a user",
               security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/tokens/{id}")
    @RolesAllowed("ADMIN")
    public List<UserToken> getTokensForUser(@PathVariable("id") Long userId) {
        return getUserService().getAvailableTokens(userId);
    }

    @Operation(description = "set user bonus balance status", security =
            {@SecurityRequirement(name =
                    "bearer-key")})
    @PutMapping("/user/balance/bonus/{userId}")
    @RolesAllowed("ADMIN")
    public Optional<UserBalanceStatus> setUserBonusBalanceStatus(
            @PathVariable("userId") long userId, @RequestBody BalanceStatus bStatus, @CurrentUser UserPrincipal currentUser) {
        return Optional.empty();
    }

}

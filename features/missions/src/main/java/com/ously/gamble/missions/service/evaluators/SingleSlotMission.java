package com.ously.gamble.missions.service.evaluators;

import com.ously.gamble.api.missions.MissionGoal;
import com.ously.gamble.api.missions.MissionService;
import com.ously.gamble.api.missions.MissionType;


public class SingleSlotMission extends AbstractMission implements MissionEvaluator {

    public SingleSlotMission(MissionService mMgtm) {
        super(mMgtm, MissionType.SINGLE_SLOT, MissionGoal.SPIN_COUNT, MissionGoal.WIN_SUM, MissionGoal.SPIN_WIN_COUNT, MissionGoal.BET_SUM);
    }

    @Override
    public MissionType getType() {
        return MissionType.SINGLE_SLOT;
    }

}

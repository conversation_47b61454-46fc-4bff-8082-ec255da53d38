package com.ously.gamble.missions.config;

import com.ously.gamble.conditions.ConditionalOnSocial;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "mission")
@ConditionalOnSocial
public class MissionsConfiguration {

    boolean enabled = true;


    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}

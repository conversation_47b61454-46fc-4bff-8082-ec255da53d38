package com.ously.gamble.missions.configuration;

import com.ously.gamble.api.cache.CachedMap;
import com.ously.gamble.api.cache.CachedMapFactory;
import com.ously.gamble.api.cache.CachedQueue;
import com.ously.gamble.api.cache.CodecType;
import com.ously.gamble.api.missions.*;
import com.ously.gamble.api.session.MonitoredTransaction;
import com.ously.gamble.persistence.model.game.GamePlatform;
import org.springframework.amqp.core.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MissionsSetup {

    @ConditionalOnProperty(prefix = "features", name = "jurisdiction", havingValue = "SOC",
            matchIfMissing = true)
    @Bean
    CachedQueue<MonitoredTransaction> monitoredTxBuffer(
            CachedMapFactory<String, MonitoredTransaction> fact) {
        return fact.createBlockingQueue("monitoredTx", CodecType.FST, MonitoredTransaction.class);
    }

    @Bean
    @ConditionalOnProperty(prefix = "features", name = "jurisdiction", havingValue = "SOC",
            matchIfMissing = true)
    CachedMap<Long, DailyMission> missionCache(
            CachedMapFactory<Long, DailyMission> fact) {
        return fact.createLocalCachedMap("missions", CodecType.FST, Long.class, DailyMission.class, 60 * 70);
    }

    @Bean
    @ConditionalOnProperty(prefix = "features", name = "jurisdiction", havingValue = "SOC",
            matchIfMissing = true)
    CachedMap<GamePlatform, MissionHolder> dailyMissionCache(
            CachedMapFactory<GamePlatform, MissionHolder> fact) {
        return fact.createLocalCachedMap("dmissions", CodecType.FST, GamePlatform.class, MissionHolder.class, 60 * 30);
    }

    @Bean
    @ConditionalOnProperty(prefix = "features", name = "jurisdiction", havingValue = "SOC",
            matchIfMissing = true)
    CachedQueue<UserMissionUpdate> missionUpdates(
            CachedMapFactory<String, UserMissionUpdate> fact) {
        return fact.createBlockingQueue("missionUpdates", CodecType.FST, UserMissionUpdate.class);
    }


    @Bean
    CachedMap<Long, UserMissionProgress> userProgressCache(
            CachedMapFactory<Long, UserMissionProgress> fact) {
        return fact.createLocalCachedMap("uprogress", CodecType.FST, Long.class, UserMissionProgress.class, 60 * 90);
    }

    @Bean
    DirectExchange missionsExchange() {
        return ExchangeBuilder.directExchange("missions").durable(true).build();
    }

    @Bean
    Binding missionsFinishedStatsBinding() {
        return BindingBuilder.bind(missionsUpdateFinishedQueue()).to(missionsExchange()).withQueueName();
    }

    @Bean
    Queue missionsUpdateFinishedQueue() {
        return QueueBuilder.durable(MissionsGateway.MISSIONS_UPDATE_FINISHED_QUEUE)
                .withArgument("x-dead-letter-exchange", "")
                .withArgument("x-dead-letter-routing-key", MissionsGateway.MISSIONS_UPDATE_FINISHED_QUEUE + "DLQ")
                .build();
    }

    @Bean
    Queue missionsUpdateFinishedQueueDLQ() {
        return QueueBuilder.durable(MissionsGateway.MISSIONS_UPDATE_FINISHED_QUEUE + "DLQ").build();
    }


}

package com.ously.gamble.doubleup.service;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertTrue;

class RandomBiasedAndUnbiasedTests {
    @Test
    void checkRandom() {
        // check that random returns 50:50 bits
        long even = 0;

        for (var i = 0; i < 10_000_000; i++) {
            var winVector = DoubleUpServiceImpl.createWinVector();
            if (winVector % 2 == 0) {
                even++;
            }
        }
        assertTrue(even > 4990000 && even < 5010000);
    }

    @Test
    void checkRandomLoweringChance() {
        long won = 0;
        for (var i = 0; i < 1_000_000; i++) {
            var b = DoubleUpServiceImpl.hasWon2(1, 1, 0.9);
            if (b) {
                won++;
            }
        }
        assertTrue(won > 890000 && won < 901000);
    }

    @Test
    void checkRandomRaisingChance() {
        long won = 0;
        for (var i = 0; i < 1_000_000; i++) {
            var b = DoubleUpServiceImpl.hasWon2(1, 0, 1.2);
            if (b) {
                won++;
            }
        }
        assertTrue(won > 180000 && won < 220000);
    }

    @Test
    void checkLevelDist() {
        // check that random returns 50:50 bits
        var levels = calcLevelDistBias(1.0f);
        assertTrue(levels[3] > 580000 && levels[3] < 650000);
        levels = calcLevelDistBias(0.9f);
        assertTrue(levels[3] > 380000 && levels[3] < 450000);
        levels = calcLevelDistBias(1.1f);
        assertTrue(levels[3] > 850000 && levels[3] < 950000);
    }

    private int[] calcLevelDistBias(float bias) {
        var levels = new int[10];

        for (var i = 0; i < 10_000_000; i++) {
            var winVector = DoubleUpServiceImpl.createWinVector();
            for (var level = 1; level < 11; level++) {
                var levelWon = DoubleUpServiceImpl.hasWon2(level, winVector, bias);
                if (levelWon) {
                    levels[level - 1]++;
                } else {
                    break;
                }
            }
        }
        return levels;
    }


}

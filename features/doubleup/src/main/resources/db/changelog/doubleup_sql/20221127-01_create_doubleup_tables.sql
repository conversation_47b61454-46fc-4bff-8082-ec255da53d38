CREATE TABLE if not exists `doubleup_foes` (
                                 `id` bigint NOT NULL AUTO_INCREMENT,
                                 `created_at` datetime DEFAULT NULL,
                                 `updated_at` datetime DEFAULT NULL,
                                 `active` bit(1) NOT NULL,
                                 `chance_factor` decimal(19,2) NOT NULL,
                                 `level` int NOT NULL,
                                 `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
                                 `win_factor` decimal(19,2) NOT NULL,
                                 `card_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE if not exists `doubleup_statistics` (
                                       `user_id` bigint NOT NULL,
                                       `session_id` bigint NOT NULL,
                                       `finished_at` datetime NOT NULL,
                                       `last_round` int NOT NULL,
                                       `maxbet` decimal(19,2) NOT NULL,
                                       `status` int NOT NULL,
                                       `sum_bet` decimal(19,2) NOT NULL,
                                       `sum_win` decimal(19,2) NOT NULL,
                                       PRIMARY KEY (`user_id`,`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE  if not exists `doubleup_weapons` (
                                    `id` bigint NOT NULL AUTO_INCREMENT,
                                    `created_at` datetime DEFAULT NULL,
                                    `updated_at` datetime DEFAULT NULL,
                                    `active` bit(1) NOT NULL,
                                    `chance_factor` decimal(19,2) NOT NULL,
                                    `freeforall` bit(1) NOT NULL,
                                    `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
                                    `win_factor` decimal(19,2) NOT NULL,
                                    `card_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE if not exists `doubleups` (
                             `user_id` bigint NOT NULL,
                             `session_id` bigint NOT NULL,
                             `created_at` datetime DEFAULT NULL,
                             `updated_at` datetime DEFAULT NULL,
                             `active` bit(1) NOT NULL,
                             `active_round` int NOT NULL,
                             `current_maxbet` decimal(19,2) DEFAULT NULL,
                             `expires` datetime NOT NULL,
                             `secret` varchar(40) COLLATE utf8mb4_unicode_ci NOT NULL,
                             `sum_bet` decimal(19,2) DEFAULT NULL,
                             `sum_win` decimal(19,2) DEFAULT NULL,
                             `winvector` int NOT NULL,
                             PRIMARY KEY (`user_id`,`session_id`),
                             INDEX `du_exp_claimed_idx` (expires,active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


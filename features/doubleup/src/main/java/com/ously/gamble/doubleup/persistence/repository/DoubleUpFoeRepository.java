package com.ously.gamble.doubleup.persistence.repository;

import com.ously.gamble.doubleup.persistence.model.DoubleUpFoe;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ConditionalOnProperty(prefix = "features", name = "jurisdiction", havingValue = "SOC", matchIfMissing = true)
public interface DoubleUpFoeRepository extends JpaRepository<DoubleUpFoe, Long> {

    List<DoubleUpFoe> findByActive(boolean active);

}
package com.ously.gamble.doubleup.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "doubleup")
public class DoubleUpConfiguration {

    long doubleUpSaldoLimit = 5L;
    boolean enabled = true;

    public long getDoubleUpSaldoLimit() {
        return doubleUpSaldoLimit;
    }

    public void setDoubleUpSaldoLimit(long doubleUpSaldoLimit) {
        this.doubleUpSaldoLimit = doubleUpSaldoLimit;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}

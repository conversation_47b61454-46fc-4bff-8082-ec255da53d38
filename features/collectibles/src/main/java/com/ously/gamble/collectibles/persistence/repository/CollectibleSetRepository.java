package com.ously.gamble.collectibles.persistence.repository;

import com.ously.gamble.collectibles.persistence.model.CollectibleSet;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
@ConditionalOnProperty(prefix = "collectibles", name = "enabled", havingValue = "true")
public interface CollectibleSetRepository extends JpaRepository<CollectibleSet, Integer> {


}
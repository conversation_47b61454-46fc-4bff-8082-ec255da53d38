package com.ously.gamble.collectibles.persistence.model;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

public class UserCollectiblesSerializer extends JsonSerializer<UserCollectibles> {
    @Override
    public void serialize(UserCollectibles value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeStartObject();

        if (value.collectibleCounts != null && !value.collectibleCounts.isEmpty()) {

            value.collectibleCounts.forEachKeyValue((k, v) -> {
                try {
                    gen.writeNumberField("" + k, v);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
        }

        gen.writeEndObject();
    }
}



package com.ously.gamble.collectibles.persistence.model;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import org.hibernate.annotations.Type;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@EntityListeners(AuditingEntityListener.class)
@Entity
@Table(name = "collectibles_user")
public class UserCollectible {

    @Id
    @Column(name = "user_id")
    long userId;

    @Version
    @Column(name = "version")
    int version = 1;

    @Type(JsonType.class)
    @Column(name = "counts")
    UserCollectibles counts;

    protected UserCollectible() {
    }

    protected UserCollectible(long userId) {
        this.userId = userId;
        this.counts = new UserCollectibles();
    }

    public long getUserId() {
        return userId;
    }

    public UserCollectibles getCounts() {
        return counts;
    }

    public void setCounts(UserCollectibles nCounts) {
        this.counts = nCounts;
    }

    public void addCollectible(int id, int count) {
        counts.addCollectible(id);
    }

    public void addCollectible(int id) {
        addCollectible(id, 1);
    }

    public static UserCollectible newUserCollectible(long userId) {
        return new UserCollectible(userId);
    }

}

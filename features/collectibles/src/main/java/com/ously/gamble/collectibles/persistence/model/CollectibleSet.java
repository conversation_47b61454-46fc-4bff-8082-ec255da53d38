package com.ously.gamble.collectibles.persistence.model;

import com.ously.gamble.collectibles.api.CollectibleSetDto;
import jakarta.persistence.*;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "collectible_set")
public class CollectibleSet {


    @Id
    @GeneratedValue(generator = "collectibleSet")
    @GenericGenerator(name = "collectibleSet", type = org.hibernate.id.enhanced.TableGenerator.class,
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "table_name", value = "custom_sequences"),
                    @org.hibernate.annotations.Parameter(name = "value_column_name",
                            value = "sequence_next_hi_value"),
                    @org.hibernate.annotations.Parameter(name = "prefer_entity_table_as_segment_value",
                            value = "true"),
                    @org.hibernate.annotations.Parameter(name = "optimizer", value = "pooled-lo"),
                    @org.hibernate.annotations.Parameter(name = "initial_value", value = "100"),
                    @org.hibernate.annotations.Parameter(name = "increment_size", value = "2"),
                    @org.hibernate.annotations.Parameter(name = "segment_value", value = "collectibleSet")
            })
    @Column(name = "id", updatable = false)
    private Integer id;

    @Column(name = "sort_order")
    private int order;

    protected CollectibleSet() {
    }

    public int getId() {
        return id;
    }

    public int getOrder() {
        return order;
    }

    public CollectibleSetDto toDto() {
        return new CollectibleSetDto(id, order);
    }

}

### DDD Card Collection Management API Examples

### 1. Create Collection
POST http://localhost:8080/api/admin/card-collections
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "name": "Magical Library Collection",
  "description": "First collection of magical cards",
  "startDate": "2025-01-01T00:00:00",
  "endDate": "2025-12-31T23:59:59",
  "sortOrder": 1
}

### 2. Get All Collections
GET http://localhost:8080/api/admin/card-collections
Authorization: Bearer YOUR_ADMIN_TOKEN

### 3. Get Collection with Full Details
GET http://localhost:8080/api/admin/card-collections/1
Authorization: Bearer YOUR_ADMIN_TOKEN

### 4. Update Collection
PUT http://localhost:8080/api/admin/card-collections/1
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "name": "Updated Magical Library",
  "description": "Updated description",
  "status": "ENABLED"
}

### 5. Create Card in Collection (через Aggregate Root)
POST http://localhost:8080/api/admin/card-collections/1/cards
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "name": "Fire Dragon",
  "imageUrl": "https://example.com/fire-dragon.jpg",
  "rarityLevel": 3,
  "sortOrder": 1
}

### 6. Update Card in Collection (через Aggregate Root)
PUT http://localhost:8080/api/admin/card-collections/1/cards/1
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "name": "Ancient Fire Dragon",
  "imageUrl": "https://example.com/ancient-fire-dragon.jpg",
  "status": "ENABLED"
}

### 7. Create Collection Completion Reward (через Aggregate Root)
POST http://localhost:8080/api/admin/card-collections/1/rewards
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "cardId": null,
  "rewardType": "COMPLETION",
  "rewardData": {
    "type": "coins",
    "amount": 1000,
    "description": "Collection completion bonus"
  }
}

### 8. Create Card Milestone Reward (через Aggregate Root)
POST http://localhost:8080/api/admin/card-collections/1/rewards
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "cardId": 1,
  "rewardType": "MILESTONE",
  "milestonePercentage": 50,
  "rewardData": {
    "type": "experience",
    "amount": 500,
    "description": "50% card completion bonus"
  }
}

### 9. Update Reward (через Aggregate Root)
PUT http://localhost:8080/api/admin/card-collections/1/rewards/1
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "milestonePercentage": 75,
  "rewardData": {
    "type": "experience",
    "amount": 750,
    "description": "75% card completion bonus"
  }
}

### 10. Delete Card from Collection (через Aggregate Root)
DELETE http://localhost:8080/api/admin/card-collections/1/cards/1
Authorization: Bearer YOUR_ADMIN_TOKEN

### 11. Delete Reward (через Aggregate Root)
DELETE http://localhost:8080/api/admin/card-collections/1/rewards/1
Authorization: Bearer YOUR_ADMIN_TOKEN

### 12. Activate Collection
POST http://localhost:8080/api/admin/card-collections/1/activate
Authorization: Bearer YOUR_ADMIN_TOKEN

### 13. Get Cards by Collection (read-only endpoint)
GET http://localhost:8080/api/admin/cards/collection/1
Authorization: Bearer YOUR_ADMIN_TOKEN

### 14. Get Rare Cards (read-only endpoint)
GET http://localhost:8080/api/admin/cards/rare
Authorization: Bearer YOUR_ADMIN_TOKEN

### 15. Search Cards by Name (read-only endpoint)
GET http://localhost:8080/api/admin/cards/search?name=dragon
Authorization: Bearer YOUR_ADMIN_TOKEN

### 16. Get Card Details (read-only endpoint)
GET http://localhost:8080/api/admin/cards/1
Authorization: Bearer YOUR_ADMIN_TOKEN

### Response Examples:

# Collection Response (после создания/обновления):
{
  "id": 1,
  "name": "Magical Library Collection",
  "description": "First collection of magical cards",
  "startDate": "2025-01-01T00:00:00",
  "endDate": "2025-12-31T23:59:59",
  "status": "ENABLED",
  "sortOrder": 1,
  "cards": [
    {
      "id": 1,
      "name": "Fire Dragon",
      "imageUrl": "https://example.com/fire-dragon.jpg",
      "rarityLevel": 3,
      "startDate": "2025-01-01T00:00:00",
      "endDate": null,
      "status": "ENABLED",
      "sortOrder": 1,
      "rewards": [
        {
          "id": 2,
          "rewardType": "MILESTONE",
          "milestonePercentage": 50,
          "rewardData": {
            "type": "experience",
            "amount": 500
          },
          "targetType": "CARD",
          "targetId": 1,
          "targetName": "Fire Dragon"
        }
      ]
    }
  ],
  "rewards": [
    {
      "id": 1,
      "rewardType": "COMPLETION",
      "milestonePercentage": null,
      "rewardData": {
        "type": "coins",
        "amount": 1000
      },
      "targetType": "COLLECTION",
      "targetId": 1,
      "targetName": "Magical Library Collection"
    }
  ]
}

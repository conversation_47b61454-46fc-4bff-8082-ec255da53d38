package com.ously.gamble.api.rankings;

import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public interface RankingSchedulerService {
    List<RankingDto> doSchedule();

    List<RankingScheduleDto> getActiveRankingSchedules();

    List<RankingDto> getActiveRankings();

    RankingScheduleDto addSchedule(RankingScheduleDto newSchedule);

    @Transactional
    RankingScheduleDto updateSchedule(RankingScheduleDto updSchedule);

    @Transactional
    void deleteSchedule(int schedId);

    Optional<RankingDto> updateRanking(RankingDto upd);

    int closeFinishedRankings();

    void doLinkageCleanup();
}

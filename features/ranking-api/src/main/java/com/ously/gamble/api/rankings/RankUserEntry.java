package com.ously.gamble.api.rankings;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public record RankUserEntry(
        @JsonIgnore
        long userId,
        @JsonProperty("rank")
        Integer rank,
        @JsonProperty("username")
        String username,
        @JsonProperty("score")
        double score,
        @JsonProperty("isRequestingUser")
        boolean isRequestingUser
) implements Serializable {
        public RankUserEntry(RankingResult r) {
                this(r.u(), r.pos(), r.playername(), r.s(), false);
        }
}

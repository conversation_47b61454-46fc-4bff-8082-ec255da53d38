package com.ously.gamble.api.rankings;

import java.io.Serializable;
import java.util.List;

public class TournamentPositions implements Serializable {
    String tournamentId;
    int pricePool;
    List<TournamentPosition> positions;

    public TournamentPositions(
            String tournamentId, int pricePool,
            List<TournamentPosition> positions) {
        this.tournamentId = tournamentId;
        this.pricePool = pricePool;
        this.positions = positions;
    }

    public String getTournamentId() {
        return tournamentId;
    }

    public void setTournamentId(String tournamentId) {
        this.tournamentId = tournamentId;
    }

    public int getPricePool() {
        return pricePool;
    }

    public void setPricePool(int pricePool) {
        this.pricePool = pricePool;
    }

    public List<TournamentPosition> getPositions() {
        return positions;
    }

    public void setPositions(List<TournamentPosition> positions) {
        this.positions = positions;
    }
}

package com.ously.gamble.api.rankings;

import java.io.Serializable;

public class TournamentPosition implements Serializable {
    long userId;
    int position;
    double score;
    String extId;
    String playername;
    int level;

    public TournamentPosition(long userId, int position, double score, String extId, String playername, int level) {
        this.userId = userId;
        this.position = position;
        this.score = score;
        this.extId = extId;
        this.playername = playername;
        this.level=level;
    }

    public int getPosition() {
        return position;
    }

    public double getScore() {
        return score;
    }

    public String getExtId() {
        return extId;
    }

    public String getPlayername() {
        return playername;
    }

    public int getLevel() {
        return level;
    }

    public long getUserId() {
        return userId;
    }
}
package com.ously.gamble.api.rankings.bots;

import java.math.BigDecimal;

public record TournamentBotTransaction(String botname, BigDecimal bet, BigDecimal win, int betlevel, int gameId) {

    public TournamentBotTransaction(String botname, BigDecimal bet, BigDecimal win, int betlevel) {
        this(botname, bet, win, betlevel, -1);
    }

    public TournamentBotTransaction(String botname, BigDecimal bet, BigDecimal win) {
        this(botname, bet, win, 0, -1);
    }

    @Override
    public String toString() {
        return "TournamentBotTransaction{" +
                "botname='" + botname + '\'' +
                ", bet=" + bet +
                ", win=" + win +
                ", betlevel=" + betlevel +
                ", gameId=" + gameId +
                '}';
    }
}

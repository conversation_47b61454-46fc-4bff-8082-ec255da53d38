buildscript {

    dependencies {
        if (project.hasProperty("swagger")) {
            println "** NO bytecode enhancements"
        } else {
            println "** Using bytecode enhancements (dep)"
            classpath "org.hibernate.orm:hibernate-gradle-plugin:$hibernateVersion"
        }
    }
}

plugins {
    id 'java-library'
}

repositories {
    mavenCentral()
}


if (project.hasProperty("swagger")) {
    println "** NOT Using bytecode enhancements"

} else {
    println "** Using bytecode enhancements"
    apply plugin: 'org.hibernate.orm'
}


dependencies {
    implementation project(':api')
    implementation project(':persistence')
    implementation project(':configuration')
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
    implementation "org.springframework.amqp:spring-amqp"
    implementation "org.springframework.amqp:spring-rabbit"
    implementation "com.github.ben-manes.caffeine:caffeine"
    implementation "org.eclipse.collections:eclipse-collections:8.2.0"

    testImplementation 'org.junit.jupiter:junit-jupiter-api'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

test {
    useJUnitPlatform()
}
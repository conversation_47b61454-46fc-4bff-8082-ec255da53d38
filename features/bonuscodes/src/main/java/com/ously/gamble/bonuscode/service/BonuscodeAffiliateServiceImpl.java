package com.ously.gamble.bonuscode.service;

import com.ously.gamble.api.bonuscode.*;
import com.ously.gamble.api.crm.CRMUserEvent;
import com.ously.gamble.api.localisation.LanguageCode;
import com.ously.gamble.api.localisation.LocalisationService;
import com.ously.gamble.api.user.UserService;
import com.ously.gamble.bonuscode.persistence.model.BonusCode;
import com.ously.gamble.bonuscode.persistence.model.UserBonuscodeId;
import com.ously.gamble.bonuscode.persistence.repository.BonusCodeRepository;
import com.ously.gamble.bonuscode.persistence.repository.UserBonuscodeLinkRepository;
import com.ously.gamble.conditions.ConditionalOnNotBridge;
import com.ously.gamble.conditions.ConditionalOnSocial;
import jakarta.persistence.OptimisticLockException;
import org.hibernate.StaleStateException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


@Service
@ConditionalOnBean(name = "affiliateConfiguration")
@ConditionalOnNotBridge
@ConditionalOnSocial
public class BonuscodeAffiliateServiceImpl implements BonuscodeAffiliateService {

    private final BonusCodeRepository bcRepo;

    private final Optional<BonuscodeDistributionService> bonuscodeDistributorService;

    private final UserService uService;

    private final LocalisationService locService;

    private final UserBonuscodeLinkRepository ubcRepo;

    private final ApplicationEventPublisher eventPublisher;

    public BonuscodeAffiliateServiceImpl(UserService uService, LocalisationService locService,
                                         Optional<BonuscodeDistributionService> bonuscodeDistributorService, BonusCodeRepository bcRepo,
                                         ApplicationEventPublisher ePub,
                                         UserBonuscodeLinkRepository ubcRepo) {
        this.uService = uService;
        this.locService = locService;
        this.bonuscodeDistributorService = bonuscodeDistributorService;
        this.bcRepo = bcRepo;
        this.ubcRepo = ubcRepo;
        this.eventPublisher = ePub;
    }


    @Override
    @Transactional
    public AffiliateBonusCode createBonusCodeForAffiliate(Integer aId, AffiliateBonusCode abc) {
        var newBc = new BonusCode();
        BonuscodeUtil.updateModel(newBc, abc);
        abc.setAffiliateId(aId);
        return BonuscodeUtil.getAffiliateBonusCode(bcRepo.save(newBc));
    }


    @Override
    public List<BonusCodeType> getBonusTypes() {
        return Arrays.asList(BonusCodeType.values());
    }

    @Override
    @Transactional(timeout = 10)
    @Retryable(
            retryFor = {OptimisticLockException.class, StaleStateException.class, SQLException.class, StaleStateException.class},
            backoff = @Backoff(delay = 50L))
    public ActivateCodeResponse activateBonusCodeForUser(long userId, String bonusCodeLiteral) {

        if (bonuscodeDistributorService.isEmpty()) {
            return createCodeActivationResponse(ActivationStatus.NOT_APPLICABLE, userId);
        }
        // does that code exist ?
        var bonusCode = bcRepo.findByBonusCode(bonusCodeLiteral).orElse(null);
        if (bonusCode == null) {
            return createCodeActivationResponse(ActivationStatus.CODE_UNKNOWN, userId);
        }
        // availability range
        var ldtNow = LocalDateTime.now();
        if (bonusCode.getValidFrom().isAfter(ldtNow) || bonusCode.getValidUntil().isBefore(ldtNow)) {
            return createCodeActivationResponse(ActivationStatus.CODE_EXPIRED, userId);
        }
        var type = bonusCode.getType();
        switch (type) {
            case USER_BOUND -> {
                if (userId == (Objects.requireNonNullElse(bonusCode.getUserId(), 0L))) {
                    return createCodeActivationResponse(ActivationStatus.CODE_UNKNOWN, userId);
                }
            }
            case SHARED, REGISTRATION -> {
                if (bonusCode.getUsedCount() >= bonusCode.getMaxCount()) {
                    return createCodeActivationResponse(ActivationStatus.CODELIMIT_REACHED, userId);
                }
            }
            case ONETIME -> {
                if (bonusCode.getUsedCount() > 0) {
                    return createCodeActivationResponse(ActivationStatus.CODELIMIT_REACHED, userId);
                }
            }
            default -> {
            }
        }

        // each bonuscode can only be used once by a user;
        // the qualifier is "BC_"+type+":"+ bonusCode.id
        var bTypeStr = "BC_" + type.name();
        var qualifier = bTypeStr + ':' + bonusCode.getId();

        var status = bonuscodeDistributorService.get().applyBonuscode(userId, qualifier, bonusCode.getBonusCode(), bTypeStr, bonusCode.getPriceDefinition());
        // ok, now add Achievement
        if (status != ActivationStatus.CODE_ACTIVATED) {
            return createCodeActivationResponse(status, userId);
        }

        // ok, now link to affiliate for REGISTRATION and SHARED ONLY. And only if aff. is not already set
        if (type == BonusCodeType.REGISTRATION || type == BonusCodeType.SHARED) {
            linkUserToBonuscode(userId, bonusCode.getId());
            bonusCode.setUsedCount(bonusCode.getUsedCount() + 1);
            bcRepo.save(bonusCode);
        }

        // Event
        eventPublisher.publishEvent(new CRMUserEvent(userId, "bonus_activated", "type_code", bTypeStr, "affiliate_set", false, "qualifier", qualifier, "affiliate_id", ""));
        return createCodeActivationResponse(ActivationStatus.CODE_ACTIVATED, userId);
    }

    private ActivateCodeResponse createCodeActivationResponse(ActivationStatus codeUnknown,
                                                              Long userId) {
        var lit = codeUnknown.errorLiteral();
        var userInfo = uService.getCasinoUser(userId);

        var lcode = LanguageCode.EN;
        var langCode = userInfo.getLangCode();
        try {
            lcode = LanguageCode.valueOf(langCode);
        } catch (Exception ignored) {
            //
        }

        var stringStringMap = locService.resolveTemplatesForLang("", lcode, lit);
        return new ActivateCodeResponse(codeUnknown.claimed(), stringStringMap.get(lit));
    }

    @Override
    @Transactional
    public List<AffiliateBonusCode> getBonusCodesForAffiliate(Integer affiliateId) {
        return bcRepo.findAllByAffiliateId(affiliateId).stream().map(BonuscodeUtil::getAffiliateBonusCode).toList();
    }

    @Override
    @Transactional
    public AffiliateBonusCode updateBonusCodeForAffiliate(Integer affiliateId, Integer bcId,
                                                          AffiliateBonusCode abc) {
        var one = bcRepo.findById(bcId).get();
        if (one.getAffiliateId().equals(affiliateId)) {
            BonuscodeUtil.updateModel(one, abc);
            bcRepo.save(one);
        }
        return BonuscodeUtil.getAffiliateBonusCode(one);
    }


    @Override
    @Transactional
    public boolean linkUserToBonuscode(long userId, Integer bcId) {
        if (bcId != null) {
            try {
                if (ubcRepo.findById(new UserBonuscodeId(userId, bcId)).isEmpty()) {
                    bcRepo.addUserToBonuscode(userId, bcId);
                    return true;
                }
            } catch (Exception e) {
                //
            }
        }
        return false;
    }

}

package com.ously.gamble.bonuscode.persistence.model;

import com.ously.gamble.api.bonuscode.BonusCodeType;
import com.ously.gamble.persistence.model.audit.DateAudit;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

@SuppressWarnings("unused")
@Entity
@Table(name = "bonuscodes", uniqueConstraints = {@UniqueConstraint(columnNames = {"bonuscode"}),

})
public class BonusCode extends DateAudit {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Version
	private Long version;

	@Column(name = "aff_id")
	private Integer affiliateId;

	@NotNull
	@Column(name = "bonuscode", length = 150)
	private String bonusCode;

	@Column(name = "type", length = 30)
	@NotNull
	@Enumerated(EnumType.STRING)
	private BonusCodeType type;

	@Column(name = "active")
	private boolean active = true;

	@Column(name = "valid_from")
	private LocalDateTime validFrom;

	@Column(name = "valid_until")
	private LocalDateTime validUntil;

	@Column(name = "maxusage")
	private int maxCount;

	@Column(name = "usedcount")
	private int usedCount;

	@Column(name = "pricedef", length = 50)
	private String priceDefinition;

	/**
	 * Userid as string when bound to a user! maxusage = 1, can only be taken by the user!!
	 * Usually handed out from support.
	 */
	@Column(name = "user_id")
	private Long userId;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public Integer getAffiliateId() {
		return affiliateId;
	}

	public void setAffiliateId(Integer affiliateId) {
		this.affiliateId = affiliateId;
	}

	public String getBonusCode() {
		return bonusCode;
	}

	public void setBonusCode(String bonusCode) {
		this.bonusCode = bonusCode;
	}

	public BonusCodeType getType() {
		return type;
	}

	public void setType(BonusCodeType type) {
		this.type = type;
	}

	public boolean isActive() {
		return active;
	}

	public void setActive(boolean active) {
		this.active = active;
	}

	public LocalDateTime getValidFrom() {
		return validFrom;
	}

	public void setValidFrom(LocalDateTime validFrom) {
		this.validFrom = validFrom;
	}

	public LocalDateTime getValidUntil() {
		return validUntil;
	}

	public void setValidUntil(LocalDateTime validUntil) {
		this.validUntil = validUntil;
	}

	public int getMaxCount() {
		return maxCount;
	}

	public void setMaxCount(int maxCount) {
		this.maxCount = maxCount;
	}

	public int getUsedCount() {
		return usedCount;
	}

	public void setUsedCount(int usedCount) {
		this.usedCount = usedCount;
	}

	public String getPriceDefinition() {
		return priceDefinition;
	}

	public void setPriceDefinition(String priceDefinition) {
		this.priceDefinition = priceDefinition;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
}

package com.ously.gamble.bonuscode.persistence.repository;


import com.ously.gamble.bonuscode.persistence.model.BonusCode;
import com.ously.gamble.bonuscode.persistence.model.QBonusCode;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.galegofer.spring.data.querydsl.value.operators.ExpressionProviderFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@SuppressWarnings({"NullableProblems", "unused"})

@Repository
@ConditionalOnBean(name = "affiliateConfiguration")
public interface BonusCodeRepository extends JpaRepository<BonusCode, Integer>,
        QuerydslPredicateExecutor<BonusCode>, QuerydslBinderCustomizer<QBonusCode> {

    List<BonusCode> findAllByAffiliateId(Integer affiliateId);

    Page<BonusCode> findAllByAffiliateId(Integer affiliateId, Pageable pageable);

    Optional<BonusCode> findByBonusCode(String bonusCode);

    @Modifying
    @Query(nativeQuery = true,
           value = "insert into user_bonuscode_link (user_id,bonuscode_id) values (:userId,:bonuscodeId)")
    int addUserToBonuscode(@Param("userId") long userId, @Param("bonuscodeId") int bonuscodeId);

    @Override
    default void customize(QuerydslBindings bindings, QBonusCode root) {
        bindings.bind(String.class).first(
                (SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
        bindings.bind(String.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Long.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Integer.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Double.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Boolean.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Instant.class).all(ExpressionProviderFactory::getPredicate);

    }
}
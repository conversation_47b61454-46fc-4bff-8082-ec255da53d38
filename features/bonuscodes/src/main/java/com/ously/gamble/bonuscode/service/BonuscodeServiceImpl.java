package com.ously.gamble.bonuscode.service;

import com.ously.gamble.api.achievements.Reward;
import com.ously.gamble.api.bonuscode.BonuscodeService;
import com.ously.gamble.api.bonuscode.CasinoBonusCode;
import com.ously.gamble.bonuscode.persistence.model.BonusCode;
import com.ously.gamble.bonuscode.persistence.repository.BonusCodeRepository;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.querydsl.core.types.Predicate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@ConditionalOnOffloader
@Service
public class BonuscodeServiceImpl implements BonuscodeService {

    private static final Logger log = LoggerFactory.getLogger(BonuscodeServiceImpl.class);

    private final BonusCodeRepository bcRepo;

    BonuscodeServiceImpl(BonusCodeRepository bcRep) {
        this.bcRepo = bcRep;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<CasinoBonusCode> getBonuscode(int id) {
        return bcRepo.findById(id).map(BonuscodeUtil::getCasinoBonusCode);
    }

    @Override
    @Transactional
    public Optional<CasinoBonusCode> createBonuscode(CasinoBonusCode cbc) {
        var bcNew = new BonusCode();
        if (checkRewardsDefinition(cbc.getPriceDefinition())) {
            return Optional.empty();
        }
        BonuscodeUtil.updateBonusCode(bcNew, cbc);
        return Optional.of(BonuscodeUtil.getCasinoBonusCode(bcRepo.saveAndFlush(bcNew)));
    }

    @Override
    @Transactional
    public Optional<CasinoBonusCode> updateBonuscode(CasinoBonusCode cbc) {
        if (checkRewardsDefinition(cbc.getPriceDefinition())) {
            return Optional.empty();
        }
        var byId = bcRepo.findById(cbc.getId());
        return byId.map(a -> {
            BonuscodeUtil.updateBonusCode(a, cbc);
            return BonuscodeUtil.getCasinoBonusCode(bcRepo.saveAndFlush(a));
        });
    }

    private static boolean checkRewardsDefinition(String priceDefinition) {
        try {
            Reward.createRewardsFromDefinition(priceDefinition);
            return false;
        } catch (Exception e) {
            log.warn("Malformed rewards definition: '{}'", priceDefinition);
        }
        return true;
    }

    @Override
    @Transactional
    public void deleteBonuscode(int id) {
        bcRepo.deleteById(id);
    }

    @Override
    @Transactional
    public Optional<CasinoBonusCode> activate(int id) {
        return bcRepo.findById(id).map(a -> {
            a.setActive(true);
            bcRepo.saveAndFlush(a);
            return BonuscodeUtil.getCasinoBonusCode(a);
        });
    }

    @Override
    @Transactional
    public Optional<CasinoBonusCode> deactivate(int id) {
        return bcRepo.findById(id).map(a -> {
            a.setActive(false);
            bcRepo.saveAndFlush(a);
            return BonuscodeUtil.getCasinoBonusCode(a);
        });
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CasinoBonusCode> getAllBonuscodesForPredicate(Predicate predicate, Pageable page) {
        var all = bcRepo.findAll(predicate, page);
        return new PageImpl<>(all.getContent().stream().map(BonuscodeUtil::getCasinoBonusCode).toList(), page, all.getTotalElements());
    }
}

package com.ously.gamble.bonuscode.service;

import com.ously.gamble.api.bonuscode.AffiliateBonusCode;
import com.ously.gamble.api.bonuscode.CasinoBonusCode;
import com.ously.gamble.bonuscode.persistence.model.BonusCode;
import com.ously.gamble.payload.TxPrice;

public final class BonuscodeUtil {

    private BonuscodeUtil() {
    }

    public static CasinoBonusCode getCasinoBonusCode(BonusCode bc) {
        CasinoBonusCode cbc = new CasinoBonusCode();
        cbc.setId(bc.getId());
        cbc.setActive(bc.isActive());
        cbc.setBonusCode(bc.getBonusCode());
        cbc.setMaxCount(bc.getMaxCount());
        cbc.setUsedCount(bc.getUsedCount());
        cbc.setPriceDefinition(bc.getPriceDefinition());
        cbc.setType(bc.getType());
        cbc.setValidFrom(bc.getValidFrom());
        cbc.setValidUntil(bc.getValidUntil());
        cbc.setUserId(bc.getUserId());
        cbc.setCreatedAt(bc.getCreatedAt());
        cbc.setUpdatedAt(bc.getUpdatedAt());

        return cbc;
    }

    public static void updateBonusCode(BonusCode bc, CasinoBonusCode cbc) {
        bc.setActive(cbc.isActive());
        bc.setBonusCode(cbc.getBonusCode());
        bc.setAffiliateId(2);
        bc.setMaxCount(cbc.getMaxCount());
        bc.setValidFrom(cbc.getValidFrom());
        bc.setValidUntil(cbc.getValidUntil());
        bc.setPriceDefinition(cbc.getPriceDefinition());
        bc.setType(cbc.getType());
        bc.setUserId(cbc.getUserId());
        if (bc.getId() == null) {
            bc.setUsedCount(0);
        }
    }


    public static AffiliateBonusCode getAffiliateBonusCode(BonusCode bc) {
        var abs = new AffiliateBonusCode();
        abs.setActive(bc.isActive());
        abs.setAffiliateId(bc.getAffiliateId());
        abs.setBonusCode(bc.getBonusCode());
        abs.setId(bc.getId());
        abs.setMaxCount(bc.getMaxCount());
        abs.setUsedCount(bc.getUsedCount());
        abs.setValidFrom(bc.getValidFrom());
        abs.setValidUntil(bc.getValidUntil());
        abs.setType(bc.getType());
        abs.setUserId(bc.getUserId());
        abs.setRewards(TxPrice.parsePriceDef(bc.getPriceDefinition()));
        return abs;
    }

    public static void updateModel(BonusCode bc, AffiliateBonusCode abc) {
        bc.setActive(abc.isActive());
        bc.setAffiliateId(abc.getAffiliateId());
        bc.setBonusCode(abc.getBonusCode());
        bc.setMaxCount(abc.getMaxCount());
        bc.setUsedCount(abc.getUsedCount());
        bc.setPriceDefinition(TxPrice.createStringRepresantation(abc.getRewards()));
        bc.setUserId(abc.getUserId());
        bc.setValidFrom(abc.getValidFrom());
        bc.setValidUntil(abc.getValidUntil());
        bc.setType(abc.getType());
    }

}

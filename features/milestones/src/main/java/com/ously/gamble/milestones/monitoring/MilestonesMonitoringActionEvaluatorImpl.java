package com.ously.gamble.milestones.monitoring;

import com.ously.gamble.api.monitoring.MonitoredAction;
import com.ously.gamble.api.monitoring.MonitoredItemsBatchEvent;
import com.ously.gamble.api.monitoring.MonitoringSender;
import com.ously.gamble.conditions.ConditionalOnMonitor;
import com.ously.gamble.milestones.api.MilestoneCacheService;
import com.ously.gamble.milestones.api.MilestoneUpdateItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@ConditionalOnMonitor
@ConditionalOnProperty(prefix = "monitor.milestones", name = "action", havingValue = "true")
public class MilestonesMonitoringActionEvaluatorImpl {

    private final static int MAX_EVAL_BATCH_SIZE = 200;

    private final static Logger log = LoggerFactory.getLogger(MilestonesMonitoringActionEvaluatorImpl.class);


    private final MilestoneCacheService milestoneCache;
    private final MonitoringSender<MilestoneUpdateItem> msupdSender;

    public MilestonesMonitoringActionEvaluatorImpl(MilestoneCacheService milestoneCache,
                                                   MonitoringSender<MilestoneUpdateItem> msupdSender) {
        this.milestoneCache = milestoneCache;
        this.msupdSender = msupdSender;
    }


    @EventListener
    public void handleTx(MonitoredItemsBatchEvent<MonitoredAction> txItems) {

        List<MonitoredAction> items = txItems.getItems();
        log.debug("Got {} action events for milestones eval", items.size());
        List<MilestoneUpdateItem> updates = new ArrayList<>(items.size() * 5);
        int curpos = 0;
        int remItemCount = items.size();
        int itemCount = items.size();

        // process incoming batch smaller batches as defined in MAX_EVAL_BATCH_SIZE

        while (curpos < itemCount) {
            List<MonitoredAction> monitoredGameTxes = items.subList(curpos, curpos + Math.min(remItemCount, MAX_EVAL_BATCH_SIZE));
            curpos += monitoredGameTxes.size();
            remItemCount -= monitoredGameTxes.size();
            evalSubBatch(monitoredGameTxes, updates);
        }

        if (!updates.isEmpty()) {
            MilestoneUpdateItem[] optimized = MilestoneUpdateItem.optimize(updates);
            for (MilestoneUpdateItem it : optimized) {
                msupdSender.addItem(it);
            }
            log.debug("Got {} (opt: {}) updates from batch with {} tx events", updates.size(), optimized.length, items.size());
        }
    }

    private void evalSubBatch(List<MonitoredAction> monitoredGameTxes, List<MilestoneUpdateItem> updates) {
        prewarmUserMilestonesCache(monitoredGameTxes);
        for (MonitoredAction tx : monitoredGameTxes) {
            List<MilestoneUpdateItem> milestoneUpdateItems = milestoneCache.getUserMilestones(tx.userId()).evalAction(tx);
            if (milestoneUpdateItems != null && !milestoneUpdateItems.isEmpty()) {
                updates.addAll(milestoneUpdateItems);
            }
        }
    }

    private void prewarmUserMilestonesCache(List<MonitoredAction> monitoredGameTxes) {
        Set<Long> uids = monitoredGameTxes.stream().map(MonitoredAction::userId).collect(Collectors.toSet());
        milestoneCache.getUserMilestoneMappingsMulti(uids);
    }

}

package com.ously.gamble.milestones;

import com.ously.gamble.api.features.AbstractPlatformFeature;
import com.ously.gamble.api.features.FeatureDescription;
import com.ously.gamble.api.features.PlatformFeature;
import com.ously.gamble.conditions.ConditionalOnSocial;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnSocial
public class MilestonesFeature extends AbstractPlatformFeature implements PlatformFeature {
    @Override
    public FeatureDescription getDescription() {
        return new FeatureDescription("Achievements", "static and temporary achievment ladders - Social only");
    }

}

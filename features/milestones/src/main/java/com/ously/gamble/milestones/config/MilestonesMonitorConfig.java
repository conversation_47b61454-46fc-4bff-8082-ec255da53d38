package com.ously.gamble.milestones.config;

import com.ously.gamble.api.monitoring.MonitoringReceiver;
import com.ously.gamble.api.monitoring.MonitoringSender;
import com.ously.gamble.conditions.ConditionalOnMonitor;
import com.ously.gamble.milestones.api.MilestoneUpdateItem;
import com.ously.gamble.milestones.codec.MilestoneUpdateItemCodec;
import com.ously.gamble.monitoring.endpoints.MonitoringCollectorImpl;
import com.ously.gamble.monitoring.endpoints.MonitoringReceiverImpl;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;


@Configuration
@ConditionalOnProperty(prefix = "monitor.milestones", name = "enabled", havingValue = "true")
@ConditionalOnMonitor
public class MilestonesMonitorConfig {

    @Bean(destroyMethod = "shutdown")
    public MonitoringSender<MilestoneUpdateItem> msupdSender(
            MilestoneConfiguration mConfig,
            RedissonClient rClient,
            ThreadPoolTaskScheduler tpTS) {
        return new MonitoringCollectorImpl<>(mConfig.getTxQueueName(), rClient, tpTS, MilestoneUpdateItemCodec.INSTANCE);
    }

    @Bean(destroyMethod = "shutdown")
    public MonitoringReceiver<MilestoneUpdateItem> msupdReceiver(
            MilestoneConfiguration mConfig,
            RedissonClient rClient,
            ThreadPoolTaskScheduler tpTS,
            ApplicationEventPublisher eventPublisher) {
        return new MonitoringReceiverImpl<>(mConfig.getTxQueueName(), rClient, tpTS, MilestoneUpdateItemCodec.INSTANCE, eventPublisher);
    }


}

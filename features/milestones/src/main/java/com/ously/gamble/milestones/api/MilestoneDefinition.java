package com.ously.gamble.milestones.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

public class MilestoneDefinition {

    private static final ObjectMapper om;

    static {
        om = new ObjectMapper().findAndRegisterModules();
    }

    private static final long[] EMPTY_SCORES = new long[0];
    private static final int[] EMPTY_REWARDS = new int[0];
    private static final String[] EMPTY_REWARDTOKENS = new String[0];

    MilestoneType type;

    long minBet;
    @JsonProperty(defaultValue = "-1")
    long maxBet = -1;
    long minWin;
    short vendorId;
    short gameId;
    short betlevel;
    @JsonProperty(defaultValue = "-1")
    short maxBetlevel = -1;

    short stages;

    long[] scores=EMPTY_SCORES;
    int[] rewards=EMPTY_REWARDS;
    String[] rewardTokens = EMPTY_REWARDTOKENS;


    public MilestoneDefinition(){}

    public static MilestoneDefinition from(String defs) {
        try{
            return om.readValue(defs, MilestoneDefinition.class);
        } catch (Exception ignore) {
            // e.printStackTrace();
        }
        return null;
    }

    public static String asString(MilestoneDefinition def){
        try {
            return om.writeValueAsString(def);
        } catch (JsonProcessingException e) {
            return "{}";
        }
    }


    public long getMaxBet() {
        return maxBet;
    }

    public void setMaxBet(long maxBet) {
        this.maxBet = maxBet;
    }

    public short getMaxBetlevel() {
        return maxBetlevel;
    }

    public void setMaxBetlevel(short maxBetlevel) {
        this.maxBetlevel = maxBetlevel;
    }

    public long getMinBet() {
        return minBet;
    }

    public void setMinBet(long minBet) {
        this.minBet = minBet;
    }

    public long getMinWin() {
        return minWin;
    }

    public void setMinWin(long minWin) {
        this.minWin = minWin;
    }

    public short getVendorId() {
        return vendorId;
    }

    public void setVendorId(short vendorId) {
        this.vendorId = vendorId;
    }

    public short getGameId() {
        return gameId;
    }

    public void setGameId(short gameId) {
        this.gameId = gameId;
    }

    public MilestoneType getType() {
        return type;
    }

    public void setType(MilestoneType type) {
        this.type = type;
    }

    public short getBetlevel() {
        return betlevel;
    }

    public void setBetlevel(short betlevel) {
        this.betlevel = betlevel;
    }

    public short getStages() {
        return stages;
    }

    public void setStages(short stages) {
        this.stages = stages;
    }

    public long[] getScores() {
        if(scores==null){
            return EMPTY_SCORES;
        }
        return scores;
    }

    public void setScores(long[] scores) {
        this.scores = scores;
    }

    public int[] getRewards() {
        if(rewards==null){
            return EMPTY_REWARDS;
        }
        return rewards;
    }

    public String[] getRewardTokens() {
        if (rewards == null) {
            return EMPTY_REWARDTOKENS;
        }
        return rewardTokens;
    }

    public void setRewards(int[] rewards) {
        this.rewards = rewards;
    }

    public void setRewardTokens(String[] rewardTokens) {
        this.rewardTokens = rewardTokens;
    }
}

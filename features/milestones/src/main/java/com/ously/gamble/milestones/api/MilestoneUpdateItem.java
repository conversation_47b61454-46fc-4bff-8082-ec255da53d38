package com.ously.gamble.milestones.api;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public final class MilestoneUpdateItem implements Serializable,Comparable<MilestoneUpdateItem> {
    private  long userId;
    private  long milestoneId;
    private  long scoreIncrement;
    private  long targetScore;
    private  int stage;
    private  int stages;

    public MilestoneUpdateItem(){}

    public MilestoneUpdateItem(long userId, long milestoneId,  long scoreIncrement, long targetScore,
        int stage, int stages
    ) {
        this.userId = userId;
        this.milestoneId=milestoneId;
        this.scoreIncrement=scoreIncrement;
        this.targetScore = targetScore;
        this.stage = stage;
        this.stages=stages;
    }

    public static MilestoneUpdateItem[] optimize(List<MilestoneUpdateItem> updates) {
        // group by userId:milestoneId
        Map<String,MilestoneUpdateItem> resMap=new HashMap<>(100);
        for(MilestoneUpdateItem mui:updates){
            String key = mui.userId+":"+ mui.milestoneId;
            if(resMap.containsKey(key)){
                MilestoneUpdateItem oldItem = resMap.get(key);
                resMap.replace(key,new MilestoneUpdateItem(
                        oldItem.userId,
                        oldItem.milestoneId,
                        oldItem.scoreIncrement+mui.getScoreIncrement(),
                        Math.max(oldItem.targetScore,mui.targetScore) ,
                        Math.max(oldItem.getStage(), mui.stage),
                        Math.max(oldItem.getStages(), mui.stages)));
            }else{
                resMap.put(key,mui);
            }
        }
        MilestoneUpdateItem[] result = new MilestoneUpdateItem[resMap.size()];
        int i=0;
        for(MilestoneUpdateItem ni:resMap.values()){
            result[i]=ni;
            i++;
        }
        Arrays.sort(result);
        return result;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public long getMilestoneId() {
        return milestoneId;
    }

    public void setMilestoneId(long milestoneId) {
        this.milestoneId = milestoneId;
    }

    public long getScoreIncrement() {
        return scoreIncrement;
    }

    public void setScoreIncrement(long scoreIncrement) {
        this.scoreIncrement = scoreIncrement;
    }

    public long getTargetScore() {
        return targetScore;
    }

    public void setTargetScore(long targetScore) {
        this.targetScore = targetScore;
    }

    public int getStage() {
        return stage;
    }

    public void setStage(int stage) {
        this.stage = stage;
    }

    public int getStages() {
        return stages;
    }

    public void setStages(int stages) {
        this.stages = stages;
    }

    @Override
    public int compareTo(MilestoneUpdateItem o) {
        if(this.userId != o.userId) {
            return (int)(this.userId - o.userId);
        }

        if( this.milestoneId != o.milestoneId){
            return (int)(this.milestoneId - o.milestoneId);
        }

        return 0;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        MilestoneUpdateItem that = (MilestoneUpdateItem) o;

        if (userId != that.userId) return false;
        if (milestoneId != that.milestoneId) return false;
        if (scoreIncrement != that.scoreIncrement) return false;
        if (targetScore != that.targetScore) return false;
        if (stage != that.stage) return false;
        return stages == that.stages;
    }

    @Override
    public int hashCode() {
        int result = Long.hashCode(userId);
        result = 31 * result + Long.hashCode(milestoneId);
        result = 31 * result + Long.hashCode(scoreIncrement);
        result = 31 * result + Long.hashCode(targetScore);
        result = 31 * result + stage;
        result = 31 * result + stages;
        return result;
    }
}

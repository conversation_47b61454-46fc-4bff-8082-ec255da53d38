package com.ously.gamble.milestones.controller;

import com.ously.gamble.api.security.CurrentUser;
import com.ously.gamble.api.security.UserPrincipal;
import com.ously.gamble.conditions.ConditionalOnBackend;
import com.ously.gamble.milestones.api.UserMilestoneClaimStatus;
import com.ously.gamble.milestones.api.UserMilestoneService;
import com.ously.gamble.milestones.api.UserMilestoneStatus;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController
@ConditionalOnBackend
public class MilestoneController {

    private final UserMilestoneService userMilestoneService;

    public MilestoneController(@Autowired(required = false) UserMilestoneService userMilestoneService) {
        this.userMilestoneService = userMilestoneService;
    }

    //
    // get Users current Milestones
    //
    @Operation(description = "user milestones retrieval",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/api/app/milestones")
    @RolesAllowed("USER")
    public ResponseEntity<UserMilestoneStatus> getMilestoneStatus(@CurrentUser UserPrincipal currentUser) {
        if (userMilestoneService == null) {
            return ResponseEntity.of(Optional.empty());
        }
        return ResponseEntity.ok(userMilestoneService.getUserMilestones(currentUser.getId()));
    }

    //
    // claim stageRewards for user milestones
    //
    @Operation(description = "user milestones claim",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping(value = "/api/app/milestones/{milestoneId}")
    @RolesAllowed("USER")
    public ResponseEntity<UserMilestoneClaimStatus> claimMilestoneStageReward(@CurrentUser UserPrincipal currentUser, @PathVariable("milestoneId") long milestoneId) {
        if (userMilestoneService == null) {
            return ResponseEntity.of(Optional.empty());
        }
        return ResponseEntity.ok(userMilestoneService.claimStageRewards(currentUser.getId(), milestoneId));
    }


}

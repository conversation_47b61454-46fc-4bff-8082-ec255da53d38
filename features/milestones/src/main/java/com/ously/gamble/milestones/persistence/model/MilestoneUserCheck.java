package com.ously.gamble.milestones.persistence.model;

import jakarta.persistence.*;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;

/**
 * Entity which defines the users progress for a milestone
 */
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "milestone_check")
@IdClass(MilestoneUserProgressId.class)
public class MilestoneUserCheck implements Persistable<MilestoneUserProgressId> {

    @Transient
    boolean wasLoaded;

    @Id
    @Column(name = "user_id", nullable = false)
    long userId;

    @Id
    @Column(name = "milestone_id", nullable = false)
    long milestoneId;

    @Column(name="updated_at")
    Instant updatedAt;

    @PostLoad
    @PostPersist
    public void setTransientLoaded() {
        this.wasLoaded = true;
    }

    @Override
    public MilestoneUserProgressId getId() {
        return new MilestoneUserProgressId(userId, milestoneId);
    }

    @Override
    public boolean isNew() {
        return !wasLoaded;
    }


    public long getUserId() {
        return userId;
    }

    public void setUserId(long user_id) {
        this.userId = user_id;
    }

    public long getMilestoneId() {
        return milestoneId;
    }

    public void setMilestoneId(long milestone_id) {
        this.milestoneId = milestone_id;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }
}

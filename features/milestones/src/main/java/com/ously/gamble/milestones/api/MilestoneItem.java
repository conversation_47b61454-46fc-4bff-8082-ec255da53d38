package com.ously.gamble.milestones.api;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ously.gamble.api.monitoring.MonitoredAction;
import com.ously.gamble.api.monitoring.MonitoredActionType;
import com.ously.gamble.api.monitoring.MonitoredGameTx;
import com.ously.gamble.milestones.persistence.model.Milestone;
import com.ously.gamble.persistence.model.TransactionType;

import java.time.Instant;

/**
 * internal repr. of a milestone
 * TODO: convert to record, eval functional
 */
public class MilestoneItem {
    final long milestoneId;
    final int validFromEpochSeconds;
    final int validToEpochSeconds;
    final int stages;
    final MilestoneType type;

    @JsonProperty("scores")
    float[] scoresf;
    @JsonIgnore
    final
    long[] scores;
    final int[] rewards;
    final String[] tokens;

    // Filter attributes

    final int vendorId;
    int gameId;
    final long minBet;
    final long maxBet;
    final long minWin;
    final int minBetlevel;
    final int maxBetlevel;

    TxEval evalMethod;
    ActionEval actionEvalMethod;

    public MilestoneItem(long msId, Instant validFrom, Instant validTo,int stages,
                         MilestoneDefinition defs){
        this.milestoneId=msId;
        this.validFromEpochSeconds=(int)validFrom.getEpochSecond();
        this.validToEpochSeconds=(int)validTo.getEpochSecond();
        this.stages=stages;

        this.type=defs.getType();

        // set evalMethod reference
        selectEvalReference();
        selectActionEvalReference();

        this.vendorId = defs.getVendorId();
        this.minBet = defs.getMinBet();
        this.maxBet = defs.getMaxBet();
        this.minBetlevel = defs.getBetlevel();
        this.maxBetlevel = defs.getMaxBetlevel();
        this.minWin = defs.getMinWin();
        this.scoresf = scoresToFloat(defs.getScores(), defs.getType());
        this.scores = defs.getScores();
        this.rewards = defs.getRewards();
        this.tokens = defs.getRewardTokens();
    }

    public MilestoneItem(MilestoneDto updMs) {
        this.milestoneId = updMs.milestoneId();
        this.validFromEpochSeconds = (int) updMs.validFrom().getEpochSecond();
        this.validToEpochSeconds = (int) updMs.validTo().getEpochSecond();
        this.type = updMs.type();
        this.stages = updMs.stages();
        this.scoresf = updMs.scores();
        this.scores = scoresToLong(updMs.scores(), updMs.type());
        this.rewards = updMs.rewards();
        this.tokens = updMs.tokens();
        this.vendorId = updMs.vendorId();
        this.gameId = updMs.gameId();
        this.minBet = (updMs.minBet() < 0) ? -1L : (long) (updMs.minBet() * 100);
        this.maxBet = (updMs.maxBet() < 0) ? -1L : (long) (updMs.maxBet() * 100);
        this.minWin = (updMs.minWin() < 0) ? -1L : (long) (updMs.minWin() * 100);
        this.minBetlevel = updMs.minBetlevel();
        this.maxBetlevel = updMs.maxBetlevel();
    }

    private long[] scoresToLong(float[] scores, MilestoneType type) {

        double factor = switch (type) {
            case SUM_BETS, SUM_WINS, E_SUM_BETS, E_SUM_WINS -> 100;
            default -> 1;
        };

        var nl = new long[scores.length];
        for (int i = 0; i < scores.length; i++) {
            nl[i] = (long) (scores[i] * factor);
        }
        return nl;
    }

    private float[] scoresToFloat(long[] scores, MilestoneType type) {

        double factor = switch (type) {
            case SUM_BETS, SUM_WINS, E_SUM_BETS, E_SUM_WINS -> 100;
            default -> 1;
        };

        var nl = new float[scores.length];
        for (int i = 0; i < scores.length; i++) {
            nl[i] = (float) (scores[i] / factor);
        }
        return nl;
    }


    private void selectEvalReference() {
        this.evalMethod = switch (this.type) {
            case SUM_BETS, E_SUM_BETS -> this::checkAllBets;
            case SUM_WINS, E_SUM_WINS -> this::checkAllWins;
            case NUM_ROUNDS, E_NUM_ROUNDS -> this::checkAllRounds;
            case NUM_WINS, E_NUM_WINS -> this::checkNumWins;
            default -> null;
        };
    }

    private void selectActionEvalReference() {
        this.actionEvalMethod = switch (this.type) {
            case SUM_DIAMONDS_USED, E_SUM_DIAMONDS_USED -> this::checkDiamondsUsed;
            case NUM_ADS_VIEWED, E_NUM_ADS_VIEWED -> this::checkAdsViewed;
            case NUM_WHEELSPINS, E_NUM_WHEELSPINS -> this::checkWheelspinNum;
            case NUM_BOOSTER_USED, E_NUM_BOOSTER_USED -> this::checkBoosterUsed;
            case NUM_UNLOCKED_GAME, E_NUM_UNLOCKED_GAME -> this::checkUnlockedGame;
            default -> null;
        };
    }

    public MilestoneItem(Milestone ms) {
        this.milestoneId = ms.getId();
        this.validFromEpochSeconds = (int) ms.getValidFrom().getEpochSecond();
        this.validToEpochSeconds = (int) ms.getValidTo().getEpochSecond();
        this.stages = ms.getStages();
        MilestoneDefinition defs = ms.getMilestoneDefinition();
        this.type = defs.getType();
        this.vendorId = defs.getVendorId();
        this.gameId = defs.getGameId();
        this.minBet = defs.getMinBet();
        this.maxBet = defs.getMaxBet();
        this.minBetlevel = defs.getBetlevel();
        this.maxBetlevel = defs.getMaxBetlevel();
        this.minWin = defs.getMinWin();
        this.scores = defs.getScores();
        this.scoresf = scoresToFloat(defs.getScores(), defs.getType());
        this.rewards = defs.getRewards();
        this.tokens = defs.getRewardTokens();
        // set evalMethod reference
        selectEvalReference();
        selectActionEvalReference();
    }


    public MilestoneUpdateItem evalAction(MonitoredAction tx) {
        if (tx.epochSecondsUTC() >= validFromEpochSeconds && tx.epochSecondsUTC() <= validToEpochSeconds && actionEvalMethod != null) {
            return actionEvalMethod.check(tx);
        }
        return null;
    }


    public MilestoneUpdateItem evalTx(MonitoredGameTx tx) {
        if (tx.epochSecondsUTC() >= validFromEpochSeconds && tx.epochSecondsUTC() <= validToEpochSeconds && evalMethod != null) {
            return evalMethod.check(tx);
        }
        return null;
    }

    private MilestoneUpdateItem checkDiamondsUsed(MonitoredAction tx) {
        if (tx.type() != MonitoredActionType.DIAMONDS_USED) {
            return null;
        }
        if (tx.value() == 0) {
            return null;
        }
        //  userId, milestoneId, scoreIncrement, targetScore, stage, stages
        return new MilestoneUpdateItem(tx.userId(), milestoneId, tx.value(), scores[0], 0, stages);
    }

    private MilestoneUpdateItem checkWheelspinNum(MonitoredAction tx) {
        if (tx.type() != MonitoredActionType.WHEELSPIN) {
            return null;
        }
        if (tx.value() == 0) {
            return null;
        }
        //  userId, milestoneId, scoreIncrement, targetScore, stage, stages
        return new MilestoneUpdateItem(tx.userId(), milestoneId, tx.value(), scores[0], 0, stages);
    }

    private MilestoneUpdateItem checkAdsViewed(MonitoredAction tx) {
        if (tx.type() != MonitoredActionType.AD_VIEWED) {
            return null;
        }
        if (tx.value() == 0) {
            return null;
        }
        //  userId, milestoneId, scoreIncrement, targetScore, stage, stages
        return new MilestoneUpdateItem(tx.userId(), milestoneId, tx.value(), scores[0], 0, stages);
    }


    private MilestoneUpdateItem checkBoosterUsed(MonitoredAction tx) {
        if (tx.type() != MonitoredActionType.BOOSTER_USED) {
            return null;
        }

        if (tx.value() == 0) {
            return null;
        }
        return new MilestoneUpdateItem(tx.userId(), milestoneId, tx.value(), scores[0], 0, stages);
    }

    private MilestoneUpdateItem checkUnlockedGame(MonitoredAction tx) {
        if (tx.type() != MonitoredActionType.UNLOCKED_GAME) {
            return null;
        }
        if (tx.value() == 0) {
            return null;
        }
        return new MilestoneUpdateItem(tx.userId(), milestoneId, tx.value(), scores[0], 0, stages);
    }


    private MilestoneUpdateItem checkAllBets(MonitoredGameTx tx) {
        if (tx.bet() == 0) {
            return null;
        }
        if (tx.inBonus()) {
            return null;
        }
        if (vendorId > 0 && tx.vendorId() != vendorId) {
            return null;
        }
        if (gameId > 0 && tx.gameId() != gameId) {
            return null;
        }


        if(tx.bet()<minBet){
            return null;
        }

        if (maxBet > 0 && tx.bet() > maxBet) {
            return null;
        }


        if(tx.betlevel()<minBetlevel){
            return null;
        }
        if (maxBetlevel >= 0 && tx.betlevel() > maxBetlevel) {
            return null;
        }


        //  userId, milestoneId, scoreIncrement, targetScore, stage, stages
        return new MilestoneUpdateItem(tx.userId(),milestoneId,tx.bet(),scores[0], 0, stages);

    }
    private MilestoneUpdateItem checkAllWins(MonitoredGameTx tx) {
        if(tx.win() == 0){
            return null;
        }
        if(tx.inBonus()){
            return null;
        }
        if(vendorId>0 && tx.vendorId() != vendorId){
            return null;
        }

        if(gameId>0 && tx.gameId() != gameId){
            return null;
        }

        if(tx.win()<minWin){
            return null;
        }
        if (tx.betlevel() < minBetlevel) {
            return null;
        }
        if (maxBetlevel >= 0 && tx.betlevel() > maxBetlevel) {
            return null;
        }


        //  userId, milestoneId, scoreIncrement, targetScore, stage, stages
        return new MilestoneUpdateItem(tx.userId(), milestoneId, tx.win(), scores[0], 0, stages);

    }


    private MilestoneUpdateItem checkNumWins(MonitoredGameTx tx) {
        if (tx.type() != TransactionType.WIN && tx.type() != TransactionType.DIRECTWIN) {
            return null;
        }
        if (tx.win() == 0) {
            return null;
        }
        if (tx.inBonus()) {
            return null;
        }
        if (vendorId > 0 && tx.vendorId() != vendorId) {
            return null;
        }
        if (gameId > 0 && tx.gameId() != gameId) {
            return null;
        }
        if (tx.betlevel() < minBetlevel) {
            return null;
        }
        if (maxBetlevel >= 0 && tx.betlevel() > maxBetlevel) {
            return null;
        }

        //  userId, milestoneId, scoreIncrement, targetScore, stage, stages
        return new MilestoneUpdateItem(tx.userId(), milestoneId, 1, scores[0], 0, stages);
    }


    private MilestoneUpdateItem checkAllRounds(MonitoredGameTx tx) {
        if (tx.type() != TransactionType.BET && tx.type() != TransactionType.DIRECTWIN) {
            return null;
        }
        if (tx.bet() == 0) {
            return null;
        }
        if (tx.inBonus()) {
            return null;
        }

        if (tx.bet() < minBet) {
            return null;
        }

        if (maxBet > 0 && tx.bet() > maxBet) {
            return null;
        }

        if(vendorId>0 && tx.vendorId() != vendorId){
            return null;
        }
        if(gameId>0 && tx.gameId() != gameId){
            return null;
        }
        if(tx.betlevel()<minBetlevel){
            return null;
        }
        if (maxBetlevel >= 0 && tx.betlevel() > maxBetlevel) {
            return null;
        }

        //  userId, milestoneId, scoreIncrement, targetScore, stage, stages
        return new MilestoneUpdateItem(tx.userId(),milestoneId,1,scores[0], 0, stages);
    }

    public long getMilestoneId() {
        return milestoneId;
    }

    public int getValidFromEpochSeconds() {
        return validFromEpochSeconds;
    }

    public int getValidToEpochSeconds() {
        return validToEpochSeconds;
    }

    public int getStages() {
        return stages;
    }

    public MilestoneType getType() {
        return type;
    }

    public long[] getScores() {
        return scores;
    }

    public int[] getRewards() {
        return rewards;
    }

    public String[] getRewardTokens() {
        return tokens;
    }


    public int getVendorId() {
        return vendorId;
    }

    public long getMinBet() {
        return minBet;
    }

    public long getMinWin() {
        return minWin;
    }

    public int getMinBetlevel() {
        return minBetlevel;
    }

    public int getGameId() {
        return gameId;
    }

    public long getMaxBet() {
        return maxBet;
    }

    public int getMaxBetlevel() {
        return maxBetlevel;
    }

    public float[] getScoresf() {
        return scoresf;
    }
}

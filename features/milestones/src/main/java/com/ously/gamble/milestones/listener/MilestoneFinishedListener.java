package com.ously.gamble.milestones.listener;

import com.ously.gamble.api.cache.CachedMap;
import com.ously.gamble.api.notification.Message;
import com.ously.gamble.api.notification.NotificationSendService;
import com.ously.gamble.api.notification.NotificationService;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.milestones.api.MilestoneFinishedEvent;
import com.ously.gamble.milestones.api.UserMilestoneService;
import com.ously.gamble.milestones.api.UserMilestoneStatus;
import com.ously.gamble.persistence.model.NotificationType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;

@Component
@ConditionalOnOffloader
@ConditionalOnProperty(prefix = "monitor.milestones", name = "enabled", havingValue = "true")
public class MilestoneFinishedListener {
    public static final String MILESTONE_FINISHED_QUEUE = "milestones.finished";
    final Logger log = LoggerFactory.getLogger(getClass());

    private final CachedMap<Long, UserMilestoneStatus> umc;
    private final UserMilestoneService umsService;
    private final NotificationSendService notSndSrv;
    private final NotificationService notificationService;

    public MilestoneFinishedListener(CachedMap<Long, UserMilestoneStatus> userMilestoneCache,
                                     UserMilestoneService umsSrv,
                                     NotificationSendService notSndSrv,
                                     NotificationService notificationService) {
        this.umc = userMilestoneCache;
        this.umsService = umsSrv;
        this.notSndSrv = notSndSrv;
        this.notificationService = notificationService;
        log.info("Started MilestoneFinishedListener");
    }

    @RabbitListener(queues = MILESTONE_FINISHED_QUEUE, containerFactory =
            "directRabbitListenerContainerFactory",
            concurrency = "1")
    @Transactional
    public void handleMilestoneFinishedEvent(MilestoneFinishedEvent evnt) {
        try {
            umc.removeFast(evnt.userId());
            UserMilestoneStatus userMilestones = umsService.getUserMilestones(evnt.userId());
            sendNotification(evnt);
        } catch (Exception e) {
            log.error("Error expire entry for user {} in userMilestoneCache", evnt.userId(), e);
            throw new RuntimeException(e);
        }
    }

    private void sendNotification(MilestoneFinishedEvent evnt) {
        var fcm = notificationService.getCurrentFCM(evnt.userId());
        if (fcm != null && fcm.trim().length() > 20 && !fcm.startsWith("DEDUPL-")) {
            log.debug("Sending refresh milestone push for userId {} and fcm {}", evnt.userId(), fcm);

            var message = new Message();
            message.setuId(evnt.userId());
            message.setType(NotificationType.MOBILE_PUSH);
            message.setSubject("stage done");
            message.setMsg(" ");

            HashMap<String, String> config = new HashMap<>();
            config.put("action", "refresh");
            config.put("feature", "milestone");

            message.setProperties(config);
            message.setDestination(fcm);
            notSndSrv.sendNotification(message);
        }
    }

}

package com.ously.gamble.milestones.api;

import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public interface MilestoneManagementService {

    void addMilestoneFinishedAchievement(MilestoneFinishedEvent evnt);

    List<MilestoneDto> getMilestoneItems();

    List<MilestoneDto> getMilestoneItemsUncached();

    Optional<MilestoneDto> getMilestoneItem(long milestoneId);

    @Transactional
    Optional<MilestoneDto> updateMilestone(long milestoneId, MilestoneDto upd);

    @Transactional
    Optional<MilestoneDto> addMilestone(MilestoneDto upd);

    @Transactional
    void deleteMilestone(long id);
}

package com.ously.gamble.milestones.api;

import org.apache.commons.lang3.StringUtils;

public record MilestoneFilter(
        long minBet,
        long minWin,
        int vendorId,
        int minBetlevel
) {

    public static MilestoneFilter fromString(String criteriaString) {

        if (StringUtils.isAllBlank(criteriaString)) {
            return new MilestoneFilter(0, 0, 0, 0);
        }

        String[] comps = criteriaString.trim().split(",");
        if (comps.length == 0) {
            return new MilestoneFilter(0, 0, 0, 0);
        }

        long mb = 0;
        long mw = 0;
        int vi = 0;
        int bl = 0;

        for (String comp : comps) {
            if (comp.startsWith("mb=")) {
                mb = Long.parseLong(comp.substring(3));
            }
            if (comp.startsWith("mw=")) {
                mw = Long.parseLong(comp.substring(3));
            }
            if (comp.startsWith("vi=")) {
                vi = Integer.parseInt(comp.substring(3));
            }
            if (comp.startsWith("bl=")) {
                bl = Integer.parseInt(comp.substring(3));
            }
        }

        return new MilestoneFilter(mb, mw, vi, bl);
    }

    public static String asString(MilestoneFilter msCriteria) {
        StringBuilder sB = new StringBuilder(10);
        boolean useComma = false;

        if (msCriteria.minBet > 0) {
            if (useComma) {
                sB.append(',');
            }
            sB.append("mb=").append(msCriteria.minBet);
            useComma = true;
        }

        if (msCriteria.minWin > 0) {
            if (useComma) {
                sB.append(',');
            }
            sB.append("mw=").append(msCriteria.minWin);
            useComma = true;
        }

        if (msCriteria.vendorId > 0) {
            if (useComma) {
                sB.append(',');
            }
            sB.append("vi=").append(msCriteria.vendorId);
            useComma = true;
        }

        if (msCriteria.minBetlevel > 0) {
            if (useComma) {
                sB.append(',');
            }
            sB.append("bl=").append(msCriteria.minBetlevel);
        }
        return sB.toString();
    }

}

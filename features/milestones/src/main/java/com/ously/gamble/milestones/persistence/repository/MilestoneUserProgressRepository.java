package com.ously.gamble.milestones.persistence.repository;


import com.ously.gamble.milestones.persistence.model.MilestoneUserProgress;
import com.ously.gamble.milestones.persistence.model.MilestoneUserProgressId;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;


@Repository
@ConditionalOnProperty(prefix = "monitor.milestones", name = "enabled", havingValue = "true")
public interface MilestoneUserProgressRepository extends JpaRepository<MilestoneUserProgress, MilestoneUserProgressId> {

//   List<Milestone> getActiveMilestonesForUser(long userId);


    @Query(nativeQuery = true, value = """
            select  CONCAT(mp.user_id,'-',group_concat(mp.milestone_id)) as results from milestone_progress mp join  milestone ms on ms.milestone_id = mp.milestone_id
             where
                 ms.valid_from < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 15 MINUTE )
            and
                ms.valid_to > DATE_ADD(CURRENT_TIMESTAMP, INTERVAL 10 MINUTE )
            and
                mp.user_id in (?1)
            and mp.stage>mp.stages
            group by mp.user_id
            """)
    List<String> getFinishedMilestonesOfUsers(Collection<Long> userId);


    List<MilestoneUserProgress> findAllByUserId(long userId);

    @Query(nativeQuery = true, value = """
            update milestone_progress set claimed=claimed+1 where user_id=?1 and milestone_id=?2 and claimed=?3 and claimed < stages""")
    @Modifying
    int increaseClaimedFrom(long userId, long milestoneId, int previousRStage);

}
package com.ously.gamble.milestones.api;

import com.ously.gamble.api.monitoring.MonitoredGameTx;
import com.ously.gamble.persistence.model.TransactionType;
import com.ously.gamble.persistence.model.game.GamePlatform;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class MilestoneUserItemTest {

    @Test
    void testSingleEval(){

        MilestoneDefinition mdef = new MilestoneDefinition();
        mdef.setBetlevel((short) 0);
        mdef.setMinBet(50);
        mdef.setMinWin(0L);
        mdef.setType(MilestoneType.SUM_BETS);
        mdef.setStages((short) 2);
        mdef.setScores(new long[]{2000,3000});
        mdef.setRewards(new int[]{100,200});

        MilestoneUserItem mui = new MilestoneUserItem(0L,
                new MilestoneItem(1,Instant.now(), Instant.now().plus(10, ChronoUnit.SECONDS),2,
        mdef)
        );

        MonitoredGameTx tx = new MonitoredGameTx(100L,99, 142, GamePlatform.DESKTOP, TransactionType.BET,
        75, 0, 0, (short) 0, false, 99.5f);

        MilestoneUpdateItem upd = mui.evalTx(tx);
        assertNotNull(upd);
        assertEquals(75, upd.getScoreIncrement());
        assertEquals(1,upd.getMilestoneId());
        assertEquals(100, upd.getUserId());
        assertEquals(0, upd.getStage());
        assertEquals(2, upd.getStages());
        assertEquals(2000, upd.getTargetScore());

        // bet too low
        MonitoredGameTx tx2 = new MonitoredGameTx(100L,99, 142, GamePlatform.DESKTOP, TransactionType.BET,
                45, 0, 0, (short) 0, false, 99.5f);
         upd = mui.evalTx(tx2);
        assertNull(upd);


        // wrong type
        MonitoredGameTx tx3 = new MonitoredGameTx(100L,99, 142, GamePlatform.DESKTOP, TransactionType.WIN,
                0,44, 0, (short) 0, false, 99.5f);
        upd = mui.evalTx(tx3);
        assertNull(upd);

    }


    @Test
    void testMultiEval(){

        MilestoneDefinition mdef = new MilestoneDefinition();
        mdef.setBetlevel((short) 0);
        mdef.setMinBet(50);
        mdef.setMinWin(0L);
        mdef.setType(MilestoneType.SUM_BETS);
        mdef.setStages((short) 2);
        mdef.setScores(new long[]{2000,3000});
        mdef.setRewards(new int[]{100,200});

        MilestoneDefinition mdef2 = new MilestoneDefinition();
        mdef2.setBetlevel((short) 0);
        mdef2.setMinBet(30);
        mdef2.setMinWin(0L);
        mdef2.setType(MilestoneType.SUM_BETS);
        mdef2.setStages((short) 2);
        mdef2.setScores(new long[]{2000, 3000});
        mdef2.setRewards(new int[]{100, 200});

        String mdefStr = MilestoneDefinition.asString(mdef);
        String mdefStr2 = MilestoneDefinition.asString(mdef2);

        List<MilestoneItem> miList = new ArrayList<>();
        miList.add(new MilestoneItem(1, Instant.now(), Instant.now().plus(10, ChronoUnit.SECONDS), 2,
                mdef));
        miList.add(new MilestoneItem(2, Instant.now(), Instant.now().plus(10, ChronoUnit.SECONDS), 2,
                mdef2)
        );

        MilestoneUserItems mui = new MilestoneUserItems(0L, miList);

        MonitoredGameTx tx = new MonitoredGameTx(100L, 99, 142, GamePlatform.DESKTOP, TransactionType.BET,
                35, 0, 0, (short) 0, false, 99.5f);
        List<MilestoneUpdateItem> upts = mui.evalTx(tx);
        assertEquals(1, upts.size());

        MonitoredGameTx tx2 = new MonitoredGameTx(100L, 99, 142, GamePlatform.DESKTOP, TransactionType.BET,
                85, 0, 0, (short) 0, false, 99.5f);
        upts = mui.evalTx(tx2);
        assertEquals(2, upts.size());

    }


}
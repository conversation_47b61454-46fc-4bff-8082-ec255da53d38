

CREATE TABLE if not exists `popups`
(
    `user_id`         bigint NOT NULL,
    `id` int NOT NULL DEFAULT 0,
    `sender` varchar(50) NOT NULL DEFAULT 'UNDEFINED',
    `expires_at` timestamp not null,
    `created_at` timestamp not null,
    `content` JSON,
    `result` int not null DEFAULT 0,
        PRIMARY KEY (`user_id`,`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

create index cpopups_expires_idx
    on popups (expires_at);


CREATE TABLE if not exists `popups_archive`
(
    `user_id`         bigint NOT NULL,
    `id` int NOT NULL DEFAULT 0,
    `sender` varchar(50) NOT NULL DEFAULT 'UNDEFINED',
    `archived_at` timestamp not null DEFAULT CURRENT_TIMESTAMP,
    `created_at` timestamp not null,
    `content` JSON,
    `result` int not null DEFAULT -1,
    PRIMARY KEY (`user_id`,`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;


create index cpopups_archive_created_at_idx
    on popups_archive (created_at);



package com.ously.gamble.safes.persistence.repositories;

import com.ously.gamble.safes.persistence.model.BacklogSafe;
import com.ously.gamble.safes.persistence.model.BacklogSafeId;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
@ConditionalOnProperty(prefix = "safes", name = "enabled", havingValue = "true")
public interface BacklogSafeRepository extends JpaRepository<BacklogSafe, BacklogSafeId> {

    @Query
    List<BacklogSafe> findAllByUserId(long userId);

    @Query(value = "SELECT BLS from BacklogSafe BLS order by BLS.createdAt ASC LIMIT 1")
    Optional<BacklogSafe> findLatestByUserId(long userId);


    @Query
    int countByUserId(long userId);
}

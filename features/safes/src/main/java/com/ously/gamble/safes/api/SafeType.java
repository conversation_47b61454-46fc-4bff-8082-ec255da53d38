package com.ously.gamble.safes.api;

import com.ously.gamble.api.achievements.TokenType;

import static com.ously.gamble.api.achievements.TokenType.*;

public enum SafeType {
    WOOD(TRESOR_WOOD),
    SILVER(TRESOR_SILVER),
    GOLD(TRESOR_GOLD),
    EPIC(TRESOR_EPIC),
    LEGENDARY(TRESOR_LEGENDARY),
    ULTIMATE(TRESOR_ULTIMATE);

    private final TokenType tokenType;

    SafeType(TokenType tokenType) {
        this.tokenType = tokenType;
    }

    public TokenType getTokenType() {
        return tokenType;
    }

    public static SafeType fromTokenType(TokenType tokenType) {
        for (SafeType safeType : SafeType.values()) {
            if (safeType.tokenType == tokenType) {
                return safeType;
            }
        }
        return null;
    }

}

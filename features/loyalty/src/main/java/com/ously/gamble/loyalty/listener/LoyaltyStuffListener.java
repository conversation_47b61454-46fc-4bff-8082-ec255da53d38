package com.ously.gamble.loyalty.listener;

import com.ously.gamble.api.events.UserEventGateway;
import com.ously.gamble.api.features.LoyaltyService;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Detect players which have played and might need an update on crm (balance, tokens, ...)
 */
@Component
@Profile("!test")
@ConditionalOnOffloader
public class LoyaltyStuffListener {

    final Logger log = LoggerFactory.getLogger(getClass());

    private final LoyaltyService loyaltySrv;


    public LoyaltyStuffListener(LoyaltyService ls) {
        this.loyaltySrv = ls;
        log.info("Started LoyaltyStuffListener");
    }


    @RabbitListener(queues = UserEventGateway.USER_EVENT_LOGIN_QUEUE, containerFactory =
            "directRabbitListenerContainerFactory",
            concurrency = "2")
    @Transactional
    public void handleUserLoyalty(Long userId) {
        try {
            loyaltySrv.calculateLoyalty(userId);
        } catch (Exception e) {
            log.error("Error handling loyalty for {}", userId);
            throw new RuntimeException(e);
        }
    }

}

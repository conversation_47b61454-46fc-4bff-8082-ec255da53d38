package com.ously.gamble.social.persistence.model;


import com.ously.gamble.api.achievements.TokenType;

import java.io.Serializable;
import java.util.Objects;

@SuppressWarnings("unused")
public class TokenId implements Serializable {

    private Long userId;
    private Long id;
    private TokenType type;

    public TokenId() {
    }

    public TokenId(Long uId, Long tkId, TokenType type) {
        this.userId = uId;
        this.id = tkId;
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        var tokenId = (TokenId) o;
        return userId.equals(tokenId.userId) && id.equals(tokenId.id) && type == tokenId.type;
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, id, type);
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public TokenType getType() {
        return type;
    }

    public void setType(TokenType type) {
        this.type = type;
    }
}

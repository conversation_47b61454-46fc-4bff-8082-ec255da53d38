package com.ously.gamble.social.persistence.repository;


import com.ously.gamble.api.achievements.AchievementType;
import com.ously.gamble.social.persistence.model.Achievement;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
@ConditionalOnProperty(prefix = "features", name = "jurisdiction", havingValue = "SOC",
        matchIfMissing = true)
public interface AchievementRepository extends JpaRepository<Achievement, Long> {

    @Query("SELECT a FROM Achievement a WHERE a.userId = ?1 order by a.createdAt DESC")
    List<Achievement> findAllByUserId(Long uid);

    Optional<Achievement> findByUserIdAndId(Long uid, Long id);

    List<Achievement> findAllByUserIdAndClaimed(Long uid, boolean claimed);

    List<Achievement> findAllByUserIdAndTypeAndQualifier(Long uid, AchievementType type,
                                                         String qualifier);

    List<Achievement> findAllBySessionIdAndTypeAndUserId(Long sessionId, AchievementType type,
                                                         Long uid);

    @Modifying
    @Query("update Achievement a set a.claimed=true, a.updatedAt = instant where a.userId = ?1 " +
            "and a.type= ?2 and a.qualifier= ?3 and a.id = ?4 and a.claimed=false")
    int setClaimed(Long userId, AchievementType type, String qualifier, Long id);

    @Query("SELECT a FROM Achievement a WHERE a.userId = ?1 and a.type = ?2 and a.createdAt >= ?3")
    List<Achievement> getAllAchievementsForUserTypeAndAgeInDays(long userId, AchievementType type
            , Instant barrier);

    @Query(nativeQuery = true, value = "delete from achievements where user_id = ?1")
    @Modifying
    void deleteAllByUserId(long userId);

    void deleteByUserIdAndId(Long userId, Long id);
}
package com.ously.gamble.social.persistence.model;

import com.ously.gamble.api.achievements.AccomplishmentType;
import jakarta.persistence.*;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;

/**
 * Entity which collects accomplishments
 */
@SuppressWarnings("unused")
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "accomplishments")
@IdClass(AccomplishmentId.class)
public class Accomplishment {

	@Id
	@Column(name = "user_id", nullable = false)
	Long userId;

	@Id
	@Enumerated(EnumType.STRING)
	@Column(name = "type", length = 60, nullable = false)
	AccomplishmentType type;

	/**
	 * unique (by user) text selector for the accomplishment. Can be human readable, but must not!!
	 */
	@Id
	@Column(name = "selector", length = 100, nullable = false)
	String selector;

	/**
	 * reference to type if that type is persistent. For (daily missions) this is useless but nonetheless set to avoid duplicates.
	 * But for campaignMissions, campaigns, and evtl. Tournaments (stable entities) this is a
	 * reference to the id!
	 */
	@Column(name = "ref")
	Long ref = 0L;

	/**
	 * Rank in mission accomplishments - or other numeric selector
	 */
	@Column(name = "m_rank")
	Long rank = 0L;

	/**
	 * variables for use in localisation templates
	 */
	@Column(name = "variables", length = 200)
	String variables;

	/**
	 * date of accomplishment
	 */
	@Column(name = "created_at")
	Instant createdAt = Instant.now();

	/**
	 * Link to achievement, if any
	 */
	@Column(name = "achievement_id")
	Long achievementId;

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public AccomplishmentType getType() {
		return type;
	}

	public void setType(AccomplishmentType type) {
		this.type = type;
	}

	public String getSelector() {
		return selector;
	}

	public void setSelector(String selector) {
		this.selector = selector;
	}

	public Long getRef() {
		return ref;
	}

	public void setRef(Long ref) {
		this.ref = ref;
	}

	public Long getRank() {
		return rank;
	}

	public void setRank(Long rank) {
		this.rank = rank;
	}

	public String getVariables() {
		return variables;
	}

	public void setVariables(String variables) {
		this.variables = variables;
	}

	public Instant getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Instant createdAt) {
		this.createdAt = createdAt;
	}

	public Long getAchievementId() {
		return achievementId;
	}

	public void setAchievementId(Long achievementId) {
		this.achievementId = achievementId;
	}
}

package com.ously.gamble.leaderboards.service;

import com.ously.gamble.api.leaderboards.LeaderboardType;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class LeaderboardTemplateQueryBuilderTest {

    private static final LeaderboardType[] EMPTY_LBTYPE_ARRAY = new LeaderboardType[0];

    @Test
    void testQueryCreation() {

        // EMPTY
        Map<String, String> emptyMap = GameLeaderboardUtils.createReconcileParameterizedQueries(EMPTY_LBTYPE_ARRAY);
        assertTrue(emptyMap.isEmpty());

        // 2 TYPES
        Map<String, String> sql2Map =
                GameLeaderboardUtils.createReconcileParameterizedQueries(new LeaderboardType[]{LeaderboardType.MAXWIN_ALLTIME,
                        LeaderboardType.MAXMULT_ALLTIME});
        assertFalse(sql2Map.isEmpty());
        assertEquals("delete from leaderboards where game_id=:gid and type in (0,1) and user_id in (:uids)",
                sql2Map.get("DELETE"));


    }

}

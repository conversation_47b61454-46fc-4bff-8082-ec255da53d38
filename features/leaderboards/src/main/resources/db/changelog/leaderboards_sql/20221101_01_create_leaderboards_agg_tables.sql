create table leaderboards_user_stats(
        user_id bigint not null,
        rdate DATE not null,
        primary key (user_id),
        INDEX lbus_rdate(rdate)
);

create table leaderboards_game_stats(
     session_id bigint not null,
     rdate DATE not null,
     game_id int not null,
     user_id bigint not null,
     maxwin bigint not null,
     maxmult bigint not null,
     primary key (game_id,user_id,session_id),
     index lb_game_stats_date_idx (rdate)
);

insert into leaderboards_user_stats  (user_id, rdate)
      select user_id,
            DATE(us.last_login) as rdate
      from user_statistics us
      where us.last_login >= DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 90 DAY);


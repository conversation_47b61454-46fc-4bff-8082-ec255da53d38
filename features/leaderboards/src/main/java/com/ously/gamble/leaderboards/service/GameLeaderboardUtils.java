package com.ously.gamble.leaderboards.service;

import com.ously.gamble.api.leaderboards.GameLeaderboardEntry;
import com.ously.gamble.api.leaderboards.LeaderboardType;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public final class GameLeaderboardUtils {

    private GameLeaderboardUtils() {
    }

    static Map<String, String> createReconcileParameterizedQueries(LeaderboardType[] lbTypes) {

        int numEnabledTypes = lbTypes.length;

        if (numEnabledTypes == 0) {
            return Collections.emptyMap();
        }

        StringBuilder reconSelect = new StringBuilder();
        StringBuilder reconDelete = new StringBuilder();
        StringBuilder reconReinsert = new StringBuilder();

        reconSelect.append("select coalesce(group_concat(distinct xx.user_id),'') as muids,count(distinct xx.user_id) as " +
                "mmcount from" +
                " leaderboards l right join (");
        reconDelete.append("delete from leaderboards where game_id=:gid and type in (");
        reconReinsert.append("insert into leaderboards (type, game_id, user_id, val) ");

        List<String> rsList = new ArrayList<>(numEnabledTypes);
        List<Integer> typeValList = new ArrayList<>(numEnabledTypes);
        List<String> riList = new ArrayList<>(numEnabledTypes);

        for (LeaderboardType tp : lbTypes) {
            int typeVal = tp.typeValue();
            int daysback = tp.periodType().daysback();
            String gsFieldname = tp.valueType().lbFieldname();

            // typeVal list
            typeValList.add(tp.typeValue());

            // ReconSelect

            String sbRS = "select " + typeVal + " as type,gs.game_id,gs.user_id,max(gs." + gsFieldname +
                    ") as val from leaderboards_game_stats gs join leaderboards_user_stats us on gs.user_id= us.user_id " +
                    "where gs.game_id= :gid and gs." + gsFieldname + " >0 and " +
                    "gs.rdate >= DATE_SUB(CURRENT_DATE, INTERVAL " + daysback + " DAY)" +
                    "group by gs.game_id, gs.user_id";


            rsList.add(sbRS);
            // ReconReinsert
            String sbRI = "select " + typeVal + " as type,gs.game_id,gs.user_id,max(gs." + gsFieldname +
                    ") as val from leaderboards_game_stats gs join leaderboards_user_stats us on gs.user_id=us.user_id " +
                    "where gs.game_id= :gid and gs.user_id in (:uids) and gs." + gsFieldname + " >0 and " +
                    "gs.rdate >= DATE_SUB(CURRENT_DATE, INTERVAL " + daysback + " DAY)" +
                    "group by gs.game_id, gs.user_id";
            riList.add(sbRI);
        }

        reconSelect.append(String.join(" union all ", rsList));
        reconSelect.append(") xx on l.user_id=xx.user_id and l.type= xx.type and l.game_id=xx.game_id and l.val=xx.val where " +
                "coalesce(l.val,-1) <> coalesce(xx.val,-2)");
        reconDelete.append(typeValList.stream().map(Object::toString).collect(Collectors.joining(",")));
        reconDelete.append(") and user_id in (:uids)");

        reconReinsert.append(String.join(" union all ", riList));

        return Map.of("SELECT", reconSelect.toString(), "DELETE", reconDelete.toString(), "REINSERT",
                reconReinsert.toString());

    }


    public static void recalcPositions(List<GameLeaderboardEntry> entries) {
        int position = 0;
        int index;
        long value = Long.MAX_VALUE;

        for (index = 0; index < entries.size(); index++) {
            GameLeaderboardEntry entry = entries.get(index);
            if (entry.value() < value) {
                position = index + 1;
            }
            value = entry.value();
            entries.set(index, new GameLeaderboardEntry(entry.playerId(), position, entry.value(), entry.level(), entry.username()));
        }
    }

}

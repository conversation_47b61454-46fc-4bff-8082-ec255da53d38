package com.ously.gamble.leaderboards.persistence.model;

import java.io.Serializable;


@SuppressWarnings("unused")
public class GameLeaderboardDaoId implements Serializable {

    int type;
    int gameId;
    long userId;

    public GameLeaderboardDaoId(int type, int gameId, long userId) {
        this.type = type;
        this.gameId = gameId;
        this.userId = userId;
    }

    public GameLeaderboardDaoId() {
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getGameId() {
        return gameId;
    }

    public void setGameId(int gameId) {
        this.gameId = gameId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        GameLeaderboardDaoId that = (GameLeaderboardDaoId) o;

        if (type != that.type) return false;
        if (gameId != that.gameId) return false;
        return userId == that.userId;
    }

    @Override
    public int hashCode() {
        int result = type;
        result = 31 * result + gameId;
        result = 31 * result + Long.hashCode(userId);
        return result;
    }
}

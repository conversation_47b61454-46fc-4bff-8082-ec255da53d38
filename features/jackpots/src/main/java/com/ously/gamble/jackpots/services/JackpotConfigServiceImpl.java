package com.ously.gamble.jackpots.services;

import com.ously.gamble.api.jackpots.JackpotConfig;
import com.ously.gamble.api.jackpots.JackpotConfigHolder;
import com.ously.gamble.api.jackpots.JackpotConfigService;
import com.ously.gamble.jackpots.events.JackpotConfigHasChangedEvent;
import com.ously.gamble.jackpots.persistence.model.JackpotConfigItem;
import com.ously.gamble.jackpots.persistence.repositories.JackpotConfigItemRepository;
import jakarta.annotation.PreDestroy;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.List;
import java.util.Optional;

@ConditionalOnProperty(prefix = "jackpots", name = "enabled")
@Service
public class JackpotConfigServiceImpl implements JackpotConfigService {
    private final static String JACKPOT_GAMETAG_NAME = "JACKPOT_ENABLED";
    private final static String JACKPOT_GAMETAG_DESCRIPTION = "Game jackpot is active";

    private final JackpotConfigItemRepository jpConfigRepo;
    private final RTopic jackpotConfigTopic;
    private final ApplicationEventPublisher eventPublisher;

    private JackpotConfigHolder configsHolder;


    public JackpotConfigServiceImpl(JackpotConfigItemRepository jackpotConfigItemRepository,
                                    ApplicationEventPublisher eventPublisher,
                                    RedissonClient client, ApplicationEventPublisher applicationEventPublisher) {
        this.jpConfigRepo = jackpotConfigItemRepository;
        this.eventPublisher = eventPublisher;
        this.jackpotConfigTopic = client.getTopic("jackpotsConfig");
        this.jackpotConfigTopic.addListener(String.class, (channel, message) -> reloadConfig(message));
        reloadConfig("init");
    }

    @PreDestroy
    public void cleanup() {
        this.jackpotConfigTopic.removeAllListeners();
    }

    private void reloadConfig(String message) {
        configsHolder = new JackpotConfigHolder(getConfigs());
    }

    @Transactional
    @Override
    public List<JackpotConfig> getConfigs() {
        return jpConfigRepo.findAll().stream().map(a -> new JackpotConfig(a.getId(), a.getMinBet(), a.getProbability(), a.getRepetitions())).sorted().toList();
    }

    @Override
    public JackpotConfigHolder getJackpotConfigHolder() {
        return configsHolder;
    }

    @Transactional
    @Override
    public JackpotConfig addOrUpdateNewConfig(JackpotConfig nCfg) {


        if (nCfg.id() == null) {
            JackpotConfigItem newItem = new JackpotConfigItem();
            newItem.setMinBet(nCfg.minBet());
            newItem.setProbability(nCfg.probability());
            newItem.setRepetitions(nCfg.repetitions());
            JackpotConfigItem save = jpConfigRepo.save(newItem);
            eventPublisher.publishEvent(new JackpotConfigHasChangedEvent(true));
            return new JackpotConfig(save.getId(), save.getMinBet(), save.getProbability(), save.getRepetitions());
        } else {
            Optional<JackpotConfigItem> byId =
                    jpConfigRepo.findById(nCfg.id());
            byId.get().setRepetitions(nCfg.repetitions());
            byId.get().setMinBet(nCfg.minBet());
            byId.get().setProbability(nCfg.probability());
            jpConfigRepo.save(byId.get());
            eventPublisher.publishEvent(new JackpotConfigHasChangedEvent(true));
            return nCfg;
        }
    }

    @Transactional
    @Override
    public void deleteConfig(JackpotConfig nCfg) {
        jpConfigRepo.deleteById(nCfg.id());
        eventPublisher.publishEvent(new JackpotConfigHasChangedEvent(true));
    }


    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleJackpotConfigHasChangedEvent(JackpotConfigHasChangedEvent event) {
        if (event.forceReload()) {
            jackpotConfigTopic.publish("forceReload");
        }
    }

}

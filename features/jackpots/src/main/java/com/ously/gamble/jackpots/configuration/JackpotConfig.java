package com.ously.gamble.jackpots.configuration;

import com.ously.gamble.api.cache.CachedMapFactory;
import com.ously.gamble.api.cache.ScoredSet;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
@ConditionalOnProperty(prefix = "jackpots", name = "enabled", havingValue = "true")

public class JackpotConfig {

    @Bean
    ScoredSet<Integer> jackpotSet(
            CachedMapFactory<Integer, Integer> fact) {
        return fact.createScoredSet("jackpots", Integer.class);
    }


}

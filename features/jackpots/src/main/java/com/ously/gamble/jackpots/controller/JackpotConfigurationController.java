package com.ously.gamble.jackpots.controller;

import com.ously.gamble.api.jackpots.JackpotConfig;
import com.ously.gamble.api.jackpots.JackpotConfigService;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/admin/jackpots")
@ConditionalOnOffloader
public class JackpotConfigurationController {

    private final JackpotConfigService jpConfigService;


    public JackpotConfigurationController(@Autowired(required = false) JackpotConfigService jpConfigService) {
        this.jpConfigService = jpConfigService;
    }

    @Operation(description = "get all jackpot config items", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/config")
    @RolesAllowed("ADMIN")
    public List<JackpotConfig> getJackpotConfigs() {
        if (jpConfigService == null) {
            throw new ResponseStatusException(
                    HttpStatus.NOT_IMPLEMENTED, "not implemented"
            );
        }
        return jpConfigService.getConfigs();
    }


    @Operation(description = "update jackpot config item", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/config/{id}")
    @RolesAllowed("ADMIN")
    public Optional<JackpotConfig> updateJackpotConfig(@PathVariable("id") Integer id, @RequestBody JackpotConfig config) {
        if (jpConfigService == null) {
            throw new ResponseStatusException(
                    HttpStatus.NOT_IMPLEMENTED, "not implemented"
            );
        }
        return Optional.ofNullable(jpConfigService.addOrUpdateNewConfig(config));
    }

    @Operation(description = "add jackpot config item", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/config")
    @RolesAllowed("ADMIN")
    public Optional<JackpotConfig> addJackpotConfig(@RequestBody JackpotConfig config) {
        if (jpConfigService == null) {
            throw new ResponseStatusException(
                    HttpStatus.NOT_IMPLEMENTED, "not implemented"
            );
        }
        if (config.id() != null) {
            throw new ResponseStatusException(
                    HttpStatus.EXPECTATION_FAILED, "malformed config item (id must be null)"
            );
        }
        return Optional.ofNullable(jpConfigService.addOrUpdateNewConfig(config));
    }


}

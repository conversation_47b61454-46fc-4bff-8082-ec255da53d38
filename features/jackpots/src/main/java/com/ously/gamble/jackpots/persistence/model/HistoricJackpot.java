package com.ously.gamble.jackpots.persistence.model;

import jakarta.persistence.*;
import org.springframework.data.domain.Persistable;

import java.time.Instant;

/**
 * These entities are created whenever a jackpot has been won. Since we want to use this table as
 * a means to derive new probability/initial_pot for new jackpots this table is clustered by gameId, createdAt.
 * To keep table size around 100 entries per game we will do a delete of oldest entries whenever we insert a new one
 * assuming 4000 games this table will never grow beyound 100*4000 = 400.000 rows in total.
 * 100 historic entries is a lot, we make this configurable to further lower the historic jackpots for each game - even later.
 */

@SqlResultSetMapping(name = "Jackpots.HistoricJackpotData",
        classes = @ConstructorResult(targetClass = HistoricJackpotData.class,
                columns = {
                        @ColumnResult(name = "hoursBack", type =
                                int.class),
                        @ColumnResult(name = "probability", type =
                                long.class),
                        @ColumnResult(name = "initialAmount", type =
                                long.class),
                        @ColumnResult(name = "potsize", type =
                                long.class),
                        @ColumnResult(name = "minutesToWin", type =
                                int.class),
                }))


@NamedNativeQuery(name = "HistoricJackpot.getLatestHistoricJackpots", query = """
         select
         ROUND(TIME_TO_SEC(TIMEDIFF(CURRENT_TIMESTAMP,created_at))/3600, 0) as hoursBack,
         probability,
         start_pot as initialAmount,
         pot as potsize,
          ROUND(TIME_TO_SEC(TIMEDIFF(won_at,created_at))/60, 0) as minutesToWin
         from jackpots_historic where game_id= ?1 order by won_at desc limit ?2
        """, resultSetMapping = "Jackpots.HistoricJackpotData")


@Entity
@Table(name = "jackpots_historic")
@IdClass(HistoricJackpotId.class)
public class HistoricJackpot implements Persistable<HistoricJackpotId> {

    @Id
    @Column(name = "game_id")
    int gameId;

    @Id
    @Column(name = "created_at")
    Instant createdAt;

    @Column(name = "won_at")
    Instant wonAt;

    @Column(name = "probability")
    long probability;

    @Column(name = "minbet")
    int minBet;

    @Column(name = "start_pot")
    long initialPot;

    @Column(name = "pot")
    long pot;

    @Column(name = "user_id")
    long userId;

    @Transient
    boolean wasLoaded;

    @PostLoad
    @PostPersist
    public void setTransientLoaded() {
        this.wasLoaded = true;
    }

    public Instant getWonAt() {
        return wonAt;
    }

    public void setWonAt(Instant wonAt) {
        this.wonAt = wonAt;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public int getGameId() {
        return gameId;
    }

    public void setGameId(int gameId) {
        this.gameId = gameId;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public long getProbability() {
        return probability;
    }

    public void setProbability(long probability) {
        this.probability = probability;
    }

    public long getInitialPot() {
        return initialPot;
    }

    public void setInitialPot(long initialPot) {
        this.initialPot = initialPot;
    }

    public long getPot() {
        return pot;
    }

    public void setPot(long pot) {
        this.pot = pot;
    }


    public int getMinBet() {
        return minBet;
    }

    public void setMinBet(int minBet) {
        this.minBet = minBet;
    }

    @Override
    public HistoricJackpotId getId() {
        return new HistoricJackpotId(createdAt, gameId);
    }

    @Override
    public boolean isNew() {
        return !wasLoaded;
    }

    public boolean isWasLoaded() {
        return wasLoaded;
    }

    public void setWasLoaded(boolean wasLoaded) {
        this.wasLoaded = wasLoaded;
    }


    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;

        HistoricJackpot that = (HistoricJackpot) o;
        int c = createdAt.compareTo(that.createdAt);
        if (c == 0) {
            return gameId == that.gameId;
        } else {
            return false;
        }
    }

    @Override
    public int hashCode() {
        return gameId;
    }
}

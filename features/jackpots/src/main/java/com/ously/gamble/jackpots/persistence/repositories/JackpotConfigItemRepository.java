package com.ously.gamble.jackpots.persistence.repositories;


import com.ously.gamble.jackpots.persistence.model.JackpotConfigItem;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
@ConditionalOnProperty(prefix = "jackpots", name = "enabled", havingValue = "true")
public interface JackpotConfigItemRepository extends JpaRepository<JackpotConfigItem, Integer> {

}
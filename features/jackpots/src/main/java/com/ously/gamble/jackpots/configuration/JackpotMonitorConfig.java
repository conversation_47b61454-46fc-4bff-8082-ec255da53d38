package com.ously.gamble.jackpots.configuration;

import com.ously.gamble.api.jackpots.JackpotUpdate;
import com.ously.gamble.api.monitoring.MonitoringReceiver;
import com.ously.gamble.api.monitoring.MonitoringSender;
import com.ously.gamble.conditions.ConditionalOnMonitor;
import com.ously.gamble.jackpots.codec.JackpotUpdateItemCodec;
import com.ously.gamble.monitoring.endpoints.MonitoringCollectorImpl;
import com.ously.gamble.monitoring.endpoints.MonitoringReceiverImpl;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;


@Configuration
@ConditionalOnProperty(prefix = "jackpots", name = "enabled", havingValue = "true")
@ConditionalOnMonitor
public class JackpotMonitorConfig {

    @Bean(destroyMethod = "shutdown")
    public MonitoringSender<JackpotUpdate> jackpotUpdSender(
            JackpotConfiguration mConfig,
            RedissonClient rClient,
            ThreadPoolTaskScheduler tpTS) {
        return new MonitoringCollectorImpl<>(mConfig.getJackpotTxQueueName(), rClient, tpTS, JackpotUpdateItemCodec.INSTANCE);
    }

    //
    @Bean(destroyMethod = "shutdown")
    public MonitoringReceiver<JackpotUpdate> jackpotUpdReceiver(
            JackpotConfiguration mConfig,
            RedissonClient rClient,
            ThreadPoolTaskScheduler tpTS,
            ApplicationEventPublisher eventPublisher) {
        return new MonitoringReceiverImpl<>(mConfig.getJackpotTxQueueName(), rClient, tpTS, JackpotUpdateItemCodec.INSTANCE, eventPublisher);
    }


}

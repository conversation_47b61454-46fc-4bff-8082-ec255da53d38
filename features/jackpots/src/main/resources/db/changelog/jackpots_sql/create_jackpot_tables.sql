
create table jackpots_active
(
    game_id     INT not null,
    created_at  TIMESTAMP not null,
    probability BIGINT not null,
    minbet      INTEGER not null,
    repetition  INTEGER not null,
    start_pot   BIGINT not null,
    pot         BIGINT not null,
    PRIMARY KEY (`game_id`)
)
    collate = utf8mb4_unicode_ci;


create table jackpots_historic
(
    game_id     INT not null,
    created_at  TIMESTAMP not null,
    won_at  TIMESTAMP not null,
    probability BIGINT not null,
    minbet      INTEGER not null,
    start_pot   BIGINT not null,
    pot         BIGINT not null,
    user_id     BIGINT not null,
    PRIMARY KEY (`game_id`,`created_at`)
)
    collate = utf8mb4_unicode_ci;

create table jackpots_configuration
(
    id INTEGER not null AUTO_INCREMENT,
    minbet     INTEGER not null DEFAULT 0,
    probability BIGINT not null,
    repetitions INTEGER not null DEFAULT 1,
    PRIMARY KEY (`id`)
)
    collate = utf8mb4_unicode_ci;



databaseChangeLog:
  - changeSet:
      id: rk-20220502-01
      author: <PERSON><PERSON><PERSON>
      runOnChange: false
      preConditions:
        - onFail: MARK_RAN
        - not:
            - tableExists:
                tableName: rankings
      changes:
        - sqlFile:
            fullDefinition: false
            remarks: add rankings table
            path: /db/changelog/ranking_sql/create_rankings_table.sql
            stripComments: true
            splitStatements: true
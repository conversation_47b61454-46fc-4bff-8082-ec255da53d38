package com.ously.gamble.ranking.scheduler;

import com.ously.gamble.api.maintenance.ScheduleExecutionService;
import com.ously.gamble.api.maintenance.ScheduledTaskInformation;
import com.ously.gamble.api.rankings.RankingSchedulerService;
import com.ously.gamble.api.rankings.events.UserNameRankingEvent;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.ranking.config.RankingsQueueConfiguration;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
@ConditionalOnOffloader
@ConditionalOnProperty(prefix = "monitor.rankings", name = "enabled", havingValue = "true")
public class RankingScheduler {
    private static final Logger log =
            LoggerFactory.getLogger(RankingScheduler.class);

    private final RankingSchedulerService rsService;
    private final ScheduleExecutionService schedService;
    private final RabbitTemplate rbTmpl;

    public RankingScheduler(RankingSchedulerService rsService,
                            ScheduleExecutionService schedSrv,
                            @Qualifier("rabbitTemplateNoTx")
                            RabbitTemplate rbTmplNoTx
    ) {
        this.rsService = rsService;
        this.schedService = schedSrv;
        this.rbTmpl = rbTmplNoTx;
    }

    @Transactional
    @SchedulerLock(name = "rankLinkageRemoval", lockAtLeastFor = "PT60S")
    @Scheduled(cron = "0 33 3 ? * *")
    public void scheduledRankLinkageCleanup() {
        schedService.doSchedule(new ScheduledTaskInformation("rankings", "tournamentLinkageExpiry", null), this::doLinkageExpiry);
    }

    private ScheduledTaskInformation doLinkageExpiry(ScheduledTaskInformation sti) {
        log.info("Removing old tournament Linkage");
        rsService.doLinkageCleanup();
        return sti;
    }


    @Transactional
    @SchedulerLock(name = "rankSchedule", lockAtLeastFor = "PT60S")
    @Scheduled(cron = "0 0/15 * ? * *")
    public void scheduledRankCreation() {
        schedService.doSchedule(new ScheduledTaskInformation("rankings", "tournamentCreationSchedule", null), this::doSchedule);
    }

    private ScheduledTaskInformation doSchedule(ScheduledTaskInformation sti) {
        log.info("Checking for RankingSchedule");
        var rankingDtos = rsService.doSchedule();
        if (!rankingDtos.isEmpty()) {
            log.info("Created {} new rankings", rankingDtos.size());
        }
        return sti;
    }


    @Transactional
    @SchedulerLock(name = "refreshUsernamesTournamentSchedule", lockAtLeastFor = "PT60S")
    @Scheduled(cron = "0 5 4 ? * *")
    public void scheduledTournamentRefreshUsernames() {
        schedService.doSchedule(new ScheduledTaskInformation("rankings", "refreshUsernames", null), this::doRefreshRankingUsernames);
    }

    private ScheduledTaskInformation doRefreshRankingUsernames(ScheduledTaskInformation sti) {
        log.info("Refreshing usernames for tournaments");
        rbTmpl.convertAndSend(RankingsQueueConfiguration.TOURNAMENT_USR_NAME_MAP_QUEUE, new UserNameRankingEvent(-1L));
        return sti;
    }


    @Transactional
    @SchedulerLock(name = "closeRankSchedule", lockAtLeastFor = "PT60S")
    @Scheduled(cron = "0 0/5 * ? * *")
    public void scheduledCloseRankCreation() {
        schedService.doSchedule(new ScheduledTaskInformation("rankings", "closingFinishedTournaments", null), this::doScheduleCloseFinished);
    }

    private ScheduledTaskInformation doScheduleCloseFinished(ScheduledTaskInformation sti) {
        log.info("Checking for closable Rankings");
        var numQueued = rsService.closeFinishedRankings();
        if (numQueued > 0) {
            log.info("Closed {} new rankings", numQueued);
        }
        return sti;
    }



}

package com.ously.gamble.ranking.service;

import com.ously.gamble.api.rankings.TournamentCreationService;
import com.ously.gamble.api.rankings.TournamentPoolType;
import com.ously.gamble.api.rankings.TournamentSelector;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.ranking.persistence.repository.RankingScheduleRepository;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Random;

@Service
@ConditionalOnOffloader
@ConditionalOnProperty(prefix = "monitor.rankings", name = "enabled", havingValue = "true")
public class TournamentCreationServiceImpl implements TournamentCreationService {

    private final RankingScheduleRepository rsRepo;
    private final Random rndGen;

    public TournamentCreationServiceImpl(RankingScheduleRepository rsRepo) {
        this.rsRepo = rsRepo;
        this.rndGen = new Random(System.nanoTime());
    }


    private List<Long> getLowestRtpGameIds(int daysBack, int limit) {
        return rsRepo.getLowestRtp(daysBack, limit);
    }

    private List<Long> getHighestRtpGameIds(int daysBack, int limit) {
        return rsRepo.getHighestRtp(daysBack, limit);
    }

    private List<Long> getMostSpinned(int daysBack, int limit) {
        return rsRepo.getTopSpinned(daysBack, limit);
    }

    private List<Long> getLatestReleases(int daysBack, int limit) {
        return rsRepo.getLastReleased(limit);
    }


    @Override
    public TournamentSelector createSelector(TournamentPoolType type, int daysBack, int limit) {
        int vendorId = 0;

        List<Long> gameIds = switch (type) {
            case GAME_LEAST_RTP -> getLowestRtpGameIds(daysBack, limit);
            case GAME_HIGHEST_RTP -> getHighestRtpGameIds(daysBack, limit);
            case GAME_NEW_RELEASES -> getLatestReleases(daysBack, limit);
            case GAME_MOST_SPINNED -> getMostSpinned(daysBack, limit);
            default -> Collections.emptyList();
        };

        if (gameIds.isEmpty()) {
            return null;
        }

        // Now select random candidate
        int[] selectedGameIds = selectRandomGameIds(gameIds, 1);
        return new TournamentSelector(type, vendorId, selectedGameIds);
    }

    int[] selectRandomGameIds(List<Long> gameIds, int numToSelect) {
        if (numToSelect > gameIds.size()) {
            throw new IllegalArgumentException("the list does not contain " + numToSelect + " elements");
        }
        int[] selection = new int[numToSelect];
        for (int i = 0; i < numToSelect; i++) {
            selection[i] = gameIds.get(rndGen.nextInt(gameIds.size())).intValue();
        }
        return selection;
    }
}

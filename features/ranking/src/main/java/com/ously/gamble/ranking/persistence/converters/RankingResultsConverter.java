package com.ously.gamble.ranking.persistence.converters;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.api.rankings.RankingResults;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter()
public class RankingResultsConverter implements
        AttributeConverter<RankingResults, String> {

    private static final ObjectMapper om = new ObjectMapper();

    @Override
    public String convertToDatabaseColumn(RankingResults rankingResults) {
        if (rankingResults == null) {
            return null;
        }
        try {
            return om.writeValueAsString(rankingResults);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    @Override
    public RankingResults convertToEntityAttribute(String s) {
        if (s != null) {
            try {
                return om.readValue(s, RankingResults.class);
            } catch (JsonProcessingException e) {
                //
            }
        }
        return null;
    }
}

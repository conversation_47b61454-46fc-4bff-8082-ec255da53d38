package com.ously.gamble.ranking.config;

import com.ously.gamble.api.missions.RankUpdate;
import com.ously.gamble.api.monitoring.MonitoringReceiver;
import com.ously.gamble.api.monitoring.MonitoringSender;
import com.ously.gamble.conditions.ConditionalOnMonitor;
import com.ously.gamble.monitoring.endpoints.MonitoringCollectorImpl;
import com.ously.gamble.monitoring.endpoints.MonitoringReceiverImpl;
import com.ously.gamble.ranking.codec.RankUpdateItemCodec;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;


@Configuration
@ConditionalOnProperty(prefix = "monitor.rankings", name = "enabled", havingValue = "true")
@ConditionalOnMonitor
public class RankingsMonitorConfig {

    @Bean(destroyMethod = "shutdown")
    public MonitoringSender<RankUpdate> rnkupdSender(
            RankingConfiguration mConfig,
            RedissonClient rClient,
            ThreadPoolTaskScheduler tpTS) {
        return new MonitoringCollectorImpl<>(mConfig.getTxQueueName(), rClient, tpTS, RankUpdateItemCodec.INSTANCE);
    }

    @Bean(destroyMethod = "shutdown")
    public MonitoringReceiver<RankUpdate> rnkupdReceiver(
            RankingConfiguration mConfig,
            RedissonClient rClient,
            ThreadPoolTaskScheduler tpTS,
            ApplicationEventPublisher eventPublisher) {
        return new MonitoringReceiverImpl<>(mConfig.getTxQueueName(), rClient, tpTS, RankUpdateItemCodec.INSTANCE, eventPublisher);
    }


}

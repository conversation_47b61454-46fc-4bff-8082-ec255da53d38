package com.ously.gamble.ranking.service;

import com.github.benmanes.caffeine.cache.LoadingCache;
import com.ously.gamble.api.cache.CachedMapFactory;
import com.ously.gamble.api.cache.LCacheFactory;
import com.ously.gamble.api.cache.LCacheLoader;
import com.ously.gamble.api.cache.ScoredSet;
import com.ously.gamble.api.rankings.*;
import com.ously.gamble.api.rewards.tickets.AwardedRewardTicket;
import com.ously.gamble.api.rewards.tickets.RewardTicketDetails;
import com.ously.gamble.api.rewards.tickets.RewardType;
import com.ously.gamble.api.user.UserManagementService;
import com.ously.gamble.conditions.ConditionalOnNotBridge;
import com.ously.gamble.conditions.ConditionalOnNotMonitor;
import com.ously.gamble.ranking.config.RankingConfiguration;
import com.ously.gamble.ranking.management.ActiveRanking;
import com.ously.gamble.ranking.persistence.model.Ranking;
import com.ously.gamble.ranking.persistence.model.RankingId;
import com.ously.gamble.ranking.persistence.repository.RankingRepository;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.ously.gamble.ranking.management.ActiveRanking.EMPTY_RANKING_ARRAY;

@Service
@ConditionalOnNotBridge
@ConditionalOnNotMonitor
@ConditionalOnProperty(prefix = "monitor.rankings", name = "enabled", havingValue = "true")
public class RankingServiceImpl implements RankingService {
    static final Logger log = LoggerFactory.getLogger(RankingServiceImpl.class);
    static final int TOPENTRYCOUNT = 5;
    private final UserManagementService uMgmt;
    private final RankingConfiguration rankingConfiguration;

    private final Map<String, ScoredSet<Long>> scoredSets = new ConcurrentHashMap<>();

    /**
     * Maps userId to current ranks (ttl 3 min)
     */
    private final LoadingCache<Long, Map<String, RankResults>> topRanksForUser;


    /**
     * Map of the last n-hours of closed Ranks
     */
    private final LoadingCache<RankingId, RankResults> latestClosedRanksCache;


    /**
     * Maps userId to username (ttl 15 min)
     */
    private final LoadingCache<Long, String> userIdToUserName;
    private final CachedMapFactory<String, Long> cmFactory;
    private final RankingRepository rankRepo;

    private final RankingScriptService rankScripts;

    private final ApplicationEventPublisher eventPublisher;
    private final RankingLinkageService rankLinkageService;
    private ActiveRanking[] activeRanks = EMPTY_RANKING_ARRAY;
    private List<RankResults> activeRanksNoScores;

    @SuppressWarnings({"rawtypes", "unchecked"})
    public RankingServiceImpl(UserManagementService uMgmt, CachedMapFactory<String, Long> cmFact,
                              LCacheFactory lcf, RankingRepository rR,
                              RankingScriptService rankScripts,
                              RankingLinkageService rankLinkSrv,
                              RankingConfiguration rankingConfiguration,
                              ApplicationEventPublisher eventPublisher) {

        this.uMgmt = uMgmt;
        this.rankingConfiguration = rankingConfiguration;
        this.cmFactory = cmFact;
        this.rankRepo = rR;
        this.rankLinkageService = rankLinkSrv;
        this.eventPublisher = eventPublisher;

        topRanksForUser = lcf.registerCacheLoader("topRanksByUser", 1000, 10, 60 * 2, (LCacheLoader<Long, Map<String, RankResults>>) this::getRanksForUserInternal);
        userIdToUserName = lcf.registerCacheLoader("userIdToName", 2000, 5, 60 * 15, (LCacheLoader<Long, String>) this::getUsernameForId);

        this.rankScripts = rankScripts;
        latestClosedRanksCache = lcf.registerCacheLoader("latestClosedRankings", 1000, 5, 73 * 60 * 60, (LCacheLoader<RankingId, RankResults>) this::getClosedRankResults);
        reloadRankings();
    }

    private @Nullable RankResults getClosedRankResults(RankingId rankId) {
        Optional<Ranking> optClRank = rankRepo.findById(rankId);
        if (optClRank.isPresent()) {
            Ranking ranking = optClRank.get();

            Pair<Integer, Set<Integer>> selectors = getSelectorsFromString(ranking.getSelector());

            List<RankUserEntry> entries = new ArrayList<>(0);
            if (ranking.getResults() != null && ranking.getResults().getResults() != null) {
                entries = ranking.getResults().getResults().stream().map(RankUserEntry::new).toList();
            }
            return new RankResults(
                    ranking.getTitle(),
                    ranking.getRankingType(),
                    ranking.getType(),
                    ranking.getBackground(),
                    // Vendor ID
                    selectors.getValue0(),
                    // GameIds
                    selectors.getValue1(),
                    ranking.getStartAt(),
                    ranking.getEndAt(),
                    ranking.getTitle(),
                    ranking.getEntryCost(),
                    ranking.getPricePool(),
                    entries,
                    null
            );
        } else {
            return null;
        }

    }

    private Pair<Integer, Set<Integer>> getSelectorsFromString(String selector) {
        if (selector.isBlank()) {
            return Pair.with(0, Collections.emptySet());
        }
        String[] comps = selector.split("=");
        if (comps.length != 2) {
            return Pair.with(0, Collections.emptySet());
        }

        if ("gid".equalsIgnoreCase(comps[0])) {
            String[] ids = comps[1].split(",");
            return Pair.with(-1, Arrays.stream(ids).map(Integer::parseInt).collect(Collectors.toSet()));
        } else {
            return Pair.with(Integer.parseInt(comps[1]), Collections.emptySet());
        }
    }


    private String getUsernameForId(final Long uid) {
        final var casinoUserById = uMgmt.getCasinoUserById(uid).orElse(null);
        return casinoUserById == null ? "unknown" : casinoUserById.getDisplayName();
    }

    @Override
    public List<String> getJoinedTournaments(long userId) {
        if (rankLinkageService != null) {
            return rankLinkageService.getCurrentRankIdsForUser(userId);
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> joinTournament(long userId, String rankId) {
        if (rankLinkageService != null) {
            //TODO: add handling for tournament token
            LocalDate rDate = LocalDate.parse(rankId.substring(0, rankId.indexOf(':')));
            rankLinkageService.addLinkage(rDate, rankId, userId, true);
            return rankLinkageService.getCurrentRankIdsForUser(userId);
        }
        return Collections.emptyList();
    }

    @Override
    public List<RankResults> getActiveRanks() {
        return this.activeRanksNoScores;
    }


    private RankResults createRankResults(ActiveRanking a) {
        return new RankResults(a.getSetname(), a.getrType(),
                a.getType(), a.getBackground(),
                a.getVendorId(), a.getGameIds(),
                Instant.ofEpochSecond(a.getStartTS()), Instant.ofEpochSecond(a.getEndTS()),
                a.getTitle(), a.getEntryCost(), a.getPricePool(),
                null, null);
    }

    @Override
    public Map<String, RankResults> getRanksForUser(long userId) {
        return topRanksForUser.get(userId);
    }

    /**
     * Testing the lua script which retrieves TOP N results from redis
     * <p>
     * KEY1
     * USER1
     * SCORE1
     * USER2
     * SCORE2
     * #####
     * KEY2
     * USER1
     * SCORE1
     * ...
     * #####
     * <p>
     * this will be parsed into Map of keys which contain lists of Pairs (userid,score)
     * So we have the topN entries per scoredSet
     * <p>
     * Next script would be KEYS, USERID which then gets the -1,userid,+1 ranks for each key
     * <p>
     * then we can create the user ranks.
     *
     * @return Map of the topn-lua script results
     */
    @Override
    public Map<String, List<RankingResult>> getTopRanksMulti() {
        final var nowTS = Instant.now().getEpochSecond();

        // Filter active ranks
        List<Object> keys =
                Arrays.stream(activeRanks).filter(ar -> ar.getEndTS() >= nowTS && ar.getStartTS() <= nowTS).map(ActiveRanking::getSetname).collect(Collectors.toList());
        // Get top N ranks+score per key/ranking
        var objects = rankScripts.getTopNScripts(keys, 4);
        // Now we would need to parse and cache the topN ranks per key
        return RankingUtils.parseTopNResults(objects);
    }

    @Override
    @Transactional
    public RankingResults closeRanking(LocalDate localDate, final String rankId) {

        Optional<Ranking> byId = rankRepo.findById(new RankingId(localDate, rankId));
        if (byId.isEmpty()) {
            return null;
        }

        if (byId.get().getRankingStatus() != RankingStatus.ACTIVE) {
            return null;
        }

        RankingResults rs = getCurrentRankingResults2(rankId);

        Ranking ranking = byId.get();
        ranking.setRankingStatus(RankingStatus.CLOSED);
        rs.setRewards(getRewards(rs, ranking, rankingConfiguration));
        ranking.setResults(rs);
        rankRepo.saveAndFlush(ranking);

        // now delete from redis
        cmFactory.createScoredSet(rankId, Long.class).clear();
        return rs;
    }

    @Override
    @Transactional
    public void rewardRanking(LocalDate localDate, final String rankId) {

        Optional<Ranking> byId = rankRepo.findById(new RankingId(localDate, rankId));
        if (byId.isEmpty()) {
            return;
        }

        if (byId.get().getRankingStatus() != RankingStatus.CLOSED) {
            return;
        }

        Ranking ranking = byId.get();

        awardWinnings(ranking);

        ranking.setRankingStatus(RankingStatus.CLOSED);
        rankRepo.saveAndFlush(ranking);

    }


    @Override
    public TournamentPositions getCurrentPositions(String rankId) {
        return rankScripts.getCachedPositions(rankId);
    }

    private void awardWinnings(Ranking ranking) {
        if (ranking.getResults() == null) {
            log.warn("Results for ranking are zero/empty - no winnings");
            return;
        }
        List<RankingResult> results = ranking.getResults().getResults();
        List<RankingReward> rewards = ranking.getResults().getRewards();

        int i = 0;
        List<AwardedRewardTicket> awardedTickets = new ArrayList<>(10);
        for (RankingReward r : rewards) {
            if (r.aSelector().startsWith("Rank")) {
                RankingResult rankingResult = results.get(i);
                AwardedRewardTicket arTicket = createAwardedRewardTicket(rankingResult.u(), r.reward(), i + 1, ranking);
                if (arTicket != null) {
                    awardedTickets.add(arTicket);
                }
            }
            i++;
        }
        log.info("Got {} awarded tickets for tournament {}", awardedTickets, ranking.getRankId());
        awardedTickets.forEach(eventPublisher::publishEvent);

    }

    private AwardedRewardTicket createAwardedRewardTicket(long u, double reward, int position, Ranking ranking) {
        if (reward > 1) {
            RewardTicketDetails details = new RewardTicketDetails("S#" + (int) reward, "Tournament", "You scored rank " + position, ranking.getRankId(), true);
            return new AwardedRewardTicket(u, ranking.getRankId(), RewardType.TOURNAMENT_WIN, Instant.now(), "PT48H", details);
        }
        return null;
    }


    private RankingResults getCurrentRankingResults2(String rankId) {

        RankingResults rs = new RankingResults();

        TournamentPositions currentPositions = rankScripts.getCurrentPositions(rankId);
        rs.setNumUsers(currentPositions.getPositions().size());

        List<RankingResult> results = new ArrayList<>(currentPositions.getPositions().size());
        for (int i = 0; i < currentPositions.getPositions().size(); i++) {
            TournamentPosition cp = currentPositions.getPositions().get(i);
            results.add(new RankingResult(cp.getUserId(), cp.getScore(), cp.getPosition(), cp.getLevel(), cp.getPlayername(), cp.getExtId()));
        }
        rs.setResults(results);
        RankingAggregation aggScoresOfRanking = rankScripts.getAggScoresOfRanking(rankId);
        rs.setSumScores(aggScoresOfRanking.getSum());
        return rs;
    }


    public double getSumScoresOfRanking(String rankId) {
        return rankScripts.getSumScoresOfRanking(rankId);
    }

    @Override
    public RankingAggregation getAggScoresOfRanking(String rankId) {
        return rankScripts.getAggScoresOfRanking(rankId);
    }

    @Override
    public List<RankingRewardDefinitionItem> getRankingRewardDefinitions() {
        return rankingConfiguration.getRewards();
    }

    @Override
    public List<RankResults> getLatestClosedRankings(int hoursBack, int offset, int length) {
        // TODO: also cache those finished pages (at least for 1-5 minutes)
        // so: a cache for the id lists so we do not query them on every access. We can flush those as soon
        // as we have a tournament-close event
        List<RankingId> closedRankingIds = rankRepo.getClosedRankingIds(hoursBack, offset, length);
        Map<RankingId, RankResults> all = latestClosedRanksCache.getAll(closedRankingIds);
        return all.values().stream().toList();
    }


    private static List<RankingReward> getRewards(RankingResults rs, Ranking ranking, RankingConfiguration rConfig) {
        return RankingRewardCalculator.calculateRewards(ranking.getRankingType(), rs, null, rConfig);
    }


    private Map<String, RankResults> getRanksForUserInternal(long userId) {

        Map<String, RankResults> ranks = new HashMap<>();
        var nowTS = Instant.now().getEpochSecond();
        for (var ar : activeRanks) {
            if (ar.getEndTS() >= nowTS && ar.getStartTS() <= nowTS) {
                ranks.put(ar.getSetname(), getRanksForUser(userId, ar));
            }
        }
        return ranks;
    }


    private RankResults getRanksForUser(long userId, ActiveRanking ar) {
        // Find top N results
        var scoredSet = scoredSets.computeIfAbsent(ar.getSetname(), a -> cmFactory.createScoredSet(ar.getSetname(), Long.class));
        var topEntries = scoredSet.getTopEntries(TOPENTRYCOUNT);
        if (topEntries.isEmpty()) {
            return new RankResults(ar.getSetname(), ar.getrType(), ar.getType(), ar.getBackground(),
                    ar.getVendorId(), ar.getGameIds(),
                    Instant.ofEpochSecond(ar.getStartTS()),
                    Instant.ofEpochSecond(ar.getEndTS()), "title", 0, 0, Collections.emptyList(), Collections.emptyList());
        }
        List<RankUserEntry> tops = new ArrayList<>(TOPENTRYCOUNT);
        var userInTop = false;
        var i = 0;
        for (var se : topEntries) {
            var uMatch = se.getKey() == userId;
            var rue = new RankUserEntry(se.getKey(), i, userIdToUserName.get(se.getKey()),
                    Math.round(se.getScore() * 100.0) / 100.0, uMatch);
            if (uMatch) {
                userInTop = true;
            }
            i++;
            tops.add(rue);
        }
        if (userInTop) {
            return new RankResults(ar.getSetname(), ar.getrType(), ar.getType(), ar.getBackground(),
                    ar.getVendorId(), ar.getGameIds(),
                    Instant.ofEpochSecond(ar.getStartTS()), Instant.ofEpochSecond(ar.getEndTS()), "title", 0, 0, tops, Collections.emptyList());
        }
        // Find trailing entries
        List<RankUserEntry> trail = new ArrayList<>(3);
        var rank = scoredSet.getRank(userId);
        if (rank >= 0) {
            var trailing = scoredSet.getRankEntries(rank - 1, rank + 1);
            i = rank - 1;
            for (var se : trailing) {
                var uMatch = se.getKey() == userId;
                var rue = new RankUserEntry(se.getKey(), i, userIdToUserName.get(se.getKey()), Math.round(se.getScore() * 100.0) / 100.0, uMatch);
                i++;
                trail.add(rue);
            }
        } else {
            trail = Collections.emptyList();
        }
        return new RankResults(ar.getSetname(), ar.getrType(), ar.getType(), ar.getBackground(),
                ar.getVendorId(), ar.getGameIds(),
                Instant.ofEpochSecond(ar.getStartTS()), Instant.ofEpochSecond(ar.getEndTS()), "title", 0, 0, tops, trail);
    }

    @Transactional
    @Override
    public void reloadRankings() {

        // remove old scoredSets
        var currentRankNames = Arrays.stream(this.activeRanks).map(ActiveRanking::getSetname).collect(Collectors.toSet());
        var cachedSets = new HashSet<>(scoredSets.keySet());
        cachedSets.removeAll(currentRankNames);
        cachedSets.forEach(scoredSets::remove);


        // load next rankings
        var endMin = Instant.now();
        var startMax = endMin.plus(24, ChronoUnit.HOURS);
        var newRankings =
                rankRepo.findAllRankingsForManager(endMin, startMax).stream().map(ActiveRanking::new).sorted().toList().toArray(EMPTY_RANKING_ARRAY);
        if (!Arrays.equals(newRankings, this.activeRanks)) {
            log.info("Added new rankings to active rankings: {}", (Object) newRankings);
            this.activeRanks = newRankings;
            this.activeRanksNoScores = Arrays.stream(newRankings).map(this::createRankResults).toList();
        }
    }

}

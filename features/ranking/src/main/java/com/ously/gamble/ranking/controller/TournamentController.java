package com.ously.gamble.ranking.controller;

import com.ously.gamble.api.rankings.*;
import com.ously.gamble.api.security.CurrentUser;
import com.ously.gamble.api.security.UserPrincipal;
import com.ously.gamble.conditions.ConditionalOnBackend;
import com.ously.gamble.ranking.service.RankingServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@ConditionalOnBackend
@Tag(name = "tournament-controller", description = "Tournaments controller")
@ConditionalOnProperty(prefix = "monitor.rankings", name = "enabled", havingValue = "true")
public class TournamentController {

    private final RankingService rankService;

    public TournamentController(@Autowired(required = false) RankingServiceImpl rnkSrv) {
        this.rankService = rnkSrv;
    }


    // get active Rankings

    @Operation(description = "active and upcoming rankings",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/api/app/tournaments/active")
    @RolesAllowed("USER")
    public ResponseEntity<List<RankResults>> getActiveRankings() {
        if (rankService == null) {
            return ResponseEntity.of(Optional.empty());
        }
        return ResponseEntity.ok(rankService.getActiveRanks());
    }

    @Operation(description = "latest finished rankings",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/api/app/tournaments/finished/{page}")
    @RolesAllowed("USER")
    public ResponseEntity<List<RankResults>> getClosedRankings(@PathVariable("page") int page) {
        if (rankService == null) {
            return ResponseEntity.of(Optional.empty());
        }
        return ResponseEntity.ok(rankService.getLatestClosedRankings(30 * 24, page * 10, 10));
    }


    //
    // get Users current rankings
    //
    @Operation(description = "user rankings retrieval",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/api/app/tournaments/user")
    @RolesAllowed("USER")
    public ResponseEntity<Map<String, RankResults>> getUserRankings(@CurrentUser UserPrincipal currentUser) {
        if (rankService == null) {
            return ResponseEntity.of(Optional.empty());
        }
        return ResponseEntity.ok(rankService.getRanksForUser(currentUser.getId()));
    }

    @Operation(description = "user rankings joinstatus",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/api/app/tournaments/join")
    @RolesAllowed("USER")
    public ResponseEntity<List<String>> getUserRankingsJoinStatus(@CurrentUser UserPrincipal currentUser) {
        if (rankService == null) {
            return ResponseEntity.of(Optional.empty());
        }
        return ResponseEntity.ok(rankService.getJoinedTournaments(currentUser.getId()));
    }

    @Operation(description = "add user ranking join",
            security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping(value = "/api/app/tournaments/join/{rId}")
    @RolesAllowed("USER")
    public ResponseEntity<List<String>> joinRanking(@CurrentUser UserPrincipal currentUser, @PathVariable("rId") String rankId) {
        if (rankService == null) {
            return ResponseEntity.of(Optional.empty());
        }
        return ResponseEntity.ok(rankService.joinTournament(currentUser.getId(), rankId));
    }


    /**
     * @param currentUser the user (not really needed)
     * @param rankId      the id of the tournament
     * @return the aggr. figures (count,min,max, sum)
     */

    @Operation(description = "aggregated tournament data",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/api/app/tournaments/agg/{rId}")
    @RolesAllowed("USER")
    public ResponseEntity<RankingAggregation> getTournamentAggStatus(@CurrentUser UserPrincipal currentUser, @PathVariable("rId") String rankId) {
        if (rankService == null) {
            return ResponseEntity.of(Optional.empty());
        }
        return ResponseEntity.ok(rankService.getAggScoresOfRanking(rankId));
    }

    /**
     * These methods are for getting current ranks for one tournament
     */
    @Operation(description = "the ranks of a tournament",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/api/app/tournaments/scores/{rId}")
    @RolesAllowed("USER")
    public ResponseEntity<TournamentPositions> getTournamentPositions(@CurrentUser UserPrincipal currentUser, @PathVariable("rId") String rankId) {
        if (rankService == null) {
            return ResponseEntity.of(Optional.empty());
        }
        return ResponseEntity.ok(rankService.getCurrentPositions(rankId));
    }


    @Operation(description = "ranking rewards definition items",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/api/app/tournaments/rdef")
    @RolesAllowed("USER")
    public ResponseEntity<List<RankingRewardDefinitionItem>> getRewardDefinitions() {
        if (rankService == null) {
            return ResponseEntity.of(Optional.empty());
        }
        return ResponseEntity.ok(rankService.getRankingRewardDefinitions());
    }
}

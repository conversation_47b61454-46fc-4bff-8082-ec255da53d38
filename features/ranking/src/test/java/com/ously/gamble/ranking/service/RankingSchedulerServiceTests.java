package com.ously.gamble.ranking.service;

import com.ously.gamble.api.rankings.*;
import com.ously.gamble.ranking.persistence.repository.RankingScheduleRepository;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;

@SuppressWarnings("unused")
public class RankingSchedulerServiceTests {

    @Test
    void testTimeFormatter() {
        var rSched = new RankingSchedulerServiceImpl(null, null, null, null, null);
        assertEquals("000000", RankingSchedulerServiceImpl.formatTime(LocalTime.of(0, 0, 0)));
    }


    private List<Long> getGameList1() {
        return Arrays.asList(100L, 200L, 300L, 400L, 500L);
    }


    @Test
    public void testScheduling1() {

        RankingScheduleRepository rsRepo = Mockito.mock(RankingScheduleRepository.class);
        Mockito.when(rsRepo.getLastReleased(anyInt())).thenReturn(getGameList1());
        Mockito.when(rsRepo.getTopSpinned(anyInt(), anyInt())).thenReturn(getGameList1());
        Mockito.when(rsRepo.getHighestRtp(anyInt(), anyInt())).thenReturn(getGameList1());
        Mockito.when(rsRepo.getLowestRtp(anyInt(), anyInt())).thenReturn(getGameList1());
        TournamentCreationServiceImpl tcsI = new TournamentCreationServiceImpl(rsRepo);


        var rSched = new RankingSchedulerServiceImpl(null, null, null, null, null);

        var aSchedules = Collections.singletonList(
                new RankingScheduleDto(1, true, LocalDate.of(2020, 2, 1), LocalDate.of(2024, 10, 1),
                        RankingSchedulePeriod.DAILY, "24H",
                        new RankingDefinition(RankingType.WAGER_SUM, TournamentPoolType.RANDOM))
        );

        var bSchedules = Collections.singletonList(

                new RankingScheduleDto(2, true, LocalDate.of(2020, 2, 1), LocalDate.of(2024, 10, 1),
                        RankingSchedulePeriod.HOURLY, "60M",
                        new RankingDefinition(RankingType.WIN_MAX, TournamentPoolType.RANDOM)
                ));

        var m15Schedules = Collections.singletonList(

                new RankingScheduleDto(2, true, LocalDate.of(2020, 2, 1), LocalDate.of(2024, 10, 1),
                        RankingSchedulePeriod.MIN15, "15M",
                        new RankingDefinition(RankingType.WIN_MAX, TournamentPoolType.RANDOM)
                ));


        // First round (no rankings defined yet)
        var rankingDtos = RankingSchedulerServiceImpl.scheduleNewRankings(aSchedules, Collections.emptyList(), tcsI);
        assertEquals(1, rankingDtos.size());

        // Second round (
        rankingDtos = RankingSchedulerServiceImpl.scheduleNewRankings(aSchedules, rankingDtos, tcsI);
        assertEquals(0, rankingDtos.size());

        // First round of HOURLY (no rankings defined yet)
        var rankingDtos2 = RankingSchedulerServiceImpl.scheduleNewRankings(bSchedules, Collections.emptyList(), tcsI);
        assertEquals(1, rankingDtos2.size());

        // Second round of HOURLY (
        rankingDtos2 = RankingSchedulerServiceImpl.scheduleNewRankings(bSchedules, rankingDtos2, tcsI);
        assertEquals(0, rankingDtos2.size());

        // First round of 15M (no rankings defined yet)
        var rankingDtos3 = RankingSchedulerServiceImpl.scheduleNewRankings(m15Schedules, Collections.emptyList(), tcsI);
        assertEquals(1, rankingDtos3.size());

        // Second round of 15M (
        rankingDtos3 = RankingSchedulerServiceImpl.scheduleNewRankings(m15Schedules, rankingDtos3, tcsI);
        assertEquals(0, rankingDtos3.size());


    }


}

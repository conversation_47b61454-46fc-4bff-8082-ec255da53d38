package com.ously.gamble.payment.manager.persistence.repository;

import com.ously.gamble.payment.manager.persistence.idclasses.PaymentDepositStepId;
import com.ously.gamble.payment.manager.persistence.model.PaymentDepositStep;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ConditionalOnProperty(prefix = "payment.manager", name = "enabled", havingValue = "true")
public interface PaymentDepositStepRepository extends JpaRepository<PaymentDepositStep, PaymentDepositStepId> {

    @Query(nativeQuery = true,value = "select coalesce(max(seq),0)+1 from payment_deposit_step " +
                                      "where user_id=?1 and deposit_id=?2")
    int getNextSeqForStep(long user_id, String deposit_id);

    List<PaymentDepositStep> findAllByUserIdAndDepositIdOrderBySeq(long userId,String depositId);
}

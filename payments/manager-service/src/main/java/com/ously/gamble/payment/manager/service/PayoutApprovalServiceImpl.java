package com.ously.gamble.payment.manager.service;

import com.ously.gamble.api.user.EUserTag;
import com.ously.gamble.api.user.UserManagementService;
import com.ously.gamble.payment.api.PayoutApprovalService;
import com.ously.gamble.payment.api.PayoutManagementService;
import com.ously.gamble.payment.manager.config.PaymentManagerConfig;
import com.ously.gamble.payment.manager.configprops.PaymentLimitsConfig;
import com.ously.gamble.payment.payload.payout.ApprovalStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

@Service
@ConditionalOnBean(PaymentManagerConfig.class)
public class PayoutApprovalServiceImpl implements PayoutApprovalService {

    private static final Logger log = LoggerFactory.getLogger(PayoutApprovalServiceImpl.class);
    private final PaymentLimitsConfig pLimitsConfig;
    private final PayoutManagementService poMgmtService;
    private final UserManagementService usMgmtService;


    @SuppressWarnings("ResultOfMethodCallIgnored")
    public PayoutApprovalServiceImpl(
            PaymentLimitsConfig pLimitsConfig,
            PayoutManagementService pomService,
            UserManagementService usMgmtS) {
        this.pLimitsConfig = pLimitsConfig;
        this.poMgmtService = pomService;
        this.usMgmtService = usMgmtS;
        // init settings in db
        pLimitsConfig.getDailyPayoutLimit();
        pLimitsConfig.getSinglePayoutLimit();
        pLimitsConfig.getTotalKycLimit();
        pLimitsConfig.getWeeklyPayoutLimit();
        pLimitsConfig.getTotalSowLimit();
        pLimitsConfig.getVipLimitMultiplier();

    }

    @Override
    @Transactional
    public ApprovalStatus checkApprovalStatus(long userId, BigDecimal amount, boolean preCheck) {
        // Get user (and tags)
        var optCUser = usMgmtService.getCasinoUserById(userId);
        if (optCUser.isEmpty()) {
            return ApprovalStatus.DECLINED_UNKNOWN;
        }

        var cUser = optCUser.get();

        // If user is tagged with "MANUAL_PAYOUT", then


        // Get sum of PAYOUTs (CREATED,PENDING,SUCCESS,SENDING)...
        var periodSums = poMgmtService.getPayoutSums(userId);

        // user tags
        var kycDone = cUser.getTags().contains(EUserTag.KYC_DONE.name());
        var vipUser = cUser.getTags().contains(EUserTag.VIP.name());
        var manPayoutApproval = cUser.getTags().contains(EUserTag.MANUAL_PAYOUT.name());

        if (manPayoutApproval) {
            return ApprovalStatus.MANUAL_APPROVAL;
        }

        // amounts, vip have double the limits
        var vipMult = Math.max(1, pLimitsConfig.getVipLimitMultiplier());

        var total =
                (periodSums.total().doubleValue() + ((preCheck) ? amount.doubleValue() : 0.0d)) / ((vipUser) ? vipMult : 1);
        if (total > pLimitsConfig.getTotalKycLimit() && !kycDone) {
            return ApprovalStatus.KYC_NEEDED;
        }

        // Single payout limit check
        if (amount.doubleValue() > pLimitsConfig.getSinglePayoutLimit()) {
            log.info("Payout MANUAL_APPROVAL due to amount (USD {}) higher than single payout limit {}. ", amount,
                    pLimitsConfig.getSinglePayoutLimit());
            return ApprovalStatus.MANUAL_APPROVAL;
        }
        // daily payout limit check
        var day =
                (periodSums.day().doubleValue() + ((preCheck) ? amount.doubleValue() : 0.0d)) / ((vipUser) ? vipMult : 1);
        if (day > pLimitsConfig.getDailyPayoutLimit()) {
            return ApprovalStatus.MANUAL_APPROVAL;
        }

        // weekly payout limit check
        var week =
                (periodSums.week().doubleValue() + ((preCheck) ? amount.doubleValue() : 0.0d)) / ((vipUser) ? vipMult : 1);
        if (week > pLimitsConfig.getWeeklyPayoutLimit()) {
            return ApprovalStatus.MANUAL_APPROVAL;
        }

        // monthly payout limit check
        var month =
                (periodSums.month().doubleValue() + ((preCheck) ? amount.doubleValue() : 0.0d)) / ((vipUser) ? vipMult : 1);
        if (month > pLimitsConfig.getWeeklyPayoutLimit()) {
            return ApprovalStatus.MANUAL_APPROVAL;
        }

        return ApprovalStatus.APPROVED;
    }


}

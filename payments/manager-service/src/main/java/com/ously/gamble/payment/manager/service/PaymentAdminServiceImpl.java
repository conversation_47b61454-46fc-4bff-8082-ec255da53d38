package com.ously.gamble.payment.manager.service;

import com.ously.gamble.payment.api.*;
import com.ously.gamble.payment.manager.config.PaymentManagerConfig;
import com.ously.gamble.payment.payload.deposit.UserDeposit;
import com.ously.gamble.payment.payload.deposit.UserDepositStep;
import com.ously.gamble.payment.payload.payout.UserPayout;
import com.ously.gamble.payment.payload.payout.UserPayoutStep;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@ConditionalOnBean(PaymentManagerConfig.class)
@Service
public class PaymentAdminServiceImpl implements PaymentAdminService {
    private final DepositManagementService depositMgmtService;
    private final PayoutManagementService payoutMgmtService;
    private final PaymentCallbackService callbackService;

    PaymentAdminServiceImpl(DepositManagementService dpMgmtSrv, PayoutManagementService poMgmtSrv,
                            PaymentCallbackService callbackService) {
        this.depositMgmtService = dpMgmtSrv;
        this.payoutMgmtService = poMgmtSrv;
        this.callbackService = callbackService;
    }

    /**
     * Research
     */

    @Override
    public Page<UserDeposit> getDepositsForUser(Long userId, LocalDate from,
                                                LocalDate to, Pageable pageable) {
        return depositMgmtService.getDepositsForUser(userId, from, to, pageable);
    }

    @Override
    public Page<UserDeposit> getAllDeposits(LocalDate from,
                                            LocalDate to, Pageable pageable) {
        return depositMgmtService.getAllDeposits(from, to, pageable);
    }

    @Override
    public List<UserDepositStep> getDepositStepsForUserAndDepositId(Long userId, String depositId) {
        return depositMgmtService.getStepsForDeposit(userId, depositId);
    }


    @Override
    public Page<UserPayout> getPayoutsForUser(Long userId, LocalDate from,
                                              LocalDate to, Pageable pageable) {
        return payoutMgmtService.getPayoutsForUser(userId, from, to, pageable);
    }

    @Override
    public List<UserPayoutStep> getPayoutStepsForUserAndPayoutId(Long userId, String payoutId) {
        return payoutMgmtService.getStepsForPayout(userId, payoutId);
    }

    @Override
    public Optional<PaymentCallback> getNotification(String notificationMd5) {
        return callbackService.getCallback(notificationMd5);
    }

    @Override
    public Optional<PaymentCallback> requeueNotification(String notificationMd5) {
        return callbackService.requeueCallback(notificationMd5);
    }

    @Override
    public Page<UserPayout> getPayoutsNeedingApproval(Pageable pageable) {
        return payoutMgmtService.getPayoutsNeedingApproval(pageable);
    }

    @Override
    public Page<UserPayout> getPayoutsNeedingIntervention(Pageable pageable) {
        return payoutMgmtService.getPayoutsNeedingIntervention(pageable);
    }

    @Override
    public Page<UserPayout> getValidPayouts(LocalDate from,
                                            LocalDate to, Pageable pageable) {
        return payoutMgmtService.getValidPayouts(from, to, pageable);
    }

}

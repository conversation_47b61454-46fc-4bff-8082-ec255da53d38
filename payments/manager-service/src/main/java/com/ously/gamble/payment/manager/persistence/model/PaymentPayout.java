package com.ously.gamble.payment.manager.persistence.model;

import com.ously.gamble.payment.api.PaymentStatus;
import com.ously.gamble.payment.manager.persistence.idclasses.PaymentPayoutId;
import com.ously.gamble.payment.payload.payout.PayoutPeriodSums;
import jakarta.persistence.*;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;

@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "payment_payout")
@IdClass(PaymentPayoutId.class)

@NamedNativeQuery(name = "PaymentPayout.getPayoutSums", query = """
        select coalesce(sum(IF(created_at < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 1 DAY), 0, amount)),0)   as day,
               coalesce(sum(IF(created_at < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 7 DAY), 0, amount)),0)   as week,
               coalesce(sum(IF(created_at < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 1 MONTH), 0, amount)),0) as month,
               coalesce(sum(amount),0)                                                                    as total
        from payment_payout
        where user_id = ?1
          and status not in ('ABORTED')
        """, resultSetMapping = "PaymentPayout.PayoutPeriodSums")
@SqlResultSetMapping(name = "PaymentPayout.PayoutPeriodSums",
        classes = @ConstructorResult(targetClass = PayoutPeriodSums.class,
                columns = {
                        @ColumnResult(name = "day", type =
                                BigDecimal.class),
                        @ColumnResult(name = "week", type =
                                BigDecimal.class),
                        @ColumnResult(name = "month", type =
                                BigDecimal.class),
                        @ColumnResult(name = "total", type =
                                BigDecimal.class)
                }))


public class PaymentPayout implements Persistable<PaymentPayoutId> {

    @Transient
    boolean wasLoaded;

    @PostLoad
    @PrePersist
    public void setTransientLoaded() {
        this.wasLoaded = true;
    }


    @Id
    @Column(name = "user_id")
    long userId;

    @Id
    @Column(name = "payout_id", length = 200)
    String payoutId;

    @Version
    @Column(name = "version")
    int version = 1;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    PaymentStatus status;

    @Column(name = "handler")
    String handler;

    @Column(name = "amount")
    BigDecimal amount = BigDecimal.ZERO;

    @Column(name = "fee")
    BigDecimal fee = BigDecimal.ZERO;

    @Column(name = "conv_rate")
    BigDecimal conversionRate = BigDecimal.ZERO;

    @Column(name = "currency")
    String currency;

    @Column(name = "currency_to")
    String currencyTo;

    @Column(name = "book_id")
    Long internalTransactionId;

    @Column(name = "address", length = 128)
    String address;

    @Column(name = "tag", length = 50)
    String tag;

    @Column(name = "created_at")
    Instant createdAt;

    @Column(name = "updated_at")
    Instant updatedAt;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Long getInternalTransactionId() {
        return internalTransactionId;
    }

    public void setInternalTransactionId(Long internalTransactionId) {
        this.internalTransactionId = internalTransactionId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }


    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public PaymentStatus getStatus() {
        return status;
    }

    public void setStatus(PaymentStatus status) {
        this.status = status;
    }

    public String getHandler() {
        return handler;
    }

    public void setHandler(String handler) {
        this.handler = handler;
    }

    @Override
    public PaymentPayoutId getId() {
        return new PaymentPayoutId(userId, payoutId);
    }

    @Override
    public boolean isNew() {
        return !wasLoaded;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public String getPayoutId() {
        return payoutId;
    }

    public void setPayoutId(String payoutId) {
        this.payoutId = payoutId;
    }

    public String getCurrencyTo() {
        return currencyTo;
    }

    public void setCurrencyTo(String currencyTo) {
        this.currencyTo = currencyTo;
    }
}

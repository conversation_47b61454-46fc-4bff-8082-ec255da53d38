package com.ously.gamble.payment.manager.persistence.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.payment.api.handler.info.PaymentContext;
import org.junit.jupiter.api.Test;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class PaymentPayoutStepTest {

    @Test
    void deserContext() throws IOException {
        ObjectMapper om = new ObjectMapper();

        PaymentContext paymentContext = om.readValue(PaymentPayoutStep.class.getResourceAsStream("/payload/pmnt_ctx_1.json"), PaymentContext.class);
        assertNotNull(paymentContext);
        assertEquals("549", paymentContext.getAttributes().getOrDefault("adminId", 0L).toString());

    }

}
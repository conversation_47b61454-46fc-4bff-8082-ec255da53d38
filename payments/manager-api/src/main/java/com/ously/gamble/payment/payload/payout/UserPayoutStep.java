package com.ously.gamble.payment.payload.payout;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.InstantSerializer;
import com.ously.gamble.payment.api.PaymentStatus;
import com.ously.gamble.payment.api.handler.info.PaymentContext;

import java.time.Instant;

public record UserPayoutStep(
        long userId,
        String payoutId,
        int step,
        @JsonSerialize(using = InstantSerializer.class)
        Instant createdAt,
        PaymentStatus before,
        PaymentStatus after,
        PaymentContext ctx,
        String notificationMd5
) {
}

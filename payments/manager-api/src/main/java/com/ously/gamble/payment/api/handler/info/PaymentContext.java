package com.ously.gamble.payment.api.handler.info;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ously.gamble.payment.api.actions.PaymentAction;

import java.time.Instant;
import java.util.*;

public class PaymentContext {
    ActionStatus currentStatus = ActionStatus.OK;
    List<ActionLog> actionLogs = new ArrayList<>(0);
    Map<String, Object> attributes = new HashMap<>(0);

    @JsonIgnore
    public void addActionLog(ActionLog log) {
        if (actionLogs == null) {
            actionLogs = new ArrayList<>(1);
        }
        actionLogs.add(log);
        if (currentStatus != ActionStatus.ERROR) {
            currentStatus = log.status();
        }
    }

    @JsonIgnore
    public ActionLog getLastActionLog() {
        if (actionLogs == null || actionLogs.isEmpty()) {
            return null;
        }
        return actionLogs.getLast();
    }

    public ActionStatus getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(ActionStatus currentStatus) {
        this.currentStatus = currentStatus;
    }

    public List<ActionLog> getActionLogs() {
        return actionLogs;
    }

    public void setActionLogs(List<ActionLog> actionLogs) {
        this.actionLogs = actionLogs;
    }

    public Map<String, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }

    @JsonIgnore
    public void addLogEntry(PaymentAction action, ActionStatus error,
                            String descr, Object... params) {


        for (var p : params) {
            descr = descr.replaceFirst("[{]}", p.toString());
        }

        var actionLogEntry = new ActionLogEntry(Instant.now(), error, descr,
                Collections.emptyMap());
        for (var pLog : actionLogs) {
            if (Objects.equals(pLog.action(), action.getClass().getName())) {
                pLog.logEntries().add(actionLogEntry);
                return;
            }
        }
        List<ActionLogEntry> nLog = new ArrayList<>(1);
        nLog.add(actionLogEntry);
        actionLogs.add(new ActionLog(action.getClass().getName(), error, nLog));
        if (error == ActionStatus.ERROR) {
            this.currentStatus = error;
        }
    }
}

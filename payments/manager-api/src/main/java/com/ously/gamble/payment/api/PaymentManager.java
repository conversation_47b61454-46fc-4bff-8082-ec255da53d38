package com.ously.gamble.payment.api;

import com.ously.gamble.payment.api.handler.PaymentHandler;
import com.ously.gamble.payment.payload.callback.Callback;
import com.ously.gamble.payment.payload.deposit.DepositDetails;
import com.ously.gamble.payment.payload.deposit.DepositMethod;
import com.ously.gamble.payment.payload.deposit.UserDeposit;
import com.ously.gamble.payment.payload.payout.*;
import com.ously.gamble.persistence.model.session.Jurisdiction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * The Basic Service used to handle payout/deposit requests
 */
public interface PaymentManager {


    /**
     * @param userId the userid
     * @return the payment stats of a user
     */
    UserPaymentStatistic getPaymentStatisticsForUser(long userId);


    /**
     * @return all active handlers
     */
    Collection<PaymentHandler> getActiveHandlers();

    /**
     * @param jd jurisdiction
     * @return all handlers available in given jurisdiction
     */
    Collection<PaymentHandler> getActiveHandlersForJurisdiction(Jurisdiction jd);

    /**
     * @param userId   id of user
     * @param pageable pageable infos
     * @return Page of UserDeposits
     */
    Page<UserDeposit> getDeposits(Long userId, LocalDate from,
                                  LocalDate to, Pageable pageable);

    UserDeposit getDeposit(String depositId);

    /**
     * @param userId id of user
     * @return list of payouts of given user
     */
    Page<UserPayout> getPayouts(Long userId, LocalDate from,
                                LocalDate to, Pageable pageable);

    List<UserDepositAddress> getUserDepositAddresses(Long userId);

    /**
     * @return list of DepositMethods available
     */
    Collection<DepositMethod> getAvailableDepositMethods();

    /**
     * @param userId   id of req. user
     * @param pmntName method name
     * @return details for a deposit
     */
    Optional<DepositDetails> getDepositDetails(long userId, String pmntName);

    /**
     * @return available payout methods and open wager
     */
    PayoutInfo getPayoutInfo(long userId);

    /**
     * @param hnAction the callback which needs to be handled
     */
    void handleCallback(Callback hnAction);

    /**
     * @param userId     id of user requesting details
     * @param pmntMethod the payout method selected
     * @return Details of selected payout method
     */
    Optional<PayoutDetails> getPayoutDetails(Long userId, String pmntMethod);

    /**
     * method to select and verify/confirm a payout.
     *
     * @param userId id of user
     * @param req    the payout request
     * @return result of payout-request
     */
    Optional<PayoutResult> requestPayout(long userId, PayoutRequest req);

    void performExecutePayout(PayoutExecuteRequest req);

    void performPayoutApproval(PayoutApprovalRequest req, Long id);

    void performPayoutAbort(PayoutAbortRequest req, Long id);

    Optional<UserPayout> getUserPayout(long userId, String payoutId);
}

package com.ously.gamble.payment.payload.deposit;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.InstantSerializer;
import com.ously.gamble.payment.api.PaymentStatus;
import com.ously.gamble.payment.api.handler.info.PaymentContext;

import java.time.Instant;

public record UserDepositStep(long userId,
                              String depositId,
                              int step,
                              @JsonSerialize(using = InstantSerializer.class)
                              Instant createdAt,
                              PaymentStatus before,
                              PaymentStatus after,
                              PaymentContext ctx,
                              String notificationMd5
                              ) {
}

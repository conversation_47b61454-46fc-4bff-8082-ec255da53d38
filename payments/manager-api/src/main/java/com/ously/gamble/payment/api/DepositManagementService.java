package com.ously.gamble.payment.api;

import com.ously.gamble.payment.payload.deposit.UserDeposit;
import com.ously.gamble.payment.payload.deposit.UserDepositStep;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * internal DepositMgmt Service. Not exposed to Controller
 */
public interface DepositManagementService {
    Page<UserDeposit> getDepositsForUser(Long userId, LocalDate from,
                                         LocalDate to, Pageable pageable);

    UserDeposit getDeposit(String depositId);

    Page<UserDeposit> getAllDeposits(LocalDate from, LocalDate to, Pageable pageable);

    List<UserDepositStep> getStepsForDeposit(Long userId, String depositId);
}

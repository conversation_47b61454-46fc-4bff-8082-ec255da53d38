package com.ously.gamble.payment.api.actions.handler;

import com.ously.gamble.payment.api.actions.AbstractAction;
import com.ously.gamble.payment.api.actions.ActionType;
import com.ously.gamble.payment.api.actions.HandlerAction;
import com.ously.gamble.payment.payload.PMContext;

public abstract class AbstractHandlerAction extends AbstractAction implements HandlerAction {

    protected AbstractHandlerAction() {
        this(null, null);
    }

    protected AbstractHandlerAction(ActionType type) {
        this(type, null);
    }

    protected AbstractHandlerAction(ActionType type, PMContext context) {
        super(type, context);
    }

}

package com.ously.gamble.payment.mobile.service;

import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.model.InAppProduct;
import com.google.api.services.androidpublisher.model.InappproductsListResponse;
import com.ously.gamble.api.store.StoreInfoService;
import com.ously.gamble.api.store.StoreProductDefinition;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.payment.mobile.config.PlaystoreConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@ConditionalOnOffloader
public class PlaystoreInfoServiceImpl implements StoreInfoService {

    private static final BigDecimal BD1MIO = new BigDecimal(1_000_000);

    private final AndroidPublisher publisher;
    private final PlaystoreConfig psConfig;
    private final Logger log = LoggerFactory.getLogger(PlaystoreInfoServiceImpl.class);


    public PlaystoreInfoServiceImpl(AndroidPublisher playstorePublisher, PlaystoreConfig psConfig) {
        this.publisher = playstorePublisher;
        this.psConfig = psConfig;
    }

    @Override
    public String getStoreName() {
        return "Playstore";
    }

    @Override
    public List<StoreProductDefinition> getProductDefinitions() {
        log.info("Get Google Packages");
        try {
            AndroidPublisher.Inappproducts.List list = publisher.inappproducts().list(psConfig.getPackageName());
            InappproductsListResponse execute = list.execute();
            List<StoreProductDefinition> result = new ArrayList<>(10);
            for (InAppProduct iap : execute.getInappproduct()) {
                result.add(new StoreProductDefinition(
                        iap.getSku(),
                        new BigDecimal(iap.getDefaultPrice().getPriceMicros()).divide(BD1MIO, 2, RoundingMode.DOWN),
                        "active".equalsIgnoreCase(iap.getStatus())));
            }
            return result;
        } catch (Exception e) {
            log.warn("Cannot retrieve Google Store product definitions", e);
            return Collections.emptyList();
        }
    }
}

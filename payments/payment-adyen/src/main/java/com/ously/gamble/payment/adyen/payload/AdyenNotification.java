package com.ously.gamble.payment.adyen.payload;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import java.util.List;

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@JsonInclude(Include.NON_NULL)
public class AdyenNotification {
    String live;
    List<AdyenNotificationRequestItemContainer> notificationItems;

    public String getLive() {
        return live;
    }

    public void setLive(String live) {
        this.live = live;
    }

    public List<AdyenNotificationRequestItemContainer> getNotificationItems() {
        return notificationItems;
    }

    public void setNotificationItems(List<AdyenNotificationRequestItemContainer> notificationItems) {
        this.notificationItems = notificationItems;
    }

    @Override
    public String toString() {
        return "AdyenNotification{" +
                "live='" + live + '\'' +
                ", notificationItems=" + notificationItems.toString() +
                '}';
    }
}

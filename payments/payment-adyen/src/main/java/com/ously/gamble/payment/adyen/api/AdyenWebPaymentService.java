package com.ously.gamble.payment.adyen.api;

import com.adyen.model.checkout.PaymentMethodsResponse;
import com.adyen.model.checkout.PaymentsResponse;
import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.payload.purchase.*;
import com.ously.gamble.payment.adyen.payload.AdyenNotification;
import com.ously.gamble.persistence.model.Purchase;

import java.math.BigDecimal;

public interface AdyenWebPaymentService {

    PaymentMethodsResponse requestPaymentMethods(String country, String locale, String currency, BigDecimal amount, long userId) throws Exception;

    DropInPaymentResponse<PaymentsResponse> additionalData(PaymentDetailRequest pd, long userId) throws Exception;

    DropInPaymentResponse<PaymentsResponse> paymentRequest(DropInPaymentRequest req, long userId) throws Exception;

    void processNotification(AdyenNotification notification) throws OuslyTransactionException;

    CheckPaymentResult checkPurchase(String payload, String orderRef, long id);

    void abortPurchase(String payload, String orderRef, long id);

    RefundOrCancelResponse cancelPayment(RefundOrCancelRequest req, Purchase p);
}

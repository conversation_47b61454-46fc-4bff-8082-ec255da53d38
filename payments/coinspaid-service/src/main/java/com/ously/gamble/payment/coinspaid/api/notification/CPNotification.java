package com.ously.gamble.payment.coinspaid.api.notification;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public record CPNotification(
        @JsonProperty("id") String id,
        @JsonProperty("type") CPNotificationType type,
        @JsonProperty("crypto_address") CPCryptoAddress cryptoAddress,
        @JsonProperty("currency_sent")CPNotificationCurrency currencySent,
        @JsonProperty("currency_received")CPNotificationCurrency currencyReceived,
        @JsonProperty("transactions")List<CPNotificationTransaction> transactions,
        @JsonProperty("fees") List<CPNotificationFee> fees,
        @JsonProperty("error")String error,
        @JsonProperty("status")CPNotificationStatus status,
        @JsonProperty("foreign_id")String foreignId

) {
}

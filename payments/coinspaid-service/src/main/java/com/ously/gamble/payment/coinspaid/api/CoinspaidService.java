package com.ously.gamble.payment.coinspaid.api;

import com.ously.gamble.payment.coinspaid.persistence.model.UserAddress;
import com.ously.gamble.payment.payload.PMCurrency;
import com.ously.gamble.payment.payload.deposit.DepositAddress;
import com.ously.gamble.payment.payload.deposit.DepositMethod;
import com.ously.gamble.payment.payload.payout.PayoutDetails;
import com.ously.gamble.payment.payload.payout.PayoutMethod;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface CoinspaidService {
    //    List<? extends ManagerAction> performAction(HandlerAction action, PaymentContext ctx);
    Collection<DepositMethod> getMethodsForTargetCurrency(PMCurrency targetCurrency);

    Optional<BigDecimal> getDepositExchangeRate(String currency);

    Optional<DepositAddress> getDepositAddressFor(long userId, String currency);

    Collection<PayoutMethod> getMethodsForPayout(PMCurrency srcCurrency);

    List<UserAddress> getUserDepositAddresses(long userId);

    Optional<PayoutDetails> getPayoutDetailsFor(Long userId, String currency);
}

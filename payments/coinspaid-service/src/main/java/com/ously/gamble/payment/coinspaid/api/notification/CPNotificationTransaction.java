package com.ously.gamble.payment.coinspaid.api.notification;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * "id": 296321,
 * "currency": "XRP",
 * "transaction_type": "blockchain",
 * "type": "deposit",
 * "address": "rDMN7BDio5NQ3X6i2Ucobzta758kgJTSzE",
 * "tag": "1130680",
 * "amount": "990.00000000",
 * "txid": "EF9B6CDD5E1B26DF198442EB823C32C4A09AB51134C0BCCF1DFCCA5747D91870",
 * "confirmations": "4"
 */


public record CPNotificationTransaction(
        @JsonProperty("id") String id,
        @JsonProperty("currency") String currency,
        @JsonProperty("currency_to") String currencyTo,
        @JsonProperty("transaction_type") CPNotificationTransactionType transactionType,
        @JsonProperty("type") CPNotificationType type,
        @JsonProperty("address") String address,
        @JsonProperty("tag") String tag,
        @JsonProperty("amount") String amount,
        @JsonProperty("amount_to") String amountTo,
        @JsonProperty("txid") String txId,
        @JsonProperty("riskscore") String riskScore,
        @JsonProperty("confirmations") String confirmations
) {

    @JsonIgnore
    public String uniqueId() {
        return txId + "-" + id;
    }

}

package com.ously.gamble.payment.coinspaid.service;

import com.ously.gamble.payment.api.actions.ActionType;
import com.ously.gamble.payment.api.actions.HandlerAction;
import com.ously.gamble.payment.api.actions.ManagerAction;
import com.ously.gamble.payment.api.handler.PaymentHandler;
import com.ously.gamble.payment.api.handler.PaymentHandlerInfo;
import com.ously.gamble.payment.api.handler.info.ActionStatus;
import com.ously.gamble.payment.api.handler.info.PaymentContext;
import com.ously.gamble.payment.coinspaid.api.ActionHandler;
import com.ously.gamble.payment.coinspaid.api.CoinspaidService;
import com.ously.gamble.payment.coinspaid.config.CoinspaidConfig;
import com.ously.gamble.payment.payload.PMCurrency;
import com.ously.gamble.payment.payload.deposit.DepositDetails;
import com.ously.gamble.payment.payload.deposit.DepositMethod;
import com.ously.gamble.payment.payload.payout.PayoutDetails;
import com.ously.gamble.payment.payload.payout.PayoutMethod;
import com.ously.gamble.payment.payload.payout.UserDepositAddress;
import com.ously.gamble.persistence.model.session.Jurisdiction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ously.gamble.payment.api.handler.info.ActionStatus.OK;

@Component
@ConditionalOnBean(CoinspaidConfig.class)
public class CoinspaidPaymentHandler implements PaymentHandler {
    private final Logger log = LoggerFactory.getLogger(CoinspaidPaymentHandler.class);
    private final PaymentHandlerInfo phInfo = new PaymentHandlerInfo(CoinspaidConfig.HANDLERNAME, "1.0.1",
            new Jurisdiction[]{Jurisdiction.CPT});

    private final CoinspaidService cpService;
    private final Map<ActionType, ActionHandler> actionHandlersByType;

    public CoinspaidPaymentHandler(
            CoinspaidService cpS,
            List<ActionHandler> handlers

    ) {
        this.cpService = cpS;
        this.actionHandlersByType =
                handlers.stream().collect(Collectors.toMap(ActionHandler::getHandledActionType, Function.identity()));
    }

    @Override
    public PaymentHandlerInfo getInfo() {
        return phInfo;
    }

    @Override
    public List<ManagerAction> performActions(List<HandlerAction> actions, PaymentContext ctx) {
        log.info("Got {} actions to perform", actions.size());

        var currentStatus = OK;
        List<ManagerAction> resultActions = new ArrayList<>(1);
        for (var action : actions) {
            if (currentStatus == ActionStatus.ERROR) {
                break;
            }
            resultActions.addAll(performAction(action, ctx));
            switch (ctx.getCurrentStatus()) {
                case INFO -> log.info("Action {} -> {}", action.getType(), ctx.getLastActionLog());
                case WARN -> log.warn("Action {} -> {}", action.getType(), ctx.getLastActionLog());
                case ERROR -> {
                    log.error("Action {} error:{}", action.getType(), ctx.getLastActionLog());
                    currentStatus = ActionStatus.ERROR;
                }
                case TRACE -> {
                    if (log.isTraceEnabled()) {
                        log.info("Action {} -> {}", action.getType(), ctx.getLastActionLog());
                    }
                }
                default -> {
                }
            }
        }

        // now compile Result
        return resultActions;
    }

    @SuppressWarnings("unchecked")
    private Collection<? extends ManagerAction> performAction(HandlerAction action,
                                                              PaymentContext ctx) {
        var actionHandler = actionHandlersByType.get(action.getType());
        if (actionHandler == null) {
            return handleUnknownAction(action, ctx);
        }
        return actionHandler.performAction(action, ctx);
    }


    private List<? extends ManagerAction> handleUnknownAction(HandlerAction action,
                                                              PaymentContext ctx) {
        ctx.addLogEntry(action, ActionStatus.ERROR, "Unknown action in coinspaid service");
        return Collections.emptyList();
    }

    @Override
    public Collection<DepositMethod> getAvailableDepositMethods(Jurisdiction jd) {
        if (jd == Jurisdiction.CPT) {
            return cpService.getMethodsForTargetCurrency(PMCurrency.USDTE);
        }
        return Collections.emptyList();
    }

    @Override
    public Collection<PayoutMethod> getAvailablePayoutMethods(Jurisdiction jd) {
        if (jd == Jurisdiction.CPT) {
            return cpService.getMethodsForPayout(PMCurrency.USDTE);
        }
        return Collections.emptyList();
    }


    @Override
    public Optional<DepositDetails> getDepositDetailsForUserAndPaymentName(long userId,
                                                                           String currency) {
        var address = cpService.getDepositAddressFor(userId, currency);
        var exchangeRate = cpService.getDepositExchangeRate(currency).orElse(BigDecimal.ONE);
        return address.map(depositAddress -> new DepositDetails(true, depositAddress, exchangeRate));
    }

    @Override
    public List<UserDepositAddress> getUserDepositAddresses(long userId) {
        return cpService.getUserDepositAddresses(userId).stream().map(e -> new UserDepositAddress(e.getFromCurrency(), e.getAddress(), e.getTag(), e.getCreatedAt())).toList();
    }


    @Override
    public Optional<PayoutDetails> getPayoutDetailsForUserAndPaymentName(Long userId,
                                                                         String currency) {
        return cpService.getPayoutDetailsFor(userId, currency);
    }


}
